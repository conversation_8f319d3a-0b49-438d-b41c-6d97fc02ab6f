{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/character/CharacterManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Avatar,\n  Tag,\n  Tabs,\n  List,\n  Popconfirm,\n  message,\n  Row,\n  Col,\n  Divider,\n  Badge,\n  Tooltip\n} from 'antd';\nimport {\n  TeamOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  UserOutlined,\n  HeartOutlined,\n  ThunderboltOutlined,\n  CrownOutlined,\n  StarOutlined,\n  EyeOutlined,\n  LinkOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { Character } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst CharacterManager: React.FC = () => {\n  const {\n    currentProject,\n    characters,\n    addCharacter,\n    updateCharacter,\n    deleteCharacter,\n    getCharacters\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingCharacter, setEditingCharacter] = useState<Character | null>(null);\n  const [selected<PERSON>haracter, setSelectedCharacter] = useState<Character | null>(null);\n  const [activeTab, setActiveTab] = useState('list');\n  const [form] = Form.useForm();\n\n  const projectCharacters = currentProject ? getCharacters(currentProject.id) : [];\n\n  // 角色模板\n  const characterTemplates = [\n    {\n      name: '主角模板',\n      role: 'protagonist' as const,\n      personality: ['勇敢', '善良', '坚韧'],\n      description: '故事的主要角色，推动情节发展'\n    },\n    {\n      name: '反派模板',\n      role: 'antagonist' as const,\n      personality: ['狡猾', '野心勃勃', '冷酷'],\n      description: '与主角对立的角色'\n    },\n    {\n      name: '配角模板',\n      role: 'supporting' as const,\n      personality: ['忠诚', '幽默', '可靠'],\n      description: '支持主角的重要角色'\n    }\n  ];\n\n  const handleCreateCharacter = (template?: any) => {\n    setEditingCharacter(null);\n    if (template) {\n      form.setFieldsValue({\n        role: template.role,\n        personality: template.personality,\n      });\n    } else {\n      form.resetFields();\n    }\n    setIsModalVisible(true);\n  };\n\n  const handleEditCharacter = (character: Character) => {\n    setEditingCharacter(character);\n    form.setFieldsValue({\n      name: character.name,\n      age: character.age,\n      gender: character.gender,\n      role: character.role,\n      personality: character.personality,\n      background: character.background,\n      appearance: character.appearance,\n      dialogueStyle: character.dialogueStyle,\n      developmentArc: character.developmentArc,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteCharacter = (characterId: string) => {\n    if (currentProject) {\n      deleteCharacter(currentProject.id, characterId);\n      message.success('角色已删除');\n      if (selectedCharacter?.id === characterId) {\n        setSelectedCharacter(null);\n      }\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (editingCharacter) {\n        // 更新角色\n        if (currentProject) {\n          updateCharacter(currentProject.id, editingCharacter.id, {\n            ...values,\n            personality: values.personality || [],\n            relationships: editingCharacter.relationships || [],\n            appearances: editingCharacter.appearances || [],\n          });\n          message.success('角色已更新');\n        }\n      } else {\n        // 创建新角色\n        if (currentProject) {\n          addCharacter(currentProject.id, {\n            ...values,\n            personality: values.personality || [],\n            relationships: [],\n            appearances: [],\n          });\n          message.success('角色已创建');\n        }\n      }\n\n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n    setEditingCharacter(null);\n  };\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'protagonist': return <CrownOutlined className=\"text-yellow-500\" />;\n      case 'antagonist': return <ThunderboltOutlined className=\"text-red-500\" />;\n      case 'supporting': return <StarOutlined className=\"text-blue-500\" />;\n      default: return <UserOutlined className=\"text-gray-500\" />;\n    }\n  };\n\n  const getRoleText = (role: string) => {\n    switch (role) {\n      case 'protagonist': return '主角';\n      case 'antagonist': return '反派';\n      case 'supporting': return '配角';\n      case 'minor': return '次要角色';\n      default: return '未知';\n    }\n  };\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'protagonist': return 'gold';\n      case 'antagonist': return 'red';\n      case 'supporting': return 'blue';\n      case 'minor': return 'default';\n      default: return 'default';\n    }\n  };\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理角色。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>角色管理</Title>\n          <Text type=\"secondary\">管理小说角色设定和关系 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => handleCreateCharacter()}\n          >\n            创建角色\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'list',\n            label: (\n              <span>\n                <TeamOutlined />\n                角色列表 ({projectCharacters.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {/* 角色模板快速创建 */}\n                <Card className=\"mb-6\" title=\"快速创建\">\n                  <Row gutter={16}>\n                    {characterTemplates.map((template, index) => (\n                      <Col span={8} key={index}>\n                        <Card\n                          size=\"small\"\n                          className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                          onClick={() => handleCreateCharacter(template)}\n                        >\n                          <div className=\"text-center\">\n                            {getRoleIcon(template.role)}\n                            <div className=\"mt-2\">\n                              <Text strong>{template.name}</Text>\n                              <div className=\"text-xs text-gray-500 mt-1\">\n                                {template.description}\n                              </div>\n                            </div>\n                          </div>\n                        </Card>\n                      </Col>\n                    ))}\n                  </Row>\n                </Card>\n\n                {/* 角色列表 */}\n                {projectCharacters.length === 0 ? (\n                  <Card className=\"text-center py-12\">\n                    <TeamOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                    <div className=\"mt-4\">\n                      <Text type=\"secondary\">还没有创建任何角色</Text>\n                      <br />\n                      <Text type=\"secondary\">点击上方按钮或使用模板快速创建角色</Text>\n                    </div>\n                  </Card>\n                ) : (\n                  <Row gutter={[16, 16]}>\n                    {projectCharacters.map((character) => (\n                      <Col span={8} key={character.id}>\n                        <Card\n                          className=\"hover:shadow-lg transition-shadow cursor-pointer\"\n                          onClick={() => setSelectedCharacter(character)}\n                          actions={[\n                            <Tooltip title=\"查看详情\" key=\"view\">\n                              <EyeOutlined onClick={(e) => {\n                                e.stopPropagation();\n                                setSelectedCharacter(character);\n                              }} />\n                            </Tooltip>,\n                            <Tooltip title=\"编辑\" key=\"edit\">\n                              <EditOutlined onClick={(e) => {\n                                e.stopPropagation();\n                                handleEditCharacter(character);\n                              }} />\n                            </Tooltip>,\n                            <Popconfirm\n                              key=\"delete\"\n                              title=\"确定要删除这个角色吗？\"\n                              onConfirm={(e) => {\n                                e?.stopPropagation();\n                                handleDeleteCharacter(character.id);\n                              }}\n                              okText=\"确定\"\n                              cancelText=\"取消\"\n                            >\n                              <DeleteOutlined onClick={(e) => e.stopPropagation()} />\n                            </Popconfirm>\n                          ]}\n                        >\n                          <div className=\"text-center\">\n                            <Avatar\n                              size={64}\n                              icon={<UserOutlined />}\n                              src={character.avatar}\n                              className=\"mb-3\"\n                            />\n                            <div className=\"mb-2\">\n                              <Text strong className=\"text-lg\">{character.name}</Text>\n                              {character.age && (\n                                <Text type=\"secondary\" className=\"ml-2\">({character.age}岁)</Text>\n                              )}\n                            </div>\n                            <div className=\"mb-2\">\n                              <Tag color={getRoleColor(character.role)} icon={getRoleIcon(character.role)}>\n                                {getRoleText(character.role)}\n                              </Tag>\n                              {character.gender && (\n                                <Tag>{character.gender}</Tag>\n                              )}\n                            </div>\n                            <Paragraph\n                              ellipsis={{ rows: 2 }}\n                              type=\"secondary\"\n                              className=\"text-sm\"\n                            >\n                              {character.background || '暂无背景描述'}\n                            </Paragraph>\n                            {character.personality.length > 0 && (\n                              <div className=\"mt-2\">\n                                {character.personality.slice(0, 3).map((trait, index) => (\n                                  <Tag key={index} size=\"small\" color=\"blue\">\n                                    {trait}\n                                  </Tag>\n                                ))}\n                                {character.personality.length > 3 && (\n                                  <Tag size=\"small\">+{character.personality.length - 3}</Tag>\n                                )}\n                              </div>\n                            )}\n                          </div>\n                        </Card>\n                      </Col>\n                    ))}\n                  </Row>\n                )}\n              </div>\n            ),\n          },\n          {\n            key: 'relationships',\n            label: (\n              <span>\n                <LinkOutlined />\n                关系图谱\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <LinkOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">角色关系图谱功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持可视化角色关系网络</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n\n      {/* 角色详情侧边栏 */}\n      {selectedCharacter && (\n        <Modal\n          title={`角色详情 - ${selectedCharacter.name}`}\n          open={!!selectedCharacter}\n          onCancel={() => setSelectedCharacter(null)}\n          footer={[\n            <Button key=\"edit\" type=\"primary\" onClick={() => {\n              handleEditCharacter(selectedCharacter);\n              setSelectedCharacter(null);\n            }}>\n              编辑角色\n            </Button>,\n            <Button key=\"close\" onClick={() => setSelectedCharacter(null)}>\n              关闭\n            </Button>\n          ]}\n          width={800}\n        >\n          <Row gutter={24}>\n            <Col span={8}>\n              <div className=\"text-center\">\n                <Avatar\n                  size={120}\n                  icon={<UserOutlined />}\n                  src={selectedCharacter.avatar}\n                  className=\"mb-4\"\n                />\n                <div className=\"mb-2\">\n                  <Title level={4}>{selectedCharacter.name}</Title>\n                  {selectedCharacter.age && (\n                    <Text type=\"secondary\">年龄: {selectedCharacter.age}岁</Text>\n                  )}\n                </div>\n                <div className=\"mb-4\">\n                  <Tag color={getRoleColor(selectedCharacter.role)} icon={getRoleIcon(selectedCharacter.role)}>\n                    {getRoleText(selectedCharacter.role)}\n                  </Tag>\n                  {selectedCharacter.gender && (\n                    <Tag>{selectedCharacter.gender}</Tag>\n                  )}\n                </div>\n              </div>\n            </Col>\n            <Col span={16}>\n              <div className=\"space-y-4\">\n                <div>\n                  <Text strong>性格特征:</Text>\n                  <div className=\"mt-2\">\n                    {selectedCharacter.personality.map((trait, index) => (\n                      <Tag key={index} color=\"blue\">{trait}</Tag>\n                    ))}\n                  </div>\n                </div>\n\n                {selectedCharacter.background && (\n                  <div>\n                    <Text strong>角色背景:</Text>\n                    <Paragraph className=\"mt-2\">{selectedCharacter.background}</Paragraph>\n                  </div>\n                )}\n\n                {selectedCharacter.appearance && (\n                  <div>\n                    <Text strong>外貌描述:</Text>\n                    <Paragraph className=\"mt-2\">{selectedCharacter.appearance}</Paragraph>\n                  </div>\n                )}\n\n                {selectedCharacter.dialogueStyle && (\n                  <div>\n                    <Text strong>对话风格:</Text>\n                    <Paragraph className=\"mt-2\">{selectedCharacter.dialogueStyle}</Paragraph>\n                  </div>\n                )}\n\n                {selectedCharacter.developmentArc && (\n                  <div>\n                    <Text strong>发展轨迹:</Text>\n                    <Paragraph className=\"mt-2\">{selectedCharacter.developmentArc}</Paragraph>\n                  </div>\n                )}\n\n                <div>\n                  <Text strong>出场记录:</Text>\n                  <div className=\"mt-2\">\n                    <Badge count={selectedCharacter.appearances.length} showZero>\n                      <Text type=\"secondary\">章节出场次数</Text>\n                    </Badge>\n                  </div>\n                </div>\n              </div>\n            </Col>\n          </Row>\n        </Modal>\n      )}\n\n      {/* 创建/编辑角色模态框 */}\n      <Modal\n        title={editingCharacter ? '编辑角色' : '创建角色'}\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={800}\n        okText={editingCharacter ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"角色姓名\"\n                rules={[{ required: true, message: '请输入角色姓名' }]}\n              >\n                <Input placeholder=\"请输入角色姓名\" />\n              </Form.Item>\n            </Col>\n            <Col span={6}>\n              <Form.Item name=\"age\" label=\"年龄\">\n                <Input type=\"number\" placeholder=\"年龄\" />\n              </Form.Item>\n            </Col>\n            <Col span={6}>\n              <Form.Item name=\"gender\" label=\"性别\">\n                <Select placeholder=\"请选择性别\">\n                  <Option value=\"男\">男</Option>\n                  <Option value=\"女\">女</Option>\n                  <Option value=\"其他\">其他</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"role\"\n            label=\"角色定位\"\n            rules={[{ required: true, message: '请选择角色定位' }]}\n          >\n            <Select placeholder=\"请选择角色定位\">\n              <Option value=\"protagonist\">主角</Option>\n              <Option value=\"antagonist\">反派</Option>\n              <Option value=\"supporting\">配角</Option>\n              <Option value=\"minor\">次要角色</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"personality\"\n            label=\"性格特征\"\n            help=\"请输入角色的性格特征，用逗号分隔\"\n          >\n            <Select\n              mode=\"tags\"\n              placeholder=\"请输入性格特征，如：勇敢、善良、聪明\"\n              tokenSeparators={[',']}\n            >\n              <Option value=\"勇敢\">勇敢</Option>\n              <Option value=\"善良\">善良</Option>\n              <Option value=\"聪明\">聪明</Option>\n              <Option value=\"冷静\">冷静</Option>\n              <Option value=\"幽默\">幽默</Option>\n              <Option value=\"坚韧\">坚韧</Option>\n              <Option value=\"狡猾\">狡猾</Option>\n              <Option value=\"野心勃勃\">野心勃勃</Option>\n              <Option value=\"忠诚\">忠诚</Option>\n              <Option value=\"可靠\">可靠</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item name=\"background\" label=\"角色背景\">\n            <TextArea\n              rows={3}\n              placeholder=\"请描述角色的背景故事、成长经历等...\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"appearance\" label=\"外貌描述\">\n            <TextArea\n              rows={2}\n              placeholder=\"请描述角色的外貌特征...\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"dialogueStyle\" label=\"对话风格\">\n            <TextArea\n              rows={2}\n              placeholder=\"请描述角色的说话方式、语言特点...\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"developmentArc\" label=\"发展轨迹\">\n            <TextArea\n              rows={3}\n              placeholder=\"请描述角色在故事中的成长和变化...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default CharacterManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AArCA;;;;;AAwCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAE1B,MAAM,mBAA6B;;IACjC,MAAM,EACJ,cAAc,EACd,UAAU,EACV,YAAY,EACZ,eAAe,EACf,eAAe,EACf,aAAa,EACd,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,oBAAoB,iBAAiB,cAAc,eAAe,EAAE,IAAI,EAAE;IAEhF,OAAO;IACP,MAAM,qBAAqB;QACzB;YACE,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAM;gBAAM;aAAK;YAC/B,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAM;gBAAQ;aAAK;YACjC,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAM;gBAAM;aAAK;YAC/B,aAAa;QACf;KACD;IAED,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB;QACpB,IAAI,UAAU;YACZ,KAAK,cAAc,CAAC;gBAClB,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;YACnC;QACF,OAAO;YACL,KAAK,WAAW;QAClB;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB;QACpB,KAAK,cAAc,CAAC;YAClB,MAAM,UAAU,IAAI;YACpB,KAAK,UAAU,GAAG;YAClB,QAAQ,UAAU,MAAM;YACxB,MAAM,UAAU,IAAI;YACpB,aAAa,UAAU,WAAW;YAClC,YAAY,UAAU,UAAU;YAChC,YAAY,UAAU,UAAU;YAChC,eAAe,UAAU,aAAa;YACtC,gBAAgB,UAAU,cAAc;QAC1C;QACA,kBAAkB;IACpB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,gBAAgB;YAClB,gBAAgB,eAAe,EAAE,EAAE;YACnC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,IAAI,CAAA,8BAAA,wCAAA,kBAAmB,EAAE,MAAK,aAAa;gBACzC,qBAAqB;YACvB;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,kBAAkB;gBACpB,OAAO;gBACP,IAAI,gBAAgB;oBAClB,gBAAgB,eAAe,EAAE,EAAE,iBAAiB,EAAE,EAAE;wBACtD,GAAG,MAAM;wBACT,aAAa,OAAO,WAAW,IAAI,EAAE;wBACrC,eAAe,iBAAiB,aAAa,IAAI,EAAE;wBACnD,aAAa,iBAAiB,WAAW,IAAI,EAAE;oBACjD;oBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF,OAAO;gBACL,QAAQ;gBACR,IAAI,gBAAgB;oBAClB,aAAa,eAAe,EAAE,EAAE;wBAC9B,GAAG,MAAM;wBACT,aAAa,OAAO,WAAW,IAAI,EAAE;wBACrC,eAAe,EAAE;wBACjB,aAAa,EAAE;oBACjB;oBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF;YAEA,kBAAkB;YAClB,KAAK,WAAW;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,KAAK,WAAW;QAChB,oBAAoB;IACtB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAe,qBAAO,6LAAC,uNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAc,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACzD,KAAK;gBAAc,qBAAO,6LAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YAClD;gBAAS,qBAAO,6LAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;QAC1C;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;;oCAAY;oCAAmB,eAAe,IAAI;;;;;;;;;;;;;kCAE/D,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM;sCAChB;;;;;;;;;;;;;;;;;0BAML,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,qNAAA,CAAA,eAAY;;;;;gCAAG;gCACT,kBAAkB,MAAM;gCAAC;;;;;;;wBAGpC,wBACE,6LAAC;;8CAEC,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,OAAM;8CAC3B,cAAA,6LAAC,+KAAA,CAAA,MAAG;wCAAC,QAAQ;kDACV,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBACjC,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,sBAAsB;8DAErC,cAAA,6LAAC;wDAAI,WAAU;;4DACZ,YAAY,SAAS,IAAI;0EAC1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAM;kFAAE,SAAS,IAAI;;;;;;kFAC3B,6LAAC;wEAAI,WAAU;kFACZ,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;+CAXZ;;;;;;;;;;;;;;;gCAsBxB,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,qNAAA,CAAA,eAAY;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;;;;;8DACD,6LAAC;oDAAK,MAAK;8DAAY;;;;;;;;;;;;;;;;;2DAI3B,6LAAC,+KAAA,CAAA,MAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;8CAClB,kBAAkB,GAAG,CAAC,CAAC,0BACtB,6LAAC,+KAAA,CAAA,MAAG;4CAAC,MAAM;sDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDACH,WAAU;gDACV,SAAS,IAAM,qBAAqB;gDACpC,SAAS;kEACP,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,mNAAA,CAAA,cAAW;4DAAC,SAAS,CAAC;gEACrB,EAAE,eAAe;gEACjB,qBAAqB;4DACvB;;;;;;uDAJwB;;;;;kEAM1B,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,qNAAA,CAAA,eAAY;4DAAC,SAAS,CAAC;gEACtB,EAAE,eAAe;gEACjB,oBAAoB;4DACtB;;;;;;uDAJsB;;;;;kEAMxB,6LAAC,6LAAA,CAAA,aAAU;wDAET,OAAM;wDACN,WAAW,CAAC;4DACV,cAAA,wBAAA,EAAG,eAAe;4DAClB,sBAAsB,UAAU,EAAE;wDACpC;wDACA,QAAO;wDACP,YAAW;kEAEX,cAAA,6LAAC,yNAAA,CAAA,iBAAc;4DAAC,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;uDAT7C;;;;;iDAWP;0DAED,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qLAAA,CAAA,SAAM;4DACL,MAAM;4DACN,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4DACnB,KAAK,UAAU,MAAM;4DACrB,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,MAAM;oEAAC,WAAU;8EAAW,UAAU,IAAI;;;;;;gEAC/C,UAAU,GAAG,kBACZ,6LAAC;oEAAK,MAAK;oEAAY,WAAU;;wEAAO;wEAAE,UAAU,GAAG;wEAAC;;;;;;;;;;;;;sEAG5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+KAAA,CAAA,MAAG;oEAAC,OAAO,aAAa,UAAU,IAAI;oEAAG,MAAM,YAAY,UAAU,IAAI;8EACvE,YAAY,UAAU,IAAI;;;;;;gEAE5B,UAAU,MAAM,kBACf,6LAAC,+KAAA,CAAA,MAAG;8EAAE,UAAU,MAAM;;;;;;;;;;;;sEAG1B,6LAAC;4DACC,UAAU;gEAAE,MAAM;4DAAE;4DACpB,MAAK;4DACL,WAAU;sEAET,UAAU,UAAU,IAAI;;;;;;wDAE1B,UAAU,WAAW,CAAC,MAAM,GAAG,mBAC9B,6LAAC;4DAAI,WAAU;;gEACZ,UAAU,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC7C,6LAAC,+KAAA,CAAA,MAAG;wEAAa,MAAK;wEAAQ,OAAM;kFACjC;uEADO;;;;;gEAIX,UAAU,WAAW,CAAC,MAAM,GAAG,mBAC9B,6LAAC,+KAAA,CAAA,MAAG;oEAAC,MAAK;;wEAAQ;wEAAE,UAAU,WAAW,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;2CAnE5C,UAAU,EAAE;;;;;;;;;;;;;;;;oBA+E3C;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,qNAAA,CAAA,eAAY;;;;;gCAAG;;;;;;;wBAIpB,wBACE,6LAAC,iLAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,qNAAA,CAAA,eAAY;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;YAIF,mCACC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,AAAC,UAAgC,OAAvB,kBAAkB,IAAI;gBACvC,MAAM,CAAC,CAAC;gBACR,UAAU,IAAM,qBAAqB;gBACrC,QAAQ;kCACN,6LAAC,qMAAA,CAAA,SAAM;wBAAY,MAAK;wBAAU,SAAS;4BACzC,oBAAoB;4BACpB,qBAAqB;wBACvB;kCAAG;uBAHS;;;;;kCAMZ,6LAAC,qMAAA,CAAA,SAAM;wBAAa,SAAS,IAAM,qBAAqB;kCAAO;uBAAnD;;;;;iBAGb;gBACD,OAAO;0BAEP,cAAA,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;;sCACX,6LAAC,+KAAA,CAAA,MAAG;4BAAC,MAAM;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qLAAA,CAAA,SAAM;wCACL,MAAM;wCACN,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,KAAK,kBAAkB,MAAM;wCAC7B,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,OAAO;0DAAI,kBAAkB,IAAI;;;;;;4CACvC,kBAAkB,GAAG,kBACpB,6LAAC;gDAAK,MAAK;;oDAAY;oDAAK,kBAAkB,GAAG;oDAAC;;;;;;;;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+KAAA,CAAA,MAAG;gDAAC,OAAO,aAAa,kBAAkB,IAAI;gDAAG,MAAM,YAAY,kBAAkB,IAAI;0DACvF,YAAY,kBAAkB,IAAI;;;;;;4CAEpC,kBAAkB,MAAM,kBACvB,6LAAC,+KAAA,CAAA,MAAG;0DAAE,kBAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAKtC,6LAAC,+KAAA,CAAA,MAAG;4BAAC,MAAM;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,kBAAkB,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzC,6LAAC,+KAAA,CAAA,MAAG;wDAAa,OAAM;kEAAQ;uDAArB;;;;;;;;;;;;;;;;oCAKf,kBAAkB,UAAU,kBAC3B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,kBAAkB,UAAU;;;;;;;;;;;;oCAI5D,kBAAkB,UAAU,kBAC3B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,kBAAkB,UAAU;;;;;;;;;;;;oCAI5D,kBAAkB,aAAa,kBAC9B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,kBAAkB,aAAa;;;;;;;;;;;;oCAI/D,kBAAkB,cAAc,kBAC/B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,kBAAkB,cAAc;;;;;;;;;;;;kDAIjE,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;oDAAC,OAAO,kBAAkB,WAAW,CAAC,MAAM;oDAAE,QAAQ;8DAC1D,cAAA,6LAAC;wDAAK,MAAK;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWvC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,mBAAmB,SAAS;gBACnC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ,mBAAmB,OAAO;gBAClC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCAAC,MAAK;wCAAM,OAAM;kDAC1B,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,MAAK;4CAAS,aAAY;;;;;;;;;;;;;;;;8CAGrC,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCAAC,MAAK;wCAAS,OAAM;kDAC7B,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCAAC,aAAY;;kDAClB,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,6LAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;sCAI1B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,MAAK;sCAEL,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,MAAK;gCACL,aAAY;gCACZ,iBAAiB;oCAAC;iCAAI;;kDAEtB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;;;;;;;;;;;;sCAIvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAa,OAAM;sCACjC,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAa,OAAM;sCACjC,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAgB,OAAM;sCACpC,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAiB,OAAM;sCACrC,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GArhBM;;QAQA,wHAAA,CAAA,cAAW;QAMA,iLAAA,CAAA,OAAI,CAAC;;;KAdhB;uCAuhBS", "debugId": null}}]}