'use client';

import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Dropdown, Avatar, Badge, Space, Typography, Tooltip } from 'antd';
import { 
  MenuFoldOutlined, 
  MenuUnfoldOutlined,
  BellOutlined,
  SettingOutlined,
  UserOutlined,
  ProjectOutlined,
  FileTextOutlined,
  TeamOutlined,
  GlobalOutlined,
  BookOutlined,
  BranchesOutlined,
  ThunderboltOutlined,
  FolderOutlined,
  SunOutlined,
  MoonOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/store';
import AIConfigModal from '@/components/settings/AIConfigModal';
import { aiService } from '@/utils/aiService';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const {
    ui,
    toggleSidebar,
    setTheme,
    setActiveTab,
    currentProject
  } = useAppStore();

  const [collapsed, setCollapsed] = useState(ui.sidebarCollapsed);
  const [isAIConfigVisible, setIsAIConfigVisible] = useState(false);
  const [isAIConfigured, setIsAIConfigured] = useState(false);

  // 检查AI配置状态（仅在客户端）
  useEffect(() => {
    setIsAIConfigured(aiService.isConfigured());
  }, []);

  const handleToggleSidebar = () => {
    setCollapsed(!collapsed);
    toggleSidebar();
  };

  const handleThemeToggle = () => {
    setTheme(ui.theme === 'light' ? 'dark' : 'light');
  };

  // 侧边栏菜单项
  const menuItems: MenuProps['items'] = [
    {
      key: 'workflow',
      icon: <ThunderboltOutlined />,
      label: '工作流编辑器',
    },
    {
      key: 'projects',
      icon: <ProjectOutlined />,
      label: '项目总览',
    },
    {
      type: 'divider',
    },
    {
      key: 'content-management',
      icon: <FileTextOutlined />,
      label: '内容管理',
      children: [
        {
          key: 'outlines',
          icon: <FileTextOutlined />,
          label: '大纲管理',
        },
        {
          key: 'characters',
          icon: <TeamOutlined />,
          label: '角色管理',
        },
        {
          key: 'worldbuilding',
          icon: <GlobalOutlined />,
          label: '世界观管理',
        },
        {
          key: 'plotlines',
          icon: <BranchesOutlined />,
          label: '主线管理',
        },
        {
          key: 'titles',
          icon: <BookOutlined />,
          label: '书名管理',
        },
      ],
    },
    {
      type: 'divider',
    },
    {
      key: 'documents',
      icon: <FolderOutlined />,
      label: '文档管理',
    },
    {
      key: 'prompts',
      icon: <FileTextOutlined />,
      label: '提示词管理',
    },
  ];

  // 用户菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: '退出登录',
    },
  ];

  // 通知菜单
  const notifications = ui?.notifications || [];
  const notificationMenuItems: MenuProps['items'] = notifications.slice(0, 5).map(notification => ({
    key: notification.id,
    label: (
      <div className="max-w-xs">
        <div className="font-medium text-sm">{notification.title}</div>
        <div className="text-xs text-gray-500 mt-1">{notification.message}</div>
        <div className="text-xs text-gray-400 mt-1">
          {notification.timestamp.toLocaleTimeString()}
        </div>
      </div>
    ),
  }));

  const handleMenuClick = ({ key }: { key: string }) => {
    setActiveTab(key);
  };

  return (
    <Layout className="min-h-screen">
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        width={240}
        className="bg-white border-r border-gray-200"
        theme="light"
      >
        {/* Logo区域 */}
        <div className="h-16 flex items-center justify-center border-b border-gray-200">
          {collapsed ? (
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <ThunderboltOutlined className="text-white text-lg" />
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <ThunderboltOutlined className="text-white text-lg" />
              </div>
              <Text strong className="text-lg">AI小说工作流</Text>
            </div>
          )}
        </div>

        {/* 当前项目信息 */}
        {!collapsed && currentProject && (
          <div className="p-4 border-b border-gray-200 bg-gray-50">
            <Text type="secondary" className="text-xs">当前项目</Text>
            <div className="mt-1">
              <Text strong className="text-sm">{currentProject.name}</Text>
            </div>
            <div className="mt-1">
              <Text type="secondary" className="text-xs">
                {currentProject.status === 'draft' && '草稿'}
                {currentProject.status === 'in-progress' && '进行中'}
                {currentProject.status === 'completed' && '已完成'}
              </Text>
            </div>
          </div>
        )}

        {/* 菜单 */}
        <Menu
          mode="inline"
          selectedKeys={[ui.activeTab]}
          items={menuItems}
          onClick={handleMenuClick}
          className="border-none"
        />
      </Sider>

      <Layout>
        {/* 顶部导航栏 */}
        <Header className="bg-white border-b border-gray-200 px-4 flex items-center justify-between h-16">
          <div className="flex items-center space-x-4">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={handleToggleSidebar}
              className="text-lg"
            />
            
            {/* 面包屑导航 */}
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>
                {ui.activeTab === 'workflow' && '工作流编辑器'}
                {ui.activeTab === 'projects' && '项目总览'}
                {ui.activeTab === 'outlines' && '大纲管理'}
                {ui.activeTab === 'characters' && '角色管理'}
                {ui.activeTab === 'worldbuilding' && '世界观管理'}
                {ui.activeTab === 'plotlines' && '主线管理'}
                {ui.activeTab === 'titles' && '书名管理'}
                {ui.activeTab === 'documents' && '文档管理'}
                {ui.activeTab === 'prompts' && '提示词管理'}
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* 主题切换 */}
            <Button
              type="text"
              icon={ui.theme === 'light' ? <MoonOutlined /> : <SunOutlined />}
              onClick={handleThemeToggle}
              className="text-lg"
            />

            {/* 通知 */}
            <Dropdown
              menu={{ items: notificationMenuItems }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button type="text" className="text-lg">
                <Badge count={notifications.filter(n => !n.read).length} size="small">
                  <BellOutlined />
                </Badge>
              </Button>
            </Dropdown>

            {/* AI配置按钮 */}
            <Tooltip title={isAIConfigured ? "AI已配置" : "配置AI API"}>
              <Button
                type="text"
                className="text-lg"
                onClick={() => setIsAIConfigVisible(true)}
                style={{
                  color: isAIConfigured ? '#52c41a' : '#faad14'
                }}
              >
                <ApiOutlined />
              </Button>
            </Tooltip>

            {/* 用户菜单 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Space className="cursor-pointer">
                <Avatar icon={<UserOutlined />} />
                <Text>用户</Text>
              </Space>
            </Dropdown>
          </div>
        </Header>

        {/* 主内容区域 */}
        <Content className="bg-gray-50 overflow-auto">
          {children}
        </Content>
      </Layout>

      {/* AI配置模态框 */}
      <AIConfigModal
        visible={isAIConfigVisible}
        onClose={() => setIsAIConfigVisible(false)}
        onConfigured={() => {
          setIsAIConfigVisible(false);
          setIsAIConfigured(true);
        }}
      />
    </Layout>
  );
};

export default MainLayout;
