# AI功能测试示例

## 测试环境准备

### 1. AI API配置测试
```javascript
// 测试配置示例
const testConfig = {
  provider: 'openai',
  apiKey: 'sk-your-api-key-here',
  baseUrl: 'https://api.openai.com/v1/chat/completions',
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 2000
};
```

### 2. 基础连接测试
- 打开AI配置模态框
- 输入有效的API配置
- 点击"测试连接"按钮
- 验证返回"连接成功"消息

## 工作流助手测试

### 智能推荐模式测试
```javascript
// 测试输入数据
const testRequirements = {
  genre: '现代都市',
  style: '轻松幽默',
  length: '中篇',
  experience: '进阶',
  features: ['高质量', '快速生成']
};
```

**预期结果**:
- AI应该推荐适合的工作流模板
- 显示推荐置信度（通常>80%）
- 提供推荐理由和备选方案

### 自定义生成模式测试
```javascript
// 测试输入数据
const customRequirements = {
  genre: '奇幻冒险',
  style: '史诗风格',
  length: '长篇',
  features: ['复杂世界观', '多角色设定', '详细大纲']
};
```

**预期结果**:
- 生成包含世界观构建节点的工作流
- 包含多个角色创建节点
- 有详细大纲生成节点

## 节点功能测试

### 1. 书名生成器测试
```javascript
// 测试参数
const titleParams = {
  genre: '现代都市',
  style: '轻松幽默',
  keywords: ['爱情', '职场', '成长'],
  count: 5
};
```

**预期输出格式**:
```json
{
  "titles": [
    {
      "title": "都市恋爱进行时",
      "score": 85,
      "analysis": {
        "appeal": 80,
        "memorability": 85,
        "genre_match": 90,
        "uniqueness": 75,
        "marketability": 85
      },
      "suggestions": ["可以考虑加入更具体的职场元素"]
    }
  ]
}
```

### 2. 角色创建器测试
```javascript
// 测试参数
const characterParams = {
  genre: '现代都市',
  role: '女主角',
  background: '职场新人',
  personality: '乐观开朗但有些迷茫'
};
```

**预期输出格式**:
```json
{
  "name": "林小雨",
  "age": 25,
  "gender": "女",
  "appearance": "清秀可人，总是带着温暖的笑容...",
  "personality": "乐观开朗，但在面对职场挑战时会显得有些迷茫...",
  "background": "刚从大学毕业的职场新人...",
  "skills": ["沟通能力强", "学习能力快"],
  "relationships": "与室友关系很好，正在适应新的工作环境",
  "goals": "在职场中找到自己的位置，实现个人价值",
  "flaws": "有时过于理想化，缺乏实际经验"
}
```

### 3. 世界观构建器测试
```javascript
// 测试参数
const worldParams = {
  genre: '奇幻修仙',
  style: '东方玄幻',
  scope: '大型世界',
  elements: ['修仙体系', '门派势力', '灵兽']
};
```

**预期输出内容**:
- 完整的修仙世界设定
- 详细的修炼体系
- 门派势力分布
- 地理环境描述

### 4. 章节生成器测试
```javascript
// 测试参数
const chapterParams = {
  chapterNumber: 1,
  chapterOutline: {
    title: "初入职场",
    summary: "女主角第一天上班的经历",
    key_events: ["报到", "认识同事", "第一个任务"]
  },
  characters: [/* 角色数据 */],
  style: "轻松幽默",
  targetWordCount: 2000
};
```

**预期输出**:
- 约2000字的章节内容
- 符合轻松幽默的风格
- 包含指定的关键事件
- 角色描写生动

## 工作流执行测试

### 完整工作流测试流程

1. **创建测试工作流**
   ```
   输入节点 → 书名生成 → 角色创建 → 大纲生成 → 章节生成 → 输出节点
   ```

2. **配置节点参数**
   - 输入节点：设置基本创作参数
   - 书名生成：设置生成数量为3
   - 角色创建：设置为主角
   - 大纲生成：设置章节数为5
   - 章节生成：设置目标字数为1500

3. **执行测试**
   - 点击"开始执行"
   - 观察每个节点的执行状态
   - 检查进度条更新
   - 查看执行日志

4. **验证结果**
   - 每个节点都应该成功完成
   - 生成的内容应该符合预期格式
   - 最终输出应该包含完整的小说框架

### 错误处理测试

1. **API配置错误测试**
   - 使用无效的API密钥
   - 验证系统显示适当的错误信息
   - 确认工作流不会继续执行

2. **网络连接测试**
   - 模拟网络中断
   - 验证系统的重试机制
   - 检查错误恢复功能

3. **参数验证测试**
   - 提供无效的输入参数
   - 验证系统的参数验证功能
   - 确认显示有用的错误提示

## 性能测试

### 1. 响应时间测试
- 书名生成：应在10-30秒内完成
- 角色创建：应在15-45秒内完成
- 章节生成：应在30-90秒内完成

### 2. 并发测试
- 同时执行多个节点
- 验证系统稳定性
- 检查资源使用情况

### 3. 大量数据测试
- 生成长篇小说（20+章节）
- 验证系统处理能力
- 检查内存使用情况

## 质量评估标准

### 1. 内容质量
- **相关性**: 生成内容与输入需求的匹配度
- **连贯性**: 前后文的逻辑连贯性
- **创意性**: 内容的原创性和创意水平
- **可读性**: 文本的流畅度和可读性

### 2. 技术质量
- **响应时间**: API调用的响应速度
- **成功率**: 请求的成功完成率
- **错误处理**: 错误情况的处理能力
- **稳定性**: 系统的整体稳定性

### 3. 用户体验
- **界面友好性**: 操作界面的易用性
- **反馈及时性**: 系统反馈的及时性
- **错误提示**: 错误信息的清晰度
- **功能完整性**: 功能的完整性和实用性

## 测试检查清单

### 基础功能测试
- [ ] AI API配置和连接测试
- [ ] 工作流助手三种模式测试
- [ ] 所有节点类型的功能测试
- [ ] 工作流执行器测试

### 集成测试
- [ ] 节点间数据传递测试
- [ ] 完整工作流端到端测试
- [ ] 与其他系统模块的集成测试

### 异常测试
- [ ] API错误处理测试
- [ ] 网络异常处理测试
- [ ] 参数验证测试
- [ ] 边界条件测试

### 性能测试
- [ ] 响应时间测试
- [ ] 并发处理测试
- [ ] 大数据量测试
- [ ] 资源使用测试

### 用户体验测试
- [ ] 界面交互测试
- [ ] 错误提示测试
- [ ] 帮助文档测试
- [ ] 整体流程测试

## 测试报告模板

```markdown
# AI功能测试报告

## 测试概述
- 测试时间：[日期]
- 测试环境：[环境描述]
- 测试范围：[测试范围]

## 测试结果
### 功能测试
- 通过率：[百分比]
- 失败项目：[列表]

### 性能测试
- 平均响应时间：[时间]
- 成功率：[百分比]

### 问题总结
1. [问题描述]
2. [问题描述]

## 改进建议
1. [建议内容]
2. [建议内容]
```

通过以上测试流程，可以全面验证AI功能的正确性、稳定性和用户体验，确保系统能够为用户提供高质量的AI辅助创作服务。
