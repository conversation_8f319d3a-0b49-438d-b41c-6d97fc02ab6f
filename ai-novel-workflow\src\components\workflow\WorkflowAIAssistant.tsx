'use client';

import React, { useState } from 'react';
import {
  Modal,
  Form,
  Select,
  Button,
  Card,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Divider,
  Steps,
  Radio,
  Checkbox,
  message,
  Badge
} from 'antd';
import {
  RobotOutlined,
  BulbOutlined,
  ThunderboltOutlined,
  StarOutlined,
  CheckCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { WorkflowAI, WorkflowTemplate, WORKFLOW_TEMPLATES } from '@/utils/workflowAI';
import { aiService } from '@/utils/aiService';
import AIConfigModal from '@/components/settings/AIConfigModal';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Step } = Steps;

interface WorkflowAIAssistantProps {
  visible: boolean;
  onClose: () => void;
  onApplyWorkflow: (template: WorkflowTemplate) => void;
  currentNodes?: any[];
}

const WorkflowAIAssistant: React.FC<WorkflowAIAssistantProps> = ({
  visible,
  onClose,
  onApplyWorkflow,
  currentNodes = []
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [recommendations, setRecommendations] = useState<WorkflowTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [mode, setMode] = useState<'recommend' | 'custom' | 'optimize'>('recommend');
  const [loading, setLoading] = useState(false);
  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);

  const handleAnalyze = async () => {
    // 检查AI配置
    if (!aiService.isConfigured()) {
      message.warning('请先配置AI API');
      setIsConfigModalVisible(true);
      return;
    }

    try {
      setLoading(true);
      const values = await form.validateFields();

      if (mode === 'recommend') {
        // 使用AI分析需求
        const aiResponse = await aiService.analyzeWorkflowRequirements(values);

        if (aiResponse.success) {
          try {
            const aiResult = JSON.parse(aiResponse.data);
            // 根据AI推荐结果获取对应模板
            const recommendedTemplate = WORKFLOW_TEMPLATES.find(t => t.id === aiResult.recommended);
            const alternatives = aiResult.alternatives?.map((id: string) =>
              WORKFLOW_TEMPLATES.find(t => t.id === id)
            ).filter(Boolean) || [];

            const recs = recommendedTemplate ? [recommendedTemplate, ...alternatives] : WorkflowAI.analyzeRequirements(values);
            setRecommendations(recs);
            setCurrentStep(1);

            if (aiResult.confidence && aiResult.confidence > 80) {
              message.success(`AI推荐置信度: ${aiResult.confidence}%`);
            }
          } catch (parseError) {
            // AI返回格式不正确，使用本地分析
            const recs = WorkflowAI.analyzeRequirements(values);
            setRecommendations(recs);
            setCurrentStep(1);
            message.info('使用本地智能分析');
          }
        } else {
          // AI请求失败，使用本地分析
          const recs = WorkflowAI.analyzeRequirements(values);
          setRecommendations(recs);
          setCurrentStep(1);
          message.warning(`AI分析失败: ${aiResponse.error}，使用本地分析`);
        }
      } else if (mode === 'custom') {
        // 使用AI生成自定义工作流
        const aiResponse = await aiService.generateCustomWorkflow(values);

        if (aiResponse.success) {
          try {
            const customTemplate = JSON.parse(aiResponse.data);
            setSelectedTemplate(customTemplate);
            setCurrentStep(2);
            message.success('AI已生成定制化工作流');
          } catch (parseError) {
            // AI返回格式不正确，使用本地生成
            const customTemplate = WorkflowAI.generateCustomWorkflow(values);
            setSelectedTemplate(customTemplate);
            setCurrentStep(2);
            message.info('使用本地生成器');
          }
        } else {
          // AI请求失败，使用本地生成
          const customTemplate = WorkflowAI.generateCustomWorkflow(values);
          setSelectedTemplate(customTemplate);
          setCurrentStep(2);
          message.warning(`AI生成失败: ${aiResponse.error}，使用本地生成`);
        }
      } else if (mode === 'optimize') {
        // 使用AI优化现有工作流
        const aiResponse = await aiService.optimizeWorkflow(currentNodes, values);

        if (aiResponse.success) {
          try {
            const optimizationResult = JSON.parse(aiResponse.data);
            if (optimizationResult.optimized_workflow) {
              setSelectedTemplate(optimizationResult.optimized_workflow);
              setCurrentStep(2);
              message.success(`AI优化完成，改进评分: ${optimizationResult.improvement_score || 'N/A'}`);
            } else {
              message.info('AI建议当前工作流已经很好，无需大幅调整');
            }
          } catch (parseError) {
            // AI返回格式不正确，使用本地优化
            const optimizedTemplate = WorkflowAI.optimizeWorkflow(currentNodes, values);
            setSelectedTemplate(optimizedTemplate);
            setCurrentStep(2);
            message.info('使用本地优化器');
          }
        } else {
          // AI请求失败，使用本地优化
          const optimizedTemplate = WorkflowAI.optimizeWorkflow(currentNodes, values);
          setSelectedTemplate(optimizedTemplate);
          setCurrentStep(2);
          message.warning(`AI优化失败: ${aiResponse.error}，使用本地优化`);
        }
      }
    } catch (error) {
      console.error('分析失败:', error);
      message.error('分析过程中出现错误');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectTemplate = (template: WorkflowTemplate) => {
    setSelectedTemplate(template);
    setCurrentStep(2);
  };

  const handleApply = () => {
    if (selectedTemplate) {
      onApplyWorkflow(selectedTemplate);
      message.success('工作流已应用！');
      onClose();
      resetState();
    }
  };

  const resetState = () => {
    setCurrentStep(0);
    setRecommendations([]);
    setSelectedTemplate(null);
    form.resetFields();
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'green';
      case 'medium': return 'orange';
      case 'complex': return 'red';
      default: return 'default';
    }
  };

  const getComplexityText = (complexity: string) => {
    switch (complexity) {
      case 'simple': return '简单';
      case 'medium': return '中等';
      case 'complex': return '复杂';
      default: return '未知';
    }
  };

  return (
    <Modal
      title={
        <div className="flex items-center space-x-2">
          <RobotOutlined className="text-blue-500" />
          <span>AI工作流助手</span>
        </div>
      }
      open={visible}
      onCancel={() => {
        onClose();
        resetState();
      }}
      width={800}
      footer={null}
      destroyOnClose
    >
      <div className="space-y-6">
        {/* 模式选择 */}
        <Card size="small">
          <Radio.Group 
            value={mode} 
            onChange={(e) => {
              setMode(e.target.value);
              resetState();
            }}
            className="w-full"
          >
            <Row gutter={16}>
              <Col span={8}>
                <Radio.Button value="recommend" className="w-full text-center">
                  <div className="py-2">
                    <BulbOutlined className="text-lg block mb-1" />
                    <div>智能推荐</div>
                    <Text type="secondary" className="text-xs">基于需求推荐模板</Text>
                  </div>
                </Radio.Button>
              </Col>
              <Col span={8}>
                <Radio.Button value="custom" className="w-full text-center">
                  <div className="py-2">
                    <SettingOutlined className="text-lg block mb-1" />
                    <div>自定义生成</div>
                    <Text type="secondary" className="text-xs">AI生成定制工作流</Text>
                  </div>
                </Radio.Button>
              </Col>
              <Col span={8}>
                <Radio.Button value="optimize" className="w-full text-center">
                  <div className="py-2">
                    <ThunderboltOutlined className="text-lg block mb-1" />
                    <div>优化现有</div>
                    <Text type="secondary" className="text-xs">优化当前工作流</Text>
                  </div>
                </Radio.Button>
              </Col>
            </Row>
          </Radio.Group>
        </Card>

        {/* 步骤指示器 */}
        <Steps current={currentStep} size="small">
          <Step title="需求分析" icon={<BulbOutlined />} />
          {mode === 'recommend' && <Step title="模板推荐" icon={<StarOutlined />} />}
          <Step title="预览确认" icon={<CheckCircleOutlined />} />
        </Steps>

        {/* 步骤内容 */}
        {currentStep === 0 && (
          <Card title="告诉我您的创作需求">
            <Form form={form} layout="vertical">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="genre"
                    label="小说类型"
                    rules={[{ required: true, message: '请选择小说类型' }]}
                  >
                    <Select placeholder="请选择类型">
                      <Option value="现代都市">现代都市</Option>
                      <Option value="古代言情">古代言情</Option>
                      <Option value="玄幻修仙">玄幻修仙</Option>
                      <Option value="科幻未来">科幻未来</Option>
                      <Option value="悬疑推理">悬疑推理</Option>
                      <Option value="历史军事">历史军事</Option>
                      <Option value="奇幻冒险">奇幻冒险</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="style"
                    label="写作风格"
                    rules={[{ required: true, message: '请选择写作风格' }]}
                  >
                    <Select placeholder="请选择风格">
                      <Option value="轻松幽默">轻松幽默</Option>
                      <Option value="深沉严肃">深沉严肃</Option>
                      <Option value="浪漫温馨">浪漫温馨</Option>
                      <Option value="紧张刺激">紧张刺激</Option>
                      <Option value="文艺清新">文艺清新</Option>
                      <Option value="热血激昂">热血激昂</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="length"
                    label="作品长度"
                    rules={[{ required: true, message: '请选择作品长度' }]}
                  >
                    <Select placeholder="请选择长度">
                      <Option value="短篇">短篇 (1-5万字)</Option>
                      <Option value="中篇">中篇 (5-15万字)</Option>
                      <Option value="长篇">长篇 (15万字以上)</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="experience"
                    label="创作经验"
                    rules={[{ required: true, message: '请选择创作经验' }]}
                  >
                    <Select placeholder="请选择经验水平">
                      <Option value="新手">新手 (第一次创作)</Option>
                      <Option value="进阶">进阶 (有一定经验)</Option>
                      <Option value="专业">专业 (经验丰富)</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item name="features" label="特殊需求">
                <Checkbox.Group>
                  <Row>
                    <Col span={8}><Checkbox value="世界观">复杂世界观</Checkbox></Col>
                    <Col span={8}><Checkbox value="高质量">高质量润色</Checkbox></Col>
                    <Col span={8}><Checkbox value="快速">快速生成</Checkbox></Col>
                    <Col span={8}><Checkbox value="详细">详细大纲</Checkbox></Col>
                    <Col span={8}><Checkbox value="多角色">多角色设定</Checkbox></Col>
                    <Col span={8}><Checkbox value="一致性">一致性检查</Checkbox></Col>
                  </Row>
                </Checkbox.Group>
              </Form.Item>

              <div className="text-center space-x-4">
                <Button type="primary" onClick={handleAnalyze} size="large" loading={loading}>
                  <RobotOutlined />
                  {loading ? 'AI分析中...' : 'AI分析需求'}
                </Button>

                {!aiService.isConfigured() && (
                  <Button onClick={() => setIsConfigModalVisible(true)}>
                    <SettingOutlined />
                    配置AI API
                  </Button>
                )}
              </div>
            </Form>
          </Card>
        )}

        {/* 模板推荐步骤 */}
        {currentStep === 1 && mode === 'recommend' && (
          <Card title="AI为您推荐以下工作流模板">
            <div className="space-y-4">
              {recommendations.map((template, index) => (
                <Card
                  key={template.id}
                  size="small"
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleSelectTemplate(template)}
                  extra={
                    <Badge count={index + 1} style={{ backgroundColor: '#52c41a' }} />
                  }
                >
                  <Row gutter={16}>
                    <Col span={16}>
                      <div>
                        <Title level={5} className="mb-1">{template.name}</Title>
                        <Paragraph type="secondary" className="mb-2">
                          {template.description}
                        </Paragraph>
                        <Space>
                          {template.tags.map(tag => (
                            <Tag key={tag} color="blue">{tag}</Tag>
                          ))}
                        </Space>
                      </div>
                    </Col>
                    <Col span={8} className="text-right">
                      <div className="space-y-2">
                        <div>
                          <Tag color={getComplexityColor(template.complexity)}>
                            {getComplexityText(template.complexity)}
                          </Tag>
                        </div>
                        <div>
                          <Text type="secondary">
                            {template.nodes.length} 个节点
                          </Text>
                        </div>
                        <div>
                          <Text type="secondary">
                            {template.connections.length} 个连接
                          </Text>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Card>
              ))}
            </div>
          </Card>
        )}

        {/* 预览确认步骤 */}
        {currentStep === 2 && selectedTemplate && (
          <Card title="工作流预览">
            <div className="space-y-4">
              <div>
                <Title level={4}>{selectedTemplate.name}</Title>
                <Paragraph>{selectedTemplate.description}</Paragraph>
                <Space>
                  {selectedTemplate.tags.map(tag => (
                    <Tag key={tag} color="blue">{tag}</Tag>
                  ))}
                  <Tag color={getComplexityColor(selectedTemplate.complexity)}>
                    复杂度: {getComplexityText(selectedTemplate.complexity)}
                  </Tag>
                </Space>
              </div>

              <Divider />

              <div>
                <Title level={5}>工作流结构</Title>
                <Row gutter={16}>
                  <Col span={12}>
                    <Card size="small" title="节点列表">
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {selectedTemplate.nodes.map((node, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <Text>{node.label}</Text>
                            <Tag>{node.type}</Tag>
                          </div>
                        ))}
                      </div>
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card size="small" title="统计信息">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <Text>节点数量:</Text>
                          <Text strong>{selectedTemplate.nodes.length}</Text>
                        </div>
                        <div className="flex justify-between">
                          <Text>连接数量:</Text>
                          <Text strong>{selectedTemplate.connections.length}</Text>
                        </div>
                        <div className="flex justify-between">
                          <Text>预估时间:</Text>
                          <Text strong>
                            {selectedTemplate.complexity === 'simple' ? '10-20分钟' :
                             selectedTemplate.complexity === 'medium' ? '30-60分钟' : '1-2小时'}
                          </Text>
                        </div>
                      </div>
                    </Card>
                  </Col>
                </Row>
              </div>

              <div className="text-center space-x-4">
                <Button onClick={() => setCurrentStep(currentStep - 1)}>
                  返回修改
                </Button>
                <Button type="primary" onClick={handleApply} size="large">
                  <CheckCircleOutlined />
                  应用此工作流
                </Button>
              </div>
            </div>
          </Card>
        )}
      </div>

      {/* AI配置模态框 */}
      <AIConfigModal
        visible={isConfigModalVisible}
        onClose={() => setIsConfigModalVisible(false)}
        onConfigured={() => {
          setIsConfigModalVisible(false);
          message.success('AI配置已完成，现在可以使用AI功能了！');
        }}
      />
    </Modal>
  );
};

export default WorkflowAIAssistant;
