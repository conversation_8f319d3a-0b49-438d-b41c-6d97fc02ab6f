{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { \n  Project, \n  WorkflowNode, \n  Character, \n  WorldBuilding, \n  Outline, \n  PlotLine, \n  BookTitle, \n  Chapter,\n  PromptTemplate,\n  DocumentStructure,\n  ExecutionContext,\n  UIState,\n  Notification\n} from '@/types';\n\n// 主应用状态接口\ninterface AppState {\n  // UI状态\n  ui: UIState;\n  setTheme: (theme: 'light' | 'dark') => void;\n  setLanguage: (language: 'zh-CN' | 'en-US') => void;\n  toggleSidebar: () => void;\n  setActiveTab: (tab: string) => void;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationRead: (id: string) => void;\n  clearNotifications: () => void;\n\n  // 项目管理\n  projects: Project[];\n  currentProject: Project | null;\n  createProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateProject: (id: string, updates: Partial<Project>) => void;\n  deleteProject: (id: string) => void;\n  setCurrentProject: (id: string) => void;\n\n  // 工作流管理\n  workflows: Record<string, WorkflowNode[]>;\n  currentWorkflow: WorkflowNode[];\n  addNode: (node: Omit<WorkflowNode, 'id'>) => void;\n  updateNode: (id: string, updates: Partial<WorkflowNode>) => void;\n  deleteNode: (id: string) => void;\n  connectNodes: (sourceId: string, targetId: string) => void;\n  disconnectNodes: (sourceId: string, targetId: string) => void;\n  loadWorkflow: (projectId: string) => void;\n  saveWorkflow: (projectId: string) => void;\n\n  // 角色管理\n  characters: Record<string, Character[]>;\n  addCharacter: (projectId: string, character: Omit<Character, 'id'>) => void;\n  updateCharacter: (projectId: string, id: string, updates: Partial<Character>) => void;\n  deleteCharacter: (projectId: string, id: string) => void;\n  getCharacters: (projectId: string) => Character[];\n\n  // 世界观管理\n  worldBuilding: Record<string, WorldBuilding[]>;\n  addWorldElement: (projectId: string, element: Omit<WorldBuilding, 'id'>) => void;\n  updateWorldElement: (projectId: string, id: string, updates: Partial<WorldBuilding>) => void;\n  deleteWorldElement: (projectId: string, id: string) => void;\n  getWorldElements: (projectId: string) => WorldBuilding[];\n\n  // 大纲管理\n  outlines: Record<string, Outline[]>;\n  addOutline: (projectId: string, outline: Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateOutline: (projectId: string, id: string, updates: Partial<Outline>) => void;\n  deleteOutline: (projectId: string, id: string) => void;\n  getOutlines: (projectId: string) => Outline[];\n\n  // 主线管理\n  plotLines: Record<string, PlotLine[]>;\n  addPlotLine: (projectId: string, plotLine: Omit<PlotLine, 'id'>) => void;\n  updatePlotLine: (projectId: string, id: string, updates: Partial<PlotLine>) => void;\n  deletePlotLine: (projectId: string, id: string) => void;\n  getPlotLines: (projectId: string) => PlotLine[];\n\n  // 书名管理\n  bookTitles: Record<string, BookTitle[]>;\n  addBookTitle: (projectId: string, title: Omit<BookTitle, 'id' | 'createdAt'>) => void;\n  updateBookTitle: (projectId: string, id: string, updates: Partial<BookTitle>) => void;\n  deleteBookTitle: (projectId: string, id: string) => void;\n  toggleTitleFavorite: (projectId: string, id: string) => void;\n  getBookTitles: (projectId: string) => BookTitle[];\n\n  // 章节管理\n  chapters: Record<string, Chapter[]>;\n  addChapter: (projectId: string, chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateChapter: (projectId: string, id: string, updates: Partial<Chapter>) => void;\n  deleteChapter: (projectId: string, id: string) => void;\n  reorderChapters: (projectId: string, fromIndex: number, toIndex: number) => void;\n  getChapters: (projectId: string) => Chapter[];\n\n  // 提示词管理\n  promptTemplates: PromptTemplate[];\n  addPromptTemplate: (template: Omit<PromptTemplate, 'id'>) => void;\n  updatePromptTemplate: (id: string, updates: Partial<PromptTemplate>) => void;\n  deletePromptTemplate: (id: string) => void;\n  getPromptTemplatesByCategory: (category: string) => PromptTemplate[];\n\n  // 文档管理\n  documentStructures: Record<string, DocumentStructure>;\n  initializeDocumentStructure: (projectId: string) => void;\n  updateDocumentStructure: (projectId: string, structure: Partial<DocumentStructure>) => void;\n\n  // 执行引擎\n  executionContexts: Record<string, ExecutionContext>;\n  startExecution: (projectId: string, workflowId: string) => void;\n  pauseExecution: (projectId: string) => void;\n  resumeExecution: (projectId: string) => void;\n  stopExecution: (projectId: string) => void;\n  updateExecutionProgress: (projectId: string, progress: Partial<ExecutionContext>) => void;\n}\n\n// 生成唯一ID的工具函数\nconst generateId = () => Math.random().toString(36).substr(2, 9);\n\n// 创建Zustand store\nexport const useAppStore = create<AppState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        // 初始UI状态\n        ui: {\n          theme: 'light',\n          language: 'zh-CN',\n          sidebarCollapsed: false,\n          activeTab: 'workflow',\n          selectedProject: undefined,\n          notifications: [],\n        },\n\n        // UI状态管理\n        setTheme: (theme) => set((state) => ({ \n          ui: { ...state.ui, theme } \n        })),\n        \n        setLanguage: (language) => set((state) => ({ \n          ui: { ...state.ui, language } \n        })),\n        \n        toggleSidebar: () => set((state) => ({ \n          ui: { ...state.ui, sidebarCollapsed: !state.ui.sidebarCollapsed } \n        })),\n        \n        setActiveTab: (activeTab) => set((state) => ({ \n          ui: { ...state.ui, activeTab } \n        })),\n        \n        addNotification: (notification) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: [\n              ...state.ui.notifications,\n              {\n                ...notification,\n                id: generateId(),\n                timestamp: new Date(),\n                read: false,\n              }\n            ]\n          }\n        })),\n        \n        markNotificationRead: (id) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: state.ui.notifications.map(n => \n              n.id === id ? { ...n, read: true } : n\n            )\n          }\n        })),\n        \n        clearNotifications: () => set((state) => ({\n          ui: { ...state.ui, notifications: [] }\n        })),\n\n        // 项目管理\n        projects: [],\n        currentProject: null,\n        \n        createProject: (project) => set((state) => {\n          const newProject: Project = {\n            ...project,\n            id: generateId(),\n            createdAt: new Date(),\n            updatedAt: new Date(),\n          };\n          return {\n            projects: [...state.projects, newProject],\n            currentProject: newProject,\n            ui: { ...state.ui, selectedProject: newProject.id }\n          };\n        }),\n        \n        updateProject: (id, updates) => set((state) => ({\n          projects: state.projects.map(p => \n            p.id === id ? { ...p, ...updates, updatedAt: new Date() } : p\n          ),\n          currentProject: state.currentProject?.id === id \n            ? { ...state.currentProject, ...updates, updatedAt: new Date() }\n            : state.currentProject\n        })),\n        \n        deleteProject: (id) => set((state) => ({\n          projects: state.projects.filter(p => p.id !== id),\n          currentProject: state.currentProject?.id === id ? null : state.currentProject,\n          ui: { \n            ...state.ui, \n            selectedProject: state.ui.selectedProject === id ? undefined : state.ui.selectedProject \n          }\n        })),\n        \n        setCurrentProject: (id) => set((state) => {\n          const project = state.projects.find(p => p.id === id);\n          return {\n            currentProject: project || null,\n            ui: { ...state.ui, selectedProject: id }\n          };\n        }),\n\n        // 工作流管理\n        workflows: {},\n        currentWorkflow: [],\n        \n        addNode: (node) => set((state) => ({\n          currentWorkflow: [...state.currentWorkflow, { ...node, id: generateId() }]\n        })),\n        \n        updateNode: (id, updates) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === id ? { ...n, ...updates } : n\n          )\n        })),\n        \n        deleteNode: (id) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.filter(n => n.id !== id)\n        })),\n        \n        connectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: [...n.connections, { sourceId, targetId }] }\n              : n\n          )\n        })),\n        \n        disconnectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: n.connections.filter(c => !(c.sourceId === sourceId && c.targetId === targetId)) }\n              : n\n          )\n        })),\n        \n        loadWorkflow: (projectId) => set((state) => ({\n          currentWorkflow: state.workflows[projectId] || []\n        })),\n        \n        saveWorkflow: (projectId) => set((state) => ({\n          workflows: { ...state.workflows, [projectId]: state.currentWorkflow }\n        })),\n\n        // 角色管理\n        characters: {},\n        \n        addCharacter: (projectId, character) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: [\n              ...(state.characters[projectId] || []),\n              { ...character, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateCharacter: (projectId, id, updates) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates } : c\n            )\n          }\n        })),\n        \n        deleteCharacter: (projectId, id) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        \n        getCharacters: (projectId) => get().characters[projectId] || [],\n\n        // 世界观管理\n        worldBuilding: {},\n        \n        addWorldElement: (projectId, element) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: [\n              ...(state.worldBuilding[projectId] || []),\n              { ...element, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateWorldElement: (projectId, id, updates) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).map(w => \n              w.id === id ? { ...w, ...updates } : w\n            )\n          }\n        })),\n        \n        deleteWorldElement: (projectId, id) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).filter(w => w.id !== id)\n          }\n        })),\n        \n        getWorldElements: (projectId) => get().worldBuilding[projectId] || [],\n\n        // 其他管理功能的占位符实现\n        outlines: {},\n        addOutline: (projectId, outline) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: [\n              ...(state.outlines[projectId] || []),\n              { \n                ...outline, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateOutline: (projectId, id, updates) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).map(o => \n              o.id === id ? { ...o, ...updates, updatedAt: new Date() } : o\n            )\n          }\n        })),\n        deleteOutline: (projectId, id) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).filter(o => o.id !== id)\n          }\n        })),\n        getOutlines: (projectId) => get().outlines[projectId] || [],\n\n        plotLines: {},\n        addPlotLine: (projectId, plotLine) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: [\n              ...(state.plotLines[projectId] || []),\n              { ...plotLine, id: generateId() }\n            ]\n          }\n        })),\n        updatePlotLine: (projectId, id, updates) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).map(p => \n              p.id === id ? { ...p, ...updates } : p\n            )\n          }\n        })),\n        deletePlotLine: (projectId, id) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).filter(p => p.id !== id)\n          }\n        })),\n        getPlotLines: (projectId) => get().plotLines[projectId] || [],\n\n        bookTitles: {},\n        addBookTitle: (projectId, title) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: [\n              ...(state.bookTitles[projectId] || []),\n              { ...title, id: generateId(), createdAt: new Date() }\n            ]\n          }\n        })),\n        updateBookTitle: (projectId, id, updates) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, ...updates } : t\n            )\n          }\n        })),\n        deleteBookTitle: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).filter(t => t.id !== id)\n          }\n        })),\n        toggleTitleFavorite: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, isFavorite: !t.isFavorite } : t\n            )\n          }\n        })),\n        getBookTitles: (projectId) => get().bookTitles[projectId] || [],\n\n        chapters: {},\n        addChapter: (projectId, chapter) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: [\n              ...(state.chapters[projectId] || []),\n              { \n                ...chapter, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateChapter: (projectId, id, updates) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates, updatedAt: new Date() } : c\n            )\n          }\n        })),\n        deleteChapter: (projectId, id) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        reorderChapters: (projectId, fromIndex, toIndex) => set((state) => {\n          const chapters = [...(state.chapters[projectId] || [])];\n          const [removed] = chapters.splice(fromIndex, 1);\n          chapters.splice(toIndex, 0, removed);\n          // 重新设置order\n          chapters.forEach((chapter, index) => {\n            chapter.order = index + 1;\n          });\n          return {\n            chapters: {\n              ...state.chapters,\n              [projectId]: chapters\n            }\n          };\n        }),\n        getChapters: (projectId) => get().chapters[projectId] || [],\n\n        promptTemplates: [],\n        addPromptTemplate: (template) => set((state) => ({\n          promptTemplates: [...state.promptTemplates, { ...template, id: generateId() }]\n        })),\n        updatePromptTemplate: (id, updates) => set((state) => ({\n          promptTemplates: state.promptTemplates.map(t => \n            t.id === id ? { ...t, ...updates } : t\n          )\n        })),\n        deletePromptTemplate: (id) => set((state) => ({\n          promptTemplates: state.promptTemplates.filter(t => t.id !== id)\n        })),\n        getPromptTemplatesByCategory: (category) => \n          get().promptTemplates.filter(t => t.category === category),\n\n        documentStructures: {},\n        initializeDocumentStructure: (projectId) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              projectId,\n              folders: [],\n              files: [],\n              lastBackup: new Date()\n            }\n          }\n        })),\n        updateDocumentStructure: (projectId, structure) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              ...state.documentStructures[projectId],\n              ...structure\n            }\n          }\n        })),\n\n        executionContexts: {},\n        startExecution: (projectId, workflowId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              projectId,\n              workflowId,\n              status: 'running',\n              progress: {\n                totalSteps: 0,\n                completedSteps: 0,\n                currentStep: '',\n                percentage: 0\n              },\n              queue: [],\n              results: {},\n              startTime: new Date()\n            }\n          }\n        })),\n        pauseExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'paused'\n            }\n          }\n        })),\n        resumeExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'running'\n            }\n          }\n        })),\n        stopExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'idle',\n              endTime: new Date()\n            }\n          }\n        })),\n        updateExecutionProgress: (projectId, progress) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              ...progress\n            }\n          }\n        })),\n      }),\n      {\n        name: 'ai-novel-workflow-storage',\n        partialize: (state) => ({\n          projects: state.projects,\n          workflows: state.workflows,\n          characters: state.characters,\n          worldBuilding: state.worldBuilding,\n          outlines: state.outlines,\n          plotLines: state.plotLines,\n          bookTitles: state.bookTitles,\n          chapters: state.chapters,\n          promptTemplates: state.promptTemplates,\n          documentStructures: state.documentStructures,\n          ui: {\n            theme: state.ui.theme,\n            language: state.ui.language,\n            sidebarCollapsed: state.ui.sidebarCollapsed,\n          }\n        }),\n      }\n    ),\n    { name: 'ai-novel-workflow' }\n  )\n);\n\nexport default useAppStore;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAiHA,cAAc;AACd,MAAM,aAAa,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAGvD,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,SAAS;QACT,IAAI;YACF,OAAO;YACP,UAAU;YACV,kBAAkB;YAClB,WAAW;YACX,iBAAiB;YACjB,eAAe,EAAE;QACnB;QAEA,SAAS;QACT,UAAU,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAM;gBAC3B,CAAC;QAED,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAS;gBAC9B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,kBAAkB,CAAC,MAAM,EAAE,CAAC,gBAAgB;oBAAC;gBAClE,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAU;gBAC/B,CAAC;QAED,iBAAiB,CAAC,eAAiB,IAAI,CAAC,QAAU,CAAC;oBACjD,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe;+BACV,MAAM,EAAE,CAAC,aAAa;4BACzB;gCACE,GAAG,YAAY;gCACf,IAAI;gCACJ,WAAW,IAAI;gCACf,MAAM;4BACR;yBACD;oBACH;gBACF,CAAC;QAED,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe,MAAM,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,MAAM;4BAAK,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACxC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,eAAe,EAAE;oBAAC;gBACvC,CAAC;QAED,OAAO;QACP,UAAU,EAAE;QACZ,gBAAgB;QAEhB,eAAe,CAAC,UAAY,IAAI,CAAC;gBAC/B,MAAM,aAAsB;oBAC1B,GAAG,OAAO;oBACV,IAAI;oBACJ,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBACA,OAAO;oBACL,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAW;oBACzC,gBAAgB;oBAChB,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB,WAAW,EAAE;oBAAC;gBACpD;YACF;QAEA,eAAe,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9C,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAC3B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI;wBAAO,IAAI;oBAE9D,gBAAgB,MAAM,cAAc,EAAE,OAAO,KACzC;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;wBAAE,WAAW,IAAI;oBAAO,IAC7D,MAAM,cAAc;gBAC1B,CAAC;QAED,eAAe,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACrC,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC9C,gBAAgB,MAAM,cAAc,EAAE,OAAO,KAAK,OAAO,MAAM,cAAc;oBAC7E,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,iBAAiB,MAAM,EAAE,CAAC,eAAe,KAAK,KAAK,YAAY,MAAM,EAAE,CAAC,eAAe;oBACzF;gBACF,CAAC;QAED,mBAAmB,CAAC,KAAO,IAAI,CAAC;gBAC9B,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAClD,OAAO;oBACL,gBAAgB,WAAW;oBAC3B,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB;oBAAG;gBACzC;YACF;QAEA,QAAQ;QACR,WAAW,CAAC;QACZ,iBAAiB,EAAE;QAEnB,SAAS,CAAC,OAAS,IAAI,CAAC,QAAU,CAAC;oBACjC,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,IAAI;4BAAE,IAAI;wBAAa;qBAAE;gBAC5E,CAAC;QAED,YAAY,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QAED,YAAY,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClC,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QAED,cAAc,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa;mCAAI,EAAE,WAAW;gCAAE;oCAAE;oCAAU;gCAAS;6BAAE;wBAAC,IAChE;gBAER,CAAC;QAED,iBAAiB,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACvD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,QAAQ,KAAK,YAAY,EAAE,QAAQ,KAAK,QAAQ;wBAAG,IACtG;gBAER,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;gBACnD,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,WAAW;wBAAE,GAAG,MAAM,SAAS;wBAAE,CAAC,UAAU,EAAE,MAAM,eAAe;oBAAC;gBACtE,CAAC;QAED,OAAO;QACP,YAAY,CAAC;QAEb,cAAc,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,SAAS;gCAAE,IAAI;4BAAa;yBAClC;oBACH;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QAED,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,QAAQ;QACR,eAAe,CAAC;QAEhB,iBAAiB,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBACvD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE;+BACP,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;4BACxC;gCAAE,GAAG,OAAO;gCAAE,IAAI;4BAAa;yBAChC;oBACH;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9D,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACtD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACrD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC3E;gBACF,CAAC;QAED,kBAAkB,CAAC,YAAc,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;QAErE,eAAe;QACf,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,WAAW,CAAC;QACZ,aAAa,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE;+BACP,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;4BACpC;gCAAE,GAAG,QAAQ;gCAAE,IAAI;4BAAa;yBACjC;oBACH;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC1D,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IAClD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACjD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACvE;gBACF,CAAC;QACD,cAAc,CAAC,YAAc,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;QAE7D,YAAY,CAAC;QACb,cAAc,CAAC,WAAW,QAAU,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,KAAK;gCAAE,IAAI;gCAAc,WAAW,IAAI;4BAAO;yBACrD;oBACH;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QACD,qBAAqB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,YAAY,CAAC,EAAE,UAAU;4BAAC,IAAI;oBAExD;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,WAAW,UAAY,IAAI,CAAC;gBACvD,MAAM,WAAW;uBAAK,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;iBAAE;gBACvD,MAAM,CAAC,QAAQ,GAAG,SAAS,MAAM,CAAC,WAAW;gBAC7C,SAAS,MAAM,CAAC,SAAS,GAAG;gBAC5B,YAAY;gBACZ,SAAS,OAAO,CAAC,CAAC,SAAS;oBACzB,QAAQ,KAAK,GAAG,QAAQ;gBAC1B;gBACA,OAAO;oBACL,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;oBACf;gBACF;YACF;QACA,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,iBAAiB,EAAE;QACnB,mBAAmB,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBAC/C,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,QAAQ;4BAAE,IAAI;wBAAa;qBAAE;gBAChF,CAAC;QACD,sBAAsB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACrD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QACD,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QACD,8BAA8B,CAAC,WAC7B,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QAEnD,oBAAoB,CAAC;QACrB,6BAA6B,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC1D,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX;4BACA,SAAS,EAAE;4BACX,OAAO,EAAE;4BACT,YAAY,IAAI;wBAClB;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACjE,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,kBAAkB,CAAC,UAAU;4BACtC,GAAG,SAAS;wBACd;oBACF;gBACF,CAAC;QAED,mBAAmB,CAAC;QACpB,gBAAgB,CAAC,WAAW,aAAe,IAAI,CAAC,QAAU,CAAC;oBACzD,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX;4BACA;4BACA,QAAQ;4BACR,UAAU;gCACR,YAAY;gCACZ,gBAAgB;gCAChB,aAAa;gCACb,YAAY;4BACd;4BACA,OAAO,EAAE;4BACT,SAAS,CAAC;4BACV,WAAW,IAAI;wBACjB;oBACF;gBACF,CAAC;QACD,gBAAgB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC7C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,iBAAiB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC9C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC5C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;4BACR,SAAS,IAAI;wBACf;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBAChE,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,GAAG,QAAQ;wBACb;oBACF;gBACF,CAAC;IACH,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,eAAe,MAAM,aAAa;YAClC,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,UAAU,MAAM,QAAQ;YACxB,iBAAiB,MAAM,eAAe;YACtC,oBAAoB,MAAM,kBAAkB;YAC5C,IAAI;gBACF,OAAO,MAAM,EAAE,CAAC,KAAK;gBACrB,UAAU,MAAM,EAAE,CAAC,QAAQ;gBAC3B,kBAAkB,MAAM,EAAE,CAAC,gBAAgB;YAC7C;QACF,CAAC;AACH,IAEF;IAAE,MAAM;AAAoB;uCAIjB", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport WorkflowEditor from '@/components/workflow/WorkflowEditor';\nimport ProjectOverview from '@/components/project/ProjectOverview';\nimport OutlineManager from '@/components/outline/OutlineManager';\nimport CharacterManager from '@/components/character/CharacterManager';\nimport WorldBuildingManager from '@/components/worldbuilding/WorldBuildingManager';\nimport PlotLineManager from '@/components/plotline/PlotLineManager';\nimport TitleManager from '@/components/title/TitleManager';\nimport DocumentManager from '@/components/document/DocumentManager';\nimport PromptManager from '@/components/prompt/PromptManager';\nimport { useAppStore } from '@/store';\n\nexport default function Home() {\n  const { ui } = useAppStore();\n\n  const renderContent = () => {\n    switch (ui.activeTab) {\n      case 'workflow':\n        return <WorkflowEditor />;\n      case 'projects':\n        return <ProjectOverview />;\n      case 'outlines':\n        return <OutlineManager />;\n      case 'characters':\n        return <CharacterManager />;\n      case 'worldbuilding':\n        return <WorldBuildingManager />;\n      case 'plotlines':\n        return <PlotLineManager />;\n      case 'titles':\n        return <TitleManager />;\n      case 'documents':\n        return <DocumentManager />;\n      case 'prompts':\n        return <PromptManager />;\n      default:\n        return <WorkflowEditor />;\n    }\n  };\n\n  return (\n    <MainLayout>\n      {renderContent()}\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEzB,MAAM,gBAAgB;QACpB,OAAQ,GAAG,SAAS;YAClB,KAAK;gBACH,qBAAO,8OAAC,gJAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,gJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,+IAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,mJAAA,CAAA,UAAgB;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2JAAA,CAAA,UAAoB;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,iJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,2IAAA,CAAA,UAAY;;;;;YACtB,KAAK;gBACH,qBAAO,8OAAC,iJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,6IAAA,CAAA,UAAa;;;;;YACvB;gBACE,qBAAO,8OAAC,gJAAA,CAAA,UAAc;;;;;QAC1B;IACF;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACR;;;;;;AAGP", "debugId": null}}]}