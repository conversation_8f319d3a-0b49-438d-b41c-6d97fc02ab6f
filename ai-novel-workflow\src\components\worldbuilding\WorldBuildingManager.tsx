'use client';

import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tabs,
  List,
  Tag,
  Popconfirm,
  message,
  Row,
  Col,
  Divider,
  Tree,
  Tooltip,
  Badge
} from 'antd';
import {
  GlobalOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EnvironmentOutlined,
  BookOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  CrownOutlined,
  HomeOutlined,
  TeamOutlined,
  DollarOutlined,
  HistoryOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/store';
import type { WorldBuilding, WorldCategory } from '@/types';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const WorldBuildingManager: React.FC = () => {
  const {
    currentProject,
    worldBuilding,
    addWorldElement,
    updateWorldElement,
    deleteWorldElement,
    getWorldElements
  } = useAppStore();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingElement, setEditingElement] = useState<WorldBuilding | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<WorldCategory | 'all'>('all');
  const [activeTab, setActiveTab] = useState('elements');
  const [form] = Form.useForm();

  const projectWorldElements = currentProject ? getWorldElements(currentProject.id) : [];

  // 世界观分类配置
  const worldCategories = [
    {
      key: 'setting' as WorldCategory,
      label: '环境设定',
      icon: <EnvironmentOutlined />,
      color: 'green',
      description: '地理环境、气候、地形等自然环境设定'
    },
    {
      key: 'magic-system' as WorldCategory,
      label: '魔法体系',
      icon: <ThunderboltOutlined />,
      color: 'purple',
      description: '魔法规则、能力体系、超自然力量设定'
    },
    {
      key: 'technology' as WorldCategory,
      label: '科技水平',
      icon: <SettingOutlined />,
      color: 'blue',
      description: '科技发展程度、工具、武器、交通等'
    },
    {
      key: 'culture' as WorldCategory,
      label: '文化背景',
      icon: <BookOutlined />,
      color: 'orange',
      description: '宗教信仰、传统习俗、艺术文化等'
    },
    {
      key: 'geography' as WorldCategory,
      label: '地理结构',
      icon: <GlobalOutlined />,
      color: 'cyan',
      description: '大陆分布、城市布局、重要地标等'
    },
    {
      key: 'history' as WorldCategory,
      label: '历史背景',
      icon: <HistoryOutlined />,
      color: 'gold',
      description: '重大历史事件、时代变迁、传说故事等'
    },
    {
      key: 'society' as WorldCategory,
      label: '社会制度',
      icon: <CrownOutlined />,
      color: 'red',
      description: '政治体制、社会阶层、法律制度等'
    },
    {
      key: 'economy' as WorldCategory,
      label: '经济体系',
      icon: <DollarOutlined />,
      color: 'lime',
      description: '货币制度、贸易体系、经济结构等'
    }
  ];

  const filteredElements = selectedCategory === 'all'
    ? projectWorldElements
    : projectWorldElements.filter(element => element.category === selectedCategory);

  const handleCreateElement = (category?: WorldCategory) => {
    setEditingElement(null);
    if (category) {
      form.setFieldsValue({ category });
    } else {
      form.resetFields();
    }
    setIsModalVisible(true);
  };

  const handleEditElement = (element: WorldBuilding) => {
    setEditingElement(element);
    form.setFieldsValue({
      name: element.name,
      category: element.category,
      description: element.description,
      details: element.details,
    });
    setIsModalVisible(true);
  };

  const handleDeleteElement = (elementId: string) => {
    if (currentProject) {
      deleteWorldElement(currentProject.id, elementId);
      message.success('世界观元素已删除');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingElement) {
        // 更新元素
        if (currentProject) {
          updateWorldElement(currentProject.id, editingElement.id, {
            ...values,
            relationships: editingElement.relationships || [],
            consistency: editingElement.consistency || [],
          });
          message.success('世界观元素已更新');
        }
      } else {
        // 创建新元素
        if (currentProject) {
          addWorldElement(currentProject.id, {
            ...values,
            relationships: [],
            consistency: [],
          });
          message.success('世界观元素已创建');
        }
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setEditingElement(null);
  };

  const getCategoryConfig = (category: WorldCategory) => {
    return worldCategories.find(cat => cat.key === category) || worldCategories[0];
  };

  if (!currentProject) {
    return (
      <div className="p-8 text-center">
        <Title level={3}>请先选择或创建一个项目</Title>
        <Text type="secondary">
          您需要先在项目总览中创建或选择一个项目，然后才能管理世界观。
        </Text>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Title level={2}>世界观管理</Title>
          <Text type="secondary">管理故事世界观设定和背景 - 项目: {currentProject.name}</Text>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleCreateElement()}
          >
            添加设定
          </Button>
        </Space>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'elements',
            label: (
              <span>
                <GlobalOutlined />
                世界观元素 ({projectWorldElements.length})
              </span>
            ),
            children: (
              <div>
                {/* 分类过滤器 */}
                <Card className="mb-6" title="分类浏览">
                  <Row gutter={[16, 16]}>
                    <Col span={3}>
                      <Card
                        size="small"
                        className={`cursor-pointer transition-all ${
                          selectedCategory === 'all' ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() => setSelectedCategory('all')}
                      >
                        <div className="text-center">
                          <GlobalOutlined className="text-2xl text-gray-500" />
                          <div className="mt-2">
                            <Text strong>全部</Text>
                            <div className="text-xs text-gray-500">
                              {projectWorldElements.length} 个元素
                            </div>
                          </div>
                        </div>
                      </Card>
                    </Col>
                    {worldCategories.map((category) => {
                      const count = projectWorldElements.filter(e => e.category === category.key).length;
                      return (
                        <Col span={3} key={category.key}>
                          <Card
                            size="small"
                            className={`cursor-pointer transition-all ${
                              selectedCategory === category.key ? 'ring-2 ring-blue-500' : ''
                            }`}
                            onClick={() => setSelectedCategory(category.key)}
                          >
                            <div className="text-center">
                              <div className={`text-2xl text-${category.color}-500`}>
                                {category.icon}
                              </div>
                              <div className="mt-2">
                                <Text strong>{category.label}</Text>
                                <div className="text-xs text-gray-500">
                                  {count} 个元素
                                </div>
                              </div>
                            </div>
                          </Card>
                        </Col>
                      );
                    })}
                  </Row>
                </Card>

                {/* 快速创建模板 */}
                <Card className="mb-6" title="快速创建">
                  <Row gutter={16}>
                    {worldCategories.slice(0, 4).map((category) => (
                      <Col span={6} key={category.key}>
                        <Card
                          size="small"
                          className="cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => handleCreateElement(category.key)}
                        >
                          <div className="text-center">
                            <div className={`text-xl text-${category.color}-500`}>
                              {category.icon}
                            </div>
                            <div className="mt-2">
                              <Text strong>{category.label}</Text>
                              <div className="text-xs text-gray-500 mt-1">
                                {category.description}
                              </div>
                            </div>
                          </div>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                </Card>

                {/* 世界观元素列表 */}
                {filteredElements.length === 0 ? (
                  <Card className="text-center py-12">
                    <GlobalOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                    <div className="mt-4">
                      <Text type="secondary">
                        {selectedCategory === 'all' ? '还没有创建任何世界观元素' : `还没有创建${getCategoryConfig(selectedCategory as WorldCategory).label}相关的元素`}
                      </Text>
                      <br />
                      <Text type="secondary">点击上方按钮或使用模板快速创建</Text>
                    </div>
                  </Card>
                ) : (
                  <List
                    grid={{ gutter: 16, column: 2 }}
                    dataSource={filteredElements}
                    renderItem={(element) => {
                      const categoryConfig = getCategoryConfig(element.category);
                      return (
                        <List.Item>
                          <Card
                            className="hover:shadow-lg transition-shadow"
                            actions={[
                              <Tooltip title="编辑" key="edit">
                                <EditOutlined onClick={() => handleEditElement(element)} />
                              </Tooltip>,
                              <Popconfirm
                                key="delete"
                                title="确定要删除这个世界观元素吗？"
                                onConfirm={() => handleDeleteElement(element.id)}
                                okText="确定"
                                cancelText="取消"
                              >
                                <DeleteOutlined />
                              </Popconfirm>
                            ]}
                          >
                            <div className="mb-3">
                              <div className="flex items-center justify-between mb-2">
                                <Title level={5} className="mb-0">{element.name}</Title>
                                <Tag
                                  color={categoryConfig.color}
                                  icon={categoryConfig.icon}
                                >
                                  {categoryConfig.label}
                                </Tag>
                              </div>
                              <Paragraph
                                ellipsis={{ rows: 3 }}
                                type="secondary"
                              >
                                {element.description}
                              </Paragraph>
                            </div>

                            {element.details && Object.keys(element.details).length > 0 && (
                              <div className="mt-3 pt-3 border-t border-gray-100">
                                <Text type="secondary" className="text-sm">
                                  包含 {Object.keys(element.details).length} 个详细设定
                                </Text>
                              </div>
                            )}

                            {element.relationships.length > 0 && (
                              <div className="mt-2">
                                <Badge count={element.relationships.length} showZero={false}>
                                  <Text type="secondary" className="text-sm">关联元素</Text>
                                </Badge>
                              </div>
                            )}
                          </Card>
                        </List.Item>
                      );
                    }}
                  />
                )}
              </div>
            ),
          },
          {
            key: 'consistency',
            label: (
              <span>
                <CheckCircleOutlined />
                一致性检查
              </span>
            ),
            children: (
              <Card className="text-center py-12">
                <CheckCircleOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                <div className="mt-4">
                  <Text type="secondary">一致性检查功能开发中</Text>
                  <br />
                  <Text type="secondary">即将支持世界观元素间的逻辑一致性验证</Text>
                </div>
              </Card>
            ),
          },
        ]}
      />

      {/* 创建/编辑世界观元素模态框 */}
      <Modal
        title={editingElement ? '编辑世界观元素' : '创建世界观元素'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText={editingElement ? '更新' : '创建'}
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item
                name="name"
                label="元素名称"
                rules={[{ required: true, message: '请输入元素名称' }]}
              >
                <Input placeholder="请输入世界观元素名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  {worldCategories.map((category) => (
                    <Option key={category.key} value={category.key}>
                      <Space>
                        {category.icon}
                        {category.label}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="基本描述"
            rules={[{ required: true, message: '请输入基本描述' }]}
          >
            <TextArea
              rows={4}
              placeholder="请描述这个世界观元素的基本信息..."
            />
          </Form.Item>

          <Form.Item name="details" label="详细设定">
            <TextArea
              rows={6}
              placeholder="请输入更详细的设定信息，如规则、特点、影响等..."
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default WorldBuildingManager;
