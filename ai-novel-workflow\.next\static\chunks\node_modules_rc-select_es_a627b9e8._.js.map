{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/TransBtn.js"], "sourcesContent": ["import * as React from 'react';\nimport classNames from 'classnames';\nvar TransBtn = function TransBtn(props) {\n  var className = props.className,\n    customizeIcon = props.customizeIcon,\n    customizeIconProps = props.customizeIconProps,\n    children = props.children,\n    _onMouseDown = props.onMouseDown,\n    onClick = props.onClick;\n  var icon = typeof customizeIcon === 'function' ? customizeIcon(customizeIconProps) : customizeIcon;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: className,\n    onMouseDown: function onMouseDown(event) {\n      event.preventDefault();\n      _onMouseDown === null || _onMouseDown === void 0 || _onMouseDown(event);\n    },\n    style: {\n      userSelect: 'none',\n      WebkitUserSelect: 'none'\n    },\n    unselectable: \"on\",\n    onClick: onClick,\n    \"aria-hidden\": true\n  }, icon !== undefined ? icon : /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(className.split(/\\s+/).map(function (cls) {\n      return \"\".concat(cls, \"-icon\");\n    }))\n  }, children));\n};\nexport default TransBtn;"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,IAAI,WAAW,SAAS,SAAS,KAAK;IACpC,IAAI,YAAY,MAAM,SAAS,EAC7B,gBAAgB,MAAM,aAAa,EACnC,qBAAqB,MAAM,kBAAkB,EAC7C,WAAW,MAAM,QAAQ,EACzB,eAAe,MAAM,WAAW,EAChC,UAAU,MAAM,OAAO;IACzB,IAAI,OAAO,OAAO,kBAAkB,aAAa,cAAc,sBAAsB;IACrF,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC9C,WAAW;QACX,aAAa,SAAS,YAAY,KAAK;YACrC,MAAM,cAAc;YACpB,iBAAiB,QAAQ,iBAAiB,KAAK,KAAK,aAAa;QACnE;QACA,OAAO;YACL,YAAY;YACZ,kBAAkB;QACpB;QACA,cAAc;QACd,SAAS;QACT,eAAe;IACjB,GAAG,SAAS,YAAY,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACtE,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,UAAU,KAAK,CAAC,OAAO,GAAG,CAAC,SAAU,GAAG;YAC5D,OAAO,GAAG,MAAM,CAAC,KAAK;QACxB;IACF,GAAG;AACL;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useAllowClear.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport TransBtn from \"../TransBtn\";\nimport React from 'react';\nexport var useAllowClear = function useAllowClear(prefixCls, onClearMouseDown, displayValues, allowClear, clearIcon) {\n  var disabled = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : false;\n  var mergedSearchValue = arguments.length > 6 ? arguments[6] : undefined;\n  var mode = arguments.length > 7 ? arguments[7] : undefined;\n  var mergedClearIcon = React.useMemo(function () {\n    if (_typeof(allowClear) === 'object') {\n      return allowClear.clearIcon;\n    }\n    if (clearIcon) {\n      return clearIcon;\n    }\n  }, [allowClear, clearIcon]);\n  var mergedAllowClear = React.useMemo(function () {\n    if (!disabled && !!allowClear && (displayValues.length || mergedSearchValue) && !(mode === 'combobox' && mergedSearchValue === '')) {\n      return true;\n    }\n    return false;\n  }, [allowClear, disabled, displayValues.length, mergedSearchValue, mode]);\n  return {\n    allowClear: mergedAllowClear,\n    clearIcon: /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(prefixCls, \"-clear\"),\n      onMouseDown: onClearMouseDown,\n      customizeIcon: mergedClearIcon\n    }, \"\\xD7\")\n  };\n};"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,IAAI,gBAAgB,SAAS,cAAc,SAAS,EAAE,gBAAgB,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS;IACjH,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACnF,IAAI,oBAAoB,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG;IAC9D,IAAI,OAAO,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG;IACjD,IAAI,kBAAkB,6JAAA,CAAA,UAAK,CAAC,OAAO;kDAAC;YAClC,IAAI,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,UAAU;gBACpC,OAAO,WAAW,SAAS;YAC7B;YACA,IAAI,WAAW;gBACb,OAAO;YACT;QACF;iDAAG;QAAC;QAAY;KAAU;IAC1B,IAAI,mBAAmB,6JAAA,CAAA,UAAK,CAAC,OAAO;mDAAC;YACnC,IAAI,CAAC,YAAY,CAAC,CAAC,cAAc,CAAC,cAAc,MAAM,IAAI,iBAAiB,KAAK,CAAC,CAAC,SAAS,cAAc,sBAAsB,EAAE,GAAG;gBAClI,OAAO;YACT;YACA,OAAO;QACT;kDAAG;QAAC;QAAY;QAAU,cAAc,MAAM;QAAE;QAAmB;KAAK;IACxE,OAAO;QACL,YAAY;QACZ,WAAW,WAAW,GAAE,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iJAAA,CAAA,UAAQ,EAAE;YACpD,WAAW,GAAG,MAAM,CAAC,WAAW;YAChC,aAAa;YACb,eAAe;QACjB,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useBaseProps.js"], "sourcesContent": ["/**\n * BaseSelect provide some parsed data into context.\n * You can use this hooks to get them.\n */\n\nimport * as React from 'react';\nexport var BaseSelectContext = /*#__PURE__*/React.createContext(null);\nexport default function useBaseProps() {\n  return React.useContext(BaseSelectContext);\n}"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AACO,IAAI,oBAAoB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC;AACjD,SAAS;IACtB,OAAO,6JAAA,CAAA,aAAgB,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useDelayReset.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n\n/**\n * Similar with `useLock`, but this hook will always execute last value.\n * When set to `true`, it will keep `true` for a short time even if `false` is set.\n */\nexport default function useDelayReset() {\n  var timeout = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    bool = _React$useState2[0],\n    setBool = _React$useState2[1];\n  var delayRef = React.useRef(null);\n  var cancelLatest = function cancelLatest() {\n    window.clearTimeout(delayRef.current);\n  };\n  React.useEffect(function () {\n    return cancelLatest;\n  }, []);\n  var delaySetBool = function delaySetBool(value, callback) {\n    cancelLatest();\n    delayRef.current = window.setTimeout(function () {\n      setBool(value);\n      if (callback) {\n        callback();\n      }\n    }, timeout);\n  };\n  return [bool, delaySetBool, cancelLatest];\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAMe,SAAS;IACtB,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IAClF,IAAI,kBAAkB,6JAAA,CAAA,WAAc,CAAC,QACnC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,OAAO,gBAAgB,CAAC,EAAE,EAC1B,UAAU,gBAAgB,CAAC,EAAE;IAC/B,IAAI,WAAW,6JAAA,CAAA,SAAY,CAAC;IAC5B,IAAI,eAAe,SAAS;QAC1B,OAAO,YAAY,CAAC,SAAS,OAAO;IACtC;IACA,6JAAA,CAAA,YAAe;mCAAC;YACd,OAAO;QACT;kCAAG,EAAE;IACL,IAAI,eAAe,SAAS,aAAa,KAAK,EAAE,QAAQ;QACtD;QACA,SAAS,OAAO,GAAG,OAAO,UAAU,CAAC;YACnC,QAAQ;YACR,IAAI,UAAU;gBACZ;YACF;QACF,GAAG;IACL;IACA,OAAO;QAAC;QAAM;QAAc;KAAa;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useLock.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Locker return cached mark.\n * If set to `true`, will return `true` in a short time even if set `false`.\n * If set to `false` and then set to `true`, will change to `true`.\n * And after time duration, it will back to `null` automatically.\n */\nexport default function useLock() {\n  var duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 250;\n  var lockRef = React.useRef(null);\n  var timeoutRef = React.useRef(null);\n\n  // Clean up\n  React.useEffect(function () {\n    return function () {\n      window.clearTimeout(timeoutRef.current);\n    };\n  }, []);\n  function doLock(locked) {\n    if (locked || lockRef.current === null) {\n      lockRef.current = locked;\n    }\n    window.clearTimeout(timeoutRef.current);\n    timeoutRef.current = window.setTimeout(function () {\n      lockRef.current = null;\n    }, duration);\n  }\n  return [function () {\n    return lockRef.current;\n  }, doLock];\n}"], "names": [], "mappings": ";;;AAAA;;AAQe,SAAS;IACtB,IAAI,WAAW,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACnF,IAAI,UAAU,6JAAA,CAAA,SAAY,CAAC;IAC3B,IAAI,aAAa,6JAAA,CAAA,SAAY,CAAC;IAE9B,WAAW;IACX,6JAAA,CAAA,YAAe;6BAAC;YACd;qCAAO;oBACL,OAAO,YAAY,CAAC,WAAW,OAAO;gBACxC;;QACF;4BAAG,EAAE;IACL,SAAS,OAAO,MAAM;QACpB,IAAI,UAAU,QAAQ,OAAO,KAAK,MAAM;YACtC,QAAQ,OAAO,GAAG;QACpB;QACA,OAAO,YAAY,CAAC,WAAW,OAAO;QACtC,WAAW,OAAO,GAAG,OAAO,UAAU,CAAC;YACrC,QAAQ,OAAO,GAAG;QACpB,GAAG;IACL;IACA,OAAO;QAAC;YACN,OAAO,QAAQ,OAAO;QACxB;QAAG;KAAO;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useSelectTriggerControl.js"], "sourcesContent": ["import * as React from 'react';\nexport default function useSelectTriggerControl(elements, open, triggerOpen, customizedTrigger) {\n  var propsRef = React.useRef(null);\n  propsRef.current = {\n    open: open,\n    triggerOpen: triggerOpen,\n    customizedTrigger: customizedTrigger\n  };\n  React.useEffect(function () {\n    function onGlobalMouseDown(event) {\n      var _propsRef$current;\n      // If trigger is customized, <PERSON>gger will take control of popupVisible\n      if ((_propsRef$current = propsRef.current) !== null && _propsRef$current !== void 0 && _propsRef$current.customizedTrigger) {\n        return;\n      }\n      var target = event.target;\n      if (target.shadowRoot && event.composed) {\n        target = event.composedPath()[0] || target;\n      }\n      if (propsRef.current.open && elements().filter(function (element) {\n        return element;\n      }).every(function (element) {\n        return !element.contains(target) && element !== target;\n      })) {\n        // Should trigger close\n        propsRef.current.triggerOpen(false);\n      }\n    }\n    window.addEventListener('mousedown', onGlobalMouseDown);\n    return function () {\n      return window.removeEventListener('mousedown', onGlobalMouseDown);\n    };\n  }, []);\n}"], "names": [], "mappings": ";;;AAAA;;AACe,SAAS,wBAAwB,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,iBAAiB;IAC5F,IAAI,WAAW,6JAAA,CAAA,SAAY,CAAC;IAC5B,SAAS,OAAO,GAAG;QACjB,MAAM;QACN,aAAa;QACb,mBAAmB;IACrB;IACA,6JAAA,CAAA,YAAe;6CAAC;YACd,SAAS,kBAAkB,KAAK;gBAC9B,IAAI;gBACJ,sEAAsE;gBACtE,IAAI,CAAC,oBAAoB,SAAS,OAAO,MAAM,QAAQ,sBAAsB,KAAK,KAAK,kBAAkB,iBAAiB,EAAE;oBAC1H;gBACF;gBACA,IAAI,SAAS,MAAM,MAAM;gBACzB,IAAI,OAAO,UAAU,IAAI,MAAM,QAAQ,EAAE;oBACvC,SAAS,MAAM,YAAY,EAAE,CAAC,EAAE,IAAI;gBACtC;gBACA,IAAI,SAAS,OAAO,CAAC,IAAI,IAAI,WAAW,MAAM;2EAAC,SAAU,OAAO;wBAC9D,OAAO;oBACT;0EAAG,KAAK;2EAAC,SAAU,OAAO;wBACxB,OAAO,CAAC,QAAQ,QAAQ,CAAC,WAAW,YAAY;oBAClD;2EAAI;oBACF,uBAAuB;oBACvB,SAAS,OAAO,CAAC,WAAW,CAAC;gBAC/B;YACF;YACA,OAAO,gBAAgB,CAAC,aAAa;YACrC;qDAAO;oBACL,OAAO,OAAO,mBAAmB,CAAC,aAAa;gBACjD;;QACF;4CAAG,EAAE;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/utils/keyUtil.js"], "sourcesContent": ["import KeyCode from \"rc-util/es/KeyCode\";\n\n/** keyCode Judgment function */\nexport function is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(currentKeyCode) {\n  return (\n    // Undefined for Edge bug:\n    // https://github.com/ant-design/ant-design/issues/51292\n    currentKeyCode &&\n    // Other keys\n    ![\n    // System function button\n    KeyCode.ESC, KeyCode.SHIFT, KeyCode.BACKSPACE, KeyCode.TAB, KeyCode.WIN_KEY, KeyCode.ALT, KeyCode.META, KeyCode.WIN_KEY_RIGHT, KeyCode.CTRL, KeyCode.SEMICOLON, KeyCode.EQUALS, KeyCode.CAPS_LOCK, KeyCode.CONTEXT_MENU,\n    // F1-F12\n    KeyCode.F1, KeyCode.F2, KeyCode.F3, KeyCode.F4, KeyCode.F5, KeyCode.F6, KeyCode.F7, KeyCode.F8, KeyCode.F9, KeyCode.F10, KeyCode.F11, KeyCode.F12].includes(currentKeyCode)\n  );\n}"], "names": [], "mappings": ";;;AAAA;;AAGO,SAAS,kBAAkB,cAAc;IAC9C,OACE,0BAA0B;IAC1B,wDAAwD;IACxD,kBACA,aAAa;IACb,CAAC;QACD,yBAAyB;QACzB,8IAAA,CAAA,UAAO,CAAC,GAAG;QAAE,8IAAA,CAAA,UAAO,CAAC,KAAK;QAAE,8IAAA,CAAA,UAAO,CAAC,SAAS;QAAE,8IAAA,CAAA,UAAO,CAAC,GAAG;QAAE,8IAAA,CAAA,UAAO,CAAC,OAAO;QAAE,8IAAA,CAAA,UAAO,CAAC,GAAG;QAAE,8IAAA,CAAA,UAAO,CAAC,IAAI;QAAE,8IAAA,CAAA,UAAO,CAAC,aAAa;QAAE,8IAAA,CAAA,UAAO,CAAC,IAAI;QAAE,8IAAA,CAAA,UAAO,CAAC,SAAS;QAAE,8IAAA,CAAA,UAAO,CAAC,MAAM;QAAE,8IAAA,CAAA,UAAO,CAAC,SAAS;QAAE,8IAAA,CAAA,UAAO,CAAC,YAAY;QACvN,SAAS;QACT,8IAAA,CAAA,UAAO,CAAC,EAAE;QAAE,8IAAA,CAAA,UAAO,CAAC,EAAE;QAAE,8IAAA,CAAA,UAAO,CAAC,EAAE;QAAE,8IAAA,CAAA,UAAO,CAAC,EAAE;QAAE,8IAAA,CAAA,UAAO,CAAC,EAAE;QAAE,8IAAA,CAAA,UAAO,CAAC,EAAE;QAAE,8IAAA,CAAA,UAAO,CAAC,EAAE;QAAE,8IAAA,CAAA,UAAO,CAAC,EAAE;QAAE,8IAAA,CAAA,UAAO,CAAC,EAAE;QAAE,8IAAA,CAAA,UAAO,CAAC,GAAG;QAAE,8IAAA,CAAA,UAAO,CAAC,GAAG;QAAE,8IAAA,CAAA,UAAO,CAAC,GAAG;KAAC,CAAC,QAAQ,CAAC;AAEhK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/Selector/Input.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"id\", \"inputElement\", \"autoFocus\", \"autoComplete\", \"editable\", \"activeDescendantId\", \"value\", \"open\", \"attrs\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { warning } from \"rc-util/es/warning\";\nimport composeProps from \"rc-util/es/composeProps\";\nvar Input = function Input(props, ref) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    inputElement = props.inputElement,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    editable = props.editable,\n    activeDescendantId = props.activeDescendantId,\n    value = props.value,\n    open = props.open,\n    attrs = props.attrs,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var inputNode = inputElement || /*#__PURE__*/React.createElement(\"input\", null);\n  var _inputNode = inputNode,\n    originRef = _inputNode.ref,\n    originProps = _inputNode.props;\n  warning(!('maxLength' in inputNode.props), \"Passing 'maxLength' to input element directly may not work because input in BaseSelect is controlled.\");\n  inputNode = /*#__PURE__*/React.cloneElement(inputNode, _objectSpread(_objectSpread(_objectSpread({\n    type: 'search'\n  }, composeProps(restProps, originProps, true)), {}, {\n    // Override over origin props\n    id: id,\n    ref: composeRef(ref, originRef),\n    autoComplete: autoComplete || 'off',\n    autoFocus: autoFocus,\n    className: classNames(\"\".concat(prefixCls, \"-selection-search-input\"), originProps === null || originProps === void 0 ? void 0 : originProps.className),\n    role: 'combobox',\n    'aria-expanded': open || false,\n    'aria-haspopup': 'listbox',\n    'aria-owns': \"\".concat(id, \"_list\"),\n    'aria-autocomplete': 'list',\n    'aria-controls': \"\".concat(id, \"_list\"),\n    'aria-activedescendant': open ? activeDescendantId : undefined\n  }, attrs), {}, {\n    value: editable ? value : '',\n    readOnly: !editable,\n    unselectable: !editable ? 'on' : null,\n    style: _objectSpread(_objectSpread({}, originProps.style), {}, {\n      opacity: editable ? null : 0\n    })\n  }));\n  return inputNode;\n};\nvar RefInput = /*#__PURE__*/React.forwardRef(Input);\nif (process.env.NODE_ENV !== 'production') {\n  RefInput.displayName = 'Input';\n}\nexport default RefInput;"], "names": [], "mappings": ";;;AAoDI;AApDJ;AACA;AAEA;AACA;AACA;AACA;AACA;;;AALA,IAAI,YAAY;IAAC;IAAa;IAAM;IAAgB;IAAa;IAAgB;IAAY;IAAsB;IAAS;IAAQ;CAAQ;;;;;;AAM5I,IAAI,QAAQ,SAAS,MAAM,KAAK,EAAE,GAAG;IACnC,IAAI,YAAY,MAAM,SAAS,EAC7B,KAAK,MAAM,EAAE,EACb,eAAe,MAAM,YAAY,EACjC,YAAY,MAAM,SAAS,EAC3B,eAAe,MAAM,YAAY,EACjC,WAAW,MAAM,QAAQ,EACzB,qBAAqB,MAAM,kBAAkB,EAC7C,QAAQ,MAAM,KAAK,EACnB,OAAO,MAAM,IAAI,EACjB,QAAQ,MAAM,KAAK,EACnB,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,OAAO;IAC9C,IAAI,YAAY,gBAAgB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;IAC1E,IAAI,aAAa,WACf,YAAY,WAAW,GAAG,EAC1B,cAAc,WAAW,KAAK;IAChC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,CAAC,eAAe,UAAU,KAAK,GAAG;IAC3C,YAAY,WAAW,GAAE,6JAAA,CAAA,eAAkB,CAAC,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE;QAC/F,MAAM;IACR,GAAG,CAAA,GAAA,mJAAA,CAAA,UAAY,AAAD,EAAE,WAAW,aAAa,QAAQ,CAAC,GAAG;QAClD,6BAA6B;QAC7B,IAAI;QACJ,KAAK,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACrB,cAAc,gBAAgB;QAC9B,WAAW;QACX,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,MAAM,CAAC,WAAW,4BAA4B,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,SAAS;QACtJ,MAAM;QACN,iBAAiB,QAAQ;QACzB,iBAAiB;QACjB,aAAa,GAAG,MAAM,CAAC,IAAI;QAC3B,qBAAqB;QACrB,iBAAiB,GAAG,MAAM,CAAC,IAAI;QAC/B,yBAAyB,OAAO,qBAAqB;IACvD,GAAG,QAAQ,CAAC,GAAG;QACb,OAAO,WAAW,QAAQ;QAC1B,UAAU,CAAC;QACX,cAAc,CAAC,WAAW,OAAO;QACjC,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,YAAY,KAAK,GAAG,CAAC,GAAG;YAC7D,SAAS,WAAW,OAAO;QAC7B;IACF;IACA,OAAO;AACT;AACA,IAAI,WAAW,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC;AAC7C,wCAA2C;IACzC,SAAS,WAAW,GAAG;AACzB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/utils/commonUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nexport function toArray(value) {\n  if (Array.isArray(value)) {\n    return value;\n  }\n  return value !== undefined ? [value] : [];\n}\nexport var isClient = typeof window !== 'undefined' && window.document && window.document.documentElement;\n\n/** Is client side and not jsdom */\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && isClient;\nexport function hasValue(value) {\n  return value !== undefined && value !== null;\n}\n\n/** combo mode no value judgment function */\nexport function isComboNoValue(value) {\n  return !value && value !== 0;\n}\nfunction isTitleType(title) {\n  return ['string', 'number'].includes(_typeof(title));\n}\nexport function getTitle(item) {\n  var title = undefined;\n  if (item) {\n    if (isTitleType(item.title)) {\n      title = item.title.toString();\n    } else if (isTitleType(item.label)) {\n      title = item.label.toString();\n    }\n  }\n  return title;\n}"], "names": [], "mappings": ";;;;;;;;AAU6B;AAV7B;;AACO,SAAS,QAAQ,KAAK;IAC3B,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO;IACT;IACA,OAAO,UAAU,YAAY;QAAC;KAAM,GAAG,EAAE;AAC3C;AACO,IAAI,WAAW,OAAO,WAAW,eAAe,OAAO,QAAQ,IAAI,OAAO,QAAQ,CAAC,eAAe;AAGlG,IAAI,kBAAkB,oDAAyB,UAAU;AACzD,SAAS,SAAS,KAAK;IAC5B,OAAO,UAAU,aAAa,UAAU;AAC1C;AAGO,SAAS,eAAe,KAAK;IAClC,OAAO,CAAC,SAAS,UAAU;AAC7B;AACA,SAAS,YAAY,KAAK;IACxB,OAAO;QAAC;QAAU;KAAS,CAAC,QAAQ,CAAC,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;AAC/C;AACO,SAAS,SAAS,IAAI;IAC3B,IAAI,QAAQ;IACZ,IAAI,MAAM;QACR,IAAI,YAAY,KAAK,KAAK,GAAG;YAC3B,QAAQ,KAAK,KAAK,CAAC,QAAQ;QAC7B,OAAO,IAAI,YAAY,KAAK,KAAK,GAAG;YAClC,QAAQ,KAAK,KAAK,CAAC,QAAQ;QAC7B;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useLayoutEffect.js"], "sourcesContent": ["/* eslint-disable react-hooks/rules-of-hooks */\nimport * as React from 'react';\nimport { isBrowserClient } from \"../utils/commonUtil\";\n\n/**\n * Wrap `React.useLayoutEffect` which will not throw warning message in test env\n */\nexport default function useLayoutEffect(effect, deps) {\n  // Never happen in test env\n  if (isBrowserClient) {\n    /* istanbul ignore next */\n    React.useLayoutEffect(effect, deps);\n  } else {\n    React.useEffect(effect, deps);\n  }\n}\n/* eslint-enable */"], "names": [], "mappings": "AAAA,6CAA6C;;;AAC7C;AACA;;;AAKe,SAAS,gBAAgB,MAAM,EAAE,IAAI;IAClD,2BAA2B;IAC3B,IAAI,4JAAA,CAAA,kBAAe,EAAE;QACnB,wBAAwB,GACxB,6JAAA,CAAA,kBAAqB,CAAC,QAAQ;IAChC,OAAO;QACL,6JAAA,CAAA,YAAe,CAAC,QAAQ;IAC1B;AACF,EACA,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/Selector/MultipleSelector.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport { useState } from 'react';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Overflow from 'rc-overflow';\nimport TransBtn from \"../TransBtn\";\nimport Input from \"./Input\";\nimport useLayoutEffect from \"../hooks/useLayoutEffect\";\nimport { getTitle } from \"../utils/commonUtil\";\nfunction itemKey(value) {\n  var _value$key;\n  return (_value$key = value.key) !== null && _value$key !== void 0 ? _value$key : value.value;\n}\nvar onPreventMouseDown = function onPreventMouseDown(event) {\n  event.preventDefault();\n  event.stopPropagation();\n};\nvar SelectSelector = function SelectSelector(props) {\n  var id = props.id,\n    prefixCls = props.prefixCls,\n    values = props.values,\n    open = props.open,\n    searchValue = props.searchValue,\n    autoClearSearchValue = props.autoClearSearchValue,\n    inputRef = props.inputRef,\n    placeholder = props.placeholder,\n    disabled = props.disabled,\n    mode = props.mode,\n    showSearch = props.showSearch,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    activeDescendantId = props.activeDescendantId,\n    tabIndex = props.tabIndex,\n    removeIcon = props.removeIcon,\n    maxTagCount = props.maxTagCount,\n    maxTagTextLength = props.maxTagTextLength,\n    _props$maxTagPlacehol = props.maxTagPlaceholder,\n    maxTagPlaceholder = _props$maxTagPlacehol === void 0 ? function (omittedValues) {\n      return \"+ \".concat(omittedValues.length, \" ...\");\n    } : _props$maxTagPlacehol,\n    tagRender = props.tagRender,\n    onToggleOpen = props.onToggleOpen,\n    onRemove = props.onRemove,\n    onInputChange = props.onInputChange,\n    onInputPaste = props.onInputPaste,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputMouseDown = props.onInputMouseDown,\n    onInputCompositionStart = props.onInputCompositionStart,\n    onInputCompositionEnd = props.onInputCompositionEnd,\n    onInputBlur = props.onInputBlur;\n  var measureRef = React.useRef(null);\n  var _useState = useState(0),\n    _useState2 = _slicedToArray(_useState, 2),\n    inputWidth = _useState2[0],\n    setInputWidth = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    focused = _useState4[0],\n    setFocused = _useState4[1];\n  var selectionPrefixCls = \"\".concat(prefixCls, \"-selection\");\n\n  // ===================== Search ======================\n  var inputValue = open || mode === 'multiple' && autoClearSearchValue === false || mode === 'tags' ? searchValue : '';\n  var inputEditable = mode === 'tags' || mode === 'multiple' && autoClearSearchValue === false || showSearch && (open || focused);\n\n  // We measure width and set to the input immediately\n  useLayoutEffect(function () {\n    setInputWidth(measureRef.current.scrollWidth);\n  }, [inputValue]);\n\n  // ===================== Render ======================\n  // >>> Render Selector Node. Includes Item & Rest\n  var defaultRenderSelector = function defaultRenderSelector(item, content, itemDisabled, closable, onClose) {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      title: getTitle(item),\n      className: classNames(\"\".concat(selectionPrefixCls, \"-item\"), _defineProperty({}, \"\".concat(selectionPrefixCls, \"-item-disabled\"), itemDisabled))\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(selectionPrefixCls, \"-item-content\")\n    }, content), closable && /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(selectionPrefixCls, \"-item-remove\"),\n      onMouseDown: onPreventMouseDown,\n      onClick: onClose,\n      customizeIcon: removeIcon\n    }, \"\\xD7\"));\n  };\n  var customizeRenderSelector = function customizeRenderSelector(value, content, itemDisabled, closable, onClose, isMaxTag) {\n    var onMouseDown = function onMouseDown(e) {\n      onPreventMouseDown(e);\n      onToggleOpen(!open);\n    };\n    return /*#__PURE__*/React.createElement(\"span\", {\n      onMouseDown: onMouseDown\n    }, tagRender({\n      label: content,\n      value: value,\n      disabled: itemDisabled,\n      closable: closable,\n      onClose: onClose,\n      isMaxTag: !!isMaxTag\n    }));\n  };\n  var renderItem = function renderItem(valueItem) {\n    var itemDisabled = valueItem.disabled,\n      label = valueItem.label,\n      value = valueItem.value;\n    var closable = !disabled && !itemDisabled;\n    var displayLabel = label;\n    if (typeof maxTagTextLength === 'number') {\n      if (typeof label === 'string' || typeof label === 'number') {\n        var strLabel = String(displayLabel);\n        if (strLabel.length > maxTagTextLength) {\n          displayLabel = \"\".concat(strLabel.slice(0, maxTagTextLength), \"...\");\n        }\n      }\n    }\n    var onClose = function onClose(event) {\n      if (event) {\n        event.stopPropagation();\n      }\n      onRemove(valueItem);\n    };\n    return typeof tagRender === 'function' ? customizeRenderSelector(value, displayLabel, itemDisabled, closable, onClose) : defaultRenderSelector(valueItem, displayLabel, itemDisabled, closable, onClose);\n  };\n  var renderRest = function renderRest(omittedValues) {\n    // https://github.com/ant-design/ant-design/issues/48930\n    if (!values.length) {\n      return null;\n    }\n    var content = typeof maxTagPlaceholder === 'function' ? maxTagPlaceholder(omittedValues) : maxTagPlaceholder;\n    return typeof tagRender === 'function' ? customizeRenderSelector(undefined, content, false, false, undefined, true) : defaultRenderSelector({\n      title: content\n    }, content, false);\n  };\n\n  // >>> Input Node\n  var inputNode = /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(selectionPrefixCls, \"-search\"),\n    style: {\n      width: inputWidth\n    },\n    onFocus: function onFocus() {\n      setFocused(true);\n    },\n    onBlur: function onBlur() {\n      setFocused(false);\n    }\n  }, /*#__PURE__*/React.createElement(Input, {\n    ref: inputRef,\n    open: open,\n    prefixCls: prefixCls,\n    id: id,\n    inputElement: null,\n    disabled: disabled,\n    autoFocus: autoFocus,\n    autoComplete: autoComplete,\n    editable: inputEditable,\n    activeDescendantId: activeDescendantId,\n    value: inputValue,\n    onKeyDown: onInputKeyDown,\n    onMouseDown: onInputMouseDown,\n    onChange: onInputChange,\n    onPaste: onInputPaste,\n    onCompositionStart: onInputCompositionStart,\n    onCompositionEnd: onInputCompositionEnd,\n    onBlur: onInputBlur,\n    tabIndex: tabIndex,\n    attrs: pickAttrs(props, true)\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    ref: measureRef,\n    className: \"\".concat(selectionPrefixCls, \"-search-mirror\"),\n    \"aria-hidden\": true\n  }, inputValue, \"\\xA0\"));\n\n  // >>> Selections\n  var selectionNode = /*#__PURE__*/React.createElement(Overflow, {\n    prefixCls: \"\".concat(selectionPrefixCls, \"-overflow\"),\n    data: values,\n    renderItem: renderItem,\n    renderRest: renderRest,\n    suffix: inputNode,\n    itemKey: itemKey,\n    maxCount: maxTagCount\n  });\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(selectionPrefixCls, \"-wrap\")\n  }, selectionNode, !values.length && !inputValue && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(selectionPrefixCls, \"-placeholder\")\n  }, placeholder));\n};\nexport default SelectSelector;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AACA,SAAS,QAAQ,KAAK;IACpB,IAAI;IACJ,OAAO,CAAC,aAAa,MAAM,GAAG,MAAM,QAAQ,eAAe,KAAK,IAAI,aAAa,MAAM,KAAK;AAC9F;AACA,IAAI,qBAAqB,SAAS,mBAAmB,KAAK;IACxD,MAAM,cAAc;IACpB,MAAM,eAAe;AACvB;AACA,IAAI,iBAAiB,SAAS,eAAe,KAAK;IAChD,IAAI,KAAK,MAAM,EAAE,EACf,YAAY,MAAM,SAAS,EAC3B,SAAS,MAAM,MAAM,EACrB,OAAO,MAAM,IAAI,EACjB,cAAc,MAAM,WAAW,EAC/B,uBAAuB,MAAM,oBAAoB,EACjD,WAAW,MAAM,QAAQ,EACzB,cAAc,MAAM,WAAW,EAC/B,WAAW,MAAM,QAAQ,EACzB,OAAO,MAAM,IAAI,EACjB,aAAa,MAAM,UAAU,EAC7B,YAAY,MAAM,SAAS,EAC3B,eAAe,MAAM,YAAY,EACjC,qBAAqB,MAAM,kBAAkB,EAC7C,WAAW,MAAM,QAAQ,EACzB,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW,EAC/B,mBAAmB,MAAM,gBAAgB,EACzC,wBAAwB,MAAM,iBAAiB,EAC/C,oBAAoB,0BAA0B,KAAK,IAAI,SAAU,aAAa;QAC5E,OAAO,KAAK,MAAM,CAAC,cAAc,MAAM,EAAE;IAC3C,IAAI,uBACJ,YAAY,MAAM,SAAS,EAC3B,eAAe,MAAM,YAAY,EACjC,WAAW,MAAM,QAAQ,EACzB,gBAAgB,MAAM,aAAa,EACnC,eAAe,MAAM,YAAY,EACjC,iBAAiB,MAAM,cAAc,EACrC,mBAAmB,MAAM,gBAAgB,EACzC,0BAA0B,MAAM,uBAAuB,EACvD,wBAAwB,MAAM,qBAAqB,EACnD,cAAc,MAAM,WAAW;IACjC,IAAI,aAAa,6JAAA,CAAA,SAAY,CAAC;IAC9B,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IACvB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,aAAa,UAAU,CAAC,EAAE,EAC1B,gBAAgB,UAAU,CAAC,EAAE;IAC/B,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QACxB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,YAAY,IACxC,UAAU,UAAU,CAAC,EAAE,EACvB,aAAa,UAAU,CAAC,EAAE;IAC5B,IAAI,qBAAqB,GAAG,MAAM,CAAC,WAAW;IAE9C,sDAAsD;IACtD,IAAI,aAAa,QAAQ,SAAS,cAAc,yBAAyB,SAAS,SAAS,SAAS,cAAc;IAClH,IAAI,gBAAgB,SAAS,UAAU,SAAS,cAAc,yBAAyB,SAAS,cAAc,CAAC,QAAQ,OAAO;IAE9H,oDAAoD;IACpD,CAAA,GAAA,iKAAA,CAAA,UAAe,AAAD;0CAAE;YACd,cAAc,WAAW,OAAO,CAAC,WAAW;QAC9C;yCAAG;QAAC;KAAW;IAEf,sDAAsD;IACtD,iDAAiD;IACjD,IAAI,wBAAwB,SAAS,sBAAsB,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO;QACvG,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;YAC9C,OAAO,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE;YAChB,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,MAAM,CAAC,oBAAoB,UAAU,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,oBAAoB,mBAAmB;QACrI,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;YAC1C,WAAW,GAAG,MAAM,CAAC,oBAAoB;QAC3C,GAAG,UAAU,YAAY,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,iJAAA,CAAA,UAAQ,EAAE;YAClE,WAAW,GAAG,MAAM,CAAC,oBAAoB;YACzC,aAAa;YACb,SAAS;YACT,eAAe;QACjB,GAAG;IACL;IACA,IAAI,0BAA0B,SAAS,wBAAwB,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ;QACtH,IAAI,cAAc,SAAS,YAAY,CAAC;YACtC,mBAAmB;YACnB,aAAa,CAAC;QAChB;QACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;YAC9C,aAAa;QACf,GAAG,UAAU;YACX,OAAO;YACP,OAAO;YACP,UAAU;YACV,UAAU;YACV,SAAS;YACT,UAAU,CAAC,CAAC;QACd;IACF;IACA,IAAI,aAAa,SAAS,WAAW,SAAS;QAC5C,IAAI,eAAe,UAAU,QAAQ,EACnC,QAAQ,UAAU,KAAK,EACvB,QAAQ,UAAU,KAAK;QACzB,IAAI,WAAW,CAAC,YAAY,CAAC;QAC7B,IAAI,eAAe;QACnB,IAAI,OAAO,qBAAqB,UAAU;YACxC,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;gBAC1D,IAAI,WAAW,OAAO;gBACtB,IAAI,SAAS,MAAM,GAAG,kBAAkB;oBACtC,eAAe,GAAG,MAAM,CAAC,SAAS,KAAK,CAAC,GAAG,mBAAmB;gBAChE;YACF;QACF;QACA,IAAI,UAAU,SAAS,QAAQ,KAAK;YAClC,IAAI,OAAO;gBACT,MAAM,eAAe;YACvB;YACA,SAAS;QACX;QACA,OAAO,OAAO,cAAc,aAAa,wBAAwB,OAAO,cAAc,cAAc,UAAU,WAAW,sBAAsB,WAAW,cAAc,cAAc,UAAU;IAClM;IACA,IAAI,aAAa,SAAS,WAAW,aAAa;QAChD,wDAAwD;QACxD,IAAI,CAAC,OAAO,MAAM,EAAE;YAClB,OAAO;QACT;QACA,IAAI,UAAU,OAAO,sBAAsB,aAAa,kBAAkB,iBAAiB;QAC3F,OAAO,OAAO,cAAc,aAAa,wBAAwB,WAAW,SAAS,OAAO,OAAO,WAAW,QAAQ,sBAAsB;YAC1I,OAAO;QACT,GAAG,SAAS;IACd;IAEA,iBAAiB;IACjB,IAAI,YAAY,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACtD,WAAW,GAAG,MAAM,CAAC,oBAAoB;QACzC,OAAO;YACL,OAAO;QACT;QACA,SAAS,SAAS;YAChB,WAAW;QACb;QACA,QAAQ,SAAS;YACf,WAAW;QACb;IACF,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0JAAA,CAAA,UAAK,EAAE;QACzC,KAAK;QACL,MAAM;QACN,WAAW;QACX,IAAI;QACJ,cAAc;QACd,UAAU;QACV,WAAW;QACX,cAAc;QACd,UAAU;QACV,oBAAoB;QACpB,OAAO;QACP,WAAW;QACX,aAAa;QACb,UAAU;QACV,SAAS;QACT,oBAAoB;QACpB,kBAAkB;QAClB,QAAQ;QACR,UAAU;QACV,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAS,AAAD,EAAE,OAAO;IAC1B,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC3C,KAAK;QACL,WAAW,GAAG,MAAM,CAAC,oBAAoB;QACzC,eAAe;IACjB,GAAG,YAAY;IAEf,iBAAiB;IACjB,IAAI,gBAAgB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,gJAAA,CAAA,UAAQ,EAAE;QAC7D,WAAW,GAAG,MAAM,CAAC,oBAAoB;QACzC,MAAM;QACN,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC9C,WAAW,GAAG,MAAM,CAAC,oBAAoB;IAC3C,GAAG,eAAe,CAAC,OAAO,MAAM,IAAI,CAAC,cAAc,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC1F,WAAW,GAAG,MAAM,CAAC,oBAAoB;IAC3C,GAAG;AACL;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/Selector/SingleSelector.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport Input from \"./Input\";\nimport { getTitle } from \"../utils/commonUtil\";\nvar SingleSelector = function SingleSelector(props) {\n  var inputElement = props.inputElement,\n    prefixCls = props.prefixCls,\n    id = props.id,\n    inputRef = props.inputRef,\n    disabled = props.disabled,\n    autoFocus = props.autoFocus,\n    autoComplete = props.autoComplete,\n    activeDescendantId = props.activeDescendantId,\n    mode = props.mode,\n    open = props.open,\n    values = props.values,\n    placeholder = props.placeholder,\n    tabIndex = props.tabIndex,\n    showSearch = props.showSearch,\n    searchValue = props.searchValue,\n    activeValue = props.activeValue,\n    maxLength = props.maxLength,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputMouseDown = props.onInputMouseDown,\n    onInputChange = props.onInputChange,\n    onInputPaste = props.onInputPaste,\n    onInputCompositionStart = props.onInputCompositionStart,\n    onInputCompositionEnd = props.onInputCompositionEnd,\n    onInputBlur = props.onInputBlur,\n    title = props.title;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    inputChanged = _React$useState2[0],\n    setInputChanged = _React$useState2[1];\n  var combobox = mode === 'combobox';\n  var inputEditable = combobox || showSearch;\n  var item = values[0];\n  var inputValue = searchValue || '';\n  if (combobox && activeValue && !inputChanged) {\n    inputValue = activeValue;\n  }\n  React.useEffect(function () {\n    if (combobox) {\n      setInputChanged(false);\n    }\n  }, [combobox, activeValue]);\n\n  // Not show text when closed expect combobox mode\n  var hasTextInput = mode !== 'combobox' && !open && !showSearch ? false : !!inputValue;\n\n  // Get title of selection item\n  var selectionTitle = title === undefined ? getTitle(item) : title;\n  var placeholderNode = React.useMemo(function () {\n    if (item) {\n      return null;\n    }\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"\".concat(prefixCls, \"-selection-placeholder\"),\n      style: hasTextInput ? {\n        visibility: 'hidden'\n      } : undefined\n    }, placeholder);\n  }, [item, hasTextInput, placeholder, prefixCls]);\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-wrap\")\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-search\")\n  }, /*#__PURE__*/React.createElement(Input, {\n    ref: inputRef,\n    prefixCls: prefixCls,\n    id: id,\n    open: open,\n    inputElement: inputElement,\n    disabled: disabled,\n    autoFocus: autoFocus,\n    autoComplete: autoComplete,\n    editable: inputEditable,\n    activeDescendantId: activeDescendantId,\n    value: inputValue,\n    onKeyDown: onInputKeyDown,\n    onMouseDown: onInputMouseDown,\n    onChange: function onChange(e) {\n      setInputChanged(true);\n      onInputChange(e);\n    },\n    onPaste: onInputPaste,\n    onCompositionStart: onInputCompositionStart,\n    onCompositionEnd: onInputCompositionEnd,\n    onBlur: onInputBlur,\n    tabIndex: tabIndex,\n    attrs: pickAttrs(props, true),\n    maxLength: combobox ? maxLength : undefined\n  })), !combobox && item ? /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(prefixCls, \"-selection-item\"),\n    title: selectionTitle\n    // 当 Select 已经选中选项时，还需 selection 隐藏但留在原地占位\n    // https://github.com/ant-design/ant-design/issues/27688\n    // https://github.com/ant-design/ant-design/issues/41530\n    ,\n    style: hasTextInput ? {\n      visibility: 'hidden'\n    } : undefined\n  }, item.label) : null, placeholderNode);\n};\nexport default SingleSelector;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,IAAI,iBAAiB,SAAS,eAAe,KAAK;IAChD,IAAI,eAAe,MAAM,YAAY,EACnC,YAAY,MAAM,SAAS,EAC3B,KAAK,MAAM,EAAE,EACb,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,eAAe,MAAM,YAAY,EACjC,qBAAqB,MAAM,kBAAkB,EAC7C,OAAO,MAAM,IAAI,EACjB,OAAO,MAAM,IAAI,EACjB,SAAS,MAAM,MAAM,EACrB,cAAc,MAAM,WAAW,EAC/B,WAAW,MAAM,QAAQ,EACzB,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW,EAC/B,cAAc,MAAM,WAAW,EAC/B,YAAY,MAAM,SAAS,EAC3B,iBAAiB,MAAM,cAAc,EACrC,mBAAmB,MAAM,gBAAgB,EACzC,gBAAgB,MAAM,aAAa,EACnC,eAAe,MAAM,YAAY,EACjC,0BAA0B,MAAM,uBAAuB,EACvD,wBAAwB,MAAM,qBAAqB,EACnD,cAAc,MAAM,WAAW,EAC/B,QAAQ,MAAM,KAAK;IACrB,IAAI,kBAAkB,6JAAA,CAAA,WAAc,CAAC,QACnC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,eAAe,gBAAgB,CAAC,EAAE,EAClC,kBAAkB,gBAAgB,CAAC,EAAE;IACvC,IAAI,WAAW,SAAS;IACxB,IAAI,gBAAgB,YAAY;IAChC,IAAI,OAAO,MAAM,CAAC,EAAE;IACpB,IAAI,aAAa,eAAe;IAChC,IAAI,YAAY,eAAe,CAAC,cAAc;QAC5C,aAAa;IACf;IACA,6JAAA,CAAA,YAAe;oCAAC;YACd,IAAI,UAAU;gBACZ,gBAAgB;YAClB;QACF;mCAAG;QAAC;QAAU;KAAY;IAE1B,iDAAiD;IACjD,IAAI,eAAe,SAAS,cAAc,CAAC,QAAQ,CAAC,aAAa,QAAQ,CAAC,CAAC;IAE3E,8BAA8B;IAC9B,IAAI,iBAAiB,UAAU,YAAY,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;IAC5D,IAAI,kBAAkB,6JAAA,CAAA,UAAa;mDAAC;YAClC,IAAI,MAAM;gBACR,OAAO;YACT;YACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;gBAC9C,WAAW,GAAG,MAAM,CAAC,WAAW;gBAChC,OAAO,eAAe;oBACpB,YAAY;gBACd,IAAI;YACN,GAAG;QACL;kDAAG;QAAC;QAAM;QAAc;QAAa;KAAU;IAC/C,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC9C,WAAW,GAAG,MAAM,CAAC,WAAW;IAClC,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC1C,WAAW,GAAG,MAAM,CAAC,WAAW;IAClC,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0JAAA,CAAA,UAAK,EAAE;QACzC,KAAK;QACL,WAAW;QACX,IAAI;QACJ,MAAM;QACN,cAAc;QACd,UAAU;QACV,WAAW;QACX,cAAc;QACd,UAAU;QACV,oBAAoB;QACpB,OAAO;QACP,WAAW;QACX,aAAa;QACb,UAAU,SAAS,SAAS,CAAC;YAC3B,gBAAgB;YAChB,cAAc;QAChB;QACA,SAAS;QACT,oBAAoB;QACpB,kBAAkB;QAClB,QAAQ;QACR,UAAU;QACV,OAAO,CAAA,GAAA,gJAAA,CAAA,UAAS,AAAD,EAAE,OAAO;QACxB,WAAW,WAAW,YAAY;IACpC,KAAK,CAAC,YAAY,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAChE,WAAW,GAAG,MAAM,CAAC,WAAW;QAChC,OAAO;QAKP,OAAO,eAAe;YACpB,YAAY;QACd,IAAI;IACN,GAAG,KAAK,KAAK,IAAI,MAAM;AACzB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/Selector/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/**\n * Cursor rule:\n * 1. Only `showSearch` enabled\n * 2. Only `open` is `true`\n * 3. When typing, set `open` to `true` which hit rule of 2\n *\n * Accessibility:\n * - https://www.w3.org/TR/wai-aria-practices/examples/combobox/aria1.1pattern/listbox-combo.html\n */\n\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { useRef } from 'react';\nimport useLock from \"../hooks/useLock\";\nimport { isValidateOpenKey } from \"../utils/keyUtil\";\nimport MultipleSelector from \"./MultipleSelector\";\nimport SingleSelector from \"./SingleSelector\";\nvar Selector = function Selector(props, ref) {\n  var inputRef = useRef(null);\n  var compositionStatusRef = useRef(false);\n  var prefixCls = props.prefixCls,\n    open = props.open,\n    mode = props.mode,\n    showSearch = props.showSearch,\n    tokenWithEnter = props.tokenWithEnter,\n    disabled = props.disabled,\n    prefix = props.prefix,\n    autoClearSearchValue = props.autoClearSearchValue,\n    onSearch = props.onSearch,\n    onSearchSubmit = props.onSearchSubmit,\n    onToggleOpen = props.onToggleOpen,\n    onInputKeyDown = props.onInputKeyDown,\n    onInputBlur = props.onInputBlur,\n    domRef = props.domRef;\n\n  // ======================= Ref =======================\n  React.useImperativeHandle(ref, function () {\n    return {\n      focus: function focus(options) {\n        inputRef.current.focus(options);\n      },\n      blur: function blur() {\n        inputRef.current.blur();\n      }\n    };\n  });\n\n  // ====================== Input ======================\n  var _useLock = useLock(0),\n    _useLock2 = _slicedToArray(_useLock, 2),\n    getInputMouseDown = _useLock2[0],\n    setInputMouseDown = _useLock2[1];\n  var onInternalInputKeyDown = function onInternalInputKeyDown(event) {\n    var which = event.which;\n\n    // Compatible with multiple lines in TextArea\n    var isTextAreaElement = inputRef.current instanceof HTMLTextAreaElement;\n    if (!isTextAreaElement && open && (which === KeyCode.UP || which === KeyCode.DOWN)) {\n      event.preventDefault();\n    }\n    if (onInputKeyDown) {\n      onInputKeyDown(event);\n    }\n    if (which === KeyCode.ENTER && mode === 'tags' && !compositionStatusRef.current && !open) {\n      // When menu isn't open, OptionList won't trigger a value change\n      // So when enter is pressed, the tag's input value should be emitted here to let selector know\n      onSearchSubmit === null || onSearchSubmit === void 0 || onSearchSubmit(event.target.value);\n    }\n    // Move within the text box\n    if (isTextAreaElement && !open && ~[KeyCode.UP, KeyCode.DOWN, KeyCode.LEFT, KeyCode.RIGHT].indexOf(which)) {\n      return;\n    }\n    if (isValidateOpenKey(which)) {\n      onToggleOpen(true);\n    }\n  };\n\n  /**\n   * We can not use `findDOMNode` sine it will get warning,\n   * have to use timer to check if is input element.\n   */\n  var onInternalInputMouseDown = function onInternalInputMouseDown() {\n    setInputMouseDown(true);\n  };\n\n  // When paste come, ignore next onChange\n  var pastedTextRef = useRef(null);\n  var triggerOnSearch = function triggerOnSearch(value) {\n    if (onSearch(value, true, compositionStatusRef.current) !== false) {\n      onToggleOpen(true);\n    }\n  };\n  var onInputCompositionStart = function onInputCompositionStart() {\n    compositionStatusRef.current = true;\n  };\n  var onInputCompositionEnd = function onInputCompositionEnd(e) {\n    compositionStatusRef.current = false;\n\n    // Trigger search again to support `tokenSeparators` with typewriting\n    if (mode !== 'combobox') {\n      triggerOnSearch(e.target.value);\n    }\n  };\n  var onInputChange = function onInputChange(event) {\n    var value = event.target.value;\n\n    // Pasted text should replace back to origin content\n    if (tokenWithEnter && pastedTextRef.current && /[\\r\\n]/.test(pastedTextRef.current)) {\n      // CRLF will be treated as a single space for input element\n      var replacedText = pastedTextRef.current.replace(/[\\r\\n]+$/, '').replace(/\\r\\n/g, ' ').replace(/[\\r\\n]/g, ' ');\n      value = value.replace(replacedText, pastedTextRef.current);\n    }\n    pastedTextRef.current = null;\n    triggerOnSearch(value);\n  };\n  var onInputPaste = function onInputPaste(e) {\n    var clipboardData = e.clipboardData;\n    var value = clipboardData === null || clipboardData === void 0 ? void 0 : clipboardData.getData('text');\n    pastedTextRef.current = value || '';\n  };\n  var onClick = function onClick(_ref) {\n    var target = _ref.target;\n    if (target !== inputRef.current) {\n      // Should focus input if click the selector\n      var isIE = document.body.style.msTouchAction !== undefined;\n      if (isIE) {\n        setTimeout(function () {\n          inputRef.current.focus();\n        });\n      } else {\n        inputRef.current.focus();\n      }\n    }\n  };\n  var onMouseDown = function onMouseDown(event) {\n    var inputMouseDown = getInputMouseDown();\n\n    // when mode is combobox and it is disabled, don't prevent default behavior\n    // https://github.com/ant-design/ant-design/issues/37320\n    // https://github.com/ant-design/ant-design/issues/48281\n    if (event.target !== inputRef.current && !inputMouseDown && !(mode === 'combobox' && disabled)) {\n      event.preventDefault();\n    }\n    if (mode !== 'combobox' && (!showSearch || !inputMouseDown) || !open) {\n      if (open && autoClearSearchValue !== false) {\n        onSearch('', true, false);\n      }\n      onToggleOpen();\n    }\n  };\n\n  // ================= Inner Selector ==================\n  var sharedProps = {\n    inputRef: inputRef,\n    onInputKeyDown: onInternalInputKeyDown,\n    onInputMouseDown: onInternalInputMouseDown,\n    onInputChange: onInputChange,\n    onInputPaste: onInputPaste,\n    onInputCompositionStart: onInputCompositionStart,\n    onInputCompositionEnd: onInputCompositionEnd,\n    onInputBlur: onInputBlur\n  };\n  var selectNode = mode === 'multiple' || mode === 'tags' ? /*#__PURE__*/React.createElement(MultipleSelector, _extends({}, props, sharedProps)) : /*#__PURE__*/React.createElement(SingleSelector, _extends({}, props, sharedProps));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: domRef,\n    className: \"\".concat(prefixCls, \"-selector\"),\n    onClick: onClick,\n    onMouseDown: onMouseDown\n  }, prefix && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-prefix\")\n  }, prefix), selectNode);\n};\nvar ForwardSelector = /*#__PURE__*/React.forwardRef(Selector);\nif (process.env.NODE_ENV !== 'production') {\n  ForwardSelector.displayName = 'Selector';\n}\nexport default ForwardSelector;"], "names": [], "mappings": ";;;AA+KI;AA/KJ;AACA;AACA;;;;;;;;CAQC,GAED;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;AACA,IAAI,WAAW,SAAS,SAAS,KAAK,EAAE,GAAG;IACzC,IAAI,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtB,IAAI,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAClC,IAAI,YAAY,MAAM,SAAS,EAC7B,OAAO,MAAM,IAAI,EACjB,OAAO,MAAM,IAAI,EACjB,aAAa,MAAM,UAAU,EAC7B,iBAAiB,MAAM,cAAc,EACrC,WAAW,MAAM,QAAQ,EACzB,SAAS,MAAM,MAAM,EACrB,uBAAuB,MAAM,oBAAoB,EACjD,WAAW,MAAM,QAAQ,EACzB,iBAAiB,MAAM,cAAc,EACrC,eAAe,MAAM,YAAY,EACjC,iBAAiB,MAAM,cAAc,EACrC,cAAc,MAAM,WAAW,EAC/B,SAAS,MAAM,MAAM;IAEvB,sDAAsD;IACtD,6JAAA,CAAA,sBAAyB,CAAC;wCAAK;YAC7B,OAAO;gBACL,OAAO,SAAS,MAAM,OAAO;oBAC3B,SAAS,OAAO,CAAC,KAAK,CAAC;gBACzB;gBACA,MAAM,SAAS;oBACb,SAAS,OAAO,CAAC,IAAI;gBACvB;YACF;QACF;;IAEA,sDAAsD;IACtD,IAAI,WAAW,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,IACrB,YAAY,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,UAAU,IACrC,oBAAoB,SAAS,CAAC,EAAE,EAChC,oBAAoB,SAAS,CAAC,EAAE;IAClC,IAAI,yBAAyB,SAAS,uBAAuB,KAAK;QAChE,IAAI,QAAQ,MAAM,KAAK;QAEvB,6CAA6C;QAC7C,IAAI,oBAAoB,SAAS,OAAO,YAAY;QACpD,IAAI,CAAC,qBAAqB,QAAQ,CAAC,UAAU,8IAAA,CAAA,UAAO,CAAC,EAAE,IAAI,UAAU,8IAAA,CAAA,UAAO,CAAC,IAAI,GAAG;YAClF,MAAM,cAAc;QACtB;QACA,IAAI,gBAAgB;YAClB,eAAe;QACjB;QACA,IAAI,UAAU,8IAAA,CAAA,UAAO,CAAC,KAAK,IAAI,SAAS,UAAU,CAAC,qBAAqB,OAAO,IAAI,CAAC,MAAM;YACxF,gEAAgE;YAChE,8FAA8F;YAC9F,mBAAmB,QAAQ,mBAAmB,KAAK,KAAK,eAAe,MAAM,MAAM,CAAC,KAAK;QAC3F;QACA,2BAA2B;QAC3B,IAAI,qBAAqB,CAAC,QAAQ,CAAC;YAAC,8IAAA,CAAA,UAAO,CAAC,EAAE;YAAE,8IAAA,CAAA,UAAO,CAAC,IAAI;YAAE,8IAAA,CAAA,UAAO,CAAC,IAAI;YAAE,8IAAA,CAAA,UAAO,CAAC,KAAK;SAAC,CAAC,OAAO,CAAC,QAAQ;YACzG;QACF;QACA,IAAI,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;YAC5B,aAAa;QACf;IACF;IAEA;;;GAGC,GACD,IAAI,2BAA2B,SAAS;QACtC,kBAAkB;IACpB;IAEA,wCAAwC;IACxC,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,IAAI,kBAAkB,SAAS,gBAAgB,KAAK;QAClD,IAAI,SAAS,OAAO,MAAM,qBAAqB,OAAO,MAAM,OAAO;YACjE,aAAa;QACf;IACF;IACA,IAAI,0BAA0B,SAAS;QACrC,qBAAqB,OAAO,GAAG;IACjC;IACA,IAAI,wBAAwB,SAAS,sBAAsB,CAAC;QAC1D,qBAAqB,OAAO,GAAG;QAE/B,qEAAqE;QACrE,IAAI,SAAS,YAAY;YACvB,gBAAgB,EAAE,MAAM,CAAC,KAAK;QAChC;IACF;IACA,IAAI,gBAAgB,SAAS,cAAc,KAAK;QAC9C,IAAI,QAAQ,MAAM,MAAM,CAAC,KAAK;QAE9B,oDAAoD;QACpD,IAAI,kBAAkB,cAAc,OAAO,IAAI,SAAS,IAAI,CAAC,cAAc,OAAO,GAAG;YACnF,2DAA2D;YAC3D,IAAI,eAAe,cAAc,OAAO,CAAC,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,WAAW;YAC1G,QAAQ,MAAM,OAAO,CAAC,cAAc,cAAc,OAAO;QAC3D;QACA,cAAc,OAAO,GAAG;QACxB,gBAAgB;IAClB;IACA,IAAI,eAAe,SAAS,aAAa,CAAC;QACxC,IAAI,gBAAgB,EAAE,aAAa;QACnC,IAAI,QAAQ,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,KAAK,IAAI,cAAc,OAAO,CAAC;QAChG,cAAc,OAAO,GAAG,SAAS;IACnC;IACA,IAAI,UAAU,SAAS,QAAQ,IAAI;QACjC,IAAI,SAAS,KAAK,MAAM;QACxB,IAAI,WAAW,SAAS,OAAO,EAAE;YAC/B,2CAA2C;YAC3C,IAAI,OAAO,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,KAAK;YACjD,IAAI,MAAM;gBACR,WAAW;oBACT,SAAS,OAAO,CAAC,KAAK;gBACxB;YACF,OAAO;gBACL,SAAS,OAAO,CAAC,KAAK;YACxB;QACF;IACF;IACA,IAAI,cAAc,SAAS,YAAY,KAAK;QAC1C,IAAI,iBAAiB;QAErB,2EAA2E;QAC3E,wDAAwD;QACxD,wDAAwD;QACxD,IAAI,MAAM,MAAM,KAAK,SAAS,OAAO,IAAI,CAAC,kBAAkB,CAAC,CAAC,SAAS,cAAc,QAAQ,GAAG;YAC9F,MAAM,cAAc;QACtB;QACA,IAAI,SAAS,cAAc,CAAC,CAAC,cAAc,CAAC,cAAc,KAAK,CAAC,MAAM;YACpE,IAAI,QAAQ,yBAAyB,OAAO;gBAC1C,SAAS,IAAI,MAAM;YACrB;YACA;QACF;IACF;IAEA,sDAAsD;IACtD,IAAI,cAAc;QAChB,UAAU;QACV,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;QACf,cAAc;QACd,yBAAyB;QACzB,uBAAuB;QACvB,aAAa;IACf;IACA,IAAI,aAAa,SAAS,cAAc,SAAS,SAAS,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,qKAAA,CAAA,UAAgB,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO,gBAAgB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,mKAAA,CAAA,UAAc,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;IACtN,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAC7C,KAAK;QACL,WAAW,GAAG,MAAM,CAAC,WAAW;QAChC,SAAS;QACT,aAAa;IACf,GAAG,UAAU,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QACnD,WAAW,GAAG,MAAM,CAAC,WAAW;IAClC,GAAG,SAAS;AACd;AACA,IAAI,kBAAkB,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC;AACpD,wCAA2C;IACzC,gBAAgB,WAAW,GAAG;AAChC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/SelectTrigger.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"disabled\", \"visible\", \"children\", \"popupElement\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"direction\", \"placement\", \"builtinPlacements\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"getPopupContainer\", \"empty\", \"getTriggerDOMNode\", \"onPopupVisibleChange\", \"onPopupMouseEnter\"];\nimport Trigger from '@rc-component/trigger';\nimport classNames from 'classnames';\nimport * as React from 'react';\nvar getBuiltInPlacements = function getBuiltInPlacements(dropdownMatchSelectWidth) {\n  // Enable horizontal overflow auto-adjustment when a custom dropdown width is provided\n  var adjustX = dropdownMatchSelectWidth === true ? 0 : 1;\n  return {\n    bottomLeft: {\n      points: ['tl', 'bl'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    bottomRight: {\n      points: ['tr', 'br'],\n      offset: [0, 4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topLeft: {\n      points: ['bl', 'tl'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    },\n    topRight: {\n      points: ['br', 'tr'],\n      offset: [0, -4],\n      overflow: {\n        adjustX: adjustX,\n        adjustY: 1\n      },\n      htmlRegion: 'scroll'\n    }\n  };\n};\nvar SelectTrigger = function SelectTrigger(props, ref) {\n  var prefixCls = props.prefixCls,\n    disabled = props.disabled,\n    visible = props.visible,\n    children = props.children,\n    popupElement = props.popupElement,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    _props$direction = props.direction,\n    direction = _props$direction === void 0 ? 'ltr' : _props$direction,\n    placement = props.placement,\n    builtinPlacements = props.builtinPlacements,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    getPopupContainer = props.getPopupContainer,\n    empty = props.empty,\n    getTriggerDOMNode = props.getTriggerDOMNode,\n    onPopupVisibleChange = props.onPopupVisibleChange,\n    onPopupMouseEnter = props.onPopupMouseEnter,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var dropdownPrefixCls = \"\".concat(prefixCls, \"-dropdown\");\n  var popupNode = popupElement;\n  if (dropdownRender) {\n    popupNode = dropdownRender(popupElement);\n  }\n  var mergedBuiltinPlacements = React.useMemo(function () {\n    return builtinPlacements || getBuiltInPlacements(dropdownMatchSelectWidth);\n  }, [builtinPlacements, dropdownMatchSelectWidth]);\n\n  // ===================== Motion ======================\n  var mergedTransitionName = animation ? \"\".concat(dropdownPrefixCls, \"-\").concat(animation) : transitionName;\n\n  // =================== Popup Width ===================\n  var isNumberPopupWidth = typeof dropdownMatchSelectWidth === 'number';\n  var stretch = React.useMemo(function () {\n    if (isNumberPopupWidth) {\n      return null;\n    }\n    return dropdownMatchSelectWidth === false ? 'minWidth' : 'width';\n  }, [dropdownMatchSelectWidth, isNumberPopupWidth]);\n  var popupStyle = dropdownStyle;\n  if (isNumberPopupWidth) {\n    popupStyle = _objectSpread(_objectSpread({}, popupStyle), {}, {\n      width: dropdownMatchSelectWidth\n    });\n  }\n\n  // ======================= Ref =======================\n  var triggerPopupRef = React.useRef(null);\n  React.useImperativeHandle(ref, function () {\n    return {\n      getPopupElement: function getPopupElement() {\n        var _triggerPopupRef$curr;\n        return (_triggerPopupRef$curr = triggerPopupRef.current) === null || _triggerPopupRef$curr === void 0 ? void 0 : _triggerPopupRef$curr.popupElement;\n      }\n    };\n  });\n  return /*#__PURE__*/React.createElement(Trigger, _extends({}, restProps, {\n    showAction: onPopupVisibleChange ? ['click'] : [],\n    hideAction: onPopupVisibleChange ? ['click'] : [],\n    popupPlacement: placement || (direction === 'rtl' ? 'bottomRight' : 'bottomLeft'),\n    builtinPlacements: mergedBuiltinPlacements,\n    prefixCls: dropdownPrefixCls,\n    popupTransitionName: mergedTransitionName,\n    popup: /*#__PURE__*/React.createElement(\"div\", {\n      onMouseEnter: onPopupMouseEnter\n    }, popupNode),\n    ref: triggerPopupRef,\n    stretch: stretch,\n    popupAlign: dropdownAlign,\n    popupVisible: visible,\n    getPopupContainer: getPopupContainer,\n    popupClassName: classNames(dropdownClassName, _defineProperty({}, \"\".concat(dropdownPrefixCls, \"-empty\"), empty)),\n    popupStyle: popupStyle,\n    getTriggerDOMNode: getTriggerDOMNode,\n    onPopupVisibleChange: onPopupVisibleChange\n  }), children);\n};\nvar RefSelectTrigger = /*#__PURE__*/React.forwardRef(SelectTrigger);\nif (process.env.NODE_ENV !== 'production') {\n  RefSelectTrigger.displayName = 'SelectTrigger';\n}\nexport default RefSelectTrigger;"], "names": [], "mappings": ";;;AAoII;AApIJ;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;AAHA,IAAI,YAAY;IAAC;IAAa;IAAY;IAAW;IAAY;IAAgB;IAAa;IAAkB;IAAiB;IAAqB;IAAa;IAAa;IAAqB;IAA4B;IAAkB;IAAiB;IAAqB;IAAS;IAAqB;IAAwB;CAAoB;;;;AAInW,IAAI,uBAAuB,SAAS,qBAAqB,wBAAwB;IAC/E,sFAAsF;IACtF,IAAI,UAAU,6BAA6B,OAAO,IAAI;IACtD,OAAO;QACL,YAAY;YACV,QAAQ;gBAAC;gBAAM;aAAK;YACpB,QAAQ;gBAAC;gBAAG;aAAE;YACd,UAAU;gBACR,SAAS;gBACT,SAAS;YACX;YACA,YAAY;QACd;QACA,aAAa;YACX,QAAQ;gBAAC;gBAAM;aAAK;YACpB,QAAQ;gBAAC;gBAAG;aAAE;YACd,UAAU;gBACR,SAAS;gBACT,SAAS;YACX;YACA,YAAY;QACd;QACA,SAAS;YACP,QAAQ;gBAAC;gBAAM;aAAK;YACpB,QAAQ;gBAAC;gBAAG,CAAC;aAAE;YACf,UAAU;gBACR,SAAS;gBACT,SAAS;YACX;YACA,YAAY;QACd;QACA,UAAU;YACR,QAAQ;gBAAC;gBAAM;aAAK;YACpB,QAAQ;gBAAC;gBAAG,CAAC;aAAE;YACf,UAAU;gBACR,SAAS;gBACT,SAAS;YACX;YACA,YAAY;QACd;IACF;AACF;AACA,IAAI,gBAAgB,SAAS,cAAc,KAAK,EAAE,GAAG;IACnD,IAAI,YAAY,MAAM,SAAS,EAC7B,WAAW,MAAM,QAAQ,EACzB,UAAU,MAAM,OAAO,EACvB,WAAW,MAAM,QAAQ,EACzB,eAAe,MAAM,YAAY,EACjC,YAAY,MAAM,SAAS,EAC3B,iBAAiB,MAAM,cAAc,EACrC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,MAAM,iBAAiB,EAC3C,mBAAmB,MAAM,SAAS,EAClC,YAAY,qBAAqB,KAAK,IAAI,QAAQ,kBAClD,YAAY,MAAM,SAAS,EAC3B,oBAAoB,MAAM,iBAAiB,EAC3C,2BAA2B,MAAM,wBAAwB,EACzD,iBAAiB,MAAM,cAAc,EACrC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,MAAM,iBAAiB,EAC3C,QAAQ,MAAM,KAAK,EACnB,oBAAoB,MAAM,iBAAiB,EAC3C,uBAAuB,MAAM,oBAAoB,EACjD,oBAAoB,MAAM,iBAAiB,EAC3C,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,OAAO;IAC9C,IAAI,oBAAoB,GAAG,MAAM,CAAC,WAAW;IAC7C,IAAI,YAAY;IAChB,IAAI,gBAAgB;QAClB,YAAY,eAAe;IAC7B;IACA,IAAI,0BAA0B,6JAAA,CAAA,UAAa;0DAAC;YAC1C,OAAO,qBAAqB,qBAAqB;QACnD;yDAAG;QAAC;QAAmB;KAAyB;IAEhD,sDAAsD;IACtD,IAAI,uBAAuB,YAAY,GAAG,MAAM,CAAC,mBAAmB,KAAK,MAAM,CAAC,aAAa;IAE7F,sDAAsD;IACtD,IAAI,qBAAqB,OAAO,6BAA6B;IAC7D,IAAI,UAAU,6JAAA,CAAA,UAAa;0CAAC;YAC1B,IAAI,oBAAoB;gBACtB,OAAO;YACT;YACA,OAAO,6BAA6B,QAAQ,aAAa;QAC3D;yCAAG;QAAC;QAA0B;KAAmB;IACjD,IAAI,aAAa;IACjB,IAAI,oBAAoB;QACtB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,aAAa,CAAC,GAAG;YAC5D,OAAO;QACT;IACF;IAEA,sDAAsD;IACtD,IAAI,kBAAkB,6JAAA,CAAA,SAAY,CAAC;IACnC,6JAAA,CAAA,sBAAyB,CAAC;6CAAK;YAC7B,OAAO;gBACL,iBAAiB,SAAS;oBACxB,IAAI;oBACJ,OAAO,CAAC,wBAAwB,gBAAgB,OAAO,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,YAAY;gBACrJ;YACF;QACF;;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,8JAAA,CAAA,UAAO,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,WAAW;QACvE,YAAY,uBAAuB;YAAC;SAAQ,GAAG,EAAE;QACjD,YAAY,uBAAuB;YAAC;SAAQ,GAAG,EAAE;QACjD,gBAAgB,aAAa,CAAC,cAAc,QAAQ,gBAAgB,YAAY;QAChF,mBAAmB;QACnB,WAAW;QACX,qBAAqB;QACrB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;YAC7C,cAAc;QAChB,GAAG;QACH,KAAK;QACL,SAAS;QACT,YAAY;QACZ,cAAc;QACd,mBAAmB;QACnB,gBAAgB,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,mBAAmB,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,mBAAmB,WAAW;QAC1G,YAAY;QACZ,mBAAmB;QACnB,sBAAsB;IACxB,IAAI;AACN;AACA,IAAI,mBAAmB,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC;AACrD,wCAA2C;IACzC,iBAAiB,WAAW,GAAG;AACjC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/utils/valueUtil.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _toArray from \"@babel/runtime/helpers/esm/toArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport warning from \"rc-util/es/warning\";\nfunction getKey(data, index) {\n  var key = data.key;\n  var value;\n  if ('value' in data) {\n    value = data.value;\n  }\n  if (key !== null && key !== undefined) {\n    return key;\n  }\n  if (value !== undefined) {\n    return value;\n  }\n  return \"rc-index-key-\".concat(index);\n}\nexport function isValidCount(value) {\n  return typeof value !== 'undefined' && !Number.isNaN(value);\n}\nexport function fillFieldNames(fieldNames, childrenAsData) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    options = _ref.options,\n    groupLabel = _ref.groupLabel;\n  var mergedLabel = label || (childrenAsData ? 'children' : 'label');\n  return {\n    label: mergedLabel,\n    value: value || 'value',\n    options: options || 'options',\n    groupLabel: groupLabel || mergedLabel\n  };\n}\n\n/**\n * Flat options into flatten list.\n * We use `optionOnly` here is aim to avoid user use nested option group.\n * Here is simply set `key` to the index if not provided.\n */\nexport function flattenOptions(options) {\n  var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n    fieldNames = _ref2.fieldNames,\n    childrenAsData = _ref2.childrenAsData;\n  var flattenList = [];\n  var _fillFieldNames = fillFieldNames(fieldNames, false),\n    fieldLabel = _fillFieldNames.label,\n    fieldValue = _fillFieldNames.value,\n    fieldOptions = _fillFieldNames.options,\n    groupLabel = _fillFieldNames.groupLabel;\n  function dig(list, isGroupOption) {\n    if (!Array.isArray(list)) {\n      return;\n    }\n    list.forEach(function (data) {\n      if (isGroupOption || !(fieldOptions in data)) {\n        var value = data[fieldValue];\n\n        // Option\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          groupOption: isGroupOption,\n          data: data,\n          label: data[fieldLabel],\n          value: value\n        });\n      } else {\n        var grpLabel = data[groupLabel];\n        if (grpLabel === undefined && childrenAsData) {\n          grpLabel = data.label;\n        }\n\n        // Option Group\n        flattenList.push({\n          key: getKey(data, flattenList.length),\n          group: true,\n          data: data,\n          label: grpLabel\n        });\n        dig(data[fieldOptions], true);\n      }\n    });\n  }\n  dig(options, false);\n  return flattenList;\n}\n\n/**\n * Inject `props` into `option` for legacy usage\n */\nexport function injectPropsWithOption(option) {\n  var newOption = _objectSpread({}, option);\n  if (!('props' in newOption)) {\n    Object.defineProperty(newOption, 'props', {\n      get: function get() {\n        warning(false, 'Return type is option instead of Option instance. Please read value directly instead of reading from `props`.');\n        return newOption;\n      }\n    });\n  }\n  return newOption;\n}\nexport var getSeparatedContent = function getSeparatedContent(text, tokens, end) {\n  if (!tokens || !tokens.length) {\n    return null;\n  }\n  var match = false;\n  var separate = function separate(str, _ref3) {\n    var _ref4 = _toArray(_ref3),\n      token = _ref4[0],\n      restTokens = _ref4.slice(1);\n    if (!token) {\n      return [str];\n    }\n    var list = str.split(token);\n    match = match || list.length > 1;\n    return list.reduce(function (prevList, unitStr) {\n      return [].concat(_toConsumableArray(prevList), _toConsumableArray(separate(unitStr, restTokens)));\n    }, []).filter(Boolean);\n  };\n  var list = separate(text, tokens);\n  if (match) {\n    return typeof end !== 'undefined' ? list.slice(0, end) : list;\n  } else {\n    return null;\n  }\n};"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,OAAO,IAAI,EAAE,KAAK;IACzB,IAAI,MAAM,KAAK,GAAG;IAClB,IAAI;IACJ,IAAI,WAAW,MAAM;QACnB,QAAQ,KAAK,KAAK;IACpB;IACA,IAAI,QAAQ,QAAQ,QAAQ,WAAW;QACrC,OAAO;IACT;IACA,IAAI,UAAU,WAAW;QACvB,OAAO;IACT;IACA,OAAO,gBAAgB,MAAM,CAAC;AAChC;AACO,SAAS,aAAa,KAAK;IAChC,OAAO,OAAO,UAAU,eAAe,CAAC,OAAO,KAAK,CAAC;AACvD;AACO,SAAS,eAAe,UAAU,EAAE,cAAc;IACvD,IAAI,OAAO,cAAc,CAAC,GACxB,QAAQ,KAAK,KAAK,EAClB,QAAQ,KAAK,KAAK,EAClB,UAAU,KAAK,OAAO,EACtB,aAAa,KAAK,UAAU;IAC9B,IAAI,cAAc,SAAS,CAAC,iBAAiB,aAAa,OAAO;IACjE,OAAO;QACL,OAAO;QACP,OAAO,SAAS;QAChB,SAAS,WAAW;QACpB,YAAY,cAAc;IAC5B;AACF;AAOO,SAAS,eAAe,OAAO;IACpC,IAAI,QAAQ,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC,GAC/E,aAAa,MAAM,UAAU,EAC7B,iBAAiB,MAAM,cAAc;IACvC,IAAI,cAAc,EAAE;IACpB,IAAI,kBAAkB,eAAe,YAAY,QAC/C,aAAa,gBAAgB,KAAK,EAClC,aAAa,gBAAgB,KAAK,EAClC,eAAe,gBAAgB,OAAO,EACtC,aAAa,gBAAgB,UAAU;IACzC,SAAS,IAAI,IAAI,EAAE,aAAa;QAC9B,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;YACxB;QACF;QACA,KAAK,OAAO,CAAC,SAAU,IAAI;YACzB,IAAI,iBAAiB,CAAC,CAAC,gBAAgB,IAAI,GAAG;gBAC5C,IAAI,QAAQ,IAAI,CAAC,WAAW;gBAE5B,SAAS;gBACT,YAAY,IAAI,CAAC;oBACf,KAAK,OAAO,MAAM,YAAY,MAAM;oBACpC,aAAa;oBACb,MAAM;oBACN,OAAO,IAAI,CAAC,WAAW;oBACvB,OAAO;gBACT;YACF,OAAO;gBACL,IAAI,WAAW,IAAI,CAAC,WAAW;gBAC/B,IAAI,aAAa,aAAa,gBAAgB;oBAC5C,WAAW,KAAK,KAAK;gBACvB;gBAEA,eAAe;gBACf,YAAY,IAAI,CAAC;oBACf,KAAK,OAAO,MAAM,YAAY,MAAM;oBACpC,OAAO;oBACP,MAAM;oBACN,OAAO;gBACT;gBACA,IAAI,IAAI,CAAC,aAAa,EAAE;YAC1B;QACF;IACF;IACA,IAAI,SAAS;IACb,OAAO;AACT;AAKO,SAAS,sBAAsB,MAAM;IAC1C,IAAI,YAAY,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG;IAClC,IAAI,CAAC,CAAC,WAAW,SAAS,GAAG;QAC3B,OAAO,cAAc,CAAC,WAAW,SAAS;YACxC,KAAK,SAAS;gBACZ,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACf,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AACO,IAAI,sBAAsB,SAAS,oBAAoB,IAAI,EAAE,MAAM,EAAE,GAAG;IAC7E,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,EAAE;QAC7B,OAAO;IACT;IACA,IAAI,QAAQ;IACZ,IAAI,WAAW,SAAS,SAAS,GAAG,EAAE,KAAK;QACzC,IAAI,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,QACnB,QAAQ,KAAK,CAAC,EAAE,EAChB,aAAa,MAAM,KAAK,CAAC;QAC3B,IAAI,CAAC,OAAO;YACV,OAAO;gBAAC;aAAI;QACd;QACA,IAAI,OAAO,IAAI,KAAK,CAAC;QACrB,QAAQ,SAAS,KAAK,MAAM,GAAG;QAC/B,OAAO,KAAK,MAAM,CAAC,SAAU,QAAQ,EAAE,OAAO;YAC5C,OAAO,EAAE,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,WAAW,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,SAAS,SAAS;QACtF,GAAG,EAAE,EAAE,MAAM,CAAC;IAChB;IACA,IAAI,OAAO,SAAS,MAAM;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,QAAQ,cAAc,KAAK,KAAK,CAAC,GAAG,OAAO;IAC3D,OAAO;QACL,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/SelectContext.js"], "sourcesContent": ["import * as React from 'react';\n\n// Use any here since we do not get the type during compilation\n\nvar SelectContext = /*#__PURE__*/React.createContext(null);\nexport default SelectContext;"], "names": [], "mappings": ";;;AAAA;;AAEA,+DAA+D;AAE/D,IAAI,gBAAgB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC;uCACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/BaseSelect/Polite.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nexport default function Polite(props) {\n  var visible = props.visible,\n    values = props.values;\n  if (!visible) {\n    return null;\n  }\n\n  // Only cut part of values since it's a screen reader\n  var MAX_COUNT = 50;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    \"aria-live\": \"polite\",\n    style: {\n      width: 0,\n      height: 0,\n      position: 'absolute',\n      overflow: 'hidden',\n      opacity: 0\n    }\n  }, \"\".concat(values.slice(0, MAX_COUNT).map(function (_ref) {\n    var label = _ref.label,\n      value = _ref.value;\n    return ['number', 'string'].includes(_typeof(label)) ? label : value;\n  }).join(', ')), values.length > MAX_COUNT ? ', ...' : null);\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AACe,SAAS,OAAO,KAAK;IAClC,IAAI,UAAU,MAAM,OAAO,EACzB,SAAS,MAAM,MAAM;IACvB,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qDAAqD;IACrD,IAAI,YAAY;IAChB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAC9C,aAAa;QACb,OAAO;YACL,OAAO;YACP,QAAQ;YACR,UAAU;YACV,UAAU;YACV,SAAS;QACX;IACF,GAAG,GAAG,MAAM,CAAC,OAAO,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC,SAAU,IAAI;QACxD,IAAI,QAAQ,KAAK,KAAK,EACpB,QAAQ,KAAK,KAAK;QACpB,OAAO;YAAC;YAAU;SAAS,CAAC,QAAQ,CAAC,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,QAAQ;IACjE,GAAG,IAAI,CAAC,QAAQ,OAAO,MAAM,GAAG,YAAY,UAAU;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1214, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/BaseSelect/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"id\", \"prefixCls\", \"className\", \"showSearch\", \"tagRender\", \"direction\", \"omitDomProps\", \"displayValues\", \"onDisplayValuesChange\", \"emptyOptions\", \"notFoundContent\", \"onClear\", \"mode\", \"disabled\", \"loading\", \"getInputElement\", \"getRawInputElement\", \"open\", \"defaultOpen\", \"onDropdownVisibleChange\", \"activeValue\", \"onActiveValueChange\", \"activeDescendantId\", \"searchValue\", \"autoClearSearchValue\", \"onSearch\", \"onSearchSplit\", \"tokenSeparators\", \"allowClear\", \"prefix\", \"suffixIcon\", \"clearIcon\", \"OptionList\", \"animation\", \"transitionName\", \"dropdownStyle\", \"dropdownClassName\", \"dropdownMatchSelectWidth\", \"dropdownRender\", \"dropdownAlign\", \"placement\", \"builtinPlacements\", \"getPopupContainer\", \"showAction\", \"onFocus\", \"onBlur\", \"onKeyUp\", \"onKeyDown\", \"onMouseDown\"];\nimport classNames from 'classnames';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport isMobile from \"rc-util/es/isMobile\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useAllowClear } from \"../hooks/useAllowClear\";\nimport { BaseSelectContext } from \"../hooks/useBaseProps\";\nimport useDelayReset from \"../hooks/useDelayReset\";\nimport useLock from \"../hooks/useLock\";\nimport useSelectTriggerControl from \"../hooks/useSelectTriggerControl\";\nimport Selector from \"../Selector\";\nimport SelectTrigger from \"../SelectTrigger\";\nimport TransBtn from \"../TransBtn\";\nimport { getSeparatedContent, isValidCount } from \"../utils/valueUtil\";\nimport SelectContext from \"../SelectContext\";\nimport Polite from \"./Polite\";\nvar DEFAULT_OMIT_PROPS = ['value', 'onChange', 'removeIcon', 'placeholder', 'autoFocus', 'maxTagCount', 'maxTagTextLength', 'maxTagPlaceholder', 'choiceTransitionName', 'onInputKeyDown', 'onPopupScroll', 'tabIndex'];\nexport var isMultiple = function isMultiple(mode) {\n  return mode === 'tags' || mode === 'multiple';\n};\nvar BaseSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var _customizeRawInputEle;\n  var id = props.id,\n    prefixCls = props.prefixCls,\n    className = props.className,\n    showSearch = props.showSearch,\n    tagRender = props.tagRender,\n    direction = props.direction,\n    omitDomProps = props.omitDomProps,\n    displayValues = props.displayValues,\n    onDisplayValuesChange = props.onDisplayValuesChange,\n    emptyOptions = props.emptyOptions,\n    _props$notFoundConten = props.notFoundContent,\n    notFoundContent = _props$notFoundConten === void 0 ? 'Not Found' : _props$notFoundConten,\n    onClear = props.onClear,\n    mode = props.mode,\n    disabled = props.disabled,\n    loading = props.loading,\n    getInputElement = props.getInputElement,\n    getRawInputElement = props.getRawInputElement,\n    open = props.open,\n    defaultOpen = props.defaultOpen,\n    onDropdownVisibleChange = props.onDropdownVisibleChange,\n    activeValue = props.activeValue,\n    onActiveValueChange = props.onActiveValueChange,\n    activeDescendantId = props.activeDescendantId,\n    searchValue = props.searchValue,\n    autoClearSearchValue = props.autoClearSearchValue,\n    onSearch = props.onSearch,\n    onSearchSplit = props.onSearchSplit,\n    tokenSeparators = props.tokenSeparators,\n    allowClear = props.allowClear,\n    prefix = props.prefix,\n    suffixIcon = props.suffixIcon,\n    clearIcon = props.clearIcon,\n    OptionList = props.OptionList,\n    animation = props.animation,\n    transitionName = props.transitionName,\n    dropdownStyle = props.dropdownStyle,\n    dropdownClassName = props.dropdownClassName,\n    dropdownMatchSelectWidth = props.dropdownMatchSelectWidth,\n    dropdownRender = props.dropdownRender,\n    dropdownAlign = props.dropdownAlign,\n    placement = props.placement,\n    builtinPlacements = props.builtinPlacements,\n    getPopupContainer = props.getPopupContainer,\n    _props$showAction = props.showAction,\n    showAction = _props$showAction === void 0 ? [] : _props$showAction,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyUp = props.onKeyUp,\n    onKeyDown = props.onKeyDown,\n    onMouseDown = props.onMouseDown,\n    restProps = _objectWithoutProperties(props, _excluded);\n\n  // ============================== MISC ==============================\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = (showSearch !== undefined ? showSearch : multiple) || mode === 'combobox';\n  var domProps = _objectSpread({}, restProps);\n  DEFAULT_OMIT_PROPS.forEach(function (propName) {\n    delete domProps[propName];\n  });\n  omitDomProps === null || omitDomProps === void 0 || omitDomProps.forEach(function (propName) {\n    delete domProps[propName];\n  });\n\n  // ============================= Mobile =============================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    mobile = _React$useState2[0],\n    setMobile = _React$useState2[1];\n  React.useEffect(function () {\n    // Only update on the client side\n    setMobile(isMobile());\n  }, []);\n\n  // ============================== Refs ==============================\n  var containerRef = React.useRef(null);\n  var selectorDomRef = React.useRef(null);\n  var triggerRef = React.useRef(null);\n  var selectorRef = React.useRef(null);\n  var listRef = React.useRef(null);\n  var blurRef = React.useRef(false);\n\n  /** Used for component focused management */\n  var _useDelayReset = useDelayReset(),\n    _useDelayReset2 = _slicedToArray(_useDelayReset, 3),\n    mockFocused = _useDelayReset2[0],\n    setMockFocused = _useDelayReset2[1],\n    cancelSetMockFocused = _useDelayReset2[2];\n\n  // =========================== Imperative ===========================\n  React.useImperativeHandle(ref, function () {\n    var _selectorRef$current, _selectorRef$current2;\n    return {\n      focus: (_selectorRef$current = selectorRef.current) === null || _selectorRef$current === void 0 ? void 0 : _selectorRef$current.focus,\n      blur: (_selectorRef$current2 = selectorRef.current) === null || _selectorRef$current2 === void 0 ? void 0 : _selectorRef$current2.blur,\n      scrollTo: function scrollTo(arg) {\n        var _listRef$current;\n        return (_listRef$current = listRef.current) === null || _listRef$current === void 0 ? void 0 : _listRef$current.scrollTo(arg);\n      },\n      nativeElement: containerRef.current || selectorDomRef.current\n    };\n  });\n\n  // ========================== Search Value ==========================\n  var mergedSearchValue = React.useMemo(function () {\n    var _displayValues$;\n    if (mode !== 'combobox') {\n      return searchValue;\n    }\n    var val = (_displayValues$ = displayValues[0]) === null || _displayValues$ === void 0 ? void 0 : _displayValues$.value;\n    return typeof val === 'string' || typeof val === 'number' ? String(val) : '';\n  }, [searchValue, mode, displayValues]);\n\n  // ========================== Custom Input ==========================\n  // Only works in `combobox`\n  var customizeInputElement = mode === 'combobox' && typeof getInputElement === 'function' && getInputElement() || null;\n\n  // Used for customize replacement for `rc-cascader`\n  var customizeRawInputElement = typeof getRawInputElement === 'function' && getRawInputElement();\n  var customizeRawInputRef = useComposeRef(selectorDomRef, customizeRawInputElement === null || customizeRawInputElement === void 0 || (_customizeRawInputEle = customizeRawInputElement.props) === null || _customizeRawInputEle === void 0 ? void 0 : _customizeRawInputEle.ref);\n\n  // ============================== Open ==============================\n  // SSR not support Portal which means we need delay `open` for the first time render\n  var _React$useState3 = React.useState(false),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    rendered = _React$useState4[0],\n    setRendered = _React$useState4[1];\n  useLayoutEffect(function () {\n    setRendered(true);\n  }, []);\n  var _useMergedState = useMergedState(false, {\n      defaultValue: defaultOpen,\n      value: open\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    innerOpen = _useMergedState2[0],\n    setInnerOpen = _useMergedState2[1];\n  var mergedOpen = rendered ? innerOpen : false;\n\n  // Not trigger `open` in `combobox` when `notFoundContent` is empty\n  var emptyListContent = !notFoundContent && emptyOptions;\n  if (disabled || emptyListContent && mergedOpen && mode === 'combobox') {\n    mergedOpen = false;\n  }\n  var triggerOpen = emptyListContent ? false : mergedOpen;\n  var onToggleOpen = React.useCallback(function (newOpen) {\n    var nextOpen = newOpen !== undefined ? newOpen : !mergedOpen;\n    if (!disabled) {\n      setInnerOpen(nextOpen);\n      if (mergedOpen !== nextOpen) {\n        onDropdownVisibleChange === null || onDropdownVisibleChange === void 0 || onDropdownVisibleChange(nextOpen);\n      }\n    }\n  }, [disabled, mergedOpen, setInnerOpen, onDropdownVisibleChange]);\n\n  // ============================= Search =============================\n  var tokenWithEnter = React.useMemo(function () {\n    return (tokenSeparators || []).some(function (tokenSeparator) {\n      return ['\\n', '\\r\\n'].includes(tokenSeparator);\n    });\n  }, [tokenSeparators]);\n  var _ref = React.useContext(SelectContext) || {},\n    maxCount = _ref.maxCount,\n    rawValues = _ref.rawValues;\n  var onInternalSearch = function onInternalSearch(searchText, fromTyping, isCompositing) {\n    if (multiple && isValidCount(maxCount) && (rawValues === null || rawValues === void 0 ? void 0 : rawValues.size) >= maxCount) {\n      return;\n    }\n    var ret = true;\n    var newSearchText = searchText;\n    onActiveValueChange === null || onActiveValueChange === void 0 || onActiveValueChange(null);\n    var separatedList = getSeparatedContent(searchText, tokenSeparators, isValidCount(maxCount) ? maxCount - rawValues.size : undefined);\n\n    // Check if match the `tokenSeparators`\n    var patchLabels = isCompositing ? null : separatedList;\n\n    // Ignore combobox since it's not split-able\n    if (mode !== 'combobox' && patchLabels) {\n      newSearchText = '';\n      onSearchSplit === null || onSearchSplit === void 0 || onSearchSplit(patchLabels);\n\n      // Should close when paste finish\n      onToggleOpen(false);\n\n      // Tell Selector that break next actions\n      ret = false;\n    }\n    if (onSearch && mergedSearchValue !== newSearchText) {\n      onSearch(newSearchText, {\n        source: fromTyping ? 'typing' : 'effect'\n      });\n    }\n    return ret;\n  };\n\n  // Only triggered when menu is closed & mode is tags\n  // If menu is open, OptionList will take charge\n  // If mode isn't tags, press enter is not meaningful when you can't see any option\n  var onInternalSearchSubmit = function onInternalSearchSubmit(searchText) {\n    // prevent empty tags from appearing when you click the Enter button\n    if (!searchText || !searchText.trim()) {\n      return;\n    }\n    onSearch(searchText, {\n      source: 'submit'\n    });\n  };\n\n  // Close will clean up single mode search text\n  React.useEffect(function () {\n    if (!mergedOpen && !multiple && mode !== 'combobox') {\n      onInternalSearch('', false, false);\n    }\n  }, [mergedOpen]);\n\n  // ============================ Disabled ============================\n  // Close dropdown & remove focus state when disabled change\n  React.useEffect(function () {\n    if (innerOpen && disabled) {\n      setInnerOpen(false);\n    }\n\n    // After onBlur is triggered, the focused does not need to be reset\n    if (disabled && !blurRef.current) {\n      setMockFocused(false);\n    }\n  }, [disabled]);\n\n  // ============================ Keyboard ============================\n  /**\n   * We record input value here to check if can press to clean up by backspace\n   * - null: Key is not down, this is reset by key up\n   * - true: Search text is empty when first time backspace down\n   * - false: Search text is not empty when first time backspace down\n   */\n  var _useLock = useLock(),\n    _useLock2 = _slicedToArray(_useLock, 2),\n    getClearLock = _useLock2[0],\n    setClearLock = _useLock2[1];\n  var keyLockRef = React.useRef(false);\n\n  // KeyDown\n  var onInternalKeyDown = function onInternalKeyDown(event) {\n    var clearLock = getClearLock();\n    var key = event.key;\n    var isEnterKey = key === 'Enter';\n    if (isEnterKey) {\n      // Do not submit form when type in the input\n      if (mode !== 'combobox') {\n        event.preventDefault();\n      }\n\n      // We only manage open state here, close logic should handle by list component\n      if (!mergedOpen) {\n        onToggleOpen(true);\n      }\n    }\n    setClearLock(!!mergedSearchValue);\n\n    // Remove value by `backspace`\n    if (key === 'Backspace' && !clearLock && multiple && !mergedSearchValue && displayValues.length) {\n      var cloneDisplayValues = _toConsumableArray(displayValues);\n      var removedDisplayValue = null;\n      for (var i = cloneDisplayValues.length - 1; i >= 0; i -= 1) {\n        var current = cloneDisplayValues[i];\n        if (!current.disabled) {\n          cloneDisplayValues.splice(i, 1);\n          removedDisplayValue = current;\n          break;\n        }\n      }\n      if (removedDisplayValue) {\n        onDisplayValuesChange(cloneDisplayValues, {\n          type: 'remove',\n          values: [removedDisplayValue]\n        });\n      }\n    }\n    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      rest[_key - 1] = arguments[_key];\n    }\n    if (mergedOpen && (!isEnterKey || !keyLockRef.current)) {\n      var _listRef$current2;\n      // Lock the Enter key after it is pressed to avoid repeated triggering of the onChange event.\n      if (isEnterKey) {\n        keyLockRef.current = true;\n      }\n      (_listRef$current2 = listRef.current) === null || _listRef$current2 === void 0 || _listRef$current2.onKeyDown.apply(_listRef$current2, [event].concat(rest));\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown.apply(void 0, [event].concat(rest));\n  };\n\n  // KeyUp\n  var onInternalKeyUp = function onInternalKeyUp(event) {\n    for (var _len2 = arguments.length, rest = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      rest[_key2 - 1] = arguments[_key2];\n    }\n    if (mergedOpen) {\n      var _listRef$current3;\n      (_listRef$current3 = listRef.current) === null || _listRef$current3 === void 0 || _listRef$current3.onKeyUp.apply(_listRef$current3, [event].concat(rest));\n    }\n    if (event.key === 'Enter') {\n      keyLockRef.current = false;\n    }\n    onKeyUp === null || onKeyUp === void 0 || onKeyUp.apply(void 0, [event].concat(rest));\n  };\n\n  // ============================ Selector ============================\n  var onSelectorRemove = function onSelectorRemove(val) {\n    var newValues = displayValues.filter(function (i) {\n      return i !== val;\n    });\n    onDisplayValuesChange(newValues, {\n      type: 'remove',\n      values: [val]\n    });\n  };\n  var onInputBlur = function onInputBlur() {\n    // Unlock the Enter key after the input blur; otherwise, the Enter key needs to be pressed twice to trigger the correct effect.\n    keyLockRef.current = false;\n  };\n\n  // ========================== Focus / Blur ==========================\n  /** Record real focus status */\n  var focusRef = React.useRef(false);\n  var onContainerFocus = function onContainerFocus() {\n    setMockFocused(true);\n    if (!disabled) {\n      if (onFocus && !focusRef.current) {\n        onFocus.apply(void 0, arguments);\n      }\n\n      // `showAction` should handle `focus` if set\n      if (showAction.includes('focus')) {\n        onToggleOpen(true);\n      }\n    }\n    focusRef.current = true;\n  };\n  var onContainerBlur = function onContainerBlur() {\n    blurRef.current = true;\n    setMockFocused(false, function () {\n      focusRef.current = false;\n      blurRef.current = false;\n      onToggleOpen(false);\n    });\n    if (disabled) {\n      return;\n    }\n    if (mergedSearchValue) {\n      // `tags` mode should move `searchValue` into values\n      if (mode === 'tags') {\n        onSearch(mergedSearchValue, {\n          source: 'submit'\n        });\n      } else if (mode === 'multiple') {\n        // `multiple` mode only clean the search value but not trigger event\n        onSearch('', {\n          source: 'blur'\n        });\n      }\n    }\n    if (onBlur) {\n      onBlur.apply(void 0, arguments);\n    }\n  };\n\n  // Give focus back of Select\n  var activeTimeoutIds = [];\n  React.useEffect(function () {\n    return function () {\n      activeTimeoutIds.forEach(function (timeoutId) {\n        return clearTimeout(timeoutId);\n      });\n      activeTimeoutIds.splice(0, activeTimeoutIds.length);\n    };\n  }, []);\n  var onInternalMouseDown = function onInternalMouseDown(event) {\n    var _triggerRef$current;\n    var target = event.target;\n    var popupElement = (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : _triggerRef$current.getPopupElement();\n\n    // We should give focus back to selector if clicked item is not focusable\n    if (popupElement && popupElement.contains(target)) {\n      var timeoutId = setTimeout(function () {\n        var index = activeTimeoutIds.indexOf(timeoutId);\n        if (index !== -1) {\n          activeTimeoutIds.splice(index, 1);\n        }\n        cancelSetMockFocused();\n        if (!mobile && !popupElement.contains(document.activeElement)) {\n          var _selectorRef$current3;\n          (_selectorRef$current3 = selectorRef.current) === null || _selectorRef$current3 === void 0 || _selectorRef$current3.focus();\n        }\n      });\n      activeTimeoutIds.push(timeoutId);\n    }\n    for (var _len3 = arguments.length, restArgs = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n      restArgs[_key3 - 1] = arguments[_key3];\n    }\n    onMouseDown === null || onMouseDown === void 0 || onMouseDown.apply(void 0, [event].concat(restArgs));\n  };\n\n  // ============================ Dropdown ============================\n  var _React$useState5 = React.useState({}),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    forceUpdate = _React$useState6[1];\n  // We need force update here since popup dom is render async\n  function onPopupMouseEnter() {\n    forceUpdate({});\n  }\n\n  // Used for raw custom input trigger\n  var onTriggerVisibleChange;\n  if (customizeRawInputElement) {\n    onTriggerVisibleChange = function onTriggerVisibleChange(newOpen) {\n      onToggleOpen(newOpen);\n    };\n  }\n\n  // Close when click on non-select element\n  useSelectTriggerControl(function () {\n    var _triggerRef$current2;\n    return [containerRef.current, (_triggerRef$current2 = triggerRef.current) === null || _triggerRef$current2 === void 0 ? void 0 : _triggerRef$current2.getPopupElement()];\n  }, triggerOpen, onToggleOpen, !!customizeRawInputElement);\n\n  // ============================ Context =============================\n  var baseSelectContext = React.useMemo(function () {\n    return _objectSpread(_objectSpread({}, props), {}, {\n      notFoundContent: notFoundContent,\n      open: mergedOpen,\n      triggerOpen: triggerOpen,\n      id: id,\n      showSearch: mergedShowSearch,\n      multiple: multiple,\n      toggleOpen: onToggleOpen\n    });\n  }, [props, notFoundContent, triggerOpen, mergedOpen, id, mergedShowSearch, multiple, onToggleOpen]);\n\n  // ==================================================================\n  // ==                            Render                            ==\n  // ==================================================================\n\n  // ============================= Arrow ==============================\n  var showSuffixIcon = !!suffixIcon || loading;\n  var arrowNode;\n  if (showSuffixIcon) {\n    arrowNode = /*#__PURE__*/React.createElement(TransBtn, {\n      className: classNames(\"\".concat(prefixCls, \"-arrow\"), _defineProperty({}, \"\".concat(prefixCls, \"-arrow-loading\"), loading)),\n      customizeIcon: suffixIcon,\n      customizeIconProps: {\n        loading: loading,\n        searchValue: mergedSearchValue,\n        open: mergedOpen,\n        focused: mockFocused,\n        showSearch: mergedShowSearch\n      }\n    });\n  }\n\n  // ============================= Clear ==============================\n  var onClearMouseDown = function onClearMouseDown() {\n    var _selectorRef$current4;\n    onClear === null || onClear === void 0 || onClear();\n    (_selectorRef$current4 = selectorRef.current) === null || _selectorRef$current4 === void 0 || _selectorRef$current4.focus();\n    onDisplayValuesChange([], {\n      type: 'clear',\n      values: displayValues\n    });\n    onInternalSearch('', false, false);\n  };\n  var _useAllowClear = useAllowClear(prefixCls, onClearMouseDown, displayValues, allowClear, clearIcon, disabled, mergedSearchValue, mode),\n    mergedAllowClear = _useAllowClear.allowClear,\n    clearNode = _useAllowClear.clearIcon;\n\n  // =========================== OptionList ===========================\n  var optionList = /*#__PURE__*/React.createElement(OptionList, {\n    ref: listRef\n  });\n\n  // ============================= Select =============================\n  var mergedClassName = classNames(prefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-focused\"), mockFocused), \"\".concat(prefixCls, \"-multiple\"), multiple), \"\".concat(prefixCls, \"-single\"), !multiple), \"\".concat(prefixCls, \"-allow-clear\"), allowClear), \"\".concat(prefixCls, \"-show-arrow\"), showSuffixIcon), \"\".concat(prefixCls, \"-disabled\"), disabled), \"\".concat(prefixCls, \"-loading\"), loading), \"\".concat(prefixCls, \"-open\"), mergedOpen), \"\".concat(prefixCls, \"-customize-input\"), customizeInputElement), \"\".concat(prefixCls, \"-show-search\"), mergedShowSearch));\n\n  // >>> Selector\n  var selectorNode = /*#__PURE__*/React.createElement(SelectTrigger, {\n    ref: triggerRef,\n    disabled: disabled,\n    prefixCls: prefixCls,\n    visible: triggerOpen,\n    popupElement: optionList,\n    animation: animation,\n    transitionName: transitionName,\n    dropdownStyle: dropdownStyle,\n    dropdownClassName: dropdownClassName,\n    direction: direction,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n    dropdownRender: dropdownRender,\n    dropdownAlign: dropdownAlign,\n    placement: placement,\n    builtinPlacements: builtinPlacements,\n    getPopupContainer: getPopupContainer,\n    empty: emptyOptions,\n    getTriggerDOMNode: function getTriggerDOMNode(node) {\n      return (\n        // TODO: This is workaround and should be removed in `rc-select`\n        // And use new standard `nativeElement` for ref.\n        // But we should update `rc-resize-observer` first.\n        selectorDomRef.current || node\n      );\n    },\n    onPopupVisibleChange: onTriggerVisibleChange,\n    onPopupMouseEnter: onPopupMouseEnter\n  }, customizeRawInputElement ? ( /*#__PURE__*/React.cloneElement(customizeRawInputElement, {\n    ref: customizeRawInputRef\n  })) : /*#__PURE__*/React.createElement(Selector, _extends({}, props, {\n    domRef: selectorDomRef,\n    prefixCls: prefixCls,\n    inputElement: customizeInputElement,\n    ref: selectorRef,\n    id: id,\n    prefix: prefix,\n    showSearch: mergedShowSearch,\n    autoClearSearchValue: autoClearSearchValue,\n    mode: mode,\n    activeDescendantId: activeDescendantId,\n    tagRender: tagRender,\n    values: displayValues,\n    open: mergedOpen,\n    onToggleOpen: onToggleOpen,\n    activeValue: activeValue,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    onSearchSubmit: onInternalSearchSubmit,\n    onRemove: onSelectorRemove,\n    tokenWithEnter: tokenWithEnter,\n    onInputBlur: onInputBlur\n  })));\n\n  // >>> Render\n  var renderNode;\n\n  // Render raw\n  if (customizeRawInputElement) {\n    renderNode = selectorNode;\n  } else {\n    renderNode = /*#__PURE__*/React.createElement(\"div\", _extends({\n      className: mergedClassName\n    }, domProps, {\n      ref: containerRef,\n      onMouseDown: onInternalMouseDown,\n      onKeyDown: onInternalKeyDown,\n      onKeyUp: onInternalKeyUp,\n      onFocus: onContainerFocus,\n      onBlur: onContainerBlur\n    }), /*#__PURE__*/React.createElement(Polite, {\n      visible: mockFocused && !mergedOpen,\n      values: displayValues\n    }), selectorNode, arrowNode, mergedAllowClear && clearNode);\n  }\n  return /*#__PURE__*/React.createElement(BaseSelectContext.Provider, {\n    value: baseSelectContext\n  }, renderNode);\n});\n\n// Set display name for dev\nif (process.env.NODE_ENV !== 'production') {\n  BaseSelect.displayName = 'BaseSelect';\n}\nexport default BaseSelect;"], "names": [], "mappings": ";;;;AAmlBI;AAnlBJ;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAjBA,IAAI,YAAY;IAAC;IAAM;IAAa;IAAa;IAAc;IAAa;IAAa;IAAgB;IAAiB;IAAyB;IAAgB;IAAmB;IAAW;IAAQ;IAAY;IAAW;IAAmB;IAAsB;IAAQ;IAAe;IAA2B;IAAe;IAAuB;IAAsB;IAAe;IAAwB;IAAY;IAAiB;IAAmB;IAAc;IAAU;IAAc;IAAa;IAAc;IAAa;IAAkB;IAAiB;IAAqB;IAA4B;IAAkB;IAAiB;IAAa;IAAqB;IAAqB;IAAc;IAAW;IAAU;IAAW;IAAa;CAAc;;;;;;;;;;;;;;;;;;AAkBnxB,IAAI,qBAAqB;IAAC;IAAS;IAAY;IAAc;IAAe;IAAa;IAAe;IAAoB;IAAqB;IAAwB;IAAkB;IAAiB;CAAW;AAChN,IAAI,aAAa,SAAS,WAAW,IAAI;IAC9C,OAAO,SAAS,UAAU,SAAS;AACrC;AACA,IAAI,aAAa,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IACjE,IAAI;IACJ,IAAI,KAAK,MAAM,EAAE,EACf,YAAY,MAAM,SAAS,EAC3B,YAAY,MAAM,SAAS,EAC3B,aAAa,MAAM,UAAU,EAC7B,YAAY,MAAM,SAAS,EAC3B,YAAY,MAAM,SAAS,EAC3B,eAAe,MAAM,YAAY,EACjC,gBAAgB,MAAM,aAAa,EACnC,wBAAwB,MAAM,qBAAqB,EACnD,eAAe,MAAM,YAAY,EACjC,wBAAwB,MAAM,eAAe,EAC7C,kBAAkB,0BAA0B,KAAK,IAAI,cAAc,uBACnE,UAAU,MAAM,OAAO,EACvB,OAAO,MAAM,IAAI,EACjB,WAAW,MAAM,QAAQ,EACzB,UAAU,MAAM,OAAO,EACvB,kBAAkB,MAAM,eAAe,EACvC,qBAAqB,MAAM,kBAAkB,EAC7C,OAAO,MAAM,IAAI,EACjB,cAAc,MAAM,WAAW,EAC/B,0BAA0B,MAAM,uBAAuB,EACvD,cAAc,MAAM,WAAW,EAC/B,sBAAsB,MAAM,mBAAmB,EAC/C,qBAAqB,MAAM,kBAAkB,EAC7C,cAAc,MAAM,WAAW,EAC/B,uBAAuB,MAAM,oBAAoB,EACjD,WAAW,MAAM,QAAQ,EACzB,gBAAgB,MAAM,aAAa,EACnC,kBAAkB,MAAM,eAAe,EACvC,aAAa,MAAM,UAAU,EAC7B,SAAS,MAAM,MAAM,EACrB,aAAa,MAAM,UAAU,EAC7B,YAAY,MAAM,SAAS,EAC3B,aAAa,MAAM,UAAU,EAC7B,YAAY,MAAM,SAAS,EAC3B,iBAAiB,MAAM,cAAc,EACrC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,MAAM,iBAAiB,EAC3C,2BAA2B,MAAM,wBAAwB,EACzD,iBAAiB,MAAM,cAAc,EACrC,gBAAgB,MAAM,aAAa,EACnC,YAAY,MAAM,SAAS,EAC3B,oBAAoB,MAAM,iBAAiB,EAC3C,oBAAoB,MAAM,iBAAiB,EAC3C,oBAAoB,MAAM,UAAU,EACpC,aAAa,sBAAsB,KAAK,IAAI,EAAE,GAAG,mBACjD,UAAU,MAAM,OAAO,EACvB,SAAS,MAAM,MAAM,EACrB,UAAU,MAAM,OAAO,EACvB,YAAY,MAAM,SAAS,EAC3B,cAAc,MAAM,WAAW,EAC/B,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,OAAO;IAE9C,qEAAqE;IACrE,IAAI,WAAW,WAAW;IAC1B,IAAI,mBAAmB,CAAC,eAAe,YAAY,aAAa,QAAQ,KAAK,SAAS;IACtF,IAAI,WAAW,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG;IACjC,mBAAmB,OAAO,CAAC,SAAU,QAAQ;QAC3C,OAAO,QAAQ,CAAC,SAAS;IAC3B;IACA,iBAAiB,QAAQ,iBAAiB,KAAK,KAAK,aAAa,OAAO,CAAC,SAAU,QAAQ;QACzF,OAAO,QAAQ,CAAC,SAAS;IAC3B;IAEA,qEAAqE;IACrE,IAAI,kBAAkB,6JAAA,CAAA,WAAc,CAAC,QACnC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,SAAS,gBAAgB,CAAC,EAAE,EAC5B,YAAY,gBAAgB,CAAC,EAAE;IACjC,6JAAA,CAAA,YAAe;gCAAC;YACd,iCAAiC;YACjC,UAAU,CAAA,GAAA,+IAAA,CAAA,UAAQ,AAAD;QACnB;+BAAG,EAAE;IAEL,qEAAqE;IACrE,IAAI,eAAe,6JAAA,CAAA,SAAY,CAAC;IAChC,IAAI,iBAAiB,6JAAA,CAAA,SAAY,CAAC;IAClC,IAAI,aAAa,6JAAA,CAAA,SAAY,CAAC;IAC9B,IAAI,cAAc,6JAAA,CAAA,SAAY,CAAC;IAC/B,IAAI,UAAU,6JAAA,CAAA,SAAY,CAAC;IAC3B,IAAI,UAAU,6JAAA,CAAA,SAAY,CAAC;IAE3B,0CAA0C,GAC1C,IAAI,iBAAiB,CAAA,GAAA,+JAAA,CAAA,UAAa,AAAD,KAC/B,kBAAkB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,gBAAgB,IACjD,cAAc,eAAe,CAAC,EAAE,EAChC,iBAAiB,eAAe,CAAC,EAAE,EACnC,uBAAuB,eAAe,CAAC,EAAE;IAE3C,qEAAqE;IACrE,6JAAA,CAAA,sBAAyB,CAAC;0CAAK;YAC7B,IAAI,sBAAsB;YAC1B,OAAO;gBACL,OAAO,CAAC,uBAAuB,YAAY,OAAO,MAAM,QAAQ,yBAAyB,KAAK,IAAI,KAAK,IAAI,qBAAqB,KAAK;gBACrI,MAAM,CAAC,wBAAwB,YAAY,OAAO,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,IAAI;gBACtI,UAAU,SAAS,SAAS,GAAG;oBAC7B,IAAI;oBACJ,OAAO,CAAC,mBAAmB,QAAQ,OAAO,MAAM,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,iBAAiB,QAAQ,CAAC;gBAC3H;gBACA,eAAe,aAAa,OAAO,IAAI,eAAe,OAAO;YAC/D;QACF;;IAEA,qEAAqE;IACrE,IAAI,oBAAoB,6JAAA,CAAA,UAAa;iDAAC;YACpC,IAAI;YACJ,IAAI,SAAS,YAAY;gBACvB,OAAO;YACT;YACA,IAAI,MAAM,CAAC,kBAAkB,aAAa,CAAC,EAAE,MAAM,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB,KAAK;YACtH,OAAO,OAAO,QAAQ,YAAY,OAAO,QAAQ,WAAW,OAAO,OAAO;QAC5E;gDAAG;QAAC;QAAa;QAAM;KAAc;IAErC,qEAAqE;IACrE,2BAA2B;IAC3B,IAAI,wBAAwB,SAAS,cAAc,OAAO,oBAAoB,cAAc,qBAAqB;IAEjH,mDAAmD;IACnD,IAAI,2BAA2B,OAAO,uBAAuB,cAAc;IAC3E,IAAI,uBAAuB,CAAA,GAAA,0IAAA,CAAA,gBAAa,AAAD,EAAE,gBAAgB,6BAA6B,QAAQ,6BAA6B,KAAK,KAAK,CAAC,wBAAwB,yBAAyB,KAAK,MAAM,QAAQ,0BAA0B,KAAK,IAAI,KAAK,IAAI,sBAAsB,GAAG;IAE/Q,qEAAqE;IACrE,oFAAoF;IACpF,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,QACpC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,WAAW,gBAAgB,CAAC,EAAE,EAC9B,cAAc,gBAAgB,CAAC,EAAE;IACnC,CAAA,GAAA,+JAAA,CAAA,UAAe,AAAD;sCAAE;YACd,YAAY;QACd;qCAAG,EAAE;IACL,IAAI,kBAAkB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD,EAAE,OAAO;QACxC,cAAc;QACd,OAAO;IACT,IACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,YAAY,gBAAgB,CAAC,EAAE,EAC/B,eAAe,gBAAgB,CAAC,EAAE;IACpC,IAAI,aAAa,WAAW,YAAY;IAExC,mEAAmE;IACnE,IAAI,mBAAmB,CAAC,mBAAmB;IAC3C,IAAI,YAAY,oBAAoB,cAAc,SAAS,YAAY;QACrE,aAAa;IACf;IACA,IAAI,cAAc,mBAAmB,QAAQ;IAC7C,IAAI,eAAe,6JAAA,CAAA,cAAiB;gDAAC,SAAU,OAAO;YACpD,IAAI,WAAW,YAAY,YAAY,UAAU,CAAC;YAClD,IAAI,CAAC,UAAU;gBACb,aAAa;gBACb,IAAI,eAAe,UAAU;oBAC3B,4BAA4B,QAAQ,4BAA4B,KAAK,KAAK,wBAAwB;gBACpG;YACF;QACF;+CAAG;QAAC;QAAU;QAAY;QAAc;KAAwB;IAEhE,qEAAqE;IACrE,IAAI,iBAAiB,6JAAA,CAAA,UAAa;8CAAC;YACjC,OAAO,CAAC,mBAAmB,EAAE,EAAE,IAAI;sDAAC,SAAU,cAAc;oBAC1D,OAAO;wBAAC;wBAAM;qBAAO,CAAC,QAAQ,CAAC;gBACjC;;QACF;6CAAG;QAAC;KAAgB;IACpB,IAAI,OAAO,6JAAA,CAAA,aAAgB,CAAC,sJAAA,CAAA,UAAa,KAAK,CAAC,GAC7C,WAAW,KAAK,QAAQ,EACxB,YAAY,KAAK,SAAS;IAC5B,IAAI,mBAAmB,SAAS,iBAAiB,UAAU,EAAE,UAAU,EAAE,aAAa;QACpF,IAAI,YAAY,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE,aAAa,CAAC,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,IAAI,KAAK,UAAU;YAC5H;QACF;QACA,IAAI,MAAM;QACV,IAAI,gBAAgB;QACpB,wBAAwB,QAAQ,wBAAwB,KAAK,KAAK,oBAAoB;QACtF,IAAI,gBAAgB,CAAA,GAAA,2JAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,iBAAiB,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE,YAAY,WAAW,UAAU,IAAI,GAAG;QAE1H,uCAAuC;QACvC,IAAI,cAAc,gBAAgB,OAAO;QAEzC,4CAA4C;QAC5C,IAAI,SAAS,cAAc,aAAa;YACtC,gBAAgB;YAChB,kBAAkB,QAAQ,kBAAkB,KAAK,KAAK,cAAc;YAEpE,iCAAiC;YACjC,aAAa;YAEb,wCAAwC;YACxC,MAAM;QACR;QACA,IAAI,YAAY,sBAAsB,eAAe;YACnD,SAAS,eAAe;gBACtB,QAAQ,aAAa,WAAW;YAClC;QACF;QACA,OAAO;IACT;IAEA,oDAAoD;IACpD,+CAA+C;IAC/C,kFAAkF;IAClF,IAAI,yBAAyB,SAAS,uBAAuB,UAAU;QACrE,oEAAoE;QACpE,IAAI,CAAC,cAAc,CAAC,WAAW,IAAI,IAAI;YACrC;QACF;QACA,SAAS,YAAY;YACnB,QAAQ;QACV;IACF;IAEA,8CAA8C;IAC9C,6JAAA,CAAA,YAAe;gCAAC;YACd,IAAI,CAAC,cAAc,CAAC,YAAY,SAAS,YAAY;gBACnD,iBAAiB,IAAI,OAAO;YAC9B;QACF;+BAAG;QAAC;KAAW;IAEf,qEAAqE;IACrE,2DAA2D;IAC3D,6JAAA,CAAA,YAAe;gCAAC;YACd,IAAI,aAAa,UAAU;gBACzB,aAAa;YACf;YAEA,mEAAmE;YACnE,IAAI,YAAY,CAAC,QAAQ,OAAO,EAAE;gBAChC,eAAe;YACjB;QACF;+BAAG;QAAC;KAAS;IAEb,qEAAqE;IACrE;;;;;GAKC,GACD,IAAI,WAAW,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,KACnB,YAAY,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,UAAU,IACrC,eAAe,SAAS,CAAC,EAAE,EAC3B,eAAe,SAAS,CAAC,EAAE;IAC7B,IAAI,aAAa,6JAAA,CAAA,SAAY,CAAC;IAE9B,UAAU;IACV,IAAI,oBAAoB,SAAS,kBAAkB,KAAK;QACtD,IAAI,YAAY;QAChB,IAAI,MAAM,MAAM,GAAG;QACnB,IAAI,aAAa,QAAQ;QACzB,IAAI,YAAY;YACd,4CAA4C;YAC5C,IAAI,SAAS,YAAY;gBACvB,MAAM,cAAc;YACtB;YAEA,8EAA8E;YAC9E,IAAI,CAAC,YAAY;gBACf,aAAa;YACf;QACF;QACA,aAAa,CAAC,CAAC;QAEf,8BAA8B;QAC9B,IAAI,QAAQ,eAAe,CAAC,aAAa,YAAY,CAAC,qBAAqB,cAAc,MAAM,EAAE;YAC/F,IAAI,qBAAqB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;YAC5C,IAAI,sBAAsB;YAC1B,IAAK,IAAI,IAAI,mBAAmB,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,EAAG;gBAC1D,IAAI,UAAU,kBAAkB,CAAC,EAAE;gBACnC,IAAI,CAAC,QAAQ,QAAQ,EAAE;oBACrB,mBAAmB,MAAM,CAAC,GAAG;oBAC7B,sBAAsB;oBACtB;gBACF;YACF;YACA,IAAI,qBAAqB;gBACvB,sBAAsB,oBAAoB;oBACxC,MAAM;oBACN,QAAQ;wBAAC;qBAAoB;gBAC/B;YACF;QACF;QACA,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,MAAM,OAAQ;YAC1G,IAAI,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,KAAK;QAClC;QACA,IAAI,cAAc,CAAC,CAAC,cAAc,CAAC,WAAW,OAAO,GAAG;YACtD,IAAI;YACJ,6FAA6F;YAC7F,IAAI,YAAY;gBACd,WAAW,OAAO,GAAG;YACvB;YACA,CAAC,oBAAoB,QAAQ,OAAO,MAAM,QAAQ,sBAAsB,KAAK,KAAK,kBAAkB,SAAS,CAAC,KAAK,CAAC,mBAAmB;gBAAC;aAAM,CAAC,MAAM,CAAC;QACxJ;QACA,cAAc,QAAQ,cAAc,KAAK,KAAK,UAAU,KAAK,CAAC,KAAK,GAAG;YAAC;SAAM,CAAC,MAAM,CAAC;IACvF;IAEA,QAAQ;IACR,IAAI,kBAAkB,SAAS,gBAAgB,KAAK;QAClD,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI,QAAQ,GAAG,QAAQ,OAAO,QAAS;YACjH,IAAI,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC,MAAM;QACpC;QACA,IAAI,YAAY;YACd,IAAI;YACJ,CAAC,oBAAoB,QAAQ,OAAO,MAAM,QAAQ,sBAAsB,KAAK,KAAK,kBAAkB,OAAO,CAAC,KAAK,CAAC,mBAAmB;gBAAC;aAAM,CAAC,MAAM,CAAC;QACtJ;QACA,IAAI,MAAM,GAAG,KAAK,SAAS;YACzB,WAAW,OAAO,GAAG;QACvB;QACA,YAAY,QAAQ,YAAY,KAAK,KAAK,QAAQ,KAAK,CAAC,KAAK,GAAG;YAAC;SAAM,CAAC,MAAM,CAAC;IACjF;IAEA,qEAAqE;IACrE,IAAI,mBAAmB,SAAS,iBAAiB,GAAG;QAClD,IAAI,YAAY,cAAc,MAAM,CAAC,SAAU,CAAC;YAC9C,OAAO,MAAM;QACf;QACA,sBAAsB,WAAW;YAC/B,MAAM;YACN,QAAQ;gBAAC;aAAI;QACf;IACF;IACA,IAAI,cAAc,SAAS;QACzB,+HAA+H;QAC/H,WAAW,OAAO,GAAG;IACvB;IAEA,qEAAqE;IACrE,6BAA6B,GAC7B,IAAI,WAAW,6JAAA,CAAA,SAAY,CAAC;IAC5B,IAAI,mBAAmB,SAAS;QAC9B,eAAe;QACf,IAAI,CAAC,UAAU;YACb,IAAI,WAAW,CAAC,SAAS,OAAO,EAAE;gBAChC,QAAQ,KAAK,CAAC,KAAK,GAAG;YACxB;YAEA,4CAA4C;YAC5C,IAAI,WAAW,QAAQ,CAAC,UAAU;gBAChC,aAAa;YACf;QACF;QACA,SAAS,OAAO,GAAG;IACrB;IACA,IAAI,kBAAkB,SAAS;QAC7B,QAAQ,OAAO,GAAG;QAClB,eAAe,OAAO;YACpB,SAAS,OAAO,GAAG;YACnB,QAAQ,OAAO,GAAG;YAClB,aAAa;QACf;QACA,IAAI,UAAU;YACZ;QACF;QACA,IAAI,mBAAmB;YACrB,oDAAoD;YACpD,IAAI,SAAS,QAAQ;gBACnB,SAAS,mBAAmB;oBAC1B,QAAQ;gBACV;YACF,OAAO,IAAI,SAAS,YAAY;gBAC9B,oEAAoE;gBACpE,SAAS,IAAI;oBACX,QAAQ;gBACV;YACF;QACF;QACA,IAAI,QAAQ;YACV,OAAO,KAAK,CAAC,KAAK,GAAG;QACvB;IACF;IAEA,4BAA4B;IAC5B,IAAI,mBAAmB,EAAE;IACzB,6JAAA,CAAA,YAAe;gCAAC;YACd;wCAAO;oBACL,iBAAiB,OAAO;gDAAC,SAAU,SAAS;4BAC1C,OAAO,aAAa;wBACtB;;oBACA,iBAAiB,MAAM,CAAC,GAAG,iBAAiB,MAAM;gBACpD;;QACF;+BAAG,EAAE;IACL,IAAI,sBAAsB,SAAS,oBAAoB,KAAK;QAC1D,IAAI;QACJ,IAAI,SAAS,MAAM,MAAM;QACzB,IAAI,eAAe,CAAC,sBAAsB,WAAW,OAAO,MAAM,QAAQ,wBAAwB,KAAK,IAAI,KAAK,IAAI,oBAAoB,eAAe;QAEvJ,yEAAyE;QACzE,IAAI,gBAAgB,aAAa,QAAQ,CAAC,SAAS;YACjD,IAAI,YAAY,WAAW;gBACzB,IAAI,QAAQ,iBAAiB,OAAO,CAAC;gBACrC,IAAI,UAAU,CAAC,GAAG;oBAChB,iBAAiB,MAAM,CAAC,OAAO;gBACjC;gBACA;gBACA,IAAI,CAAC,UAAU,CAAC,aAAa,QAAQ,CAAC,SAAS,aAAa,GAAG;oBAC7D,IAAI;oBACJ,CAAC,wBAAwB,YAAY,OAAO,MAAM,QAAQ,0BAA0B,KAAK,KAAK,sBAAsB,KAAK;gBAC3H;YACF;YACA,iBAAiB,IAAI,CAAC;QACxB;QACA,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,WAAW,IAAI,MAAM,QAAQ,IAAI,QAAQ,IAAI,IAAI,QAAQ,GAAG,QAAQ,OAAO,QAAS;YACrH,QAAQ,CAAC,QAAQ,EAAE,GAAG,SAAS,CAAC,MAAM;QACxC;QACA,gBAAgB,QAAQ,gBAAgB,KAAK,KAAK,YAAY,KAAK,CAAC,KAAK,GAAG;YAAC;SAAM,CAAC,MAAM,CAAC;IAC7F;IAEA,qEAAqE;IACrE,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,CAAC,IACrC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,cAAc,gBAAgB,CAAC,EAAE;IACnC,4DAA4D;IAC5D,SAAS;QACP,YAAY,CAAC;IACf;IAEA,oCAAoC;IACpC,IAAI;IACJ,IAAI,0BAA0B;QAC5B,yBAAyB,SAAS,uBAAuB,OAAO;YAC9D,aAAa;QACf;IACF;IAEA,yCAAyC;IACzC,CAAA,GAAA,yKAAA,CAAA,UAAuB,AAAD;8CAAE;YACtB,IAAI;YACJ,OAAO;gBAAC,aAAa,OAAO;gBAAE,CAAC,uBAAuB,WAAW,OAAO,MAAM,QAAQ,yBAAyB,KAAK,IAAI,KAAK,IAAI,qBAAqB,eAAe;aAAG;QAC1K;6CAAG,aAAa,cAAc,CAAC,CAAC;IAEhC,qEAAqE;IACrE,IAAI,oBAAoB,6JAAA,CAAA,UAAa;iDAAC;YACpC,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,QAAQ,CAAC,GAAG;gBACjD,iBAAiB;gBACjB,MAAM;gBACN,aAAa;gBACb,IAAI;gBACJ,YAAY;gBACZ,UAAU;gBACV,YAAY;YACd;QACF;gDAAG;QAAC;QAAO;QAAiB;QAAa;QAAY;QAAI;QAAkB;QAAU;KAAa;IAElG,qEAAqE;IACrE,qEAAqE;IACrE,qEAAqE;IAErE,qEAAqE;IACrE,IAAI,iBAAiB,CAAC,CAAC,cAAc;IACrC,IAAI;IACJ,IAAI,gBAAgB;QAClB,YAAY,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,iJAAA,CAAA,UAAQ,EAAE;YACrD,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,MAAM,CAAC,WAAW,WAAW,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,mBAAmB;YAClH,eAAe;YACf,oBAAoB;gBAClB,SAAS;gBACT,aAAa;gBACb,MAAM;gBACN,SAAS;gBACT,YAAY;YACd;QACF;IACF;IAEA,qEAAqE;IACrE,IAAI,mBAAmB,SAAS;QAC9B,IAAI;QACJ,YAAY,QAAQ,YAAY,KAAK,KAAK;QAC1C,CAAC,wBAAwB,YAAY,OAAO,MAAM,QAAQ,0BAA0B,KAAK,KAAK,sBAAsB,KAAK;QACzH,sBAAsB,EAAE,EAAE;YACxB,MAAM;YACN,QAAQ;QACV;QACA,iBAAiB,IAAI,OAAO;IAC9B;IACA,IAAI,iBAAiB,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,kBAAkB,eAAe,YAAY,WAAW,UAAU,mBAAmB,OACjI,mBAAmB,eAAe,UAAU,EAC5C,YAAY,eAAe,SAAS;IAEtC,qEAAqE;IACrE,IAAI,aAAa,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,YAAY;QAC5D,KAAK;IACP;IAEA,qEAAqE;IACrE,IAAI,kBAAkB,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,WAAW,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,WAAW,aAAa,cAAc,GAAG,MAAM,CAAC,WAAW,cAAc,WAAW,GAAG,MAAM,CAAC,WAAW,YAAY,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,iBAAiB,aAAa,GAAG,MAAM,CAAC,WAAW,gBAAgB,iBAAiB,GAAG,MAAM,CAAC,WAAW,cAAc,WAAW,GAAG,MAAM,CAAC,WAAW,aAAa,UAAU,GAAG,MAAM,CAAC,WAAW,UAAU,aAAa,GAAG,MAAM,CAAC,WAAW,qBAAqB,wBAAwB,GAAG,MAAM,CAAC,WAAW,iBAAiB;IAE7rB,eAAe;IACf,IAAI,eAAe,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,sJAAA,CAAA,UAAa,EAAE;QACjE,KAAK;QACL,UAAU;QACV,WAAW;QACX,SAAS;QACT,cAAc;QACd,WAAW;QACX,gBAAgB;QAChB,eAAe;QACf,mBAAmB;QACnB,WAAW;QACX,0BAA0B;QAC1B,gBAAgB;QAChB,eAAe;QACf,WAAW;QACX,mBAAmB;QACnB,mBAAmB;QACnB,OAAO;QACP,mBAAmB,SAAS,kBAAkB,IAAI;YAChD,OACE,gEAAgE;YAChE,gDAAgD;YAChD,mDAAmD;YACnD,eAAe,OAAO,IAAI;QAE9B;QACA,sBAAsB;QACtB,mBAAmB;IACrB,GAAG,2BAA6B,WAAW,GAAE,6JAAA,CAAA,eAAkB,CAAC,0BAA0B;QACxF,KAAK;IACP,KAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,0JAAA,CAAA,UAAQ,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACnE,QAAQ;QACR,WAAW;QACX,cAAc;QACd,KAAK;QACL,IAAI;QACJ,QAAQ;QACR,YAAY;QACZ,sBAAsB;QACtB,MAAM;QACN,oBAAoB;QACpB,WAAW;QACX,QAAQ;QACR,MAAM;QACN,cAAc;QACd,aAAa;QACb,aAAa;QACb,UAAU;QACV,gBAAgB;QAChB,UAAU;QACV,gBAAgB;QAChB,aAAa;IACf;IAEA,aAAa;IACb,IAAI;IAEJ,aAAa;IACb,IAAI,0BAA0B;QAC5B,aAAa;IACf,OAAO;QACL,aAAa,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;YAC5D,WAAW;QACb,GAAG,UAAU;YACX,KAAK;YACL,aAAa;YACb,WAAW;YACX,SAAS;YACT,SAAS;YACT,QAAQ;QACV,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,UAAM,EAAE;YAC3C,SAAS,eAAe,CAAC;YACzB,QAAQ;QACV,IAAI,cAAc,WAAW,oBAAoB;IACnD;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,8JAAA,CAAA,oBAAiB,CAAC,QAAQ,EAAE;QAClE,OAAO;IACT,GAAG;AACL;AAEA,2BAA2B;AAC3B,wCAA2C;IACzC,WAAW,WAAW,GAAG;AAC3B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1860, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/OptGroup.js"], "sourcesContent": ["/* istanbul ignore file */\n\n/** This is a placeholder, not real render in dom */\nvar OptGroup = function OptGroup() {\n  return null;\n};\nOptGroup.isSelectOptGroup = true;\nexport default OptGroup;"], "names": [], "mappings": "AAAA,wBAAwB,GAExB,kDAAkD;;;AAClD,IAAI,WAAW,SAAS;IACtB,OAAO;AACT;AACA,SAAS,gBAAgB,GAAG;uCACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1872, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/Option.js"], "sourcesContent": ["/* istanbul ignore file */\n\n/** This is a placeholder, not real render in dom */\nvar Option = function Option() {\n  return null;\n};\nOption.isSelectOption = true;\nexport default Option;"], "names": [], "mappings": "AAAA,wBAAwB,GAExB,kDAAkD;;;AAClD,IAAI,SAAS,SAAS;IACpB,OAAO;AACT;AACA,OAAO,cAAc,GAAG;uCACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1884, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/utils/platformUtil.js"], "sourcesContent": ["/* istanbul ignore file */\nexport function isPlatformMac() {\n  return /(mac\\sos|macintosh)/i.test(navigator.appVersion);\n}"], "names": [], "mappings": "AAAA,wBAAwB;;;AACjB,SAAS;IACd,OAAO,uBAAuB,IAAI,CAAC,UAAU,UAAU;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1894, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/OptionList.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar _excluded = [\"disabled\", \"title\", \"children\", \"style\", \"className\"];\nimport classNames from 'classnames';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport omit from \"rc-util/es/omit\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport List from 'rc-virtual-list';\nimport * as React from 'react';\nimport { useEffect } from 'react';\nimport SelectContext from \"./SelectContext\";\nimport TransBtn from \"./TransBtn\";\nimport useBaseProps from \"./hooks/useBaseProps\";\nimport { isPlatformMac } from \"./utils/platformUtil\";\nimport { isValidCount } from \"./utils/valueUtil\";\n\n// export interface OptionListProps<OptionsType extends object[]> {\n\nfunction isTitleType(content) {\n  return typeof content === 'string' || typeof content === 'number';\n}\n\n/**\n * Using virtual list of option display.\n * Will fallback to dom if use customize render.\n */\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    id = _useBaseProps.id,\n    open = _useBaseProps.open,\n    multiple = _useBaseProps.multiple,\n    mode = _useBaseProps.mode,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    notFoundContent = _useBaseProps.notFoundContent,\n    onPopupScroll = _useBaseProps.onPopupScroll;\n  var _React$useContext = React.useContext(SelectContext),\n    maxCount = _React$useContext.maxCount,\n    flattenOptions = _React$useContext.flattenOptions,\n    onActiveValue = _React$useContext.onActiveValue,\n    defaultActiveFirstOption = _React$useContext.defaultActiveFirstOption,\n    onSelect = _React$useContext.onSelect,\n    menuItemSelectedIcon = _React$useContext.menuItemSelectedIcon,\n    rawValues = _React$useContext.rawValues,\n    fieldNames = _React$useContext.fieldNames,\n    virtual = _React$useContext.virtual,\n    direction = _React$useContext.direction,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight,\n    optionRender = _React$useContext.optionRender;\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n  var memoFlattenOptions = useMemo(function () {\n    return flattenOptions;\n  }, [open, flattenOptions], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // =========================== List ===========================\n  var listRef = React.useRef(null);\n  var overMaxCount = React.useMemo(function () {\n    return multiple && isValidCount(maxCount) && (rawValues === null || rawValues === void 0 ? void 0 : rawValues.size) >= maxCount;\n  }, [multiple, maxCount, rawValues === null || rawValues === void 0 ? void 0 : rawValues.size]);\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var scrollIntoView = function scrollIntoView(args) {\n    var _listRef$current;\n    (_listRef$current = listRef.current) === null || _listRef$current === void 0 || _listRef$current.scrollTo(typeof args === 'number' ? {\n      index: args\n    } : args);\n  };\n\n  // https://github.com/ant-design/ant-design/issues/34975\n  var isSelected = React.useCallback(function (value) {\n    if (mode === 'combobox') {\n      return false;\n    }\n    return rawValues.has(value);\n  }, [mode, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // ========================== Active ==========================\n  var getEnabledActiveIndex = function getEnabledActiveIndex(index) {\n    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n    var len = memoFlattenOptions.length;\n    for (var i = 0; i < len; i += 1) {\n      var current = (index + i * offset + len) % len;\n      var _ref = memoFlattenOptions[current] || {},\n        group = _ref.group,\n        data = _ref.data;\n      if (!group && !(data !== null && data !== void 0 && data.disabled) && (isSelected(data.value) || !overMaxCount)) {\n        return current;\n      }\n    }\n    return -1;\n  };\n  var _React$useState = React.useState(function () {\n      return getEnabledActiveIndex(0);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeIndex = _React$useState2[0],\n    setActiveIndex = _React$useState2[1];\n  var setActive = function setActive(index) {\n    var fromKeyboard = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    setActiveIndex(index);\n    var info = {\n      source: fromKeyboard ? 'keyboard' : 'mouse'\n    };\n\n    // Trigger active event\n    var flattenItem = memoFlattenOptions[index];\n    if (!flattenItem) {\n      onActiveValue(null, -1, info);\n      return;\n    }\n    onActiveValue(flattenItem.value, index, info);\n  };\n\n  // Auto active first item when list length or searchValue changed\n  useEffect(function () {\n    setActive(defaultActiveFirstOption !== false ? getEnabledActiveIndex(0) : -1);\n  }, [memoFlattenOptions.length, searchValue]);\n\n  // https://github.com/ant-design/ant-design/issues/48036\n  var isAriaSelected = React.useCallback(function (value) {\n    if (mode === 'combobox') {\n      return String(value).toLowerCase() === searchValue.toLowerCase();\n    }\n    return rawValues.has(value);\n  }, [mode, searchValue, _toConsumableArray(rawValues).toString(), rawValues.size]);\n\n  // Auto scroll to item position in single mode\n  useEffect(function () {\n    /**\n     * React will skip `onChange` when component update.\n     * `setActive` function will call root accessibility state update which makes re-render.\n     * So we need to delay to let Input component trigger onChange first.\n     */\n    var timeoutId = setTimeout(function () {\n      if (!multiple && open && rawValues.size === 1) {\n        var value = Array.from(rawValues)[0];\n        // Scroll to the option closest to the searchValue if searching.\n        var index = memoFlattenOptions.findIndex(function (_ref2) {\n          var data = _ref2.data;\n          return searchValue ? String(data.value).startsWith(searchValue) : data.value === value;\n        });\n        if (index !== -1) {\n          setActive(index);\n          scrollIntoView(index);\n        }\n      }\n    });\n\n    // Force trigger scrollbar visible when open\n    if (open) {\n      var _listRef$current2;\n      (_listRef$current2 = listRef.current) === null || _listRef$current2 === void 0 || _listRef$current2.scrollTo(undefined);\n    }\n    return function () {\n      return clearTimeout(timeoutId);\n    };\n  }, [open, searchValue]);\n\n  // ========================== Values ==========================\n  var onSelectValue = function onSelectValue(value) {\n    if (value !== undefined) {\n      onSelect(value, {\n        selected: !rawValues.has(value)\n      });\n    }\n\n    // Single mode should always close by select\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    return {\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which,\n          ctrlKey = event.ctrlKey;\n        switch (which) {\n          // >>> Arrow keys & ctrl + n/p on Mac\n          case KeyCode.N:\n          case KeyCode.P:\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              } else if (isPlatformMac() && ctrlKey) {\n                if (which === KeyCode.N) {\n                  offset = 1;\n                } else if (which === KeyCode.P) {\n                  offset = -1;\n                }\n              }\n              if (offset !== 0) {\n                var nextActiveIndex = getEnabledActiveIndex(activeIndex + offset, offset);\n                scrollIntoView(nextActiveIndex);\n                setActive(nextActiveIndex, true);\n              }\n              break;\n            }\n\n          // >>> Select (Tab / Enter)\n          case KeyCode.TAB:\n          case KeyCode.ENTER:\n            {\n              var _item$data;\n              // value\n              var item = memoFlattenOptions[activeIndex];\n              if (item && !(item !== null && item !== void 0 && (_item$data = item.data) !== null && _item$data !== void 0 && _item$data.disabled) && !overMaxCount) {\n                onSelectValue(item.value);\n              } else {\n                onSelectValue(undefined);\n              }\n              if (open) {\n                event.preventDefault();\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {},\n      scrollTo: function scrollTo(index) {\n        scrollIntoView(index);\n      }\n    };\n  });\n\n  // ========================== Render ==========================\n  if (memoFlattenOptions.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      id: \"\".concat(id, \"_list\"),\n      className: \"\".concat(itemPrefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var omitFieldNameList = Object.keys(fieldNames).map(function (key) {\n    return fieldNames[key];\n  });\n  var getLabel = function getLabel(item) {\n    return item.label;\n  };\n  function getItemAriaProps(item, index) {\n    var group = item.group;\n    return {\n      role: group ? 'presentation' : 'option',\n      id: \"\".concat(id, \"_list_\").concat(index)\n    };\n  }\n  var renderItem = function renderItem(index) {\n    var item = memoFlattenOptions[index];\n    if (!item) {\n      return null;\n    }\n    var itemData = item.data || {};\n    var value = itemData.value;\n    var group = item.group;\n    var attrs = pickAttrs(itemData, true);\n    var mergedLabel = getLabel(item);\n    return item ? /*#__PURE__*/React.createElement(\"div\", _extends({\n      \"aria-label\": typeof mergedLabel === 'string' && !group ? mergedLabel : null\n    }, attrs, {\n      key: index\n    }, getItemAriaProps(item, index), {\n      \"aria-selected\": isAriaSelected(value)\n    }), value) : null;\n  };\n  var a11yProps = {\n    role: 'listbox',\n    id: \"\".concat(id, \"_list\")\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, virtual && /*#__PURE__*/React.createElement(\"div\", _extends({}, a11yProps, {\n    style: {\n      height: 0,\n      width: 0,\n      overflow: 'hidden'\n    }\n  }), renderItem(activeIndex - 1), renderItem(activeIndex), renderItem(activeIndex + 1)), /*#__PURE__*/React.createElement(List, {\n    itemKey: \"key\",\n    ref: listRef,\n    data: memoFlattenOptions,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    fullHeight: false,\n    onMouseDown: onListMouseDown,\n    onScroll: onPopupScroll,\n    virtual: virtual,\n    direction: direction,\n    innerProps: virtual ? null : a11yProps\n  }, function (item, itemIndex) {\n    var group = item.group,\n      groupOption = item.groupOption,\n      data = item.data,\n      label = item.label,\n      value = item.value;\n    var key = data.key;\n\n    // Group\n    if (group) {\n      var _data$title;\n      var groupTitle = (_data$title = data.title) !== null && _data$title !== void 0 ? _data$title : isTitleType(label) ? label.toString() : undefined;\n      return /*#__PURE__*/React.createElement(\"div\", {\n        className: classNames(itemPrefixCls, \"\".concat(itemPrefixCls, \"-group\"), data.className),\n        title: groupTitle\n      }, label !== undefined ? label : key);\n    }\n    var disabled = data.disabled,\n      title = data.title,\n      children = data.children,\n      style = data.style,\n      className = data.className,\n      otherProps = _objectWithoutProperties(data, _excluded);\n    var passedProps = omit(otherProps, omitFieldNameList);\n\n    // Option\n    var selected = isSelected(value);\n    var mergedDisabled = disabled || !selected && overMaxCount;\n    var optionPrefixCls = \"\".concat(itemPrefixCls, \"-option\");\n    var optionClassName = classNames(itemPrefixCls, optionPrefixCls, className, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(optionPrefixCls, \"-grouped\"), groupOption), \"\".concat(optionPrefixCls, \"-active\"), activeIndex === itemIndex && !mergedDisabled), \"\".concat(optionPrefixCls, \"-disabled\"), mergedDisabled), \"\".concat(optionPrefixCls, \"-selected\"), selected));\n    var mergedLabel = getLabel(item);\n    var iconVisible = !menuItemSelectedIcon || typeof menuItemSelectedIcon === 'function' || selected;\n\n    // https://github.com/ant-design/ant-design/issues/34145\n    var content = typeof mergedLabel === 'number' ? mergedLabel : mergedLabel || value;\n    // https://github.com/ant-design/ant-design/issues/26717\n    var optionTitle = isTitleType(content) ? content.toString() : undefined;\n    if (title !== undefined) {\n      optionTitle = title;\n    }\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, pickAttrs(passedProps), !virtual ? getItemAriaProps(item, itemIndex) : {}, {\n      \"aria-selected\": isAriaSelected(value),\n      className: optionClassName,\n      title: optionTitle,\n      onMouseMove: function onMouseMove() {\n        if (activeIndex === itemIndex || mergedDisabled) {\n          return;\n        }\n        setActive(itemIndex);\n      },\n      onClick: function onClick() {\n        if (!mergedDisabled) {\n          onSelectValue(value);\n        }\n      },\n      style: style\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"\".concat(optionPrefixCls, \"-content\")\n    }, typeof optionRender === 'function' ? optionRender(item, {\n      index: itemIndex\n    }) : content), /*#__PURE__*/React.isValidElement(menuItemSelectedIcon) || selected, iconVisible && /*#__PURE__*/React.createElement(TransBtn, {\n      className: \"\".concat(itemPrefixCls, \"-option-state\"),\n      customizeIcon: menuItemSelectedIcon,\n      customizeIconProps: {\n        value: value,\n        disabled: mergedDisabled,\n        isSelected: selected\n      }\n    }, selected ? '✓' : null));\n  }));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nif (process.env.NODE_ENV !== 'production') {\n  RefOptionList.displayName = 'OptionList';\n}\nexport default RefOptionList;"], "names": [], "mappings": ";;;AA+XI;AA/XJ;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;AAbA,IAAI,YAAY;IAAC;IAAY;IAAS;IAAY;IAAS;CAAY;;;;;;;;;;;;;;AAevE,mEAAmE;AAEnE,SAAS,YAAY,OAAO;IAC1B,OAAO,OAAO,YAAY,YAAY,OAAO,YAAY;AAC3D;AAEA;;;CAGC,GACD,IAAI,aAAa,SAAS,WAAW,CAAC,EAAE,GAAG;IACzC,IAAI,gBAAgB,CAAA,GAAA,8JAAA,CAAA,UAAY,AAAD,KAC7B,YAAY,cAAc,SAAS,EACnC,KAAK,cAAc,EAAE,EACrB,OAAO,cAAc,IAAI,EACzB,WAAW,cAAc,QAAQ,EACjC,OAAO,cAAc,IAAI,EACzB,cAAc,cAAc,WAAW,EACvC,aAAa,cAAc,UAAU,EACrC,kBAAkB,cAAc,eAAe,EAC/C,gBAAgB,cAAc,aAAa;IAC7C,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,sJAAA,CAAA,UAAa,GACpD,WAAW,kBAAkB,QAAQ,EACrC,iBAAiB,kBAAkB,cAAc,EACjD,gBAAgB,kBAAkB,aAAa,EAC/C,2BAA2B,kBAAkB,wBAAwB,EACrE,WAAW,kBAAkB,QAAQ,EACrC,uBAAuB,kBAAkB,oBAAoB,EAC7D,YAAY,kBAAkB,SAAS,EACvC,aAAa,kBAAkB,UAAU,EACzC,UAAU,kBAAkB,OAAO,EACnC,YAAY,kBAAkB,SAAS,EACvC,aAAa,kBAAkB,UAAU,EACzC,iBAAiB,kBAAkB,cAAc,EACjD,eAAe,kBAAkB,YAAY;IAC/C,IAAI,gBAAgB,GAAG,MAAM,CAAC,WAAW;IACzC,IAAI,qBAAqB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD;kDAAE;YAC/B,OAAO;QACT;iDAAG;QAAC;QAAM;KAAe;kDAAE,SAAU,IAAI,EAAE,IAAI;YAC7C,OAAO,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;QACvC;;IAEA,+DAA+D;IAC/D,IAAI,UAAU,6JAAA,CAAA,SAAY,CAAC;IAC3B,IAAI,eAAe,6JAAA,CAAA,UAAa;4CAAC;YAC/B,OAAO,YAAY,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE,aAAa,CAAC,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,IAAI,KAAK;QACzH;2CAAG;QAAC;QAAU;QAAU,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU,IAAI;KAAC;IAC7F,IAAI,kBAAkB,SAAS,gBAAgB,KAAK;QAClD,MAAM,cAAc;IACtB;IACA,IAAI,iBAAiB,SAAS,eAAe,IAAI;QAC/C,IAAI;QACJ,CAAC,mBAAmB,QAAQ,OAAO,MAAM,QAAQ,qBAAqB,KAAK,KAAK,iBAAiB,QAAQ,CAAC,OAAO,SAAS,WAAW;YACnI,OAAO;QACT,IAAI;IACN;IAEA,wDAAwD;IACxD,IAAI,aAAa,6JAAA,CAAA,cAAiB;8CAAC,SAAU,KAAK;YAChD,IAAI,SAAS,YAAY;gBACvB,OAAO;YACT;YACA,OAAO,UAAU,GAAG,CAAC;QACvB;6CAAG;QAAC;QAAM,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,WAAW,QAAQ;QAAI,UAAU,IAAI;KAAC;IAEnE,+DAA+D;IAC/D,IAAI,wBAAwB,SAAS,sBAAsB,KAAK;QAC9D,IAAI,SAAS,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QACjF,IAAI,MAAM,mBAAmB,MAAM;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;YAC/B,IAAI,UAAU,CAAC,QAAQ,IAAI,SAAS,GAAG,IAAI;YAC3C,IAAI,OAAO,kBAAkB,CAAC,QAAQ,IAAI,CAAC,GACzC,QAAQ,KAAK,KAAK,EAClB,OAAO,KAAK,IAAI;YAClB,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,QAAQ,SAAS,KAAK,KAAK,KAAK,QAAQ,KAAK,CAAC,WAAW,KAAK,KAAK,KAAK,CAAC,YAAY,GAAG;gBAC/G,OAAO;YACT;QACF;QACA,OAAO,CAAC;IACV;IACA,IAAI,kBAAkB,6JAAA,CAAA,WAAc;gDAAC;YACjC,OAAO,sBAAsB;QAC/B;gDACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,cAAc,gBAAgB,CAAC,EAAE,EACjC,iBAAiB,gBAAgB,CAAC,EAAE;IACtC,IAAI,YAAY,SAAS,UAAU,KAAK;QACtC,IAAI,eAAe,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;QACvF,eAAe;QACf,IAAI,OAAO;YACT,QAAQ,eAAe,aAAa;QACtC;QAEA,uBAAuB;QACvB,IAAI,cAAc,kBAAkB,CAAC,MAAM;QAC3C,IAAI,CAAC,aAAa;YAChB,cAAc,MAAM,CAAC,GAAG;YACxB;QACF;QACA,cAAc,YAAY,KAAK,EAAE,OAAO;IAC1C;IAEA,iEAAiE;IACjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,UAAU,6BAA6B,QAAQ,sBAAsB,KAAK,CAAC;QAC7E;+BAAG;QAAC,mBAAmB,MAAM;QAAE;KAAY;IAE3C,wDAAwD;IACxD,IAAI,iBAAiB,6JAAA,CAAA,cAAiB;kDAAC,SAAU,KAAK;YACpD,IAAI,SAAS,YAAY;gBACvB,OAAO,OAAO,OAAO,WAAW,OAAO,YAAY,WAAW;YAChE;YACA,OAAO,UAAU,GAAG,CAAC;QACvB;iDAAG;QAAC;QAAM;QAAa,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,WAAW,QAAQ;QAAI,UAAU,IAAI;KAAC;IAEhF,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;;;;KAIC,GACD,IAAI,YAAY;kDAAW;oBACzB,IAAI,CAAC,YAAY,QAAQ,UAAU,IAAI,KAAK,GAAG;wBAC7C,IAAI,QAAQ,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE;wBACpC,gEAAgE;wBAChE,IAAI,QAAQ,mBAAmB,SAAS;oEAAC,SAAU,KAAK;gCACtD,IAAI,OAAO,MAAM,IAAI;gCACrB,OAAO,cAAc,OAAO,KAAK,KAAK,EAAE,UAAU,CAAC,eAAe,KAAK,KAAK,KAAK;4BACnF;;wBACA,IAAI,UAAU,CAAC,GAAG;4BAChB,UAAU;4BACV,eAAe;wBACjB;oBACF;gBACF;;YAEA,4CAA4C;YAC5C,IAAI,MAAM;gBACR,IAAI;gBACJ,CAAC,oBAAoB,QAAQ,OAAO,MAAM,QAAQ,sBAAsB,KAAK,KAAK,kBAAkB,QAAQ,CAAC;YAC/G;YACA;wCAAO;oBACL,OAAO,aAAa;gBACtB;;QACF;+BAAG;QAAC;QAAM;KAAY;IAEtB,+DAA+D;IAC/D,IAAI,gBAAgB,SAAS,cAAc,KAAK;QAC9C,IAAI,UAAU,WAAW;YACvB,SAAS,OAAO;gBACd,UAAU,CAAC,UAAU,GAAG,CAAC;YAC3B;QACF;QAEA,4CAA4C;QAC5C,IAAI,CAAC,UAAU;YACb,WAAW;QACb;IACF;IAEA,+DAA+D;IAC/D,6JAAA,CAAA,sBAAyB,CAAC;0CAAK;YAC7B,OAAO;gBACL,WAAW,SAAS,UAAU,KAAK;oBACjC,IAAI,QAAQ,MAAM,KAAK,EACrB,UAAU,MAAM,OAAO;oBACzB,OAAQ;wBACN,qCAAqC;wBACrC,KAAK,8IAAA,CAAA,UAAO,CAAC,CAAC;wBACd,KAAK,8IAAA,CAAA,UAAO,CAAC,CAAC;wBACd,KAAK,8IAAA,CAAA,UAAO,CAAC,EAAE;wBACf,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;4BACf;gCACE,IAAI,SAAS;gCACb,IAAI,UAAU,8IAAA,CAAA,UAAO,CAAC,EAAE,EAAE;oCACxB,SAAS,CAAC;gCACZ,OAAO,IAAI,UAAU,8IAAA,CAAA,UAAO,CAAC,IAAI,EAAE;oCACjC,SAAS;gCACX,OAAO,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,OAAO,SAAS;oCACrC,IAAI,UAAU,8IAAA,CAAA,UAAO,CAAC,CAAC,EAAE;wCACvB,SAAS;oCACX,OAAO,IAAI,UAAU,8IAAA,CAAA,UAAO,CAAC,CAAC,EAAE;wCAC9B,SAAS,CAAC;oCACZ;gCACF;gCACA,IAAI,WAAW,GAAG;oCAChB,IAAI,kBAAkB,sBAAsB,cAAc,QAAQ;oCAClE,eAAe;oCACf,UAAU,iBAAiB;gCAC7B;gCACA;4BACF;wBAEF,2BAA2B;wBAC3B,KAAK,8IAAA,CAAA,UAAO,CAAC,GAAG;wBAChB,KAAK,8IAAA,CAAA,UAAO,CAAC,KAAK;4BAChB;gCACE,IAAI;gCACJ,QAAQ;gCACR,IAAI,OAAO,kBAAkB,CAAC,YAAY;gCAC1C,IAAI,QAAQ,CAAC,CAAC,SAAS,QAAQ,SAAS,KAAK,KAAK,CAAC,aAAa,KAAK,IAAI,MAAM,QAAQ,eAAe,KAAK,KAAK,WAAW,QAAQ,KAAK,CAAC,cAAc;oCACrJ,cAAc,KAAK,KAAK;gCAC1B,OAAO;oCACL,cAAc;gCAChB;gCACA,IAAI,MAAM;oCACR,MAAM,cAAc;gCACtB;gCACA;4BACF;wBAEF,YAAY;wBACZ,KAAK,8IAAA,CAAA,UAAO,CAAC,GAAG;4BACd;gCACE,WAAW;gCACX,IAAI,MAAM;oCACR,MAAM,eAAe;gCACvB;4BACF;oBACJ;gBACF;gBACA,SAAS,SAAS,WAAW;gBAC7B,UAAU,SAAS,SAAS,KAAK;oBAC/B,eAAe;gBACjB;YACF;QACF;;IAEA,+DAA+D;IAC/D,IAAI,mBAAmB,MAAM,KAAK,GAAG;QACnC,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;YAC7C,MAAM;YACN,IAAI,GAAG,MAAM,CAAC,IAAI;YAClB,WAAW,GAAG,MAAM,CAAC,eAAe;YACpC,aAAa;QACf,GAAG;IACL;IACA,IAAI,oBAAoB,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,SAAU,GAAG;QAC/D,OAAO,UAAU,CAAC,IAAI;IACxB;IACA,IAAI,WAAW,SAAS,SAAS,IAAI;QACnC,OAAO,KAAK,KAAK;IACnB;IACA,SAAS,iBAAiB,IAAI,EAAE,KAAK;QACnC,IAAI,QAAQ,KAAK,KAAK;QACtB,OAAO;YACL,MAAM,QAAQ,iBAAiB;YAC/B,IAAI,GAAG,MAAM,CAAC,IAAI,UAAU,MAAM,CAAC;QACrC;IACF;IACA,IAAI,aAAa,SAAS,WAAW,KAAK;QACxC,IAAI,OAAO,kBAAkB,CAAC,MAAM;QACpC,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QACA,IAAI,WAAW,KAAK,IAAI,IAAI,CAAC;QAC7B,IAAI,QAAQ,SAAS,KAAK;QAC1B,IAAI,QAAQ,KAAK,KAAK;QACtB,IAAI,QAAQ,CAAA,GAAA,gJAAA,CAAA,UAAS,AAAD,EAAE,UAAU;QAChC,IAAI,cAAc,SAAS;QAC3B,OAAO,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;YAC7D,cAAc,OAAO,gBAAgB,YAAY,CAAC,QAAQ,cAAc;QAC1E,GAAG,OAAO;YACR,KAAK;QACP,GAAG,iBAAiB,MAAM,QAAQ;YAChC,iBAAiB,eAAe;QAClC,IAAI,SAAS;IACf;IACA,IAAI,YAAY;QACd,MAAM;QACN,IAAI,GAAG,MAAM,CAAC,IAAI;IACpB;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,WAAW,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,WAAW;QACvI,OAAO;YACL,QAAQ;YACR,OAAO;YACP,UAAU;QACZ;IACF,IAAI,WAAW,cAAc,IAAI,WAAW,cAAc,WAAW,cAAc,KAAK,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,uJAAA,CAAA,UAAI,EAAE;QAC7H,SAAS;QACT,KAAK;QACL,MAAM;QACN,QAAQ;QACR,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,UAAU;QACV,SAAS;QACT,WAAW;QACX,YAAY,UAAU,OAAO;IAC/B,GAAG,SAAU,IAAI,EAAE,SAAS;QAC1B,IAAI,QAAQ,KAAK,KAAK,EACpB,cAAc,KAAK,WAAW,EAC9B,OAAO,KAAK,IAAI,EAChB,QAAQ,KAAK,KAAK,EAClB,QAAQ,KAAK,KAAK;QACpB,IAAI,MAAM,KAAK,GAAG;QAElB,QAAQ;QACR,IAAI,OAAO;YACT,IAAI;YACJ,IAAI,aAAa,CAAC,cAAc,KAAK,KAAK,MAAM,QAAQ,gBAAgB,KAAK,IAAI,cAAc,YAAY,SAAS,MAAM,QAAQ,KAAK;YACvI,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;gBAC7C,WAAW,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,eAAe,GAAG,MAAM,CAAC,eAAe,WAAW,KAAK,SAAS;gBACvF,OAAO;YACT,GAAG,UAAU,YAAY,QAAQ;QACnC;QACA,IAAI,WAAW,KAAK,QAAQ,EAC1B,QAAQ,KAAK,KAAK,EAClB,WAAW,KAAK,QAAQ,EACxB,QAAQ,KAAK,KAAK,EAClB,YAAY,KAAK,SAAS,EAC1B,aAAa,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,MAAM;QAC9C,IAAI,cAAc,CAAA,GAAA,2IAAA,CAAA,UAAI,AAAD,EAAE,YAAY;QAEnC,SAAS;QACT,IAAI,WAAW,WAAW;QAC1B,IAAI,iBAAiB,YAAY,CAAC,YAAY;QAC9C,IAAI,kBAAkB,GAAG,MAAM,CAAC,eAAe;QAC/C,IAAI,kBAAkB,CAAA,GAAA,sIAAA,CAAA,UAAU,AAAD,EAAE,eAAe,iBAAiB,WAAW,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,iBAAiB,aAAa,cAAc,GAAG,MAAM,CAAC,iBAAiB,YAAY,gBAAgB,aAAa,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,cAAc,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,cAAc;QAC/X,IAAI,cAAc,SAAS;QAC3B,IAAI,cAAc,CAAC,wBAAwB,OAAO,yBAAyB,cAAc;QAEzF,wDAAwD;QACxD,IAAI,UAAU,OAAO,gBAAgB,WAAW,cAAc,eAAe;QAC7E,wDAAwD;QACxD,IAAI,cAAc,YAAY,WAAW,QAAQ,QAAQ,KAAK;QAC9D,IAAI,UAAU,WAAW;YACvB,cAAc;QAChB;QACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,CAAA,GAAA,gJAAA,CAAA,UAAS,AAAD,EAAE,cAAc,CAAC,UAAU,iBAAiB,MAAM,aAAa,CAAC,GAAG;YACrI,iBAAiB,eAAe;YAChC,WAAW;YACX,OAAO;YACP,aAAa,SAAS;gBACpB,IAAI,gBAAgB,aAAa,gBAAgB;oBAC/C;gBACF;gBACA,UAAU;YACZ;YACA,SAAS,SAAS;gBAChB,IAAI,CAAC,gBAAgB;oBACnB,cAAc;gBAChB;YACF;YACA,OAAO;QACT,IAAI,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;YAC1C,WAAW,GAAG,MAAM,CAAC,iBAAiB;QACxC,GAAG,OAAO,iBAAiB,aAAa,aAAa,MAAM;YACzD,OAAO;QACT,KAAK,UAAU,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,yBAAyB,UAAU,eAAe,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,iJAAA,CAAA,UAAQ,EAAE;YAC5I,WAAW,GAAG,MAAM,CAAC,eAAe;YACpC,eAAe;YACf,oBAAoB;gBAClB,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF,GAAG,WAAW,MAAM;IACtB;AACF;AACA,IAAI,gBAAgB,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC;AAClD,wCAA2C;IACzC,cAAc,WAAW,GAAG;AAC9B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2299, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useCache.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\n/**\n * Cache `value` related LabeledValue & options.\n */\nexport default (function (labeledValues, valueOptions) {\n  var cacheRef = React.useRef({\n    values: new Map(),\n    options: new Map()\n  });\n  var filledLabeledValues = React.useMemo(function () {\n    var _cacheRef$current = cacheRef.current,\n      prevValueCache = _cacheRef$current.values,\n      prevOptionCache = _cacheRef$current.options;\n\n    // Fill label by cache\n    var patchedValues = labeledValues.map(function (item) {\n      if (item.label === undefined) {\n        var _prevValueCache$get;\n        return _objectSpread(_objectSpread({}, item), {}, {\n          label: (_prevValueCache$get = prevValueCache.get(item.value)) === null || _prevValueCache$get === void 0 ? void 0 : _prevValueCache$get.label\n        });\n      }\n      return item;\n    });\n\n    // Refresh cache\n    var valueCache = new Map();\n    var optionCache = new Map();\n    patchedValues.forEach(function (item) {\n      valueCache.set(item.value, item);\n      optionCache.set(item.value, valueOptions.get(item.value) || prevOptionCache.get(item.value));\n    });\n    cacheRef.current.values = valueCache;\n    cacheRef.current.options = optionCache;\n    return patchedValues;\n  }, [labeledValues, valueOptions]);\n  var getOption = React.useCallback(function (val) {\n    return valueOptions.get(val) || cacheRef.current.options.get(val);\n  }, [valueOptions]);\n  return [filledLabeledValues, getOption];\n});"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAIgB,SAAU,aAAa,EAAE,YAAY;IACnD,IAAI,WAAW,6JAAA,CAAA,SAAY,CAAC;QAC1B,QAAQ,IAAI;QACZ,SAAS,IAAI;IACf;IACA,IAAI,sBAAsB,6JAAA,CAAA,UAAa;wCAAC;YACtC,IAAI,oBAAoB,SAAS,OAAO,EACtC,iBAAiB,kBAAkB,MAAM,EACzC,kBAAkB,kBAAkB,OAAO;YAE7C,sBAAsB;YACtB,IAAI,gBAAgB,cAAc,GAAG;8DAAC,SAAU,IAAI;oBAClD,IAAI,KAAK,KAAK,KAAK,WAAW;wBAC5B,IAAI;wBACJ,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG;4BAChD,OAAO,CAAC,sBAAsB,eAAe,GAAG,CAAC,KAAK,KAAK,CAAC,MAAM,QAAQ,wBAAwB,KAAK,IAAI,KAAK,IAAI,oBAAoB,KAAK;wBAC/I;oBACF;oBACA,OAAO;gBACT;;YAEA,gBAAgB;YAChB,IAAI,aAAa,IAAI;YACrB,IAAI,cAAc,IAAI;YACtB,cAAc,OAAO;gDAAC,SAAU,IAAI;oBAClC,WAAW,GAAG,CAAC,KAAK,KAAK,EAAE;oBAC3B,YAAY,GAAG,CAAC,KAAK,KAAK,EAAE,aAAa,GAAG,CAAC,KAAK,KAAK,KAAK,gBAAgB,GAAG,CAAC,KAAK,KAAK;gBAC5F;;YACA,SAAS,OAAO,CAAC,MAAM,GAAG;YAC1B,SAAS,OAAO,CAAC,OAAO,GAAG;YAC3B,OAAO;QACT;uCAAG;QAAC;QAAe;KAAa;IAChC,IAAI,YAAY,6JAAA,CAAA,cAAiB;kCAAC,SAAU,GAAG;YAC7C,OAAO,aAAa,GAAG,CAAC,QAAQ,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;QAC/D;iCAAG;QAAC;KAAa;IACjB,OAAO;QAAC;QAAqB;KAAU;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2359, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useFilterOptions.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { toArray } from \"../utils/commonUtil\";\nimport { injectPropsWithOption } from \"../utils/valueUtil\";\nfunction includes(test, search) {\n  return toArray(test).join('').toUpperCase().includes(search);\n}\nexport default (function (options, fieldNames, searchValue, filterOption, optionFilterProp) {\n  return React.useMemo(function () {\n    if (!searchValue || filterOption === false) {\n      return options;\n    }\n    var fieldOptions = fieldNames.options,\n      fieldLabel = fieldNames.label,\n      fieldValue = fieldNames.value;\n    var filteredOptions = [];\n    var customizeFilter = typeof filterOption === 'function';\n    var upperSearch = searchValue.toUpperCase();\n    var filterFunc = customizeFilter ? filterOption : function (_, option) {\n      // Use provided `optionFilterProp`\n      if (optionFilterProp) {\n        return includes(option[optionFilterProp], upperSearch);\n      }\n\n      // Auto select `label` or `value` by option type\n      if (option[fieldOptions]) {\n        // hack `fieldLabel` since `OptionGroup` children is not `label`\n        return includes(option[fieldLabel !== 'children' ? fieldLabel : 'label'], upperSearch);\n      }\n      return includes(option[fieldValue], upperSearch);\n    };\n    var wrapOption = customizeFilter ? function (opt) {\n      return injectPropsWithOption(opt);\n    } : function (opt) {\n      return opt;\n    };\n    options.forEach(function (item) {\n      // Group should check child options\n      if (item[fieldOptions]) {\n        // Check group first\n        var matchGroup = filterFunc(searchValue, wrapOption(item));\n        if (matchGroup) {\n          filteredOptions.push(item);\n        } else {\n          // Check option\n          var subOptions = item[fieldOptions].filter(function (subItem) {\n            return filterFunc(searchValue, wrapOption(subItem));\n          });\n          if (subOptions.length) {\n            filteredOptions.push(_objectSpread(_objectSpread({}, item), {}, _defineProperty({}, fieldOptions, subOptions)));\n          }\n        }\n        return;\n      }\n      if (filterFunc(searchValue, wrapOption(item))) {\n        filteredOptions.push(item);\n      }\n    });\n    return filteredOptions;\n  }, [options, filterOption, optionFilterProp, searchValue, fieldNames]);\n});"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,SAAS,SAAS,IAAI,EAAE,MAAM;IAC5B,OAAO,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE,MAAM,IAAI,CAAC,IAAI,WAAW,GAAG,QAAQ,CAAC;AACvD;uCACgB,SAAU,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB;IACxF,OAAO,6JAAA,CAAA,UAAa;mBAAC;YACnB,IAAI,CAAC,eAAe,iBAAiB,OAAO;gBAC1C,OAAO;YACT;YACA,IAAI,eAAe,WAAW,OAAO,EACnC,aAAa,WAAW,KAAK,EAC7B,aAAa,WAAW,KAAK;YAC/B,IAAI,kBAAkB,EAAE;YACxB,IAAI,kBAAkB,OAAO,iBAAiB;YAC9C,IAAI,cAAc,YAAY,WAAW;YACzC,IAAI,aAAa,kBAAkB;2BAAe,SAAU,CAAC,EAAE,MAAM;oBACnE,kCAAkC;oBAClC,IAAI,kBAAkB;wBACpB,OAAO,SAAS,MAAM,CAAC,iBAAiB,EAAE;oBAC5C;oBAEA,gDAAgD;oBAChD,IAAI,MAAM,CAAC,aAAa,EAAE;wBACxB,gEAAgE;wBAChE,OAAO,SAAS,MAAM,CAAC,eAAe,aAAa,aAAa,QAAQ,EAAE;oBAC5E;oBACA,OAAO,SAAS,MAAM,CAAC,WAAW,EAAE;gBACtC;;YACA,IAAI,aAAa;2BAAkB,SAAU,GAAG;oBAC9C,OAAO,CAAA,GAAA,2JAAA,CAAA,wBAAqB,AAAD,EAAE;gBAC/B;;2BAAI,SAAU,GAAG;oBACf,OAAO;gBACT;;YACA,QAAQ,OAAO;2BAAC,SAAU,IAAI;oBAC5B,mCAAmC;oBACnC,IAAI,IAAI,CAAC,aAAa,EAAE;wBACtB,oBAAoB;wBACpB,IAAI,aAAa,WAAW,aAAa,WAAW;wBACpD,IAAI,YAAY;4BACd,gBAAgB,IAAI,CAAC;wBACvB,OAAO;4BACL,eAAe;4BACf,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,MAAM;sDAAC,SAAU,OAAO;oCAC1D,OAAO,WAAW,aAAa,WAAW;gCAC5C;;4BACA,IAAI,WAAW,MAAM,EAAE;gCACrB,gBAAgB,IAAI,CAAC,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,cAAc;4BACpG;wBACF;wBACA;oBACF;oBACA,IAAI,WAAW,aAAa,WAAW,QAAQ;wBAC7C,gBAAgB,IAAI,CAAC;oBACvB;gBACF;;YACA,OAAO;QACT;kBAAG;QAAC;QAAS;QAAc;QAAkB;QAAa;KAAW;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2448, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useId.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\nvar uuid = 0;\n\n/** Is client side and not jsdom */\nexport var isBrowserClient = process.env.NODE_ENV !== 'test' && canUseDom();\n\n/** Get unique id for accessibility usage */\nexport function getUUID() {\n  var retId;\n\n  // Test never reach\n  /* istanbul ignore if */\n  if (isBrowserClient) {\n    retId = uuid;\n    uuid += 1;\n  } else {\n    retId = 'TEST_OR_SSR';\n  }\n  return retId;\n}\nexport default function useId(id) {\n  // Inner id for accessibility usage. Only work in client side\n  var _React$useState = React.useState(),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    innerId = _React$useState2[0],\n    setInnerId = _React$useState2[1];\n  React.useEffect(function () {\n    setInnerId(\"rc_select_\".concat(getUUID()));\n  }, []);\n  return id || innerId;\n}"], "names": [], "mappings": ";;;;;AAM6B;AAN7B;AACA;AACA;;;;AACA,IAAI,OAAO;AAGJ,IAAI,kBAAkB,oDAAyB,UAAU,CAAA,GAAA,uJAAA,CAAA,UAAS,AAAD;AAGjE,SAAS;IACd,IAAI;IAEJ,mBAAmB;IACnB,sBAAsB,GACtB,IAAI,iBAAiB;QACnB,QAAQ;QACR,QAAQ;IACV,OAAO;QACL,QAAQ;IACV;IACA,OAAO;AACT;AACe,SAAS,MAAM,EAAE;IAC9B,6DAA6D;IAC7D,IAAI,kBAAkB,6JAAA,CAAA,WAAc,IAClC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,UAAU,gBAAgB,CAAC,EAAE,EAC7B,aAAa,gBAAgB,CAAC,EAAE;IAClC,6JAAA,CAAA,YAAe;2BAAC;YACd,WAAW,aAAa,MAAM,CAAC;QACjC;0BAAG,EAAE;IACL,OAAO,MAAM;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2487, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/utils/legacyUtil.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\", \"value\"],\n  _excluded2 = [\"children\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nfunction convertNodeToOption(node) {\n  var _ref = node,\n    key = _ref.key,\n    _ref$props = _ref.props,\n    children = _ref$props.children,\n    value = _ref$props.value,\n    restProps = _objectWithoutProperties(_ref$props, _excluded);\n  return _objectSpread({\n    key: key,\n    value: value !== undefined ? value : key,\n    children: children\n  }, restProps);\n}\nexport function convertChildrenToData(nodes) {\n  var optionOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  return toArray(nodes).map(function (node, index) {\n    if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n      return null;\n    }\n    var _ref2 = node,\n      isSelectOptGroup = _ref2.type.isSelectOptGroup,\n      key = _ref2.key,\n      _ref2$props = _ref2.props,\n      children = _ref2$props.children,\n      restProps = _objectWithoutProperties(_ref2$props, _excluded2);\n    if (optionOnly || !isSelectOptGroup) {\n      return convertNodeToOption(node);\n    }\n    return _objectSpread(_objectSpread({\n      key: \"__RC_SELECT_GRP__\".concat(key === null ? index : key, \"__\"),\n      label: key\n    }, restProps), {}, {\n      options: convertChildrenToData(children)\n    });\n  }).filter(function (data) {\n    return data;\n  });\n}"], "names": [], "mappings": ";;;AAAA;AACA;AAGA;AACA;;;AAHA,IAAI,YAAY;IAAC;IAAY;CAAQ,EACnC,aAAa;IAAC;CAAW;;;AAG3B,SAAS,oBAAoB,IAAI;IAC/B,IAAI,OAAO,MACT,MAAM,KAAK,GAAG,EACd,aAAa,KAAK,KAAK,EACvB,WAAW,WAAW,QAAQ,EAC9B,QAAQ,WAAW,KAAK,EACxB,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,YAAY;IACnD,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE;QACnB,KAAK;QACL,OAAO,UAAU,YAAY,QAAQ;QACrC,UAAU;IACZ,GAAG;AACL;AACO,SAAS,sBAAsB,KAAK;IACzC,IAAI,aAAa,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;IACrF,OAAO,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,OAAO,GAAG,CAAC,SAAU,IAAI,EAAE,KAAK;QAC7C,IAAI,CAAE,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC3D,OAAO;QACT;QACA,IAAI,QAAQ,MACV,mBAAmB,MAAM,IAAI,CAAC,gBAAgB,EAC9C,MAAM,MAAM,GAAG,EACf,cAAc,MAAM,KAAK,EACzB,WAAW,YAAY,QAAQ,EAC/B,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,aAAa;QACpD,IAAI,cAAc,CAAC,kBAAkB;YACnC,OAAO,oBAAoB;QAC7B;QACA,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE;YACjC,KAAK,oBAAoB,MAAM,CAAC,QAAQ,OAAO,QAAQ,KAAK;YAC5D,OAAO;QACT,GAAG,YAAY,CAAC,GAAG;YACjB,SAAS,sBAAsB;QACjC;IACF,GAAG,MAAM,CAAC,SAAU,IAAI;QACtB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2536, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useOptions.js"], "sourcesContent": ["import * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\n\n/**\n * Parse `children` to `options` if `options` is not provided.\n * Then flatten the `options`.\n */\nvar useOptions = function useOptions(options, children, fieldNames, optionFilterProp, optionLabelProp) {\n  return React.useMemo(function () {\n    var mergedOptions = options;\n    var childrenAsData = !options;\n    if (childrenAsData) {\n      mergedOptions = convertChildrenToData(children);\n    }\n    var valueOptions = new Map();\n    var labelOptions = new Map();\n    var setLabelOptions = function setLabelOptions(labelOptionsMap, option, key) {\n      if (key && typeof key === 'string') {\n        labelOptionsMap.set(option[key], option);\n      }\n    };\n    var dig = function dig(optionList) {\n      var isChildren = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      // for loop to speed up collection speed\n      for (var i = 0; i < optionList.length; i += 1) {\n        var option = optionList[i];\n        if (!option[fieldNames.options] || isChildren) {\n          valueOptions.set(option[fieldNames.value], option);\n          setLabelOptions(labelOptions, option, fieldNames.label);\n          // https://github.com/ant-design/ant-design/issues/35304\n          setLabelOptions(labelOptions, option, optionFilterProp);\n          setLabelOptions(labelOptions, option, optionLabelProp);\n        } else {\n          dig(option[fieldNames.options], true);\n        }\n      }\n    };\n    dig(mergedOptions);\n    return {\n      options: mergedOptions,\n      valueOptions: valueOptions,\n      labelOptions: labelOptions\n    };\n  }, [options, children, fieldNames, optionFilterProp, optionLabelProp]);\n};\nexport default useOptions;"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;;CAGC,GACD,IAAI,aAAa,SAAS,WAAW,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,eAAe;IACnG,OAAO,6JAAA,CAAA,UAAa;8BAAC;YACnB,IAAI,gBAAgB;YACpB,IAAI,iBAAiB,CAAC;YACtB,IAAI,gBAAgB;gBAClB,gBAAgB,CAAA,GAAA,4JAAA,CAAA,wBAAqB,AAAD,EAAE;YACxC;YACA,IAAI,eAAe,IAAI;YACvB,IAAI,eAAe,IAAI;YACvB,IAAI,kBAAkB,SAAS,gBAAgB,eAAe,EAAE,MAAM,EAAE,GAAG;gBACzE,IAAI,OAAO,OAAO,QAAQ,UAAU;oBAClC,gBAAgB,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;gBACnC;YACF;YACA,IAAI,MAAM,SAAS,IAAI,UAAU;gBAC/B,IAAI,aAAa,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;gBACrF,wCAAwC;gBACxC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;oBAC7C,IAAI,SAAS,UAAU,CAAC,EAAE;oBAC1B,IAAI,CAAC,MAAM,CAAC,WAAW,OAAO,CAAC,IAAI,YAAY;wBAC7C,aAAa,GAAG,CAAC,MAAM,CAAC,WAAW,KAAK,CAAC,EAAE;wBAC3C,gBAAgB,cAAc,QAAQ,WAAW,KAAK;wBACtD,wDAAwD;wBACxD,gBAAgB,cAAc,QAAQ;wBACtC,gBAAgB,cAAc,QAAQ;oBACxC,OAAO;wBACL,IAAI,MAAM,CAAC,WAAW,OAAO,CAAC,EAAE;oBAClC;gBACF;YACF;YACA,IAAI;YACJ,OAAO;gBACL,SAAS;gBACT,cAAc;gBACd,cAAc;YAChB;QACF;6BAAG;QAAC;QAAS;QAAU;QAAY;QAAkB;KAAgB;AACvE;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2597, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/hooks/useRefFunc.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Same as `React.useCallback` but always return a memoized function\n * but redirect to real function.\n */\nexport default function useRefFunc(callback) {\n  var funcRef = React.useRef();\n  funcRef.current = callback;\n  var cacheFn = React.useCallback(function () {\n    return funcRef.current.apply(funcRef, arguments);\n  }, []);\n  return cacheFn;\n}"], "names": [], "mappings": ";;;AAAA;;AAMe,SAAS,WAAW,QAAQ;IACzC,IAAI,UAAU,6JAAA,CAAA,SAAY;IAC1B,QAAQ,OAAO,GAAG;IAClB,IAAI,UAAU,6JAAA,CAAA,cAAiB;2CAAC;YAC9B,OAAO,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS;QACxC;0CAAG,EAAE;IACL,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2616, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/utils/warningPropsUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toNodeArray from \"rc-util/es/Children/toArray\";\nimport warning, { noteOnce } from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport { isMultiple } from \"../BaseSelect\";\nimport { toArray } from \"./commonUtil\";\nimport { convertChildrenToData } from \"./legacyUtil\";\nfunction warningProps(props) {\n  var mode = props.mode,\n    options = props.options,\n    children = props.children,\n    backfill = props.backfill,\n    allowClear = props.allowClear,\n    placeholder = props.placeholder,\n    getInputElement = props.getInputElement,\n    showSearch = props.showSearch,\n    onSearch = props.onSearch,\n    defaultOpen = props.defaultOpen,\n    autoFocus = props.autoFocus,\n    labelInValue = props.labelInValue,\n    value = props.value,\n    inputValue = props.inputValue,\n    optionLabelProp = props.optionLabelProp;\n  var multiple = isMultiple(mode);\n  var mergedShowSearch = showSearch !== undefined ? showSearch : multiple || mode === 'combobox';\n  var mergedOptions = options || convertChildrenToData(children);\n\n  // `tags` should not set option as disabled\n  warning(mode !== 'tags' || mergedOptions.every(function (opt) {\n    return !opt.disabled;\n  }), 'Please avoid setting option to disabled in tags mode since user can always type text as tag.');\n\n  // `combobox` & `tags` should option be `string` type\n  if (mode === 'tags' || mode === 'combobox') {\n    var hasNumberValue = mergedOptions.some(function (item) {\n      if (item.options) {\n        return item.options.some(function (opt) {\n          return typeof ('value' in opt ? opt.value : opt.key) === 'number';\n        });\n      }\n      return typeof ('value' in item ? item.value : item.key) === 'number';\n    });\n    warning(!hasNumberValue, '`value` of Option should not use number type when `mode` is `tags` or `combobox`.');\n  }\n\n  // `combobox` should not use `optionLabelProp`\n  warning(mode !== 'combobox' || !optionLabelProp, '`combobox` mode not support `optionLabelProp`. Please set `value` on Option directly.');\n\n  // Only `combobox` support `backfill`\n  warning(mode === 'combobox' || !backfill, '`backfill` only works with `combobox` mode.');\n\n  // Only `combobox` support `getInputElement`\n  warning(mode === 'combobox' || !getInputElement, '`getInputElement` only work with `combobox` mode.');\n\n  // Customize `getInputElement` should not use `allowClear` & `placeholder`\n  noteOnce(mode !== 'combobox' || !getInputElement || !allowClear || !placeholder, 'Customize `getInputElement` should customize clear and placeholder logic instead of configuring `allowClear` and `placeholder`.');\n\n  // `onSearch` should use in `combobox` or `showSearch`\n  if (onSearch && !mergedShowSearch && mode !== 'combobox' && mode !== 'tags') {\n    warning(false, '`onSearch` should work with `showSearch` instead of use alone.');\n  }\n  noteOnce(!defaultOpen || autoFocus, '`defaultOpen` makes Select open without focus which means it will not close by click outside. You can set `autoFocus` if needed.');\n  if (value !== undefined && value !== null) {\n    var values = toArray(value);\n    warning(!labelInValue || values.every(function (val) {\n      return _typeof(val) === 'object' && ('key' in val || 'value' in val);\n    }), '`value` should in shape of `{ value: string | number, label?: ReactNode }` when you set `labelInValue` to `true`');\n    warning(!multiple || Array.isArray(value), '`value` should be array when `mode` is `multiple` or `tags`');\n  }\n\n  // Syntactic sugar should use correct children type\n  if (children) {\n    var invalidateChildType = null;\n    toNodeArray(children).some(function (node) {\n      if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n        return false;\n      }\n      var _ref = node,\n        type = _ref.type;\n      if (type.isSelectOption) {\n        return false;\n      }\n      if (type.isSelectOptGroup) {\n        var allChildrenValid = toNodeArray(node.props.children).every(function (subNode) {\n          if (! /*#__PURE__*/React.isValidElement(subNode) || !node.type || subNode.type.isSelectOption) {\n            return true;\n          }\n          invalidateChildType = subNode.type;\n          return false;\n        });\n        if (allChildrenValid) {\n          return false;\n        }\n        return true;\n      }\n      invalidateChildType = type;\n      return true;\n    });\n    if (invalidateChildType) {\n      warning(false, \"`children` should be `Select.Option` or `Select.OptGroup` instead of `\".concat(invalidateChildType.displayName || invalidateChildType.name || invalidateChildType, \"`.\"));\n    }\n    warning(inputValue === undefined, '`inputValue` is deprecated, please use `searchValue` instead.');\n  }\n}\n\n// value in Select option should not be null\n// note: OptGroup has options too\nexport function warningNullOptions(options, fieldNames) {\n  if (options) {\n    var recursiveOptions = function recursiveOptions(optionsList) {\n      var inGroup = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      for (var i = 0; i < optionsList.length; i++) {\n        var option = optionsList[i];\n        if (option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.value] === null) {\n          warning(false, '`value` in Select options should not be `null`.');\n          return true;\n        }\n        if (!inGroup && Array.isArray(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.options]) && recursiveOptions(option[fieldNames === null || fieldNames === void 0 ? void 0 : fieldNames.options], true)) {\n          break;\n        }\n      }\n    };\n    recursiveOptions(options);\n  }\n}\nexport default warningProps;"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,SAAS,aAAa,KAAK;IACzB,IAAI,OAAO,MAAM,IAAI,EACnB,UAAU,MAAM,OAAO,EACvB,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ,EACzB,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW,EAC/B,kBAAkB,MAAM,eAAe,EACvC,aAAa,MAAM,UAAU,EAC7B,WAAW,MAAM,QAAQ,EACzB,cAAc,MAAM,WAAW,EAC/B,YAAY,MAAM,SAAS,EAC3B,eAAe,MAAM,YAAY,EACjC,QAAQ,MAAM,KAAK,EACnB,aAAa,MAAM,UAAU,EAC7B,kBAAkB,MAAM,eAAe;IACzC,IAAI,WAAW,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE;IAC1B,IAAI,mBAAmB,eAAe,YAAY,aAAa,YAAY,SAAS;IACpF,IAAI,gBAAgB,WAAW,CAAA,GAAA,4JAAA,CAAA,wBAAqB,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,SAAS,UAAU,cAAc,KAAK,CAAC,SAAU,GAAG;QAC1D,OAAO,CAAC,IAAI,QAAQ;IACtB,IAAI;IAEJ,qDAAqD;IACrD,IAAI,SAAS,UAAU,SAAS,YAAY;QAC1C,IAAI,iBAAiB,cAAc,IAAI,CAAC,SAAU,IAAI;YACpD,IAAI,KAAK,OAAO,EAAE;gBAChB,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,SAAU,GAAG;oBACpC,OAAO,OAAO,CAAC,WAAW,MAAM,IAAI,KAAK,GAAG,IAAI,GAAG,MAAM;gBAC3D;YACF;YACA,OAAO,OAAO,CAAC,WAAW,OAAO,KAAK,KAAK,GAAG,KAAK,GAAG,MAAM;QAC9D;QACA,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,gBAAgB;IAC3B;IAEA,8CAA8C;IAC9C,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,SAAS,cAAc,CAAC,iBAAiB;IAEjD,qCAAqC;IACrC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,SAAS,cAAc,CAAC,UAAU;IAE1C,4CAA4C;IAC5C,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,SAAS,cAAc,CAAC,iBAAiB;IAEjD,0EAA0E;IAC1E,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,cAAc,CAAC,mBAAmB,CAAC,cAAc,CAAC,aAAa;IAEjF,sDAAsD;IACtD,IAAI,YAAY,CAAC,oBAAoB,SAAS,cAAc,SAAS,QAAQ;QAC3E,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IACjB;IACA,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,eAAe,WAAW;IACpC,IAAI,UAAU,aAAa,UAAU,MAAM;QACzC,IAAI,SAAS,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE;QACrB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,gBAAgB,OAAO,KAAK,CAAC,SAAU,GAAG;YACjD,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,YAAY,CAAC,SAAS,OAAO,WAAW,GAAG;QACrE,IAAI;QACJ,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,YAAY,MAAM,OAAO,CAAC,QAAQ;IAC7C;IAEA,mDAAmD;IACnD,IAAI,UAAU;QACZ,IAAI,sBAAsB;QAC1B,CAAA,GAAA,0JAAA,CAAA,UAAW,AAAD,EAAE,UAAU,IAAI,CAAC,SAAU,IAAI;YACvC,IAAI,CAAE,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;gBAC3D,OAAO;YACT;YACA,IAAI,OAAO,MACT,OAAO,KAAK,IAAI;YAClB,IAAI,KAAK,cAAc,EAAE;gBACvB,OAAO;YACT;YACA,IAAI,KAAK,gBAAgB,EAAE;gBACzB,IAAI,mBAAmB,CAAA,GAAA,0JAAA,CAAA,UAAW,AAAD,EAAE,KAAK,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAU,OAAO;oBAC7E,IAAI,CAAE,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,QAAQ,IAAI,CAAC,cAAc,EAAE;wBAC7F,OAAO;oBACT;oBACA,sBAAsB,QAAQ,IAAI;oBAClC,OAAO;gBACT;gBACA,IAAI,kBAAkB;oBACpB,OAAO;gBACT;gBACA,OAAO;YACT;YACA,sBAAsB;YACtB,OAAO;QACT;QACA,IAAI,qBAAqB;YACvB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO,yEAAyE,MAAM,CAAC,oBAAoB,WAAW,IAAI,oBAAoB,IAAI,IAAI,qBAAqB;QACrL;QACA,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,eAAe,WAAW;IACpC;AACF;AAIO,SAAS,mBAAmB,OAAO,EAAE,UAAU;IACpD,IAAI,SAAS;QACX,IAAI,mBAAmB,SAAS,iBAAiB,WAAW;YAC1D,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;YAClF,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;gBAC3C,IAAI,SAAS,WAAW,CAAC,EAAE;gBAC3B,IAAI,MAAM,CAAC,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,KAAK,CAAC,KAAK,MAAM;oBAC7F,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;oBACf,OAAO;gBACT;gBACA,IAAI,CAAC,WAAW,MAAM,OAAO,CAAC,MAAM,CAAC,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,OAAO,CAAC,KAAK,iBAAiB,MAAM,CAAC,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,OAAO,CAAC,EAAE,OAAO;oBAC/N;gBACF;YACF;QACF;QACA,iBAAiB;IACnB;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2731, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/Select.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"mode\", \"prefixCls\", \"backfill\", \"fieldNames\", \"inputValue\", \"searchValue\", \"onSearch\", \"autoClearSearchValue\", \"onSelect\", \"onDeselect\", \"dropdownMatchSelectWidth\", \"filterOption\", \"filterSort\", \"optionFilterProp\", \"optionLabelProp\", \"options\", \"optionRender\", \"children\", \"defaultActiveFirstOption\", \"menuItemSelectedIcon\", \"virtual\", \"direction\", \"listHeight\", \"listItemHeight\", \"labelRender\", \"value\", \"defaultValue\", \"labelInValue\", \"onChange\", \"maxCount\"];\n/**\n * To match accessibility requirement, we always provide an input in the component.\n * Other element will not set `tabIndex` to avoid `onBlur` sequence problem.\n * For focused select, we set `aria-live=\"polite\"` to update the accessibility content.\n *\n * ref:\n * - keyboard: https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/listbox_role#Keyboard_interactions\n *\n * New api:\n * - listHeight\n * - listItemHeight\n * - component\n *\n * Remove deprecated api:\n * - multiple\n * - tags\n * - combobox\n * - firstActiveValue\n * - dropdownMenuStyle\n * - openClassName (Not list in api)\n *\n * Update:\n * - `backfill` only support `combobox` mode\n * - `combobox` mode not support `labelInValue` since it's meaningless\n * - `getInputElement` only support `combobox` mode\n * - `onChange` return OptionData instead of ReactNode\n * - `filterOption` `onChange` `onSelect` accept OptionData instead of ReactNode\n * - `combobox` mode trigger `onChange` will get `undefined` if no `value` match in Option\n * - `combobox` mode not support `optionLabelProp`\n */\n\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport BaseSelect, { isMultiple } from \"./BaseSelect\";\nimport OptGroup from \"./OptGroup\";\nimport Option from \"./Option\";\nimport OptionList from \"./OptionList\";\nimport SelectContext from \"./SelectContext\";\nimport useCache from \"./hooks/useCache\";\nimport useFilterOptions from \"./hooks/useFilterOptions\";\nimport useId from \"./hooks/useId\";\nimport useOptions from \"./hooks/useOptions\";\nimport useRefFunc from \"./hooks/useRefFunc\";\nimport { hasValue, isComboNoValue, toArray } from \"./utils/commonUtil\";\nimport { fillFieldNames, flattenOptions, injectPropsWithOption } from \"./utils/valueUtil\";\nimport warningProps, { warningNullOptions } from \"./utils/warningPropsUtil\";\nvar OMIT_DOM_PROPS = ['inputValue'];\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\nvar Select = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    mode = props.mode,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-select' : _props$prefixCls,\n    backfill = props.backfill,\n    fieldNames = props.fieldNames,\n    inputValue = props.inputValue,\n    searchValue = props.searchValue,\n    onSearch = props.onSearch,\n    _props$autoClearSearc = props.autoClearSearchValue,\n    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n    filterOption = props.filterOption,\n    filterSort = props.filterSort,\n    optionFilterProp = props.optionFilterProp,\n    optionLabelProp = props.optionLabelProp,\n    options = props.options,\n    optionRender = props.optionRender,\n    children = props.children,\n    defaultActiveFirstOption = props.defaultActiveFirstOption,\n    menuItemSelectedIcon = props.menuItemSelectedIcon,\n    virtual = props.virtual,\n    direction = props.direction,\n    _props$listHeight = props.listHeight,\n    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n    _props$listItemHeight = props.listItemHeight,\n    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n    labelRender = props.labelRender,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    labelInValue = props.labelInValue,\n    onChange = props.onChange,\n    maxCount = props.maxCount,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var multiple = isMultiple(mode);\n  var childrenAsData = !!(!options && children);\n  var mergedFilterOption = React.useMemo(function () {\n    if (filterOption === undefined && mode === 'combobox') {\n      return false;\n    }\n    return filterOption;\n  }, [filterOption, mode]);\n\n  // ========================= FieldNames =========================\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames, childrenAsData);\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [\n  // We stringify fieldNames to avoid unnecessary re-renders.\n  JSON.stringify(fieldNames), childrenAsData]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  // =========================== Search ===========================\n  var _useMergedState = useMergedState('', {\n      value: searchValue !== undefined ? searchValue : inputValue,\n      postState: function postState(search) {\n        return search || '';\n      }\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    mergedSearchValue = _useMergedState2[0],\n    setSearchValue = _useMergedState2[1];\n\n  // =========================== Option ===========================\n  var parsedOptions = useOptions(options, children, mergedFieldNames, optionFilterProp, optionLabelProp);\n  var valueOptions = parsedOptions.valueOptions,\n    labelOptions = parsedOptions.labelOptions,\n    mergedOptions = parsedOptions.options;\n\n  // ========================= Wrap Value =========================\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    // Convert to array\n    var valueList = toArray(draftValues);\n\n    // Convert to labelInValue type\n    return valueList.map(function (val) {\n      var rawValue;\n      var rawLabel;\n      var rawKey;\n      var rawDisabled;\n      var rawTitle;\n\n      // Fill label & value\n      if (isRawValue(val)) {\n        rawValue = val;\n      } else {\n        var _val$value;\n        rawKey = val.key;\n        rawLabel = val.label;\n        rawValue = (_val$value = val.value) !== null && _val$value !== void 0 ? _val$value : rawKey;\n      }\n      var option = valueOptions.get(rawValue);\n      if (option) {\n        var _option$key;\n        // Fill missing props\n        if (rawLabel === undefined) rawLabel = option === null || option === void 0 ? void 0 : option[optionLabelProp || mergedFieldNames.label];\n        if (rawKey === undefined) rawKey = (_option$key = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key !== void 0 ? _option$key : rawValue;\n        rawDisabled = option === null || option === void 0 ? void 0 : option.disabled;\n        rawTitle = option === null || option === void 0 ? void 0 : option.title;\n\n        // Warning if label not same as provided\n        if (process.env.NODE_ENV !== 'production' && !optionLabelProp) {\n          var optionLabel = option === null || option === void 0 ? void 0 : option[mergedFieldNames.label];\n          if (optionLabel !== undefined && ! /*#__PURE__*/React.isValidElement(optionLabel) && ! /*#__PURE__*/React.isValidElement(rawLabel) && optionLabel !== rawLabel) {\n            warning(false, '`label` of `value` is not same as `label` in Select options.');\n          }\n        }\n      }\n      return {\n        label: rawLabel,\n        value: rawValue,\n        key: rawKey,\n        disabled: rawDisabled,\n        title: rawTitle\n      };\n    });\n  }, [mergedFieldNames, optionLabelProp, valueOptions]);\n\n  // =========================== Values ===========================\n  var _useMergedState3 = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    internalValue = _useMergedState4[0],\n    setInternalValue = _useMergedState4[1];\n\n  // Merged value with LabelValueType\n  var rawLabeledValues = React.useMemo(function () {\n    var _values$;\n    var newInternalValue = multiple && internalValue === null ? [] : internalValue;\n    var values = convert2LabelValues(newInternalValue);\n\n    // combobox no need save value when it's no value (exclude value equal 0)\n    if (mode === 'combobox' && isComboNoValue((_values$ = values[0]) === null || _values$ === void 0 ? void 0 : _values$.value)) {\n      return [];\n    }\n    return values;\n  }, [internalValue, convert2LabelValues, mode, multiple]);\n\n  // Fill label with cache to avoid option remove\n  var _useCache = useCache(rawLabeledValues, valueOptions),\n    _useCache2 = _slicedToArray(_useCache, 2),\n    mergedValues = _useCache2[0],\n    getMixedOption = _useCache2[1];\n  var displayValues = React.useMemo(function () {\n    // `null` need show as placeholder instead\n    // https://github.com/ant-design/ant-design/issues/25057\n    if (!mode && mergedValues.length === 1) {\n      var firstValue = mergedValues[0];\n      if (firstValue.value === null && (firstValue.label === null || firstValue.label === undefined)) {\n        return [];\n      }\n    }\n    return mergedValues.map(function (item) {\n      var _ref;\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_ref = typeof labelRender === 'function' ? labelRender(item) : item.label) !== null && _ref !== void 0 ? _ref : item.value\n      });\n    });\n  }, [mode, mergedValues, labelRender]);\n\n  /** Convert `displayValues` to raw value type set */\n  var rawValues = React.useMemo(function () {\n    return new Set(mergedValues.map(function (val) {\n      return val.value;\n    }));\n  }, [mergedValues]);\n  React.useEffect(function () {\n    if (mode === 'combobox') {\n      var _mergedValues$;\n      var strValue = (_mergedValues$ = mergedValues[0]) === null || _mergedValues$ === void 0 ? void 0 : _mergedValues$.value;\n      setSearchValue(hasValue(strValue) ? String(strValue) : '');\n    }\n  }, [mergedValues]);\n\n  // ======================= Display Option =======================\n  // Create a placeholder item if not exist in `options`\n  var createTagOption = useRefFunc(function (val, label) {\n    var mergedLabel = label !== null && label !== void 0 ? label : val;\n    return _defineProperty(_defineProperty({}, mergedFieldNames.value, val), mergedFieldNames.label, mergedLabel);\n  });\n\n  // Fill tag as option if mode is `tags`\n  var filledTagOptions = React.useMemo(function () {\n    if (mode !== 'tags') {\n      return mergedOptions;\n    }\n\n    // >>> Tag mode\n    var cloneOptions = _toConsumableArray(mergedOptions);\n\n    // Check if value exist in options (include new patch item)\n    var existOptions = function existOptions(val) {\n      return valueOptions.has(val);\n    };\n\n    // Fill current value as option\n    _toConsumableArray(mergedValues).sort(function (a, b) {\n      return a.value < b.value ? -1 : 1;\n    }).forEach(function (item) {\n      var val = item.value;\n      if (!existOptions(val)) {\n        cloneOptions.push(createTagOption(val, item.label));\n      }\n    });\n    return cloneOptions;\n  }, [createTagOption, mergedOptions, valueOptions, mergedValues, mode]);\n  var filteredOptions = useFilterOptions(filledTagOptions, mergedFieldNames, mergedSearchValue, mergedFilterOption, optionFilterProp);\n\n  // Fill options with search value if needed\n  var filledSearchOptions = React.useMemo(function () {\n    if (mode !== 'tags' || !mergedSearchValue || filteredOptions.some(function (item) {\n      return item[optionFilterProp || 'value'] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    }\n    // ignore when search value equal select input value\n    if (filteredOptions.some(function (item) {\n      return item[mergedFieldNames.value] === mergedSearchValue;\n    })) {\n      return filteredOptions;\n    }\n    // Fill search value as option\n    return [createTagOption(mergedSearchValue)].concat(_toConsumableArray(filteredOptions));\n  }, [createTagOption, optionFilterProp, mode, filteredOptions, mergedSearchValue, mergedFieldNames]);\n  var sorter = function sorter(inputOptions) {\n    var sortedOptions = _toConsumableArray(inputOptions).sort(function (a, b) {\n      return filterSort(a, b, {\n        searchValue: mergedSearchValue\n      });\n    });\n    return sortedOptions.map(function (item) {\n      if (Array.isArray(item.options)) {\n        return _objectSpread(_objectSpread({}, item), {}, {\n          options: item.options.length > 0 ? sorter(item.options) : item.options\n        });\n      }\n      return item;\n    });\n  };\n  var orderedFilteredOptions = React.useMemo(function () {\n    if (!filterSort) {\n      return filledSearchOptions;\n    }\n    return sorter(filledSearchOptions);\n  }, [filledSearchOptions, filterSort, mergedSearchValue]);\n  var displayOptions = React.useMemo(function () {\n    return flattenOptions(orderedFilteredOptions, {\n      fieldNames: mergedFieldNames,\n      childrenAsData: childrenAsData\n    });\n  }, [orderedFilteredOptions, mergedFieldNames, childrenAsData]);\n\n  // =========================== Change ===========================\n  var triggerChange = function triggerChange(values) {\n    var labeledValues = convert2LabelValues(values);\n    setInternalValue(labeledValues);\n    if (onChange && (\n    // Trigger event only when value changed\n    labeledValues.length !== mergedValues.length || labeledValues.some(function (newVal, index) {\n      var _mergedValues$index;\n      return ((_mergedValues$index = mergedValues[index]) === null || _mergedValues$index === void 0 ? void 0 : _mergedValues$index.value) !== (newVal === null || newVal === void 0 ? void 0 : newVal.value);\n    }))) {\n      var returnValues = labelInValue ? labeledValues : labeledValues.map(function (v) {\n        return v.value;\n      });\n      var returnOptions = labeledValues.map(function (v) {\n        return injectPropsWithOption(getMixedOption(v.value));\n      });\n      onChange(\n      // Value\n      multiple ? returnValues : returnValues[0],\n      // Option\n      multiple ? returnOptions : returnOptions[0]);\n    }\n  };\n\n  // ======================= Accessibility ========================\n  var _React$useState = React.useState(null),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    activeValue = _React$useState2[0],\n    setActiveValue = _React$useState2[1];\n  var _React$useState3 = React.useState(0),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    accessibilityIndex = _React$useState4[0],\n    setAccessibilityIndex = _React$useState4[1];\n  var mergedDefaultActiveFirstOption = defaultActiveFirstOption !== undefined ? defaultActiveFirstOption : mode !== 'combobox';\n  var onActiveValue = React.useCallback(function (active, index) {\n    var _ref3 = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n      _ref3$source = _ref3.source,\n      source = _ref3$source === void 0 ? 'keyboard' : _ref3$source;\n    setAccessibilityIndex(index);\n    if (backfill && mode === 'combobox' && active !== null && source === 'keyboard') {\n      setActiveValue(String(active));\n    }\n  }, [backfill, mode]);\n\n  // ========================= OptionList =========================\n  var triggerSelect = function triggerSelect(val, selected, type) {\n    var getSelectEnt = function getSelectEnt() {\n      var _option$key2;\n      var option = getMixedOption(val);\n      return [labelInValue ? {\n        label: option === null || option === void 0 ? void 0 : option[mergedFieldNames.label],\n        value: val,\n        key: (_option$key2 = option === null || option === void 0 ? void 0 : option.key) !== null && _option$key2 !== void 0 ? _option$key2 : val\n      } : val, injectPropsWithOption(option)];\n    };\n    if (selected && onSelect) {\n      var _getSelectEnt = getSelectEnt(),\n        _getSelectEnt2 = _slicedToArray(_getSelectEnt, 2),\n        wrappedValue = _getSelectEnt2[0],\n        _option = _getSelectEnt2[1];\n      onSelect(wrappedValue, _option);\n    } else if (!selected && onDeselect && type !== 'clear') {\n      var _getSelectEnt3 = getSelectEnt(),\n        _getSelectEnt4 = _slicedToArray(_getSelectEnt3, 2),\n        _wrappedValue = _getSelectEnt4[0],\n        _option2 = _getSelectEnt4[1];\n      onDeselect(_wrappedValue, _option2);\n    }\n  };\n\n  // Used for OptionList selection\n  var onInternalSelect = useRefFunc(function (val, info) {\n    var cloneValues;\n\n    // Single mode always trigger select only with option list\n    var mergedSelect = multiple ? info.selected : true;\n    if (mergedSelect) {\n      cloneValues = multiple ? [].concat(_toConsumableArray(mergedValues), [val]) : [val];\n    } else {\n      cloneValues = mergedValues.filter(function (v) {\n        return v.value !== val;\n      });\n    }\n    triggerChange(cloneValues);\n    triggerSelect(val, mergedSelect);\n\n    // Clean search value if single or configured\n    if (mode === 'combobox') {\n      // setSearchValue(String(val));\n      setActiveValue('');\n    } else if (!isMultiple || autoClearSearchValue) {\n      setSearchValue('');\n      setActiveValue('');\n    }\n  });\n\n  // ======================= Display Change =======================\n  // BaseSelect display values change\n  var onDisplayValuesChange = function onDisplayValuesChange(nextValues, info) {\n    triggerChange(nextValues);\n    var type = info.type,\n      values = info.values;\n    if (type === 'remove' || type === 'clear') {\n      values.forEach(function (item) {\n        triggerSelect(item.value, false, type);\n      });\n    }\n  };\n\n  // =========================== Search ===========================\n  var onInternalSearch = function onInternalSearch(searchText, info) {\n    setSearchValue(searchText);\n    setActiveValue(null);\n\n    // [Submit] Tag mode should flush input\n    if (info.source === 'submit') {\n      var formatted = (searchText || '').trim();\n      // prevent empty tags from appearing when you click the Enter button\n      if (formatted) {\n        var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), [formatted])));\n        triggerChange(newRawValues);\n        triggerSelect(formatted, true);\n        setSearchValue('');\n      }\n      return;\n    }\n    if (info.source !== 'blur') {\n      if (mode === 'combobox') {\n        triggerChange(searchText);\n      }\n      onSearch === null || onSearch === void 0 || onSearch(searchText);\n    }\n  };\n  var onInternalSearchSplit = function onInternalSearchSplit(words) {\n    var patchValues = words;\n    if (mode !== 'tags') {\n      patchValues = words.map(function (word) {\n        var opt = labelOptions.get(word);\n        return opt === null || opt === void 0 ? void 0 : opt.value;\n      }).filter(function (val) {\n        return val !== undefined;\n      });\n    }\n    var newRawValues = Array.from(new Set([].concat(_toConsumableArray(rawValues), _toConsumableArray(patchValues))));\n    triggerChange(newRawValues);\n    newRawValues.forEach(function (newRawValue) {\n      triggerSelect(newRawValue, true);\n    });\n  };\n\n  // ========================== Context ===========================\n  var selectContext = React.useMemo(function () {\n    var realVirtual = virtual !== false && dropdownMatchSelectWidth !== false;\n    return _objectSpread(_objectSpread({}, parsedOptions), {}, {\n      flattenOptions: displayOptions,\n      onActiveValue: onActiveValue,\n      defaultActiveFirstOption: mergedDefaultActiveFirstOption,\n      onSelect: onInternalSelect,\n      menuItemSelectedIcon: menuItemSelectedIcon,\n      rawValues: rawValues,\n      fieldNames: mergedFieldNames,\n      virtual: realVirtual,\n      direction: direction,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      childrenAsData: childrenAsData,\n      maxCount: maxCount,\n      optionRender: optionRender\n    });\n  }, [maxCount, parsedOptions, displayOptions, onActiveValue, mergedDefaultActiveFirstOption, onInternalSelect, menuItemSelectedIcon, rawValues, mergedFieldNames, virtual, dropdownMatchSelectWidth, direction, listHeight, listItemHeight, childrenAsData, optionRender]);\n\n  // ========================== Warning ===========================\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n    warningNullOptions(mergedOptions, mergedFieldNames);\n  }\n\n  // ==============================================================\n  // ==                          Render                          ==\n  // ==============================================================\n  return /*#__PURE__*/React.createElement(SelectContext.Provider, {\n    value: selectContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({}, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    ref: ref,\n    omitDomProps: OMIT_DOM_PROPS,\n    mode: mode\n    // >>> Values\n    ,\n    displayValues: displayValues,\n    onDisplayValuesChange: onDisplayValuesChange\n    // >>> Trigger\n    ,\n    direction: direction\n    // >>> Search\n    ,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch,\n    autoClearSearchValue: autoClearSearchValue,\n    onSearchSplit: onInternalSearchSplit,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n    // >>> OptionList\n    ,\n    OptionList: OptionList,\n    emptyOptions: !displayOptions.length\n    // >>> Accessibility\n    ,\n    activeValue: activeValue,\n    activeDescendantId: \"\".concat(mergedId, \"_list_\").concat(accessibilityIndex)\n  })));\n});\nif (process.env.NODE_ENV !== 'production') {\n  Select.displayName = 'Select';\n}\nvar TypedSelect = Select;\nTypedSelect.Option = Option;\nTypedSelect.OptGroup = OptGroup;\nexport default TypedSelect;"], "names": [], "mappings": ";;;AAqKY;AArKZ;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA6BC,GAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AA/CA,IAAI,YAAY;IAAC;IAAM;IAAQ;IAAa;IAAY;IAAc;IAAc;IAAe;IAAY;IAAwB;IAAY;IAAc;IAA4B;IAAgB;IAAc;IAAoB;IAAmB;IAAW;IAAgB;IAAY;IAA4B;IAAwB;IAAW;IAAa;IAAc;IAAkB;IAAe;IAAS;IAAgB;IAAgB;IAAY;CAAW;;;;;;;;;;;;;;;;;AAgDpe,IAAI,iBAAiB;IAAC;CAAa;AACnC,SAAS,WAAW,KAAK;IACvB,OAAO,CAAC,SAAS,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,WAAW;AACtC;AACA,IAAI,SAAS,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IAC7D,IAAI,KAAK,MAAM,EAAE,EACf,OAAO,MAAM,IAAI,EACjB,mBAAmB,MAAM,SAAS,EAClC,YAAY,qBAAqB,KAAK,IAAI,cAAc,kBACxD,WAAW,MAAM,QAAQ,EACzB,aAAa,MAAM,UAAU,EAC7B,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW,EAC/B,WAAW,MAAM,QAAQ,EACzB,wBAAwB,MAAM,oBAAoB,EAClD,uBAAuB,0BAA0B,KAAK,IAAI,OAAO,uBACjE,WAAW,MAAM,QAAQ,EACzB,aAAa,MAAM,UAAU,EAC7B,wBAAwB,MAAM,wBAAwB,EACtD,2BAA2B,0BAA0B,KAAK,IAAI,OAAO,uBACrE,eAAe,MAAM,YAAY,EACjC,aAAa,MAAM,UAAU,EAC7B,mBAAmB,MAAM,gBAAgB,EACzC,kBAAkB,MAAM,eAAe,EACvC,UAAU,MAAM,OAAO,EACvB,eAAe,MAAM,YAAY,EACjC,WAAW,MAAM,QAAQ,EACzB,2BAA2B,MAAM,wBAAwB,EACzD,uBAAuB,MAAM,oBAAoB,EACjD,UAAU,MAAM,OAAO,EACvB,YAAY,MAAM,SAAS,EAC3B,oBAAoB,MAAM,UAAU,EACpC,aAAa,sBAAsB,KAAK,IAAI,MAAM,mBAClD,wBAAwB,MAAM,cAAc,EAC5C,iBAAiB,0BAA0B,KAAK,IAAI,KAAK,uBACzD,cAAc,MAAM,WAAW,EAC/B,QAAQ,MAAM,KAAK,EACnB,eAAe,MAAM,YAAY,EACjC,eAAe,MAAM,YAAY,EACjC,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ,EACzB,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,OAAO;IAC9C,IAAI,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAK,AAAD,EAAE;IACrB,IAAI,WAAW,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE;IAC1B,IAAI,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,QAAQ;IAC5C,IAAI,qBAAqB,6JAAA,CAAA,UAAa;8CAAC;YACrC,IAAI,iBAAiB,aAAa,SAAS,YAAY;gBACrD,OAAO;YACT;YACA,OAAO;QACT;6CAAG;QAAC;QAAc;KAAK;IAEvB,iEAAiE;IACjE,IAAI,mBAAmB,6JAAA,CAAA,UAAa;4CAAC;YACnC,OAAO,CAAA,GAAA,2JAAA,CAAA,iBAAc,AAAD,EAAE,YAAY;QACpC;2CAAG,8CAA8C,GACjD;QACA,2DAA2D;QAC3D,KAAK,SAAS,CAAC;QAAa;KAAe;IAG3C,iEAAiE;IACjE,IAAI,kBAAkB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD,EAAE,IAAI;QACrC,OAAO,gBAAgB,YAAY,cAAc;QACjD,WAAW,SAAS,UAAU,MAAM;YAClC,OAAO,UAAU;QACnB;IACF,IACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,oBAAoB,gBAAgB,CAAC,EAAE,EACvC,iBAAiB,gBAAgB,CAAC,EAAE;IAEtC,iEAAiE;IACjE,IAAI,gBAAgB,CAAA,GAAA,4JAAA,CAAA,UAAU,AAAD,EAAE,SAAS,UAAU,kBAAkB,kBAAkB;IACtF,IAAI,eAAe,cAAc,YAAY,EAC3C,eAAe,cAAc,YAAY,EACzC,gBAAgB,cAAc,OAAO;IAEvC,iEAAiE;IACjE,IAAI,sBAAsB,6JAAA,CAAA,cAAiB;mDAAC,SAAU,WAAW;YAC/D,mBAAmB;YACnB,IAAI,YAAY,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD,EAAE;YAExB,+BAA+B;YAC/B,OAAO,UAAU,GAAG;2DAAC,SAAU,GAAG;oBAChC,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBAEJ,qBAAqB;oBACrB,IAAI,WAAW,MAAM;wBACnB,WAAW;oBACb,OAAO;wBACL,IAAI;wBACJ,SAAS,IAAI,GAAG;wBAChB,WAAW,IAAI,KAAK;wBACpB,WAAW,CAAC,aAAa,IAAI,KAAK,MAAM,QAAQ,eAAe,KAAK,IAAI,aAAa;oBACvF;oBACA,IAAI,SAAS,aAAa,GAAG,CAAC;oBAC9B,IAAI,QAAQ;wBACV,IAAI;wBACJ,qBAAqB;wBACrB,IAAI,aAAa,WAAW,WAAW,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,mBAAmB,iBAAiB,KAAK,CAAC;wBACxI,IAAI,WAAW,WAAW,SAAS,CAAC,cAAc,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG,MAAM,QAAQ,gBAAgB,KAAK,IAAI,cAAc;wBACjK,cAAc,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ;wBAC7E,WAAW,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK;wBAEvE,wCAAwC;wBACxC,IAAI,oDAAyB,gBAAgB,CAAC,iBAAiB;4BAC7D,IAAI,cAAc,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,iBAAiB,KAAK,CAAC;4BAChG,IAAI,gBAAgB,aAAa,CAAE,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,gBAAgB,CAAE,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,aAAa,gBAAgB,UAAU;gCAC9J,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;4BACjB;wBACF;oBACF;oBACA,OAAO;wBACL,OAAO;wBACP,OAAO;wBACP,KAAK;wBACL,UAAU;wBACV,OAAO;oBACT;gBACF;;QACF;kDAAG;QAAC;QAAkB;QAAiB;KAAa;IAEpD,iEAAiE;IACjE,IAAI,mBAAmB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD,EAAE,cAAc;QAChD,OAAO;IACT,IACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,gBAAgB,gBAAgB,CAAC,EAAE,EACnC,mBAAmB,gBAAgB,CAAC,EAAE;IAExC,mCAAmC;IACnC,IAAI,mBAAmB,6JAAA,CAAA,UAAa;4CAAC;YACnC,IAAI;YACJ,IAAI,mBAAmB,YAAY,kBAAkB,OAAO,EAAE,GAAG;YACjE,IAAI,SAAS,oBAAoB;YAEjC,yEAAyE;YACzE,IAAI,SAAS,cAAc,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,WAAW,MAAM,CAAC,EAAE,MAAM,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,KAAK,GAAG;gBAC3H,OAAO,EAAE;YACX;YACA,OAAO;QACT;2CAAG;QAAC;QAAe;QAAqB;QAAM;KAAS;IAEvD,+CAA+C;IAC/C,IAAI,YAAY,CAAA,GAAA,0JAAA,CAAA,UAAQ,AAAD,EAAE,kBAAkB,eACzC,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,eAAe,UAAU,CAAC,EAAE,EAC5B,iBAAiB,UAAU,CAAC,EAAE;IAChC,IAAI,gBAAgB,6JAAA,CAAA,UAAa;yCAAC;YAChC,0CAA0C;YAC1C,wDAAwD;YACxD,IAAI,CAAC,QAAQ,aAAa,MAAM,KAAK,GAAG;gBACtC,IAAI,aAAa,YAAY,CAAC,EAAE;gBAChC,IAAI,WAAW,KAAK,KAAK,QAAQ,CAAC,WAAW,KAAK,KAAK,QAAQ,WAAW,KAAK,KAAK,SAAS,GAAG;oBAC9F,OAAO,EAAE;gBACX;YACF;YACA,OAAO,aAAa,GAAG;iDAAC,SAAU,IAAI;oBACpC,IAAI;oBACJ,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG;wBAChD,OAAO,CAAC,OAAO,OAAO,gBAAgB,aAAa,YAAY,QAAQ,KAAK,KAAK,MAAM,QAAQ,SAAS,KAAK,IAAI,OAAO,KAAK,KAAK;oBACpI;gBACF;;QACF;wCAAG;QAAC;QAAM;QAAc;KAAY;IAEpC,kDAAkD,GAClD,IAAI,YAAY,6JAAA,CAAA,UAAa;qCAAC;YAC5B,OAAO,IAAI,IAAI,aAAa,GAAG;6CAAC,SAAU,GAAG;oBAC3C,OAAO,IAAI,KAAK;gBAClB;;QACF;oCAAG;QAAC;KAAa;IACjB,6JAAA,CAAA,YAAe;4BAAC;YACd,IAAI,SAAS,YAAY;gBACvB,IAAI;gBACJ,IAAI,WAAW,CAAC,iBAAiB,YAAY,CAAC,EAAE,MAAM,QAAQ,mBAAmB,KAAK,IAAI,KAAK,IAAI,eAAe,KAAK;gBACvH,eAAe,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,OAAO,YAAY;YACzD;QACF;2BAAG;QAAC;KAAa;IAEjB,iEAAiE;IACjE,sDAAsD;IACtD,IAAI,kBAAkB,CAAA,GAAA,4JAAA,CAAA,UAAU,AAAD;8CAAE,SAAU,GAAG,EAAE,KAAK;YACnD,IAAI,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,QAAQ;YAC/D,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,CAAC,GAAG,iBAAiB,KAAK,EAAE,MAAM,iBAAiB,KAAK,EAAE;QACnG;;IAEA,uCAAuC;IACvC,IAAI,mBAAmB,6JAAA,CAAA,UAAa;4CAAC;YACnC,IAAI,SAAS,QAAQ;gBACnB,OAAO;YACT;YAEA,eAAe;YACf,IAAI,eAAe,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;YAEtC,2DAA2D;YAC3D,IAAI,eAAe,SAAS,aAAa,GAAG;gBAC1C,OAAO,aAAa,GAAG,CAAC;YAC1B;YAEA,+BAA+B;YAC/B,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,cAAc,IAAI;oDAAC,SAAU,CAAC,EAAE,CAAC;oBAClD,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,CAAC,IAAI;gBAClC;mDAAG,OAAO;oDAAC,SAAU,IAAI;oBACvB,IAAI,MAAM,KAAK,KAAK;oBACpB,IAAI,CAAC,aAAa,MAAM;wBACtB,aAAa,IAAI,CAAC,gBAAgB,KAAK,KAAK,KAAK;oBACnD;gBACF;;YACA,OAAO;QACT;2CAAG;QAAC;QAAiB;QAAe;QAAc;QAAc;KAAK;IACrE,IAAI,kBAAkB,CAAA,GAAA,kKAAA,CAAA,UAAgB,AAAD,EAAE,kBAAkB,kBAAkB,mBAAmB,oBAAoB;IAElH,2CAA2C;IAC3C,IAAI,sBAAsB,6JAAA,CAAA,UAAa;+CAAC;YACtC,IAAI,SAAS,UAAU,CAAC,qBAAqB,gBAAgB,IAAI;uDAAC,SAAU,IAAI;oBAC9E,OAAO,IAAI,CAAC,oBAAoB,QAAQ,KAAK;gBAC/C;uDAAI;gBACF,OAAO;YACT;YACA,oDAAoD;YACpD,IAAI,gBAAgB,IAAI;uDAAC,SAAU,IAAI;oBACrC,OAAO,IAAI,CAAC,iBAAiB,KAAK,CAAC,KAAK;gBAC1C;uDAAI;gBACF,OAAO;YACT;YACA,8BAA8B;YAC9B,OAAO;gBAAC,gBAAgB;aAAmB,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;QACxE;8CAAG;QAAC;QAAiB;QAAkB;QAAM;QAAiB;QAAmB;KAAiB;IAClG,IAAI,SAAS,SAAS,OAAO,YAAY;QACvC,IAAI,gBAAgB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,cAAc,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;YACtE,OAAO,WAAW,GAAG,GAAG;gBACtB,aAAa;YACf;QACF;QACA,OAAO,cAAc,GAAG,CAAC,SAAU,IAAI;YACrC,IAAI,MAAM,OAAO,CAAC,KAAK,OAAO,GAAG;gBAC/B,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG;oBAChD,SAAS,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI,OAAO,KAAK,OAAO,IAAI,KAAK,OAAO;gBACxE;YACF;YACA,OAAO;QACT;IACF;IACA,IAAI,yBAAyB,6JAAA,CAAA,UAAa;kDAAC;YACzC,IAAI,CAAC,YAAY;gBACf,OAAO;YACT;YACA,OAAO,OAAO;QAChB;iDAAG;QAAC;QAAqB;QAAY;KAAkB;IACvD,IAAI,iBAAiB,6JAAA,CAAA,UAAa;0CAAC;YACjC,OAAO,CAAA,GAAA,2JAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB;gBAC5C,YAAY;gBACZ,gBAAgB;YAClB;QACF;yCAAG;QAAC;QAAwB;QAAkB;KAAe;IAE7D,iEAAiE;IACjE,IAAI,gBAAgB,SAAS,cAAc,MAAM;QAC/C,IAAI,gBAAgB,oBAAoB;QACxC,iBAAiB;QACjB,IAAI,YAAY,CAChB,wCAAwC;QACxC,cAAc,MAAM,KAAK,aAAa,MAAM,IAAI,cAAc,IAAI,CAAC,SAAU,MAAM,EAAE,KAAK;YACxF,IAAI;YACJ,OAAO,CAAC,CAAC,sBAAsB,YAAY,CAAC,MAAM,MAAM,QAAQ,wBAAwB,KAAK,IAAI,KAAK,IAAI,oBAAoB,KAAK,MAAM,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK;QACxM,EAAE,GAAG;YACH,IAAI,eAAe,eAAe,gBAAgB,cAAc,GAAG,CAAC,SAAU,CAAC;gBAC7E,OAAO,EAAE,KAAK;YAChB;YACA,IAAI,gBAAgB,cAAc,GAAG,CAAC,SAAU,CAAC;gBAC/C,OAAO,CAAA,GAAA,2JAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,EAAE,KAAK;YACrD;YACA,SACA,QAAQ;YACR,WAAW,eAAe,YAAY,CAAC,EAAE,EACzC,SAAS;YACT,WAAW,gBAAgB,aAAa,CAAC,EAAE;QAC7C;IACF;IAEA,iEAAiE;IACjE,IAAI,kBAAkB,6JAAA,CAAA,WAAc,CAAC,OACnC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,cAAc,gBAAgB,CAAC,EAAE,EACjC,iBAAiB,gBAAgB,CAAC,EAAE;IACtC,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,IACpC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,qBAAqB,gBAAgB,CAAC,EAAE,EACxC,wBAAwB,gBAAgB,CAAC,EAAE;IAC7C,IAAI,iCAAiC,6BAA6B,YAAY,2BAA2B,SAAS;IAClH,IAAI,gBAAgB,6JAAA,CAAA,cAAiB;6CAAC,SAAU,MAAM,EAAE,KAAK;YAC3D,IAAI,QAAQ,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG,CAAC,GAC/E,eAAe,MAAM,MAAM,EAC3B,SAAS,iBAAiB,KAAK,IAAI,aAAa;YAClD,sBAAsB;YACtB,IAAI,YAAY,SAAS,cAAc,WAAW,QAAQ,WAAW,YAAY;gBAC/E,eAAe,OAAO;YACxB;QACF;4CAAG;QAAC;QAAU;KAAK;IAEnB,iEAAiE;IACjE,IAAI,gBAAgB,SAAS,cAAc,GAAG,EAAE,QAAQ,EAAE,IAAI;QAC5D,IAAI,eAAe,SAAS;YAC1B,IAAI;YACJ,IAAI,SAAS,eAAe;YAC5B,OAAO;gBAAC,eAAe;oBACrB,OAAO,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,iBAAiB,KAAK,CAAC;oBACrF,OAAO;oBACP,KAAK,CAAC,eAAe,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe;gBACxI,IAAI;gBAAK,CAAA,GAAA,2JAAA,CAAA,wBAAqB,AAAD,EAAE;aAAQ;QACzC;QACA,IAAI,YAAY,UAAU;YACxB,IAAI,gBAAgB,gBAClB,iBAAiB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,eAAe,IAC/C,eAAe,cAAc,CAAC,EAAE,EAChC,UAAU,cAAc,CAAC,EAAE;YAC7B,SAAS,cAAc;QACzB,OAAO,IAAI,CAAC,YAAY,cAAc,SAAS,SAAS;YACtD,IAAI,iBAAiB,gBACnB,iBAAiB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,gBAAgB,IAChD,gBAAgB,cAAc,CAAC,EAAE,EACjC,WAAW,cAAc,CAAC,EAAE;YAC9B,WAAW,eAAe;QAC5B;IACF;IAEA,gCAAgC;IAChC,IAAI,mBAAmB,CAAA,GAAA,4JAAA,CAAA,UAAU,AAAD;+CAAE,SAAU,GAAG,EAAE,IAAI;YACnD,IAAI;YAEJ,0DAA0D;YAC1D,IAAI,eAAe,WAAW,KAAK,QAAQ,GAAG;YAC9C,IAAI,cAAc;gBAChB,cAAc,WAAW,EAAE,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,eAAe;oBAAC;iBAAI,IAAI;oBAAC;iBAAI;YACrF,OAAO;gBACL,cAAc,aAAa,MAAM;2DAAC,SAAU,CAAC;wBAC3C,OAAO,EAAE,KAAK,KAAK;oBACrB;;YACF;YACA,cAAc;YACd,cAAc,KAAK;YAEnB,6CAA6C;YAC7C,IAAI,SAAS,YAAY;gBACvB,+BAA+B;gBAC/B,eAAe;YACjB,OAAO,IAAI,CAAC,4JAAA,CAAA,aAAU,IAAI,sBAAsB;gBAC9C,eAAe;gBACf,eAAe;YACjB;QACF;;IAEA,iEAAiE;IACjE,mCAAmC;IACnC,IAAI,wBAAwB,SAAS,sBAAsB,UAAU,EAAE,IAAI;QACzE,cAAc;QACd,IAAI,OAAO,KAAK,IAAI,EAClB,SAAS,KAAK,MAAM;QACtB,IAAI,SAAS,YAAY,SAAS,SAAS;YACzC,OAAO,OAAO,CAAC,SAAU,IAAI;gBAC3B,cAAc,KAAK,KAAK,EAAE,OAAO;YACnC;QACF;IACF;IAEA,iEAAiE;IACjE,IAAI,mBAAmB,SAAS,iBAAiB,UAAU,EAAE,IAAI;QAC/D,eAAe;QACf,eAAe;QAEf,uCAAuC;QACvC,IAAI,KAAK,MAAM,KAAK,UAAU;YAC5B,IAAI,YAAY,CAAC,cAAc,EAAE,EAAE,IAAI;YACvC,oEAAoE;YACpE,IAAI,WAAW;gBACb,IAAI,eAAe,MAAM,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,YAAY;oBAAC;iBAAU;gBAC1F,cAAc;gBACd,cAAc,WAAW;gBACzB,eAAe;YACjB;YACA;QACF;QACA,IAAI,KAAK,MAAM,KAAK,QAAQ;YAC1B,IAAI,SAAS,YAAY;gBACvB,cAAc;YAChB;YACA,aAAa,QAAQ,aAAa,KAAK,KAAK,SAAS;QACvD;IACF;IACA,IAAI,wBAAwB,SAAS,sBAAsB,KAAK;QAC9D,IAAI,cAAc;QAClB,IAAI,SAAS,QAAQ;YACnB,cAAc,MAAM,GAAG,CAAC,SAAU,IAAI;gBACpC,IAAI,MAAM,aAAa,GAAG,CAAC;gBAC3B,OAAO,QAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK;YAC5D,GAAG,MAAM,CAAC,SAAU,GAAG;gBACrB,OAAO,QAAQ;YACjB;QACF;QACA,IAAI,eAAe,MAAM,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,YAAY,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;QAClG,cAAc;QACd,aAAa,OAAO,CAAC,SAAU,WAAW;YACxC,cAAc,aAAa;QAC7B;IACF;IAEA,iEAAiE;IACjE,IAAI,gBAAgB,6JAAA,CAAA,UAAa;yCAAC;YAChC,IAAI,cAAc,YAAY,SAAS,6BAA6B;YACpE,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,gBAAgB,CAAC,GAAG;gBACzD,gBAAgB;gBAChB,eAAe;gBACf,0BAA0B;gBAC1B,UAAU;gBACV,sBAAsB;gBACtB,WAAW;gBACX,YAAY;gBACZ,SAAS;gBACT,WAAW;gBACX,YAAY;gBACZ,gBAAgB;gBAChB,gBAAgB;gBAChB,UAAU;gBACV,cAAc;YAChB;QACF;wCAAG;QAAC;QAAU;QAAe;QAAgB;QAAe;QAAgC;QAAkB;QAAsB;QAAW;QAAkB;QAAS;QAA0B;QAAW;QAAY;QAAgB;QAAgB;KAAa;IAExQ,iEAAiE;IACjE,wCAA2C;QACzC,CAAA,GAAA,kKAAA,CAAA,UAAY,AAAD,EAAE;QACb,CAAA,GAAA,kKAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe;IACpC;IAEA,iEAAiE;IACjE,iEAAiE;IACjE,iEAAiE;IACjE,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,sJAAA,CAAA,UAAa,CAAC,QAAQ,EAAE;QAC9D,OAAO;IACT,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,UAAU,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,WAAW;QACtE,WAAW;QACX,IAAI;QACJ,WAAW;QACX,KAAK;QACL,cAAc;QACd,MAAM;QAGN,eAAe;QACf,uBAAuB;QAGvB,WAAW;QAGX,aAAa;QACb,UAAU;QACV,sBAAsB;QACtB,eAAe;QACf,0BAA0B;QAG1B,YAAY,mJAAA,CAAA,UAAU;QACtB,cAAc,CAAC,eAAe,MAAM;QAGpC,aAAa;QACb,oBAAoB,GAAG,MAAM,CAAC,UAAU,UAAU,MAAM,CAAC;IAC3D;AACF;AACA,wCAA2C;IACzC,OAAO,WAAW,GAAG;AACvB;AACA,IAAI,cAAc;AAClB,YAAY,MAAM,GAAG,+IAAA,CAAA,UAAM;AAC3B,YAAY,QAAQ,GAAG,iJAAA,CAAA,UAAQ;uCAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3343, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-select/es/index.js"], "sourcesContent": ["import Select from \"./Select\";\nimport Option from \"./Option\";\nimport OptGroup from \"./OptGroup\";\nimport BaseSelect from \"./BaseSelect\";\nimport useBaseProps from \"./hooks/useBaseProps\";\nexport { Option, OptGroup, BaseSelect, useBaseProps };\nexport default Select;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;;uCAEe,+IAAA,CAAA,UAAM", "ignoreList": [0], "debugId": null}}]}