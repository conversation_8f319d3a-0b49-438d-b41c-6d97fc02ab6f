# AI小说生成工作流平台 - 测试指南

## 功能测试清单

### 1. 项目管理功能测试

#### 1.1 项目创建
- [ ] 点击"创建新项目"按钮
- [ ] 填写项目基本信息（名称、描述、类型、风格、字数、章节数）
- [ ] 验证表单验证功能
- [ ] 确认项目创建成功并显示在列表中

#### 1.2 项目编辑
- [ ] 点击项目卡片的"编辑"按钮
- [ ] 修改项目信息
- [ ] 确认修改保存成功

#### 1.3 项目选择
- [ ] 点击项目卡片的"选择"按钮
- [ ] 确认当前项目状态更新
- [ ] 验证侧边栏显示当前项目信息

#### 1.4 项目删除
- [ ] 点击项目卡片的"删除"按钮
- [ ] 确认删除确认对话框
- [ ] 验证项目从列表中移除

### 2. 工作流编辑器功能测试

#### 2.1 节点操作
- [ ] 从左侧节点库添加不同类型的节点
- [ ] 验证节点在画布中正确显示
- [ ] 测试节点选择和属性显示
- [ ] 测试节点删除功能

#### 2.2 工作流执行
- [ ] 点击"开始执行"按钮
- [ ] 验证执行状态显示
- [ ] 测试暂停和停止功能
- [ ] 确认执行进度更新

#### 2.3 工作流保存
- [ ] 点击"保存工作流"按钮
- [ ] 验证保存成功提示
- [ ] 刷新页面确认工作流持久化

### 3. 角色管理功能测试

#### 3.1 角色创建
- [ ] 点击"创建角色"按钮
- [ ] 使用角色模板快速创建
- [ ] 手动填写角色信息
- [ ] 验证角色保存成功

#### 3.2 角色编辑
- [ ] 点击角色卡片的编辑按钮
- [ ] 修改角色信息
- [ ] 确认修改保存成功

#### 3.3 角色详情查看
- [ ] 点击角色卡片查看详情
- [ ] 验证详情模态框显示完整信息
- [ ] 测试详情页面的编辑功能

### 4. 世界观管理功能测试

#### 4.1 世界观元素创建
- [ ] 使用快速创建模板
- [ ] 手动创建世界观元素
- [ ] 测试不同分类的元素创建

#### 4.2 分类过滤
- [ ] 点击不同分类标签
- [ ] 验证过滤结果正确
- [ ] 测试"全部"分类显示

#### 4.3 元素编辑和删除
- [ ] 编辑世界观元素
- [ ] 删除世界观元素
- [ ] 验证操作成功

### 5. 大纲管理功能测试

#### 5.1 大纲创建
- [ ] 创建主大纲和详细大纲
- [ ] 验证大纲类型区分
- [ ] 测试大纲版本管理

#### 5.2 大纲结构编辑
- [ ] 添加大纲节点
- [ ] 编辑节点信息
- [ ] 测试节点层级关系

#### 5.3 进度跟踪
- [ ] 验证完成进度计算
- [ ] 测试状态更新
- [ ] 确认进度条显示正确

### 6. 主线管理功能测试

#### 6.1 故事线创建
- [ ] 创建主线和支线
- [ ] 填写故事线信息
- [ ] 验证保存成功

#### 6.2 时间轴功能
- [ ] 查看故事时间轴
- [ ] 测试时间轴过滤
- [ ] 验证事件排序

#### 6.3 冲突管理
- [ ] 查看冲突分析
- [ ] 验证冲突分类显示
- [ ] 测试冲突信息展示

### 7. 书名管理功能测试

#### 7.1 AI书名生成
- [ ] 打开书名生成器
- [ ] 设置生成参数
- [ ] 验证书名生成成功
- [ ] 测试生成数量控制

#### 7.2 书名管理
- [ ] 收藏/取消收藏书名
- [ ] 测试排序功能
- [ ] 使用过滤功能

#### 7.3 书名分析
- [ ] 查看书名详细分析
- [ ] 验证评分显示
- [ ] 测试复制功能

### 8. 文档管理功能测试

#### 8.1 文档结构浏览
- [ ] 查看项目文档树
- [ ] 点击文件夹展开/收起
- [ ] 选择文件查看

#### 8.2 文档操作
- [ ] 测试导入文档功能
- [ ] 测试导出项目功能
- [ ] 验证文档预览

### 9. 提示词管理功能测试

#### 9.1 模板管理
- [ ] 查看提示词模板列表
- [ ] 测试分类过滤
- [ ] 复制模板内容

#### 9.2 模板创建编辑
- [ ] 创建新模板
- [ ] 编辑现有模板
- [ ] 验证变量定义

### 10. 用户界面测试

#### 10.1 主题切换
- [ ] 点击主题切换按钮
- [ ] 验证亮色/暗色主题切换
- [ ] 确认主题设置持久化

#### 10.2 侧边栏功能
- [ ] 测试侧边栏折叠/展开
- [ ] 验证菜单导航功能
- [ ] 测试当前项目信息显示

#### 10.3 响应式设计
- [ ] 在不同屏幕尺寸下测试
- [ ] 验证移动端适配
- [ ] 测试触摸操作

## 性能测试

### 1. 加载性能
- [ ] 测试首页加载时间
- [ ] 验证大量数据加载性能
- [ ] 测试页面切换速度

### 2. 内存使用
- [ ] 长时间使用后检查内存占用
- [ ] 测试大型工作流性能
- [ ] 验证内存泄漏

### 3. 数据持久化
- [ ] 刷新页面后数据保持
- [ ] 关闭浏览器重新打开
- [ ] 测试数据导入导出

## 兼容性测试

### 1. 浏览器兼容性
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (最新版本)
- [ ] Edge (最新版本)

### 2. 操作系统兼容性
- [ ] Windows 10/11
- [ ] macOS
- [ ] Linux (Ubuntu)

### 3. 设备兼容性
- [ ] 桌面电脑 (1920x1080)
- [ ] 笔记本电脑 (1366x768)
- [ ] 平板电脑 (iPad)
- [ ] 大屏显示器 (2560x1440)

## 错误处理测试

### 1. 表单验证
- [ ] 提交空表单
- [ ] 输入无效数据
- [ ] 超出字符限制

### 2. 网络错误
- [ ] 断网情况下的行为
- [ ] 网络延迟情况
- [ ] 请求超时处理

### 3. 数据错误
- [ ] 损坏的本地存储数据
- [ ] 版本不兼容的数据
- [ ] 大量数据处理

## 用户体验测试

### 1. 易用性
- [ ] 新用户首次使用体验
- [ ] 功能发现性
- [ ] 操作流程合理性

### 2. 反馈机制
- [ ] 成功操作提示
- [ ] 错误信息显示
- [ ] 加载状态指示

### 3. 帮助文档
- [ ] 用户手册完整性
- [ ] 功能说明准确性
- [ ] 示例和截图

## 自动化测试建议

### 1. 单元测试
```bash
# 安装测试依赖
npm install --save-dev @testing-library/react @testing-library/jest-dom jest-environment-jsdom

# 运行测试
npm test
```

### 2. 集成测试
```bash
# 安装 Cypress
npm install --save-dev cypress

# 运行 E2E 测试
npx cypress open
```

### 3. 性能测试
```bash
# 使用 Lighthouse
npm install -g lighthouse

# 运行性能测试
lighthouse http://localhost:3000
```

## 测试报告模板

### 测试环境
- 操作系统: 
- 浏览器: 
- 屏幕分辨率: 
- 测试时间: 

### 测试结果
- 通过测试: X/Y
- 发现问题: 
- 严重程度: 

### 问题详情
1. **问题描述**: 
   - 重现步骤: 
   - 预期结果: 
   - 实际结果: 
   - 严重程度: 高/中/低

### 建议改进
1. 功能改进建议
2. 性能优化建议
3. 用户体验改进建议

---

**注意**: 请在每次发布前完成完整的测试流程，确保所有功能正常工作。
