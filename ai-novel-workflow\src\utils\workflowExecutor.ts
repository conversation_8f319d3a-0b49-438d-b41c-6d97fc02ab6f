// 工作流执行引擎 - 所有功能调用AI模型
import { aiService } from './aiService';
import { message } from 'antd';

// 节点执行结果接口
interface NodeExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  duration?: number;
}

// 执行上下文接口
interface ExecutionContext {
  projectId: string;
  workflowId: string;
  variables: Record<string, any>;
  results: Record<string, any>;
  currentNode?: string;
}

// 工作流执行器类
export class WorkflowExecutor {
  private context: ExecutionContext;
  private onProgress?: (nodeId: string, status: 'running' | 'completed' | 'error', result?: any) => void;

  constructor(context: ExecutionContext, onProgress?: (nodeId: string, status: string, result?: any) => void) {
    this.context = context;
    this.onProgress = onProgress;
  }

  // 执行单个节点
  async executeNode(node: any): Promise<NodeExecutionResult> {
    const startTime = Date.now();
    this.context.currentNode = node.id;
    
    // 通知开始执行
    this.onProgress?.(node.id, 'running');

    try {
      let result: any;

      switch (node.data.type) {
        case 'input':
          result = await this.executeInputNode(node);
          break;
        case 'title-generator':
          result = await this.executeTitleGeneratorNode(node);
          break;
        case 'detail-generator':
          result = await this.executeDetailGeneratorNode(node);
          break;
        case 'character-creator':
          result = await this.executeCharacterCreatorNode(node);
          break;
        case 'worldbuilding':
          result = await this.executeWorldbuildingNode(node);
          break;
        case 'plotline-planner':
          result = await this.executePlotlinePlannerNode(node);
          break;
        case 'outline-generator':
          result = await this.executeOutlineGeneratorNode(node);
          break;
        case 'detailed-outline':
          result = await this.executeDetailedOutlineNode(node);
          break;
        case 'chapter-generator':
          result = await this.executeChapterGeneratorNode(node);
          break;
        case 'content-polisher':
          result = await this.executeContentPolisherNode(node);
          break;
        case 'consistency-checker':
          result = await this.executeConsistencyCheckerNode(node);
          break;
        case 'output':
          result = await this.executeOutputNode(node);
          break;
        default:
          throw new Error(`不支持的节点类型: ${node.data.type}`);
      }

      const duration = Date.now() - startTime;
      
      // 保存结果到上下文
      this.context.results[node.id] = result;
      
      // 通知执行完成
      this.onProgress?.(node.id, 'completed', result);

      return {
        success: true,
        data: result,
        duration
      };

    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      // 通知执行错误
      this.onProgress?.(node.id, 'error', error.message);

      return {
        success: false,
        error: error.message,
        duration
      };
    }
  }

  // 输入节点执行
  private async executeInputNode(node: any): Promise<any> {
    // 输入节点通常从用户界面获取数据
    const inputData = node.data.config || this.context.variables.userInput || {};
    
    return {
      type: 'input',
      data: inputData,
      timestamp: new Date().toISOString()
    };
  }

  // 书名生成节点执行
  private async executeTitleGeneratorNode(node: any): Promise<any> {
    // 检查AI配置
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    // 获取输入数据
    const inputData = this.getInputData(['input']);
    const config = node.data.config || {};

    const params = {
      genre: inputData.genre || config.genre || '现代都市',
      style: inputData.style || config.style || '轻松幽默',
      keywords: inputData.keywords || config.keywords || [],
      count: config.count || 5
    };

    const response = await aiService.generateTitles(params);
    
    if (!response.success) {
      throw new Error(`AI书名生成失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      message.success(`AI成功生成${result.titles?.length || 0}个书名候选`);
      return {
        type: 'titles',
        titles: result.titles || [],
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 详情生成节点执行
  private async executeDetailGeneratorNode(node: any): Promise<any> {
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    const inputData = this.getInputData(['input', 'title-generator']);
    const config = node.data.config || {};

    const params = {
      title: inputData.selectedTitle || inputData.titles?.[0]?.title || config.title || '未命名小说',
      genre: inputData.genre || config.genre || '现代都市',
      style: inputData.style || config.style || '轻松幽默',
      outline: inputData.outline,
      characters: inputData.characters
    };

    const response = await aiService.generateNovelDetails(params);
    
    if (!response.success) {
      throw new Error(`AI详情生成失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      message.success('AI成功生成小说详情');
      return {
        type: 'novel_details',
        details: result,
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 角色创建节点执行
  private async executeCharacterCreatorNode(node: any): Promise<any> {
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    const inputData = this.getInputData(['input']);
    const config = node.data.config || {};

    const params = {
      genre: inputData.genre || config.genre || '现代都市',
      role: config.role || '主角',
      background: config.background,
      personality: config.personality
    };

    const response = await aiService.generateCharacter(params);
    
    if (!response.success) {
      throw new Error(`AI角色生成失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      message.success(`AI成功生成角色: ${result.name}`);
      return {
        type: 'character',
        character: result,
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 世界观构建节点执行
  private async executeWorldbuildingNode(node: any): Promise<any> {
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    const inputData = this.getInputData(['input']);
    const config = node.data.config || {};

    const params = {
      genre: inputData.genre || config.genre || '现代都市',
      style: inputData.style || config.style || '轻松幽默',
      scope: config.scope || '中等规模',
      elements: config.elements || []
    };

    const response = await aiService.generateWorldbuilding(params);
    
    if (!response.success) {
      throw new Error(`AI世界观生成失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      message.success(`AI成功生成世界观: ${result.name}`);
      return {
        type: 'worldbuilding',
        worldbuilding: result,
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 主线规划节点执行
  private async executePlotlinePlannerNode(node: any): Promise<any> {
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    const inputData = this.getInputData(['input', 'character-creator', 'worldbuilding']);
    const config = node.data.config || {};

    const params = {
      genre: inputData.genre || config.genre || '现代都市',
      style: inputData.style || config.style || '轻松幽默',
      characters: this.getAllCharacters(inputData),
      worldbuilding: inputData.worldbuilding,
      themes: config.themes || []
    };

    const response = await aiService.generatePlotline(params);
    
    if (!response.success) {
      throw new Error(`AI主线规划失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      message.success(`AI成功生成主线规划: ${result.title}`);
      return {
        type: 'plotline',
        plotline: result,
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 获取输入数据的辅助方法
  private getInputData(nodeTypes: string[]): any {
    const data: any = {};
    
    for (const nodeId in this.context.results) {
      const result = this.context.results[nodeId];
      if (nodeTypes.some(type => result.type === type || nodeId.includes(type))) {
        Object.assign(data, result.data || result);
      }
    }
    
    return { ...this.context.variables, ...data };
  }

  // 大纲生成节点执行
  private async executeOutlineGeneratorNode(node: any): Promise<any> {
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    const inputData = this.getInputData(['input', 'character-creator', 'plotline-planner']);
    const config = node.data.config || {};

    const params = {
      genre: inputData.genre || config.genre || '现代都市',
      plotline: inputData.plotline || {},
      characters: this.getAllCharacters(inputData),
      targetLength: inputData.length || config.targetLength || '中篇',
      chapterCount: config.chapterCount
    };

    const response = await aiService.generateOutline(params);

    if (!response.success) {
      throw new Error(`AI大纲生成失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      message.success(`AI成功生成大纲，共${result.chapters?.length || 0}章`);
      return {
        type: 'outline',
        outline: result,
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 详细大纲节点执行
  private async executeDetailedOutlineNode(node: any): Promise<any> {
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    const inputData = this.getInputData(['outline-generator', 'character-creator', 'worldbuilding']);
    const config = node.data.config || {};

    const params = {
      outline: inputData.outline || {},
      characters: this.getAllCharacters(inputData),
      worldbuilding: inputData.worldbuilding,
      selectedChapters: config.selectedChapters
    };

    const response = await aiService.generateDetailedOutline(params);

    if (!response.success) {
      throw new Error(`AI详细大纲生成失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      message.success('AI成功生成详细大纲');
      return {
        type: 'detailed_outline',
        detailedOutline: result,
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 章节生成节点执行
  private async executeChapterGeneratorNode(node: any): Promise<any> {
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    const inputData = this.getInputData(['detailed-outline', 'outline-generator', 'character-creator', 'worldbuilding']);
    const config = node.data.config || {};

    const chapterNumber = config.chapterNumber || 1;
    const outline = inputData.detailedOutline || inputData.outline || {};
    const chapterOutline = outline.chapters?.[chapterNumber - 1] || {};

    const params = {
      chapterNumber,
      chapterOutline,
      characters: this.getAllCharacters(inputData),
      worldbuilding: inputData.worldbuilding,
      previousChapters: inputData.previousChapters || [],
      style: inputData.style || config.style || '轻松幽默',
      targetWordCount: config.targetWordCount || 2000
    };

    const response = await aiService.generateChapter(params);

    if (!response.success) {
      throw new Error(`AI章节生成失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      message.success(`AI成功生成第${chapterNumber}章，约${result.word_count || 0}字`);
      return {
        type: 'chapter',
        chapter: result,
        chapterNumber,
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 内容润色节点执行
  private async executeContentPolisherNode(node: any): Promise<any> {
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    const inputData = this.getInputData(['chapter-generator']);
    const config = node.data.config || {};

    const content = inputData.chapter?.content || config.content || '';
    if (!content) {
      throw new Error('没有找到需要润色的内容');
    }

    const params = {
      content,
      style: inputData.style || config.style || '轻松幽默',
      focusAreas: config.focusAreas || ['表达优化', '节奏调整'],
      targetAudience: config.targetAudience || '一般读者'
    };

    const response = await aiService.polishContent(params);

    if (!response.success) {
      throw new Error(`AI内容润色失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      message.success(`AI成功润色内容，改进评分: ${result.readability_score || 'N/A'}`);
      return {
        type: 'polished_content',
        polishedContent: result,
        originalContent: content,
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 一致性检查节点执行
  private async executeConsistencyCheckerNode(node: any): Promise<any> {
    if (!aiService.isConfigured()) {
      throw new Error('请先配置AI API');
    }

    const inputData = this.getInputData(['chapter-generator', 'character-creator', 'worldbuilding']);
    const config = node.data.config || {};

    const content = [];

    // 收集所有章节内容
    for (const nodeId in this.context.results) {
      const result = this.context.results[nodeId];
      if (result.type === 'chapter' && result.chapter?.content) {
        content.push(result.chapter.content);
      }
    }

    if (content.length === 0) {
      throw new Error('没有找到需要检查的内容');
    }

    const params = {
      content,
      characters: this.getAllCharacters(inputData),
      worldbuilding: inputData.worldbuilding,
      checkTypes: config.checkTypes || ['角色一致性', '情节一致性', '世界观一致性']
    };

    const response = await aiService.checkConsistency(params);

    if (!response.success) {
      throw new Error(`AI一致性检查失败: ${response.error}`);
    }

    try {
      const result = JSON.parse(response.data);
      const score = result.consistency_score || 0;
      const issueCount = result.issues?.length || 0;

      if (score >= 80) {
        message.success(`一致性检查完成，评分: ${score}分，发现${issueCount}个问题`);
      } else {
        message.warning(`一致性检查完成，评分: ${score}分，发现${issueCount}个问题`);
      }

      return {
        type: 'consistency_check',
        consistencyReport: result,
        params,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      throw new Error('AI返回数据格式错误');
    }
  }

  // 输出节点执行
  private async executeOutputNode(node: any): Promise<any> {
    const inputData = this.getInputData(['title-generator', 'detail-generator', 'chapter-generator', 'polished_content', 'consistency_check']);

    // 整理最终输出
    const output = {
      title: inputData.selectedTitle || inputData.titles?.[0]?.title || '未命名小说',
      details: inputData.details || {},
      chapters: [],
      consistencyReport: inputData.consistencyReport,
      metadata: {
        generatedAt: new Date().toISOString(),
        totalChapters: 0,
        totalWords: 0,
        aiGenerated: true
      }
    };

    // 收集所有章节
    for (const nodeId in this.context.results) {
      const result = this.context.results[nodeId];
      if (result.type === 'chapter') {
        output.chapters.push(result.chapter);
        output.metadata.totalWords += result.chapter.word_count || 0;
      }
    }

    output.metadata.totalChapters = output.chapters.length;

    message.success(`小说创作完成！共${output.metadata.totalChapters}章，约${output.metadata.totalWords}字`);

    return {
      type: 'final_output',
      output,
      timestamp: new Date().toISOString()
    };
  }

  // 获取所有角色的辅助方法
  private getAllCharacters(inputData: any): any[] {
    const characters = [];

    // 从角色创建节点获取角色
    if (inputData.character) {
      characters.push(inputData.character);
    }

    // 从其他来源获取角色
    if (inputData.characters && Array.isArray(inputData.characters)) {
      characters.push(...inputData.characters);
    }

    return characters;
  }
}

// 导出执行器工厂函数
export const createWorkflowExecutor = (
  context: ExecutionContext,
  onProgress?: (nodeId: string, status: string, result?: any) => void
) => {
  return new WorkflowExecutor(context, onProgress);
};
