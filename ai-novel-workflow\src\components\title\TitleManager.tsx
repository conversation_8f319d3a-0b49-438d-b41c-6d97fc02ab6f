'use client';

import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tabs,
  List,
  Tag,
  Rate,
  Progress,
  message,
  Row,
  Col,
  Divider,
  Tooltip,
  Badge,
  Slider,
  Switch
} from 'antd';
import {
  BookOutlined,
  PlusOutlined,
  HeartOutlined,
  HeartFilled,
  ThunderboltOutlined,
  StarOutlined,
  EyeOutlined,
  TrophyOutlined,
  FireOutlined,
  CheckCircleOutlined,
  ReloadOutlined,
  DownloadOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/store';
import type { BookTitle, TitleAnalysis } from '@/types';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const TitleManager: React.FC = () => {
  const {
    currentProject,
    bookTitles,
    addBookTitle,
    updateBookTitle,
    deleteBookTitle,
    toggleTitleFavorite,
    getBookTitles
  } = useAppStore();

  const [isGeneratorVisible, setIsGeneratorVisible] = useState(false);
  const [isAnalysisVisible, setIsAnalysisVisible] = useState(false);
  const [selectedTitle, setSelectedTitle] = useState<BookTitle | null>(null);
  const [activeTab, setActiveTab] = useState('titles');
  const [filterFavorites, setFilterFavorites] = useState(false);
  const [sortBy, setSortBy] = useState<'score' | 'created' | 'name'>('score');
  const [generatorForm] = Form.useForm();

  const projectTitles = currentProject ? getBookTitles(currentProject.id) : [];

  // 过滤和排序书名
  const filteredTitles = projectTitles
    .filter(title => !filterFavorites || title.isFavorite)
    .sort((a, b) => {
      switch (sortBy) {
        case 'score': return b.score - a.score;
        case 'created': return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'name': return a.title.localeCompare(b.title);
        default: return 0;
      }
    });

  // 模拟书名生成器
  const generateTitles = async (params: any) => {
    const { genre, style, keywords, count } = params;

    // 模拟生成的书名
    const sampleTitles = [
      '星辰大海的征途', '时光倒流的秘密', '梦境中的王国', '失落的古老传说',
      '永恒之光的守护者', '命运交响曲', '幻想世界的冒险', '心灵深处的呼唤',
      '未来科技的奇迹', '爱情与战争的史诗', '神秘力量的觉醒', '英雄的传奇故事'
    ];

    const generatedTitles = [];
    for (let i = 0; i < count; i++) {
      const title = sampleTitles[Math.floor(Math.random() * sampleTitles.length)];
      const analysis: TitleAnalysis = {
        appeal: Math.floor(Math.random() * 40) + 60,
        memorability: Math.floor(Math.random() * 40) + 60,
        genreMatch: Math.floor(Math.random() * 30) + 70,
        uniqueness: Math.floor(Math.random() * 50) + 50,
        marketability: Math.floor(Math.random() * 40) + 60,
        suggestions: [
          '考虑增加更具体的元素描述',
          '可以尝试更简洁的表达方式',
          '建议突出主角或核心冲突'
        ]
      };

      const score = Math.round((analysis.appeal + analysis.memorability + analysis.genreMatch + analysis.uniqueness + analysis.marketability) / 5);

      generatedTitles.push({
        title: `${title}${i > 0 ? ` ${i + 1}` : ''}`,
        genre,
        style,
        score,
        analysis,
        isFavorite: false,
        createdAt: new Date(),
      });
    }

    return generatedTitles;
  };

  const handleGenerate = async () => {
    try {
      const values = await generatorForm.validateFields();
      const generatedTitles = await generateTitles(values);

      if (currentProject) {
        generatedTitles.forEach(titleData => {
          addBookTitle(currentProject.id, titleData);
        });
        message.success(`成功生成 ${generatedTitles.length} 个书名`);
      }

      setIsGeneratorVisible(false);
      generatorForm.resetFields();
    } catch (error) {
      console.error('生成失败:', error);
    }
  };

  const handleToggleFavorite = (title: BookTitle) => {
    if (currentProject) {
      toggleTitleFavorite(currentProject.id, title.id);
      message.success(title.isFavorite ? '已取消收藏' : '已添加到收藏');
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'exception';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 90) return <TrophyOutlined className="text-yellow-500" />;
    if (score >= 80) return <StarOutlined className="text-blue-500" />;
    if (score >= 60) return <FireOutlined className="text-orange-500" />;
    return <EyeOutlined className="text-gray-500" />;
  };

  if (!currentProject) {
    return (
      <div className="p-8 text-center">
        <Title level={3}>请先选择或创建一个项目</Title>
        <Text type="secondary">
          您需要先在项目总览中创建或选择一个项目，然后才能管理书名。
        </Text>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Title level={2}>书名管理</Title>
          <Text type="secondary">管理书名生成和选择 - 项目: {currentProject.name}</Text>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<ThunderboltOutlined />}
            onClick={() => setIsGeneratorVisible(true)}
          >
            AI生成书名
          </Button>
        </Space>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'titles',
            label: (
              <span>
                <BookOutlined />
                书名库 ({filteredTitles.length})
              </span>
            ),
            children: (
              <div>
                {/* 过滤和排序控制 */}
                <Card className="mb-6">
                  <Row gutter={16} align="middle">
                    <Col span={6}>
                      <div className="flex items-center space-x-2">
                        <Text>只看收藏:</Text>
                        <Switch
                          checked={filterFavorites}
                          onChange={setFilterFavorites}
                          checkedChildren={<HeartFilled />}
                          unCheckedChildren={<HeartOutlined />}
                        />
                      </div>
                    </Col>
                    <Col span={6}>
                      <div className="flex items-center space-x-2">
                        <Text>排序方式:</Text>
                        <Select
                          value={sortBy}
                          onChange={setSortBy}
                          style={{ width: 120 }}
                        >
                          <Option value="score">评分</Option>
                          <Option value="created">创建时间</Option>
                          <Option value="name">书名</Option>
                        </Select>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div className="text-right">
                        <Space>
                          <Text type="secondary">
                            共 {projectTitles.length} 个书名，
                            收藏 {projectTitles.filter(t => t.isFavorite).length} 个
                          </Text>
                        </Space>
                      </div>
                    </Col>
                  </Row>
                </Card>

                {/* 书名列表 */}
                {filteredTitles.length === 0 ? (
                  <Card className="text-center py-12">
                    <BookOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                    <div className="mt-4">
                      <Text type="secondary">
                        {projectTitles.length === 0 ? '还没有任何书名' : '没有符合条件的书名'}
                      </Text>
                      <br />
                      <Text type="secondary">
                        {projectTitles.length === 0 ? '点击上方按钮使用AI生成书名' : '尝试调整过滤条件'}
                      </Text>
                    </div>
                  </Card>
                ) : (
                  <List
                    grid={{ gutter: 16, column: 2 }}
                    dataSource={filteredTitles}
                    renderItem={(title) => (
                      <List.Item>
                        <Card
                          className="hover:shadow-lg transition-shadow cursor-pointer"
                          onClick={() => {
                            setSelectedTitle(title);
                            setIsAnalysisVisible(true);
                          }}
                          actions={[
                            <Tooltip title={title.isFavorite ? "取消收藏" : "添加收藏"} key="favorite">
                              <Button
                                type="text"
                                icon={title.isFavorite ? <HeartFilled className="text-red-500" /> : <HeartOutlined />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleToggleFavorite(title);
                                }}
                              />
                            </Tooltip>,
                            <Tooltip title="查看分析" key="analysis">
                              <EyeOutlined onClick={(e) => {
                                e.stopPropagation();
                                setSelectedTitle(title);
                                setIsAnalysisVisible(true);
                              }} />
                            </Tooltip>,
                            <Tooltip title="分享" key="share">
                              <ShareAltOutlined onClick={(e) => {
                                e.stopPropagation();
                                navigator.clipboard.writeText(title.title);
                                message.success('书名已复制到剪贴板');
                              }} />
                            </Tooltip>
                          ]}
                        >
                          <div className="mb-3">
                            <div className="flex items-center justify-between mb-2">
                              <Title level={5} className="mb-0 flex-1 mr-2">
                                {title.title}
                              </Title>
                              <div className="flex items-center space-x-1">
                                {getScoreIcon(title.score)}
                                <Text strong className="text-lg">{title.score}</Text>
                              </div>
                            </div>

                            <div className="mb-3">
                              <Progress
                                percent={title.score}
                                size="small"
                                status={getScoreColor(title.score)}
                                showInfo={false}
                              />
                            </div>

                            <div className="flex items-center justify-between mb-2">
                              <Space>
                                <Tag color="blue">{title.genre}</Tag>
                                <Tag color="green">{title.style}</Tag>
                              </Space>
                              {title.isFavorite && (
                                <HeartFilled className="text-red-500" />
                              )}
                            </div>

                            <div className="text-sm text-gray-500">
                              创建于 {new Date(title.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                        </Card>
                      </List.Item>
                    )}
                  />
                )}
              </div>
            ),
          },
          {
            key: 'analytics',
            label: (
              <span>
                <StarOutlined />
                数据分析
              </span>
            ),
            children: (
              <div>
                <Row gutter={[16, 16]}>
                  <Col span={8}>
                    <Card title="评分分布">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Text>优秀 (80+)</Text>
                          <Badge count={projectTitles.filter(t => t.score >= 80).length} showZero />
                        </div>
                        <div className="flex items-center justify-between">
                          <Text>良好 (60-79)</Text>
                          <Badge count={projectTitles.filter(t => t.score >= 60 && t.score < 80).length} showZero />
                        </div>
                        <div className="flex items-center justify-between">
                          <Text>一般 (60以下)</Text>
                          <Badge count={projectTitles.filter(t => t.score < 60).length} showZero />
                        </div>
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card title="类型分布">
                      <div className="space-y-3">
                        {Array.from(new Set(projectTitles.map(t => t.genre))).map(genre => (
                          <div key={genre} className="flex items-center justify-between">
                            <Text>{genre}</Text>
                            <Badge count={projectTitles.filter(t => t.genre === genre).length} showZero />
                          </div>
                        ))}
                      </div>
                    </Card>
                  </Col>
                  <Col span={8}>
                    <Card title="收藏统计">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Text>已收藏</Text>
                          <Badge count={projectTitles.filter(t => t.isFavorite).length} showZero />
                        </div>
                        <div className="flex items-center justify-between">
                          <Text>未收藏</Text>
                          <Badge count={projectTitles.filter(t => !t.isFavorite).length} showZero />
                        </div>
                        <div className="flex items-center justify-between">
                          <Text>收藏率</Text>
                          <Text>
                            {projectTitles.length > 0
                              ? Math.round((projectTitles.filter(t => t.isFavorite).length / projectTitles.length) * 100)
                              : 0}%
                          </Text>
                        </div>
                      </div>
                    </Card>
                  </Col>
                </Row>
              </div>
            ),
          },
        ]}
      />

      {/* AI书名生成器模态框 */}
      <Modal
        title="AI书名生成器"
        open={isGeneratorVisible}
        onOk={handleGenerate}
        onCancel={() => {
          setIsGeneratorVisible(false);
          generatorForm.resetFields();
        }}
        width={600}
        okText="生成书名"
        cancelText="取消"
      >
        <Form form={generatorForm} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="genre"
                label="小说类型"
                rules={[{ required: true, message: '请选择小说类型' }]}
              >
                <Select placeholder="请选择类型">
                  <Option value="现代都市">现代都市</Option>
                  <Option value="古代言情">古代言情</Option>
                  <Option value="玄幻修仙">玄幻修仙</Option>
                  <Option value="科幻未来">科幻未来</Option>
                  <Option value="悬疑推理">悬疑推理</Option>
                  <Option value="历史军事">历史军事</Option>
                  <Option value="青春校园">青春校园</Option>
                  <Option value="商战职场">商战职场</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="style"
                label="写作风格"
                rules={[{ required: true, message: '请选择写作风格' }]}
              >
                <Select placeholder="请选择风格">
                  <Option value="轻松幽默">轻松幽默</Option>
                  <Option value="深沉严肃">深沉严肃</Option>
                  <Option value="浪漫温馨">浪漫温馨</Option>
                  <Option value="紧张刺激">紧张刺激</Option>
                  <Option value="文艺清新">文艺清新</Option>
                  <Option value="热血激昂">热血激昂</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="keywords" label="关键词">
            <Select
              mode="tags"
              placeholder="请输入关键词，如：爱情、冒险、成长等"
              tokenSeparators={[',']}
            />
          </Form.Item>

          <Form.Item
            name="count"
            label="生成数量"
            initialValue={5}
            rules={[{ required: true, message: '请选择生成数量' }]}
          >
            <Slider
              min={1}
              max={20}
              marks={{
                1: '1个',
                5: '5个',
                10: '10个',
                20: '20个'
              }}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 书名分析模态框 */}
      <Modal
        title={`书名分析 - ${selectedTitle?.title}`}
        open={isAnalysisVisible}
        onCancel={() => setIsAnalysisVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsAnalysisVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedTitle && (
          <div>
            <Row gutter={24}>
              <Col span={16}>
                <div className="space-y-4">
                  <div>
                    <Title level={4}>{selectedTitle.title}</Title>
                    <Space>
                      <Tag color="blue">{selectedTitle.genre}</Tag>
                      <Tag color="green">{selectedTitle.style}</Tag>
                      <Text type="secondary">
                        创建于 {new Date(selectedTitle.createdAt).toLocaleDateString()}
                      </Text>
                    </Space>
                  </div>

                  <Divider />

                  <div>
                    <Title level={5}>详细评分</Title>
                    <div className="space-y-3">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <Text>吸引力</Text>
                          <Text strong>{selectedTitle.analysis.appeal}/100</Text>
                        </div>
                        <Progress percent={selectedTitle.analysis.appeal} size="small" />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <Text>记忆度</Text>
                          <Text strong>{selectedTitle.analysis.memorability}/100</Text>
                        </div>
                        <Progress percent={selectedTitle.analysis.memorability} size="small" />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <Text>类型匹配</Text>
                          <Text strong>{selectedTitle.analysis.genreMatch}/100</Text>
                        </div>
                        <Progress percent={selectedTitle.analysis.genreMatch} size="small" />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <Text>独特性</Text>
                          <Text strong>{selectedTitle.analysis.uniqueness}/100</Text>
                        </div>
                        <Progress percent={selectedTitle.analysis.uniqueness} size="small" />
                      </div>

                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <Text>市场性</Text>
                          <Text strong>{selectedTitle.analysis.marketability}/100</Text>
                        </div>
                        <Progress percent={selectedTitle.analysis.marketability} size="small" />
                      </div>
                    </div>
                  </div>

                  <Divider />

                  <div>
                    <Title level={5}>优化建议</Title>
                    <List
                      size="small"
                      dataSource={selectedTitle.analysis.suggestions}
                      renderItem={(suggestion, index) => (
                        <List.Item>
                          <Text>{index + 1}. {suggestion}</Text>
                        </List.Item>
                      )}
                    />
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div className="text-center">
                  <div className="mb-4">
                    <div className="text-6xl font-bold text-blue-500 mb-2">
                      {selectedTitle.score}
                    </div>
                    <Text type="secondary">综合评分</Text>
                  </div>

                  <div className="mb-4">
                    <Progress
                      type="circle"
                      percent={selectedTitle.score}
                      status={getScoreColor(selectedTitle.score)}
                      width={120}
                    />
                  </div>

                  <div className="space-y-2">
                    <Button
                      type={selectedTitle.isFavorite ? "default" : "primary"}
                      icon={selectedTitle.isFavorite ? <HeartFilled /> : <HeartOutlined />}
                      onClick={() => handleToggleFavorite(selectedTitle)}
                      block
                    >
                      {selectedTitle.isFavorite ? '取消收藏' : '添加收藏'}
                    </Button>

                    <Button
                      icon={<ShareAltOutlined />}
                      onClick={() => {
                        navigator.clipboard.writeText(selectedTitle.title);
                        message.success('书名已复制到剪贴板');
                      }}
                      block
                    >
                      复制书名
                    </Button>
                  </div>
                </div>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default TitleManager;
