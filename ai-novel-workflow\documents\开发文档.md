# AI小说生成工作流平台 - 开发文档

## 项目概述

本项目是一个基于Next.js + TypeScript的AI小说生成工作流管理平台，旨在为小说创作者提供一个完整的AI辅助创作工具链。

## 技术架构

### 前端技术栈
- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **UI组件**: Ant Design 5.x
- **状态管理**: Zustand
- **工作流可视化**: @xyflow/react (计划)
- **图标**: Ant Design Icons + Lucide React

### 项目结构

```
ai-novel-workflow/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── layout.tsx          # 根布局组件
│   │   ├── page.tsx            # 主页面
│   │   └── globals.css         # 全局样式
│   ├── components/             # React组件
│   │   ├── layout/             # 布局相关组件
│   │   │   └── MainLayout.tsx  # 主布局组件
│   │   ├── workflow/           # 工作流相关组件
│   │   │   └── WorkflowEditor.tsx
│   │   ├── project/            # 项目管理组件
│   │   │   └── ProjectOverview.tsx
│   │   ├── character/          # 角色管理组件
│   │   ├── worldbuilding/      # 世界观管理组件
│   │   ├── outline/            # 大纲管理组件
│   │   ├── plotline/           # 主线管理组件
│   │   ├── title/              # 书名管理组件
│   │   ├── document/           # 文档管理组件
│   │   └── prompt/             # 提示词管理组件
│   ├── store/                  # 状态管理
│   │   └── index.ts            # Zustand主store
│   ├── types/                  # TypeScript类型定义
│   │   └── index.ts            # 核心类型定义
│   └── utils/                  # 工具函数 (计划)
├── public/                     # 静态资源
├── documents/                  # 项目文档
└── package.json               # 项目配置
```

## 核心功能模块

### 1. 状态管理 (Zustand)

#### 主要状态结构
- **UI状态**: 主题、语言、侧边栏状态、活动标签页、通知
- **项目管理**: 项目列表、当前项目、项目CRUD操作
- **工作流管理**: 工作流节点、连接关系、执行状态
- **内容管理**: 角色、世界观、大纲、主线、书名、章节
- **提示词管理**: 模板、版本、性能数据
- **文档管理**: 文件结构、版本控制
- **执行引擎**: 执行上下文、进度跟踪、任务队列

#### 状态持久化
使用Zustand的persist中间件，选择性持久化以下数据：
- 项目数据
- 工作流配置
- 内容数据
- UI偏好设置

### 2. 组件架构

#### 布局组件
- **MainLayout**: 主布局，包含侧边栏、顶栏、内容区域
- **Sidebar**: 导航菜单，支持折叠
- **Header**: 顶部工具栏，包含主题切换、通知、用户菜单

#### 功能组件
- **WorkflowEditor**: 工作流可视化编辑器
- **ProjectOverview**: 项目总览和管理
- **各种Manager组件**: 专门的内容管理界面

### 3. 类型系统

#### 核心类型
- **Project**: 项目基本信息和设置
- **WorkflowNode**: 工作流节点定义
- **Character**: 角色信息和关系
- **WorldBuilding**: 世界观设定
- **Outline**: 大纲结构
- **PlotLine**: 故事线定义
- **Chapter**: 章节内容
- **PromptTemplate**: 提示词模板

## 开发规范

### 代码规范
1. **组件命名**: 使用PascalCase，文件名与组件名一致
2. **函数命名**: 使用camelCase，事件处理函数以handle开头
3. **类型定义**: 接口使用PascalCase，类型别名使用PascalCase
4. **常量命名**: 使用UPPER_SNAKE_CASE

### 文件组织
1. **组件文件**: 每个组件一个文件，包含相关的类型定义
2. **样式**: 使用Tailwind CSS类名，避免自定义CSS
3. **类型**: 公共类型定义在types/index.ts中
4. **工具函数**: 放在utils目录下

### Git提交规范
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 开发流程

### 1. 环境搭建
```bash
# 克隆项目
git clone <repository-url>
cd ai-novel-workflow

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 2. 功能开发
1. 在对应的组件目录下创建新组件
2. 在types/index.ts中添加必要的类型定义
3. 在store/index.ts中添加状态管理逻辑
4. 在主页面中集成新组件

### 3. 测试
- 手动测试功能完整性
- 检查TypeScript类型错误
- 验证响应式布局
- 测试状态持久化

## 部署说明

### 开发环境
```bash
npm run dev
```

### 生产构建
```bash
npm run build
npm start
```

### 环境变量
目前项目为纯前端应用，暂无环境变量配置需求。

## 后续开发计划

### 第二阶段 - 工作流可视化
- 集成@xyflow/react
- 实现拖拽节点编辑
- 添加节点连接功能
- 完善节点配置面板

### 第三阶段 - 内容管理完善
- 完整实现角色管理功能
- 开发世界观管理系统
- 构建大纲管理工具
- 实现主线管理功能

### 第四阶段 - 高级功能
- 提示词管理系统
- 文档管理和版本控制
- 一致性检查机制
- 导入导出功能
- AI集成接口

## 常见问题

### Q: 如何添加新的节点类型？
A: 在types/index.ts中的NodeType类型中添加新类型，然后在WorkflowEditor组件的nodeTypes数组中添加对应配置。

### Q: 如何扩展状态管理？
A: 在store/index.ts的AppState接口中添加新的状态字段和对应的操作方法。

### Q: 如何添加新的管理页面？
A: 在components目录下创建新的组件目录，实现管理组件，然后在主页面的路由逻辑中添加对应的case。

## 技术债务和优化点

1. **性能优化**: 大型工作流的虚拟滚动
2. **错误处理**: 完善错误边界和用户友好的错误提示
3. **国际化**: 支持多语言切换
4. **测试**: 添加单元测试和集成测试
5. **文档**: 完善API文档和用户手册

---

最后更新: 2025-01-05
