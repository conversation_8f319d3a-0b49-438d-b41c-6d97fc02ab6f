'use client';

import React, { useState, useMemo } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tree,
  Tabs,
  List,
  Tag,
  Popconfirm,
  message,
  Row,
  Col,
  Divider,
  Progress,
  Tooltip,
  InputNumber,
  Badge
} from 'antd';
import {
  FileTextOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FolderOutlined,
  FileOutlined,
  DownOutlined,
  UpOutlined,
  CopyOutlined,
  HistoryOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/store';
import type { Outline, OutlineNode } from '@/types';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const OutlineManager: React.FC = () => {
  const {
    currentProject,
    outlines,
    addOutline,
    updateOutline,
    deleteOutline,
    getOutlines
  } = useAppStore();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isNodeModalVisible, setIsNodeModalVisible] = useState(false);
  const [editingOutline, setEditingOutline] = useState<Outline | null>(null);
  const [editingNode, setEditingNode] = useState<OutlineNode | null>(null);
  const [selectedOutline, setSelectedOutline] = useState<Outline | null>(null);
  const [selectedNode, setSelectedNode] = useState<OutlineNode | null>(null);
  const [activeTab, setActiveTab] = useState('outlines');
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [form] = Form.useForm();
  const [nodeForm] = Form.useForm();

  const projectOutlines = currentProject ? getOutlines(currentProject.id) : [];

  // 将大纲节点转换为Tree组件需要的格式
  const convertToTreeData = (nodes: OutlineNode[]): any[] => {
    return nodes.map(node => ({
      key: node.id,
      title: (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-2">
            {node.type === 'part' && <FolderOutlined className="text-blue-500" />}
            {node.type === 'chapter' && <FileOutlined className="text-green-500" />}
            {node.type === 'section' && <FileTextOutlined className="text-gray-500" />}
            <span className="font-medium">{node.title}</span>
            <Tag color={getStatusColor(node.status)}>
              {getStatusText(node.status)}
            </Tag>
            {node.wordCount && (
              <Text type="secondary" className="text-xs">
                {node.wordCount.toLocaleString()}字
              </Text>
            )}
          </div>
          <div className="flex space-x-1">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleEditNode(node);
              }}
            />
            <Button
              type="text"
              size="small"
              icon={<PlusOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleAddChildNode(node);
              }}
            />
            <Popconfirm
              title="确定要删除这个节点吗？"
              onConfirm={(e) => {
                e?.stopPropagation();
                handleDeleteNode(node);
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => e.stopPropagation()}
              />
            </Popconfirm>
          </div>
        </div>
      ),
      children: node.children.length > 0 ? convertToTreeData(node.children) : undefined,
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'writing': return 'processing';
      case 'revised': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'planned': return '计划中';
      case 'writing': return '写作中';
      case 'completed': return '已完成';
      case 'revised': return '已修订';
      default: return '未知';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircleOutlined className="text-green-500" />;
      case 'writing': return <ClockCircleOutlined className="text-blue-500" />;
      case 'revised': return <ExclamationCircleOutlined className="text-orange-500" />;
      default: return <ClockCircleOutlined className="text-gray-400" />;
    }
  };

  const calculateProgress = (nodes: OutlineNode[]): { completed: number; total: number } => {
    let completed = 0;
    let total = 0;

    const traverse = (nodeList: OutlineNode[]) => {
      nodeList.forEach(node => {
        if (node.type === 'chapter') {
          total++;
          if (node.status === 'completed') {
            completed++;
          }
        }
        if (node.children.length > 0) {
          traverse(node.children);
        }
      });
    };

    traverse(nodes);
    return { completed, total };
  };

  const handleCreateOutline = () => {
    setEditingOutline(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditOutline = (outline: Outline) => {
    setEditingOutline(outline);
    form.setFieldsValue({
      title: outline.title,
      type: outline.type,
    });
    setIsModalVisible(true);
  };

  const handleDeleteOutline = (outlineId: string) => {
    if (currentProject) {
      deleteOutline(currentProject.id, outlineId);
      message.success('大纲已删除');
      if (selectedOutline?.id === outlineId) {
        setSelectedOutline(null);
      }
    }
  };

  const handleAddChildNode = (parentNode?: OutlineNode) => {
    setEditingNode(null);
    nodeForm.resetFields();
    if (parentNode) {
      // 根据父节点类型设置子节点类型
      const childType = parentNode.type === 'part' ? 'chapter' : 'section';
      nodeForm.setFieldsValue({ type: childType });
    }
    setIsNodeModalVisible(true);
  };

  const handleEditNode = (node: OutlineNode) => {
    setEditingNode(node);
    nodeForm.setFieldsValue({
      title: node.title,
      summary: node.summary,
      type: node.type,
      wordCount: node.wordCount,
      keyPoints: node.keyPoints,
      status: node.status,
    });
    setIsNodeModalVisible(true);
  };

  const handleDeleteNode = (node: OutlineNode) => {
    // 这里应该实现删除节点的逻辑
    message.success('节点已删除');
  };

  if (!currentProject) {
    return (
      <div className="p-8 text-center">
        <Title level={3}>请先选择或创建一个项目</Title>
        <Text type="secondary">
          您需要先在项目总览中创建或选择一个项目，然后才能管理大纲。
        </Text>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Title level={2}>大纲管理</Title>
          <Text type="secondary">管理故事大纲和章节结构 - 项目: {currentProject.name}</Text>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateOutline}
          >
            创建大纲
          </Button>
        </Space>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'outlines',
            label: (
              <span>
                <FileTextOutlined />
                大纲列表 ({projectOutlines.length})
              </span>
            ),
            children: (
              <div>
                {projectOutlines.length === 0 ? (
                  <Card className="text-center py-12">
                    <FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                    <div className="mt-4">
                      <Text type="secondary">还没有创建任何大纲</Text>
                      <br />
                      <Text type="secondary">点击上方按钮创建您的第一个故事大纲</Text>
                    </div>
                  </Card>
                ) : (
                  <Row gutter={[16, 16]}>
                    {projectOutlines.map((outline) => {
                      const progress = calculateProgress(outline.structure);
                      const progressPercent = progress.total > 0 ? (progress.completed / progress.total) * 100 : 0;

                      return (
                        <Col span={8} key={outline.id}>
                          <Card
                            className="hover:shadow-lg transition-shadow cursor-pointer"
                            onClick={() => setSelectedOutline(outline)}
                            actions={[
                              <Tooltip title="编辑" key="edit">
                                <EditOutlined onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditOutline(outline);
                                }} />
                              </Tooltip>,
                              <Tooltip title="复制" key="copy">
                                <CopyOutlined onClick={(e) => {
                                  e.stopPropagation();
                                  message.info('复制功能开发中');
                                }} />
                              </Tooltip>,
                              <Popconfirm
                                key="delete"
                                title="确定要删除这个大纲吗？"
                                onConfirm={(e) => {
                                  e?.stopPropagation();
                                  handleDeleteOutline(outline.id);
                                }}
                                okText="确定"
                                cancelText="取消"
                              >
                                <DeleteOutlined onClick={(e) => e.stopPropagation()} />
                              </Popconfirm>
                            ]}
                          >
                            <div className="mb-3">
                              <div className="flex items-center justify-between mb-2">
                                <Title level={5} className="mb-0">{outline.title}</Title>
                                <Tag color={outline.type === 'main' ? 'blue' : 'green'}>
                                  {outline.type === 'main' ? '主大纲' : '详细大纲'}
                                </Tag>
                              </div>

                              <div className="mb-3">
                                <Text type="secondary" className="text-sm">
                                  版本 {outline.version} • {new Date(outline.updatedAt).toLocaleDateString()}
                                </Text>
                              </div>

                              <div className="mb-3">
                                <div className="flex items-center justify-between mb-1">
                                  <Text type="secondary" className="text-sm">完成进度</Text>
                                  <Text type="secondary" className="text-sm">
                                    {progress.completed}/{progress.total}
                                  </Text>
                                </div>
                                <Progress
                                  percent={progressPercent}
                                  size="small"
                                  status={progressPercent === 100 ? 'success' : 'active'}
                                />
                              </div>

                              <div className="flex items-center justify-between text-sm text-gray-500">
                                <span>{outline.structure.length} 个主要节点</span>
                                <span>
                                  {outline.structure.reduce((total, node) => {
                                    const countNodes = (n: OutlineNode): number => {
                                      return 1 + n.children.reduce((sum, child) => sum + countNodes(child), 0);
                                    };
                                    return total + countNodes(node);
                                  }, 0)} 个总节点
                                </span>
                              </div>
                            </div>
                          </Card>
                        </Col>
                      );
                    })}
                  </Row>
                )}
              </div>
            ),
          },
          {
            key: 'structure',
            label: (
              <span>
                <FolderOutlined />
                大纲结构
              </span>
            ),
            children: (
              <div>
                {selectedOutline ? (
                  <Card
                    title={`${selectedOutline.title} - 结构视图`}
                    extra={
                      <Space>
                        <Button
                          icon={<PlusOutlined />}
                          onClick={() => handleAddChildNode()}
                        >
                          添加节点
                        </Button>
                      </Space>
                    }
                  >
                    {selectedOutline.structure.length === 0 ? (
                      <div className="text-center py-8">
                        <FolderOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                        <div className="mt-4">
                          <Text type="secondary">大纲结构为空</Text>
                          <br />
                          <Text type="secondary">点击上方按钮添加第一个节点</Text>
                        </div>
                      </div>
                    ) : (
                      <Tree
                        showLine
                        switcherIcon={<DownOutlined />}
                        treeData={convertToTreeData(selectedOutline.structure)}
                        expandedKeys={expandedKeys}
                        onExpand={(keys) => setExpandedKeys(keys as string[])}
                        onSelect={(keys) => {
                          if (keys.length > 0) {
                            // 找到选中的节点
                            const findNode = (nodes: OutlineNode[], id: string): OutlineNode | null => {
                              for (const node of nodes) {
                                if (node.id === id) return node;
                                const found = findNode(node.children, id);
                                if (found) return found;
                              }
                              return null;
                            };
                            const node = findNode(selectedOutline.structure, keys[0] as string);
                            setSelectedNode(node);
                          }
                        }}
                      />
                    )}
                  </Card>
                ) : (
                  <Card className="text-center py-12">
                    <FolderOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                    <div className="mt-4">
                      <Text type="secondary">请先选择一个大纲</Text>
                      <br />
                      <Text type="secondary">在左侧大纲列表中点击选择要查看的大纲</Text>
                    </div>
                  </Card>
                )}
              </div>
            ),
          },
        ]}
      />

      {/* 节点详情面板 */}
      {selectedNode && (
        <Card
          className="mt-6"
          title={`节点详情 - ${selectedNode.title}`}
          extra={
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => handleEditNode(selectedNode)}
            >
              编辑节点
            </Button>
          }
        >
          <Row gutter={24}>
            <Col span={16}>
              <div className="space-y-4">
                <div>
                  <Text strong>节点摘要:</Text>
                  <Paragraph className="mt-2">{selectedNode.summary || '暂无摘要'}</Paragraph>
                </div>

                {selectedNode.keyPoints.length > 0 && (
                  <div>
                    <Text strong>关键要点:</Text>
                    <List
                      size="small"
                      className="mt-2"
                      dataSource={selectedNode.keyPoints}
                      renderItem={(point, index) => (
                        <List.Item>
                          <Text>{index + 1}. {point}</Text>
                        </List.Item>
                      )}
                    />
                  </div>
                )}
              </div>
            </Col>
            <Col span={8}>
              <div className="space-y-4">
                <div>
                  <Text strong>节点类型:</Text>
                  <div className="mt-1">
                    <Tag color="blue">
                      {selectedNode.type === 'part' ? '部分' :
                       selectedNode.type === 'chapter' ? '章节' : '小节'}
                    </Tag>
                  </div>
                </div>

                <div>
                  <Text strong>状态:</Text>
                  <div className="mt-1 flex items-center space-x-2">
                    {getStatusIcon(selectedNode.status)}
                    <Text>{getStatusText(selectedNode.status)}</Text>
                  </div>
                </div>

                {selectedNode.wordCount && (
                  <div>
                    <Text strong>字数目标:</Text>
                    <div className="mt-1">
                      <Text>{selectedNode.wordCount.toLocaleString()} 字</Text>
                    </div>
                  </div>
                )}

                <div>
                  <Text strong>排序:</Text>
                  <div className="mt-1">
                    <Text>第 {selectedNode.order} 位</Text>
                  </div>
                </div>

                <div>
                  <Text strong>子节点:</Text>
                  <div className="mt-1">
                    <Badge count={selectedNode.children.length} showZero>
                      <Text>包含子节点</Text>
                    </Badge>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {/* 创建/编辑大纲模态框 */}
      <Modal
        title={editingOutline ? '编辑大纲' : '创建大纲'}
        open={isModalVisible}
        onOk={async () => {
          try {
            const values = await form.validateFields();

            if (editingOutline) {
              // 更新大纲
              if (currentProject) {
                updateOutline(currentProject.id, editingOutline.id, {
                  ...values,
                  version: editingOutline.version + 1,
                });
                message.success('大纲已更新');
              }
            } else {
              // 创建新大纲
              if (currentProject) {
                addOutline(currentProject.id, {
                  ...values,
                  structure: [],
                  version: 1,
                });
                message.success('大纲已创建');
              }
            }

            setIsModalVisible(false);
            form.resetFields();
          } catch (error) {
            console.error('表单验证失败:', error);
          }
        }}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        okText={editingOutline ? '更新' : '创建'}
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="title"
            label="大纲标题"
            rules={[{ required: true, message: '请输入大纲标题' }]}
          >
            <Input placeholder="请输入大纲标题" />
          </Form.Item>

          <Form.Item
            name="type"
            label="大纲类型"
            rules={[{ required: true, message: '请选择大纲类型' }]}
          >
            <Select placeholder="请选择大纲类型">
              <Option value="main">主大纲</Option>
              <Option value="detailed">详细大纲</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建/编辑节点模态框 */}
      <Modal
        title={editingNode ? '编辑节点' : '创建节点'}
        open={isNodeModalVisible}
        onOk={async () => {
          try {
            const values = await nodeForm.validateFields();
            // 这里应该实现节点的创建/更新逻辑
            message.success(editingNode ? '节点已更新' : '节点已创建');
            setIsNodeModalVisible(false);
            nodeForm.resetFields();
          } catch (error) {
            console.error('表单验证失败:', error);
          }
        }}
        onCancel={() => {
          setIsNodeModalVisible(false);
          nodeForm.resetFields();
        }}
        width={800}
        okText={editingNode ? '更新' : '创建'}
        cancelText="取消"
      >
        <Form form={nodeForm} layout="vertical">
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item
                name="title"
                label="节点标题"
                rules={[{ required: true, message: '请输入节点标题' }]}
              >
                <Input placeholder="请输入节点标题" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="type"
                label="节点类型"
                rules={[{ required: true, message: '请选择节点类型' }]}
              >
                <Select placeholder="请选择类型">
                  <Option value="part">部分</Option>
                  <Option value="chapter">章节</Option>
                  <Option value="section">小节</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="summary" label="节点摘要">
            <TextArea
              rows={3}
              placeholder="请输入节点的简要描述..."
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="wordCount" label="字数目标">
                <InputNumber
                  min={0}
                  placeholder="预计字数"
                  className="w-full"
                  formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => (Number(value!.replace(/\$\s?|(,*)/g, '')) || 0) as any}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="status" label="状态">
                <Select placeholder="请选择状态">
                  <Option value="planned">计划中</Option>
                  <Option value="writing">写作中</Option>
                  <Option value="completed">已完成</Option>
                  <Option value="revised">已修订</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="keyPoints" label="关键要点">
            <Select
              mode="tags"
              placeholder="请输入关键要点，按回车添加"
              tokenSeparators={[',']}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OutlineManager;
