# AI功能使用指南

## 概述

AI小说生成工作流平台集成了真正的AI模型，支持多种主流AI服务提供商，为用户提供智能化的小说创作辅助功能。

## 🤖 支持的AI服务提供商

### 1. OpenAI
- **模型**: GPT-3.5 Turbo, GPT-4, GPT-4 Turbo
- **官网**: https://platform.openai.com/
- **API文档**: https://platform.openai.com/docs/api-reference
- **获取API Key**: https://platform.openai.com/api-keys

### 2. Anthropic Claude
- **模型**: <PERSON>-3 <PERSON><PERSON>, <PERSON>-3 Sonnet, Claude-3 Opus
- **官网**: https://www.anthropic.com/
- **API文档**: https://docs.anthropic.com/claude/reference/
- **获取API Key**: https://console.anthropic.com/

### 3. Google Gemini
- **模型**: Gemini Pro, Gemini Pro Vision
- **官网**: https://ai.google.dev/
- **API文档**: https://ai.google.dev/docs
- **获取API Key**: https://makersuite.google.com/app/apikey

### 4. 自定义API
- 支持兼容OpenAI格式的自定义API
- 适用于本地部署的模型或第三方服务

## 🔧 AI配置步骤

### 第一步：打开AI配置
1. 在应用顶部工具栏找到 **API图标** (🔗)
2. 点击图标打开"AI API配置"对话框
3. 或者在工作流编辑器中点击"AI工作流助手"时会提示配置

### 第二步：选择AI服务提供商
在配置对话框中选择您要使用的AI服务：
- **OpenAI** - 最流行的选择，模型质量高
- **Anthropic Claude** - 擅长长文本处理和创意写作
- **Google Gemini** - Google的最新AI模型
- **自定义API** - 使用您自己的API服务

### 第三步：配置API信息
根据选择的提供商填写以下信息：

#### OpenAI配置示例
```
AI服务提供商: OpenAI
API密钥: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API地址: https://api.openai.com/v1/chat/completions (默认)
模型名称: gpt-3.5-turbo
创造性: 0.7
最大输出长度: 2000
```

#### Claude配置示例
```
AI服务提供商: Anthropic Claude
API密钥: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API地址: https://api.anthropic.com/v1/messages (默认)
模型名称: claude-3-sonnet-20240229
创造性: 0.7
最大输出长度: 2000
```

#### Gemini配置示例
```
AI服务提供商: Google Gemini
API密钥: AIxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
API地址: https://generativelanguage.googleapis.com/v1beta/models (默认)
模型名称: gemini-pro
创造性: 0.7
最大输出长度: 2000
```

### 第四步：测试连接
1. 填写完配置信息后，点击"测试连接"按钮
2. 系统会发送一个测试请求验证配置是否正确
3. 如果显示"连接成功"，说明配置正确
4. 如果连接失败，请检查API密钥和网络连接

### 第五步：保存配置
1. 测试成功后，点击"保存配置"按钮
2. 配置信息会保存到本地浏览器存储
3. 顶部工具栏的API图标会变为绿色，表示AI已配置

## 🚀 AI功能使用

### 1. AI工作流助手
配置完成后，您可以使用AI工作流助手：

1. **进入工作流编辑器**
   - 点击左侧菜单"工作流编辑器"

2. **打开AI助手**
   - 点击"AI工作流助手"按钮（蓝色机器人图标）

3. **选择模式**
   - **智能推荐**: AI分析您的需求并推荐最适合的工作流模板
   - **自定义生成**: AI根据具体需求动态生成定制化工作流
   - **优化现有**: AI分析当前工作流并提供优化建议

4. **填写创作需求**
   ```
   小说类型: 现代都市 / 古代言情 / 玄幻修仙 等
   写作风格: 轻松幽默 / 深沉严肃 / 浪漫温馨 等
   作品长度: 短篇 / 中篇 / 长篇
   创作经验: 新手 / 进阶 / 专业
   特殊需求: 复杂世界观 / 高质量润色 / 快速生成 等
   ```

5. **获取AI推荐**
   - AI会分析您的需求并推荐最适合的工作流
   - 显示推荐置信度和详细理由
   - 提供多个备选方案

6. **预览并应用**
   - 查看推荐工作流的详细结构
   - 了解节点数量、连接关系和预估时间
   - 一键应用到工作流编辑器

### 2. AI书名生成
在书名管理页面：
1. 点击"AI生成书名"按钮
2. 设置生成参数（类型、风格、关键词、数量）
3. AI会生成多个书名候选并提供评分分析

### 3. AI角色生成
在角色管理页面：
1. 使用角色模板或自定义创建
2. AI会根据小说类型和角色定位生成详细设定
3. 包括外貌、性格、背景、技能等完整信息

## 🔍 故障排除

### 常见问题

#### 1. API连接失败
**可能原因**:
- API密钥错误或已过期
- 网络连接问题
- API服务暂时不可用
- 配额已用完

**解决方案**:
- 检查API密钥是否正确
- 确认账户余额充足
- 尝试更换网络环境
- 联系API服务提供商

#### 2. AI响应格式错误
**现象**: AI返回内容但无法解析
**解决方案**: 系统会自动降级到本地智能分析

#### 3. 请求超时
**原因**: 网络延迟或AI服务响应慢
**解决方案**: 
- 检查网络连接
- 尝试减少输出长度设置
- 稍后重试

#### 4. 配置丢失
**原因**: 浏览器清理了本地存储
**解决方案**: 重新配置AI API设置

### 调试技巧

1. **查看浏览器控制台**
   - 按F12打开开发者工具
   - 查看Console标签页的错误信息

2. **测试API连接**
   - 使用配置页面的"测试连接"功能
   - 确认API服务状态

3. **检查网络**
   - 确认能访问AI服务提供商的网站
   - 检查防火墙和代理设置

## 💡 使用建议

### 1. 选择合适的AI模型
- **新手用户**: 推荐使用GPT-3.5 Turbo，成本低、速度快
- **专业用户**: 推荐使用GPT-4或Claude-3 Sonnet，质量更高
- **长文本**: Claude系列模型处理长文本能力更强
- **创意写作**: 所有模型都有不错的创意能力

### 2. 优化参数设置
- **创造性(Temperature)**:
  - 0.3-0.5: 更保守、一致的输出
  - 0.7-0.8: 平衡创造性和一致性（推荐）
  - 0.9-1.0: 更有创造性但可能不够连贯

- **最大输出长度**:
  - 短篇小说: 1000-2000 tokens
  - 中长篇: 2000-4000 tokens
  - 注意：更长的输出会消耗更多API配额

### 3. 成本控制
- 合理设置最大输出长度
- 优先使用成本较低的模型进行测试
- 监控API使用量和费用

### 4. 提高效果
- 提供详细、具体的需求描述
- 使用明确的关键词和风格指导
- 多次尝试不同的参数组合

## 🔒 安全和隐私

### 数据安全
- API密钥仅存储在本地浏览器中
- 不会上传到我们的服务器
- 创作内容直接发送给您选择的AI服务提供商

### 隐私保护
- 遵循各AI服务提供商的隐私政策
- 建议不要在创作内容中包含敏感个人信息
- 定期更新API密钥以确保安全

### 最佳实践
- 定期备份重要的创作内容
- 不要在公共设备上保存API配置
- 及时删除不再使用的API密钥

---

**注意**: 使用AI功能需要有效的API密钥和网络连接。请确保遵守各AI服务提供商的使用条款和政策。
