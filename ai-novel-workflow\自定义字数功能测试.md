# 自定义字数功能测试指南

## 🎯 功能概述

新增的自定义字数功能允许用户在AI工作流助手中指定精确的目标字数，而不仅限于预设的"短篇"、"中篇"、"长篇"分类。

## 📋 测试步骤

### 第一步：启动系统
1. 确保系统正在运行：`npm run dev`
2. 在浏览器中访问：`http://localhost:3001`

### 第二步：打开AI工作流助手
1. 点击"AI工作流助手"按钮
2. 选择"智能推荐"或"自定义生成"模式

### 第三步：测试自定义字数功能

#### 测试用例1：小型自定义项目
1. **配置参数**：
   - 小说类型：现代都市
   - 写作风格：轻松幽默
   - 作品长度：选择"自定义字数"
   - 目标字数：输入 `8000`
   - 创作经验：新手
   - 特殊需求：勾选"快速生成"

2. **预期结果**：
   - 字数输入框应该出现
   - 可以输入1000-1000000之间的数字
   - AI分析时应该识别为简单复杂度工作流
   - 推荐的工作流应该适合短篇创作

#### 测试用例2：中型自定义项目
1. **配置参数**：
   - 小说类型：奇幻冒险
   - 写作风格：史诗风格
   - 作品长度：选择"自定义字数"
   - 目标字数：输入 `80000`
   - 创作经验：进阶
   - 特殊需求：勾选"复杂世界观"、"多角色设定"

2. **预期结果**：
   - AI应该识别为中等复杂度工作流
   - 推荐包含世界观构建和角色创建节点
   - 工作流描述应该显示"自定义(80,000字)"

#### 测试用例3：大型自定义项目
1. **配置参数**：
   - 小说类型：科幻
   - 写作风格：硬科幻
   - 作品长度：选择"自定义字数"
   - 目标字数：输入 `300000`
   - 创作经验：专业
   - 特殊需求：勾选"高质量润色"、"一致性检查"

2. **预期结果**：
   - AI应该识别为复杂工作流
   - 包含完整的创作流程节点
   - 工作流描述应该显示"自定义(300,000字)"

### 第四步：验证输入验证

#### 边界值测试
1. **最小值测试**：
   - 输入 `999` - 应该显示错误："字数不能少于1000字"
   - 输入 `1000` - 应该通过验证

2. **最大值测试**：
   - 输入 `1000001` - 应该显示错误："字数不能超过100万字"
   - 输入 `1000000` - 应该通过验证

3. **格式测试**：
   - 输入非数字字符 - 应该被自动过滤
   - 输入小数 - 应该被自动转换为整数

### 第五步：验证AI分析结果

#### 智能推荐模式
1. 点击"AI分析需求"
2. 检查AI返回的推荐结果
3. 验证推荐理由是否包含自定义字数信息
4. 确认置信度评分合理

#### 自定义生成模式
1. 点击"AI分析需求"
2. 检查生成的自定义工作流
3. 验证节点配置是否符合字数要求
4. 确认工作流复杂度正确

## 🔍 验证要点

### 界面验证
- [ ] 作品长度下拉菜单包含"自定义字数"选项
- [ ] 选择自定义字数后，字数输入框正确显示
- [ ] 输入框有正确的占位符文本
- [ ] 输入框支持千分位分隔符显示
- [ ] 输入框有"字"后缀标识

### 功能验证
- [ ] 字数输入验证规则正确执行
- [ ] 切换回预设长度时，自定义字数输入框隐藏
- [ ] 表单提交时包含自定义字数数据
- [ ] AI分析能正确处理自定义字数

### 逻辑验证
- [ ] 1000-50000字识别为简单复杂度
- [ ] 50001-150000字识别为中等复杂度
- [ ] 150001字以上识别为复杂复杂度
- [ ] 工作流描述正确显示自定义字数
- [ ] 推荐算法考虑自定义字数因素

## 📊 测试数据

### 推荐测试数据集
```javascript
// 短篇范围
{ customWordCount: 3000, expectedComplexity: 'simple' }
{ customWordCount: 15000, expectedComplexity: 'simple' }
{ customWordCount: 45000, expectedComplexity: 'simple' }

// 中篇范围  
{ customWordCount: 60000, expectedComplexity: 'medium' }
{ customWordCount: 100000, expectedComplexity: 'medium' }
{ customWordCount: 140000, expectedComplexity: 'medium' }

// 长篇范围
{ customWordCount: 180000, expectedComplexity: 'complex' }
{ customWordCount: 350000, expectedComplexity: 'complex' }
{ customWordCount: 800000, expectedComplexity: 'complex' }
```

## 🐛 常见问题排查

### 问题1：自定义字数输入框不显示
**可能原因**：
- 没有选择"自定义字数"选项
- 表单组件渲染异常

**解决方法**：
- 确认下拉菜单选择正确
- 刷新页面重试
- 检查浏览器控制台错误

### 问题2：字数验证不生效
**可能原因**：
- 验证规则配置错误
- 表单验证被绕过

**解决方法**：
- 检查Form.Item的rules配置
- 验证InputNumber的min/max属性

### 问题3：AI分析不包含自定义字数
**可能原因**：
- 数据传递丢失
- AI服务函数未更新

**解决方法**：
- 检查表单数据结构
- 验证AI服务函数参数

## 📈 性能测试

### 响应时间测试
- 输入框显示/隐藏切换：< 100ms
- 字数格式化显示：< 50ms
- 表单验证响应：< 200ms
- AI分析请求：< 30s

### 用户体验测试
- 输入流畅性：无卡顿
- 错误提示清晰：用户友好
- 界面响应及时：即时反馈
- 数据持久性：切换保持

## ✅ 测试完成标准

当以下所有项目都通过测试时，自定义字数功能验证完成：

1. **基础功能**：
   - [ ] 自定义字数选项正常显示
   - [ ] 字数输入框正确工作
   - [ ] 输入验证规则有效

2. **AI集成**：
   - [ ] AI分析包含自定义字数
   - [ ] 推荐结果考虑字数因素
   - [ ] 工作流生成适配字数

3. **用户体验**：
   - [ ] 界面交互流畅
   - [ ] 错误提示友好
   - [ ] 数据处理正确

4. **边界测试**：
   - [ ] 最小值验证通过
   - [ ] 最大值验证通过
   - [ ] 异常输入处理正确

通过这个全面的测试，可以确保自定义字数功能完全符合用户需求，提供了更精确和灵活的创作规划体验。
