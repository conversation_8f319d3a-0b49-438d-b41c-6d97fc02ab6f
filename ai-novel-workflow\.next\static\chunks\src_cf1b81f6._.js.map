{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { \n  Project, \n  WorkflowNode, \n  Character, \n  WorldBuilding, \n  Outline, \n  PlotLine, \n  BookTitle, \n  Chapter,\n  PromptTemplate,\n  DocumentStructure,\n  ExecutionContext,\n  UIState,\n  Notification\n} from '@/types';\n\n// 主应用状态接口\ninterface AppState {\n  // UI状态\n  ui: UIState;\n  setTheme: (theme: 'light' | 'dark') => void;\n  setLanguage: (language: 'zh-CN' | 'en-US') => void;\n  toggleSidebar: () => void;\n  setActiveTab: (tab: string) => void;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationRead: (id: string) => void;\n  clearNotifications: () => void;\n\n  // 项目管理\n  projects: Project[];\n  currentProject: Project | null;\n  createProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateProject: (id: string, updates: Partial<Project>) => void;\n  deleteProject: (id: string) => void;\n  setCurrentProject: (id: string) => void;\n\n  // 工作流管理\n  workflows: Record<string, WorkflowNode[]>;\n  currentWorkflow: WorkflowNode[];\n  addNode: (node: Omit<WorkflowNode, 'id'>) => void;\n  updateNode: (id: string, updates: Partial<WorkflowNode>) => void;\n  deleteNode: (id: string) => void;\n  connectNodes: (sourceId: string, targetId: string) => void;\n  disconnectNodes: (sourceId: string, targetId: string) => void;\n  loadWorkflow: (projectId: string) => void;\n  saveWorkflow: (projectId: string) => void;\n\n  // 角色管理\n  characters: Record<string, Character[]>;\n  addCharacter: (projectId: string, character: Omit<Character, 'id'>) => void;\n  updateCharacter: (projectId: string, id: string, updates: Partial<Character>) => void;\n  deleteCharacter: (projectId: string, id: string) => void;\n  getCharacters: (projectId: string) => Character[];\n\n  // 世界观管理\n  worldBuilding: Record<string, WorldBuilding[]>;\n  addWorldElement: (projectId: string, element: Omit<WorldBuilding, 'id'>) => void;\n  updateWorldElement: (projectId: string, id: string, updates: Partial<WorldBuilding>) => void;\n  deleteWorldElement: (projectId: string, id: string) => void;\n  getWorldElements: (projectId: string) => WorldBuilding[];\n\n  // 大纲管理\n  outlines: Record<string, Outline[]>;\n  addOutline: (projectId: string, outline: Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateOutline: (projectId: string, id: string, updates: Partial<Outline>) => void;\n  deleteOutline: (projectId: string, id: string) => void;\n  getOutlines: (projectId: string) => Outline[];\n\n  // 主线管理\n  plotLines: Record<string, PlotLine[]>;\n  addPlotLine: (projectId: string, plotLine: Omit<PlotLine, 'id'>) => void;\n  updatePlotLine: (projectId: string, id: string, updates: Partial<PlotLine>) => void;\n  deletePlotLine: (projectId: string, id: string) => void;\n  getPlotLines: (projectId: string) => PlotLine[];\n\n  // 书名管理\n  bookTitles: Record<string, BookTitle[]>;\n  addBookTitle: (projectId: string, title: Omit<BookTitle, 'id' | 'createdAt'>) => void;\n  updateBookTitle: (projectId: string, id: string, updates: Partial<BookTitle>) => void;\n  deleteBookTitle: (projectId: string, id: string) => void;\n  toggleTitleFavorite: (projectId: string, id: string) => void;\n  getBookTitles: (projectId: string) => BookTitle[];\n\n  // 章节管理\n  chapters: Record<string, Chapter[]>;\n  addChapter: (projectId: string, chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateChapter: (projectId: string, id: string, updates: Partial<Chapter>) => void;\n  deleteChapter: (projectId: string, id: string) => void;\n  reorderChapters: (projectId: string, fromIndex: number, toIndex: number) => void;\n  getChapters: (projectId: string) => Chapter[];\n\n  // 提示词管理\n  promptTemplates: PromptTemplate[];\n  addPromptTemplate: (template: Omit<PromptTemplate, 'id'>) => void;\n  updatePromptTemplate: (id: string, updates: Partial<PromptTemplate>) => void;\n  deletePromptTemplate: (id: string) => void;\n  getPromptTemplatesByCategory: (category: string) => PromptTemplate[];\n\n  // 文档管理\n  documentStructures: Record<string, DocumentStructure>;\n  initializeDocumentStructure: (projectId: string) => void;\n  updateDocumentStructure: (projectId: string, structure: Partial<DocumentStructure>) => void;\n\n  // 执行引擎\n  executionContexts: Record<string, ExecutionContext>;\n  startExecution: (projectId: string, workflowId: string) => void;\n  pauseExecution: (projectId: string) => void;\n  resumeExecution: (projectId: string) => void;\n  stopExecution: (projectId: string) => void;\n  updateExecutionProgress: (projectId: string, progress: Partial<ExecutionContext>) => void;\n}\n\n// 生成唯一ID的工具函数\nconst generateId = () => Math.random().toString(36).substr(2, 9);\n\n// 创建Zustand store\nexport const useAppStore = create<AppState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        // 初始UI状态\n        ui: {\n          theme: 'light',\n          language: 'zh-CN',\n          sidebarCollapsed: false,\n          activeTab: 'workflow',\n          selectedProject: undefined,\n          notifications: [],\n        },\n\n        // UI状态管理\n        setTheme: (theme) => set((state) => ({ \n          ui: { ...state.ui, theme } \n        })),\n        \n        setLanguage: (language) => set((state) => ({ \n          ui: { ...state.ui, language } \n        })),\n        \n        toggleSidebar: () => set((state) => ({ \n          ui: { ...state.ui, sidebarCollapsed: !state.ui.sidebarCollapsed } \n        })),\n        \n        setActiveTab: (activeTab) => set((state) => ({ \n          ui: { ...state.ui, activeTab } \n        })),\n        \n        addNotification: (notification) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: [\n              ...state.ui.notifications,\n              {\n                ...notification,\n                id: generateId(),\n                timestamp: new Date(),\n                read: false,\n              }\n            ]\n          }\n        })),\n        \n        markNotificationRead: (id) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: state.ui.notifications.map(n => \n              n.id === id ? { ...n, read: true } : n\n            )\n          }\n        })),\n        \n        clearNotifications: () => set((state) => ({\n          ui: { ...state.ui, notifications: [] }\n        })),\n\n        // 项目管理\n        projects: [],\n        currentProject: null,\n        \n        createProject: (project) => set((state) => {\n          const newProject: Project = {\n            ...project,\n            id: generateId(),\n            createdAt: new Date(),\n            updatedAt: new Date(),\n          };\n          return {\n            projects: [...state.projects, newProject],\n            currentProject: newProject,\n            ui: { ...state.ui, selectedProject: newProject.id }\n          };\n        }),\n        \n        updateProject: (id, updates) => set((state) => ({\n          projects: state.projects.map(p => \n            p.id === id ? { ...p, ...updates, updatedAt: new Date() } : p\n          ),\n          currentProject: state.currentProject?.id === id \n            ? { ...state.currentProject, ...updates, updatedAt: new Date() }\n            : state.currentProject\n        })),\n        \n        deleteProject: (id) => set((state) => ({\n          projects: state.projects.filter(p => p.id !== id),\n          currentProject: state.currentProject?.id === id ? null : state.currentProject,\n          ui: { \n            ...state.ui, \n            selectedProject: state.ui.selectedProject === id ? undefined : state.ui.selectedProject \n          }\n        })),\n        \n        setCurrentProject: (id) => set((state) => {\n          const project = state.projects.find(p => p.id === id);\n          return {\n            currentProject: project || null,\n            ui: { ...state.ui, selectedProject: id }\n          };\n        }),\n\n        // 工作流管理\n        workflows: {},\n        currentWorkflow: [],\n        \n        addNode: (node) => set((state) => ({\n          currentWorkflow: [...state.currentWorkflow, { ...node, id: generateId() }]\n        })),\n        \n        updateNode: (id, updates) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === id ? { ...n, ...updates } : n\n          )\n        })),\n        \n        deleteNode: (id) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.filter(n => n.id !== id)\n        })),\n        \n        connectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: [...n.connections, { sourceId, targetId }] }\n              : n\n          )\n        })),\n        \n        disconnectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: n.connections.filter(c => !(c.sourceId === sourceId && c.targetId === targetId)) }\n              : n\n          )\n        })),\n        \n        loadWorkflow: (projectId) => set((state) => ({\n          currentWorkflow: state.workflows[projectId] || []\n        })),\n        \n        saveWorkflow: (projectId) => set((state) => ({\n          workflows: { ...state.workflows, [projectId]: state.currentWorkflow }\n        })),\n\n        // 角色管理\n        characters: {},\n        \n        addCharacter: (projectId, character) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: [\n              ...(state.characters[projectId] || []),\n              { ...character, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateCharacter: (projectId, id, updates) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates } : c\n            )\n          }\n        })),\n        \n        deleteCharacter: (projectId, id) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        \n        getCharacters: (projectId) => get().characters[projectId] || [],\n\n        // 世界观管理\n        worldBuilding: {},\n        \n        addWorldElement: (projectId, element) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: [\n              ...(state.worldBuilding[projectId] || []),\n              { ...element, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateWorldElement: (projectId, id, updates) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).map(w => \n              w.id === id ? { ...w, ...updates } : w\n            )\n          }\n        })),\n        \n        deleteWorldElement: (projectId, id) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).filter(w => w.id !== id)\n          }\n        })),\n        \n        getWorldElements: (projectId) => get().worldBuilding[projectId] || [],\n\n        // 其他管理功能的占位符实现\n        outlines: {},\n        addOutline: (projectId, outline) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: [\n              ...(state.outlines[projectId] || []),\n              { \n                ...outline, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateOutline: (projectId, id, updates) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).map(o => \n              o.id === id ? { ...o, ...updates, updatedAt: new Date() } : o\n            )\n          }\n        })),\n        deleteOutline: (projectId, id) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).filter(o => o.id !== id)\n          }\n        })),\n        getOutlines: (projectId) => get().outlines[projectId] || [],\n\n        plotLines: {},\n        addPlotLine: (projectId, plotLine) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: [\n              ...(state.plotLines[projectId] || []),\n              { ...plotLine, id: generateId() }\n            ]\n          }\n        })),\n        updatePlotLine: (projectId, id, updates) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).map(p => \n              p.id === id ? { ...p, ...updates } : p\n            )\n          }\n        })),\n        deletePlotLine: (projectId, id) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).filter(p => p.id !== id)\n          }\n        })),\n        getPlotLines: (projectId) => get().plotLines[projectId] || [],\n\n        bookTitles: {},\n        addBookTitle: (projectId, title) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: [\n              ...(state.bookTitles[projectId] || []),\n              { ...title, id: generateId(), createdAt: new Date() }\n            ]\n          }\n        })),\n        updateBookTitle: (projectId, id, updates) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, ...updates } : t\n            )\n          }\n        })),\n        deleteBookTitle: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).filter(t => t.id !== id)\n          }\n        })),\n        toggleTitleFavorite: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, isFavorite: !t.isFavorite } : t\n            )\n          }\n        })),\n        getBookTitles: (projectId) => get().bookTitles[projectId] || [],\n\n        chapters: {},\n        addChapter: (projectId, chapter) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: [\n              ...(state.chapters[projectId] || []),\n              { \n                ...chapter, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateChapter: (projectId, id, updates) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates, updatedAt: new Date() } : c\n            )\n          }\n        })),\n        deleteChapter: (projectId, id) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        reorderChapters: (projectId, fromIndex, toIndex) => set((state) => {\n          const chapters = [...(state.chapters[projectId] || [])];\n          const [removed] = chapters.splice(fromIndex, 1);\n          chapters.splice(toIndex, 0, removed);\n          // 重新设置order\n          chapters.forEach((chapter, index) => {\n            chapter.order = index + 1;\n          });\n          return {\n            chapters: {\n              ...state.chapters,\n              [projectId]: chapters\n            }\n          };\n        }),\n        getChapters: (projectId) => get().chapters[projectId] || [],\n\n        promptTemplates: [],\n        addPromptTemplate: (template) => set((state) => ({\n          promptTemplates: [...state.promptTemplates, { ...template, id: generateId() }]\n        })),\n        updatePromptTemplate: (id, updates) => set((state) => ({\n          promptTemplates: state.promptTemplates.map(t => \n            t.id === id ? { ...t, ...updates } : t\n          )\n        })),\n        deletePromptTemplate: (id) => set((state) => ({\n          promptTemplates: state.promptTemplates.filter(t => t.id !== id)\n        })),\n        getPromptTemplatesByCategory: (category) => \n          get().promptTemplates.filter(t => t.category === category),\n\n        documentStructures: {},\n        initializeDocumentStructure: (projectId) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              projectId,\n              folders: [],\n              files: [],\n              lastBackup: new Date()\n            }\n          }\n        })),\n        updateDocumentStructure: (projectId, structure) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              ...state.documentStructures[projectId],\n              ...structure\n            }\n          }\n        })),\n\n        executionContexts: {},\n        startExecution: (projectId, workflowId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              projectId,\n              workflowId,\n              status: 'running',\n              progress: {\n                totalSteps: 0,\n                completedSteps: 0,\n                currentStep: '',\n                percentage: 0\n              },\n              queue: [],\n              results: {},\n              startTime: new Date()\n            }\n          }\n        })),\n        pauseExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'paused'\n            }\n          }\n        })),\n        resumeExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'running'\n            }\n          }\n        })),\n        stopExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'idle',\n              endTime: new Date()\n            }\n          }\n        })),\n        updateExecutionProgress: (projectId, progress) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              ...progress\n            }\n          }\n        })),\n      }),\n      {\n        name: 'ai-novel-workflow-storage',\n        partialize: (state) => ({\n          projects: state.projects,\n          workflows: state.workflows,\n          characters: state.characters,\n          worldBuilding: state.worldBuilding,\n          outlines: state.outlines,\n          plotLines: state.plotLines,\n          bookTitles: state.bookTitles,\n          chapters: state.chapters,\n          promptTemplates: state.promptTemplates,\n          documentStructures: state.documentStructures,\n          ui: {\n            theme: state.ui.theme,\n            language: state.ui.language,\n            sidebarCollapsed: state.ui.sidebarCollapsed,\n          }\n        }),\n      }\n    ),\n    { name: 'ai-novel-workflow' }\n  )\n);\n\nexport default useAppStore;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAiHA,cAAc;AACd,MAAM,aAAa,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAGvD,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,SAAS;QACT,IAAI;YACF,OAAO;YACP,UAAU;YACV,kBAAkB;YAClB,WAAW;YACX,iBAAiB;YACjB,eAAe,EAAE;QACnB;QAEA,SAAS;QACT,UAAU,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAM;gBAC3B,CAAC;QAED,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAS;gBAC9B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,kBAAkB,CAAC,MAAM,EAAE,CAAC,gBAAgB;oBAAC;gBAClE,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAU;gBAC/B,CAAC;QAED,iBAAiB,CAAC,eAAiB,IAAI,CAAC,QAAU,CAAC;oBACjD,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe;+BACV,MAAM,EAAE,CAAC,aAAa;4BACzB;gCACE,GAAG,YAAY;gCACf,IAAI;gCACJ,WAAW,IAAI;gCACf,MAAM;4BACR;yBACD;oBACH;gBACF,CAAC;QAED,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe,MAAM,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,MAAM;4BAAK,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACxC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,eAAe,EAAE;oBAAC;gBACvC,CAAC;QAED,OAAO;QACP,UAAU,EAAE;QACZ,gBAAgB;QAEhB,eAAe,CAAC,UAAY,IAAI,CAAC;gBAC/B,MAAM,aAAsB;oBAC1B,GAAG,OAAO;oBACV,IAAI;oBACJ,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBACA,OAAO;oBACL,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAW;oBACzC,gBAAgB;oBAChB,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB,WAAW,EAAE;oBAAC;gBACpD;YACF;QAEA,eAAe,CAAC,IAAI,UAAY,IAAI,CAAC;oBAInB;uBAJ8B;oBAC9C,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAC3B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI;wBAAO,IAAI;oBAE9D,gBAAgB,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,EAAE,MAAK,KACzC;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;wBAAE,WAAW,IAAI;oBAAO,IAC7D,MAAM,cAAc;gBAC1B;;QAEA,eAAe,CAAC,KAAO,IAAI,CAAC;oBAEV;uBAFqB;oBACrC,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC9C,gBAAgB,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,EAAE,MAAK,KAAK,OAAO,MAAM,cAAc;oBAC7E,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,iBAAiB,MAAM,EAAE,CAAC,eAAe,KAAK,KAAK,YAAY,MAAM,EAAE,CAAC,eAAe;oBACzF;gBACF;;QAEA,mBAAmB,CAAC,KAAO,IAAI,CAAC;gBAC9B,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAClD,OAAO;oBACL,gBAAgB,WAAW;oBAC3B,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB;oBAAG;gBACzC;YACF;QAEA,QAAQ;QACR,WAAW,CAAC;QACZ,iBAAiB,EAAE;QAEnB,SAAS,CAAC,OAAS,IAAI,CAAC,QAAU,CAAC;oBACjC,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,IAAI;4BAAE,IAAI;wBAAa;qBAAE;gBAC5E,CAAC;QAED,YAAY,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QAED,YAAY,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClC,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QAED,cAAc,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa;mCAAI,EAAE,WAAW;gCAAE;oCAAE;oCAAU;gCAAS;6BAAE;wBAAC,IAChE;gBAER,CAAC;QAED,iBAAiB,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACvD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,QAAQ,KAAK,YAAY,EAAE,QAAQ,KAAK,QAAQ;wBAAG,IACtG;gBAER,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;gBACnD,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,WAAW;wBAAE,GAAG,MAAM,SAAS;wBAAE,CAAC,UAAU,EAAE,MAAM,eAAe;oBAAC;gBACtE,CAAC;QAED,OAAO;QACP,YAAY,CAAC;QAEb,cAAc,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,SAAS;gCAAE,IAAI;4BAAa;yBAClC;oBACH;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QAED,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,QAAQ;QACR,eAAe,CAAC;QAEhB,iBAAiB,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBACvD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE;+BACP,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;4BACxC;gCAAE,GAAG,OAAO;gCAAE,IAAI;4BAAa;yBAChC;oBACH;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9D,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACtD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACrD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC3E;gBACF,CAAC;QAED,kBAAkB,CAAC,YAAc,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;QAErE,eAAe;QACf,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,WAAW,CAAC;QACZ,aAAa,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE;+BACP,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;4BACpC;gCAAE,GAAG,QAAQ;gCAAE,IAAI;4BAAa;yBACjC;oBACH;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC1D,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IAClD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACjD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACvE;gBACF,CAAC;QACD,cAAc,CAAC,YAAc,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;QAE7D,YAAY,CAAC;QACb,cAAc,CAAC,WAAW,QAAU,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,KAAK;gCAAE,IAAI;gCAAc,WAAW,IAAI;4BAAO;yBACrD;oBACH;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QACD,qBAAqB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,YAAY,CAAC,EAAE,UAAU;4BAAC,IAAI;oBAExD;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,WAAW,UAAY,IAAI,CAAC;gBACvD,MAAM,WAAW;uBAAK,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;iBAAE;gBACvD,MAAM,CAAC,QAAQ,GAAG,SAAS,MAAM,CAAC,WAAW;gBAC7C,SAAS,MAAM,CAAC,SAAS,GAAG;gBAC5B,YAAY;gBACZ,SAAS,OAAO,CAAC,CAAC,SAAS;oBACzB,QAAQ,KAAK,GAAG,QAAQ;gBAC1B;gBACA,OAAO;oBACL,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;oBACf;gBACF;YACF;QACA,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,iBAAiB,EAAE;QACnB,mBAAmB,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBAC/C,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,QAAQ;4BAAE,IAAI;wBAAa;qBAAE;gBAChF,CAAC;QACD,sBAAsB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACrD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QACD,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QACD,8BAA8B,CAAC,WAC7B,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QAEnD,oBAAoB,CAAC;QACrB,6BAA6B,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC1D,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX;4BACA,SAAS,EAAE;4BACX,OAAO,EAAE;4BACT,YAAY,IAAI;wBAClB;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACjE,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,kBAAkB,CAAC,UAAU;4BACtC,GAAG,SAAS;wBACd;oBACF;gBACF,CAAC;QAED,mBAAmB,CAAC;QACpB,gBAAgB,CAAC,WAAW,aAAe,IAAI,CAAC,QAAU,CAAC;oBACzD,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX;4BACA;4BACA,QAAQ;4BACR,UAAU;gCACR,YAAY;gCACZ,gBAAgB;gCAChB,aAAa;gCACb,YAAY;4BACd;4BACA,OAAO,EAAE;4BACT,SAAS,CAAC;4BACV,WAAW,IAAI;wBACjB;oBACF;gBACF,CAAC;QACD,gBAAgB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC7C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,iBAAiB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC9C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC5C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;4BACR,SAAS,IAAI;wBACf;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBAChE,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,GAAG,QAAQ;wBACb;oBACF;gBACF,CAAC;IACH,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,eAAe,MAAM,aAAa;YAClC,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,UAAU,MAAM,QAAQ;YACxB,iBAAiB,MAAM,eAAe;YACtC,oBAAoB,MAAM,kBAAkB;YAC5C,IAAI;gBACF,OAAO,MAAM,EAAE,CAAC,KAAK;gBACrB,UAAU,MAAM,EAAE,CAAC,QAAQ;gBAC3B,kBAAkB,MAAM,EAAE,CAAC,gBAAgB;YAC7C;QACF,CAAC;AACH,IAEF;IAAE,MAAM;AAAoB;uCAIjB", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Layout, Menu, Button, Dropdown, Avatar, Badge, Space, Typography } from 'antd';\nimport { \n  MenuFoldOutlined, \n  MenuUnfoldOutlined,\n  BellOutlined,\n  SettingOutlined,\n  UserOutlined,\n  ProjectOutlined,\n  FileTextOutlined,\n  TeamOutlined,\n  GlobalOutlined,\n  BookOutlined,\n  BranchesOutlined,\n  ThunderboltOutlined,\n  FolderOutlined,\n  SunOutlined,\n  MoonOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { MenuProps } from 'antd';\n\nconst { Header, Sider, Content } = Layout;\nconst { Text } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const {\n    ui,\n    toggleSidebar,\n    setTheme,\n    setActiveTab,\n    currentProject\n  } = useAppStore();\n\n  const [collapsed, setCollapsed] = useState(ui.sidebarCollapsed);\n\n  const handleToggleSidebar = () => {\n    setCollapsed(!collapsed);\n    toggleSidebar();\n  };\n\n  const handleThemeToggle = () => {\n    setTheme(ui.theme === 'light' ? 'dark' : 'light');\n  };\n\n  // 侧边栏菜单项\n  const menuItems: MenuProps['items'] = [\n    {\n      key: 'workflow',\n      icon: <ThunderboltOutlined />,\n      label: '工作流编辑器',\n    },\n    {\n      key: 'projects',\n      icon: <ProjectOutlined />,\n      label: '项目总览',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'content-management',\n      icon: <FileTextOutlined />,\n      label: '内容管理',\n      children: [\n        {\n          key: 'outlines',\n          icon: <FileTextOutlined />,\n          label: '大纲管理',\n        },\n        {\n          key: 'characters',\n          icon: <TeamOutlined />,\n          label: '角色管理',\n        },\n        {\n          key: 'worldbuilding',\n          icon: <GlobalOutlined />,\n          label: '世界观管理',\n        },\n        {\n          key: 'plotlines',\n          icon: <BranchesOutlined />,\n          label: '主线管理',\n        },\n        {\n          key: 'titles',\n          icon: <BookOutlined />,\n          label: '书名管理',\n        },\n      ],\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'documents',\n      icon: <FolderOutlined />,\n      label: '文档管理',\n    },\n    {\n      key: 'prompts',\n      icon: <FileTextOutlined />,\n      label: '提示词管理',\n    },\n  ];\n\n  // 用户菜单\n  const userMenuItems: MenuProps['items'] = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      label: '退出登录',\n    },\n  ];\n\n  // 通知菜单\n  const notifications = ui?.notifications || [];\n  const notificationMenuItems: MenuProps['items'] = notifications.slice(0, 5).map(notification => ({\n    key: notification.id,\n    label: (\n      <div className=\"max-w-xs\">\n        <div className=\"font-medium text-sm\">{notification.title}</div>\n        <div className=\"text-xs text-gray-500 mt-1\">{notification.message}</div>\n        <div className=\"text-xs text-gray-400 mt-1\">\n          {notification.timestamp.toLocaleTimeString()}\n        </div>\n      </div>\n    ),\n  }));\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    setActiveTab(key);\n  };\n\n  return (\n    <Layout className=\"min-h-screen\">\n      {/* 侧边栏 */}\n      <Sider \n        trigger={null} \n        collapsible \n        collapsed={collapsed}\n        width={240}\n        className=\"bg-white border-r border-gray-200\"\n        theme=\"light\"\n      >\n        {/* Logo区域 */}\n        <div className=\"h-16 flex items-center justify-center border-b border-gray-200\">\n          {collapsed ? (\n            <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n              <ThunderboltOutlined className=\"text-white text-lg\" />\n            </div>\n          ) : (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n                <ThunderboltOutlined className=\"text-white text-lg\" />\n              </div>\n              <Text strong className=\"text-lg\">AI小说工作流</Text>\n            </div>\n          )}\n        </div>\n\n        {/* 当前项目信息 */}\n        {!collapsed && currentProject && (\n          <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n            <Text type=\"secondary\" className=\"text-xs\">当前项目</Text>\n            <div className=\"mt-1\">\n              <Text strong className=\"text-sm\">{currentProject.name}</Text>\n            </div>\n            <div className=\"mt-1\">\n              <Text type=\"secondary\" className=\"text-xs\">\n                {currentProject.status === 'draft' && '草稿'}\n                {currentProject.status === 'in-progress' && '进行中'}\n                {currentProject.status === 'completed' && '已完成'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {/* 菜单 */}\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[ui.activeTab]}\n          items={menuItems}\n          onClick={handleMenuClick}\n          className=\"border-none\"\n        />\n      </Sider>\n\n      <Layout>\n        {/* 顶部导航栏 */}\n        <Header className=\"bg-white border-b border-gray-200 px-4 flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={handleToggleSidebar}\n              className=\"text-lg\"\n            />\n            \n            {/* 面包屑导航 */}\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n              <span>\n                {ui.activeTab === 'workflow' && '工作流编辑器'}\n                {ui.activeTab === 'projects' && '项目总览'}\n                {ui.activeTab === 'outlines' && '大纲管理'}\n                {ui.activeTab === 'characters' && '角色管理'}\n                {ui.activeTab === 'worldbuilding' && '世界观管理'}\n                {ui.activeTab === 'plotlines' && '主线管理'}\n                {ui.activeTab === 'titles' && '书名管理'}\n                {ui.activeTab === 'documents' && '文档管理'}\n                {ui.activeTab === 'prompts' && '提示词管理'}\n              </span>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* 主题切换 */}\n            <Button\n              type=\"text\"\n              icon={ui.theme === 'light' ? <MoonOutlined /> : <SunOutlined />}\n              onClick={handleThemeToggle}\n              className=\"text-lg\"\n            />\n\n            {/* 通知 */}\n            <Dropdown\n              menu={{ items: notificationMenuItems }}\n              trigger={['click']}\n              placement=\"bottomRight\"\n            >\n              <Button type=\"text\" className=\"text-lg\">\n                <Badge count={notifications.filter(n => !n.read).length} size=\"small\">\n                  <BellOutlined />\n                </Badge>\n              </Button>\n            </Dropdown>\n\n            {/* 用户菜单 */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              trigger={['click']}\n              placement=\"bottomRight\"\n            >\n              <Space className=\"cursor-pointer\">\n                <Avatar icon={<UserOutlined />} />\n                <Text>用户</Text>\n              </Space>\n            </Dropdown>\n          </div>\n        </Header>\n\n        {/* 主内容区域 */}\n        <Content className=\"bg-gray-50 overflow-auto\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;;;AArBA;;;;;AAwBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAM3B,MAAM,aAAwC;QAAC,EAAE,QAAQ,EAAE;;IACzD,MAAM,EACJ,EAAE,EACF,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,cAAc,EACf,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,gBAAgB;IAE9D,MAAM,sBAAsB;QAC1B,aAAa,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,SAAS,GAAG,KAAK,KAAK,UAAU,SAAS;IAC3C;IAEA,SAAS;IACT,MAAM,YAAgC;QACpC;YACE,KAAK;YACL,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;QACT;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;gBACT;aACD;QACH;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;KACD;IAED,OAAO;IACP,MAAM,gBAAoC;QACxC;YACE,KAAK;YACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;QACT;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;QACT;KACD;IAED,OAAO;IACP,MAAM,gBAAgB,CAAA,eAAA,yBAAA,GAAI,aAAa,KAAI,EAAE;IAC7C,MAAM,wBAA4C,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,eAAgB,CAAC;YAC/F,KAAK,aAAa,EAAE;YACpB,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAuB,aAAa,KAAK;;;;;;kCACxD,6LAAC;wBAAI,WAAU;kCAA8B,aAAa,OAAO;;;;;;kCACjE,6LAAC;wBAAI,WAAU;kCACZ,aAAa,SAAS,CAAC,kBAAkB;;;;;;;;;;;;QAIlD,CAAC;IAED,MAAM,kBAAkB;YAAC,EAAE,GAAG,EAAmB;QAC/C,aAAa;IACf;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAU;;0BAEhB,6LAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,WAAU;gBACV,OAAM;;kCAGN,6LAAC;wBAAI,WAAU;kCACZ,0BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;;;qFAGjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAEjC,6LAAC;oCAAK,MAAM;oCAAC,WAAU;8CAAU;;;;;;;;;;;;;;;;;oBAMtC,CAAC,aAAa,gCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,MAAK;gCAAY,WAAU;0CAAU;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,MAAM;oCAAC,WAAU;8CAAW,eAAe,IAAI;;;;;;;;;;;0CAEvD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,MAAK;oCAAY,WAAU;;wCAC9B,eAAe,MAAM,KAAK,WAAW;wCACrC,eAAe,MAAM,KAAK,iBAAiB;wCAC3C,eAAe,MAAM,KAAK,eAAe;;;;;;;;;;;;;;;;;;kCAOlD,6LAAC,iLAAA,CAAA,OAAI;wBACH,MAAK;wBACL,cAAc;4BAAC,GAAG,SAAS;yBAAC;wBAC5B,OAAO;wBACP,SAAS;wBACT,WAAU;;;;;;;;;;;;0BAId,6LAAC,qLAAA,CAAA,SAAM;;kCAEL,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,0BAAY,6LAAC,iOAAA,CAAA,qBAAkB;;;;mEAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;gDACE,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,gBAAgB;gDACjC,GAAG,SAAS,KAAK,mBAAmB;gDACpC,GAAG,SAAS,KAAK,eAAe;gDAChC,GAAG,SAAS,KAAK,YAAY;gDAC7B,GAAG,SAAS,KAAK,eAAe;gDAChC,GAAG,SAAS,KAAK,aAAa;;;;;;;;;;;;;;;;;;0CAKrC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,GAAG,KAAK,KAAK,wBAAU,6LAAC,qNAAA,CAAA,eAAY;;;;mEAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAIZ,6LAAC,yLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAsB;wCACrC,SAAS;4CAAC;yCAAQ;wCAClB,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,WAAU;sDAC5B,cAAA,6LAAC,mLAAA,CAAA,QAAK;gDAAC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;gDAAE,MAAK;0DAC5D,cAAA,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kDAMnB,6LAAC,yLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAc;wCAC7B,SAAS;4CAAC;yCAAQ;wCAClB,WAAU;kDAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,qLAAA,CAAA,SAAM;oDAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;8DAC3B,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,6LAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;GAtPM;;QAOA,wHAAA,CAAA,cAAW;;;KAPX;uCAwPS", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/workflow/CustomNode.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Handle, Position, NodeProps } from '@xyflow/react';\nimport { Card, Typography, Tag, Button } from 'antd';\nimport { \n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  SettingOutlined,\n  DeleteOutlined\n} from '@ant-design/icons';\n\nconst { Text } = Typography;\n\ninterface CustomNodeData {\n  label: string;\n  type: string;\n  status: 'idle' | 'running' | 'completed' | 'error';\n  description?: string;\n  config?: Record<string, any>;\n  onEdit?: () => void;\n  onDelete?: () => void;\n}\n\nconst CustomNode: React.FC<NodeProps<CustomNodeData>> = ({ data, selected }) => {\n  const getStatusIcon = () => {\n    switch (data.status) {\n      case 'running':\n        return <PauseCircleOutlined className=\"text-blue-500\" />;\n      case 'completed':\n        return <CheckCircleOutlined className=\"text-green-500\" />;\n      case 'error':\n        return <ExclamationCircleOutlined className=\"text-red-500\" />;\n      default:\n        return <PlayCircleOutlined className=\"text-gray-400\" />;\n    }\n  };\n\n  const getStatusColor = () => {\n    switch (data.status) {\n      case 'running':\n        return 'processing';\n      case 'completed':\n        return 'success';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const getNodeIcon = () => {\n    switch (data.type) {\n      case 'input':\n        return '📝';\n      case 'title-generator':\n        return '📚';\n      case 'detail-generator':\n        return '📄';\n      case 'character-creator':\n        return '👥';\n      case 'worldbuilding':\n        return '🌍';\n      case 'plotline-planner':\n        return '📈';\n      case 'outline-generator':\n        return '📋';\n      case 'chapter-count-input':\n        return '🔢';\n      case 'detailed-outline':\n        return '📝';\n      case 'chapter-generator':\n        return '📖';\n      case 'content-polisher':\n        return '✨';\n      case 'consistency-checker':\n        return '✅';\n      case 'condition':\n        return '🔀';\n      case 'loop':\n        return '🔄';\n      case 'output':\n        return '📤';\n      default:\n        return '⚙️';\n    }\n  };\n\n  return (\n    <div className={`custom-node ${selected ? 'selected' : ''}`}>\n      <Handle\n        type=\"target\"\n        position={Position.Top}\n        className=\"w-3 h-3 !bg-gray-400 border-2 border-white\"\n      />\n      \n      <Card\n        size=\"small\"\n        className={`min-w-48 shadow-md transition-all duration-200 ${\n          selected ? 'ring-2 ring-blue-500 shadow-lg' : ''\n        } ${data.status === 'running' ? 'animate-pulse' : ''}`}\n        bodyStyle={{ padding: '12px' }}\n      >\n        <div className=\"flex items-start justify-between mb-2\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg\">{getNodeIcon()}</span>\n            <div>\n              <Text strong className=\"text-sm block\">\n                {data.label}\n              </Text>\n              {data.description && (\n                <Text type=\"secondary\" className=\"text-xs\">\n                  {data.description}\n                </Text>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            {getStatusIcon()}\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <Tag color={getStatusColor()} size=\"small\">\n            {data.status === 'idle' && '等待'}\n            {data.status === 'running' && '运行中'}\n            {data.status === 'completed' && '已完成'}\n            {data.status === 'error' && '错误'}\n          </Tag>\n          \n          <div className=\"flex space-x-1\">\n            <Button\n              type=\"text\"\n              size=\"small\"\n              icon={<SettingOutlined />}\n              onClick={data.onEdit}\n              className=\"text-xs\"\n            />\n            <Button\n              type=\"text\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={data.onDelete}\n              className=\"text-xs\"\n            />\n          </div>\n        </div>\n\n        {/* 配置信息 */}\n        {data.config && Object.keys(data.config).length > 0 && (\n          <div className=\"mt-2 pt-2 border-t border-gray-100\">\n            <Text type=\"secondary\" className=\"text-xs\">\n              已配置 {Object.keys(data.config).length} 个参数\n            </Text>\n          </div>\n        )}\n      </Card>\n\n      <Handle\n        type=\"source\"\n        position={Position.Bottom}\n        className=\"w-3 h-3 !bg-gray-400 border-2 border-white\"\n      />\n\n      <style jsx>{`\n        .custom-node.selected {\n          z-index: 1000;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default CustomNode;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAcA,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAY3B,MAAM,aAAkD;QAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;IACzE,MAAM,gBAAgB;QACpB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACxC,KAAK;gBACH,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACxC,KAAK;gBACH,qBAAO,6LAAC,+OAAA,CAAA,4BAAyB;oBAAC,WAAU;;;;;;YAC9C;gBACE,qBAAO,6LAAC,iOAAA,CAAA,qBAAkB;oBAAC,WAAU;;;;;;QACzC;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc;QAClB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;kDAAe,AAAC,eAAyC,OAA3B,WAAW,aAAa;;0BACrD,6LAAC,4KAAA,CAAA,SAAM;gBACL,MAAK;gBACL,UAAU,6JAAA,CAAA,WAAQ,CAAC,GAAG;gBACtB,WAAU;;;;;;0BAGZ,6LAAC,iLAAA,CAAA,OAAI;gBACH,MAAK;gBACL,WAAW,AAAC,kDAER,OADF,WAAW,mCAAmC,IAC/C,KAAoD,OAAjD,KAAK,MAAM,KAAK,YAAY,kBAAkB;gBAClD,WAAW;oBAAE,SAAS;gBAAO;;kCAE7B,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;kDACb,6LAAC;kFAAe;kDAAW;;;;;;kDAC3B,6LAAC;;;0DACC,6LAAC;gDAAK,MAAM;gDAAC,WAAU;0DACpB,KAAK,KAAK;;;;;;4CAEZ,KAAK,WAAW,kBACf,6LAAC;gDAAK,MAAK;gDAAY,WAAU;0DAC9B,KAAK,WAAW;;;;;;;;;;;;;;;;;;0CAKzB,6LAAC;0EAAc;0CACZ;;;;;;;;;;;;kCAIL,6LAAC;kEAAc;;0CACb,6LAAC,+KAAA,CAAA,MAAG;gCAAC,OAAO;gCAAkB,MAAK;;oCAChC,KAAK,MAAM,KAAK,UAAU;oCAC1B,KAAK,MAAM,KAAK,aAAa;oCAC7B,KAAK,MAAM,KAAK,eAAe;oCAC/B,KAAK,MAAM,KAAK,WAAW;;;;;;;0CAG9B,6LAAC;0EAAc;;kDACb,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAK;wCACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;wCACtB,SAAS,KAAK,MAAM;wCACpB,WAAU;;;;;;kDAEZ,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAK;wCACL,MAAM;wCACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,KAAK,QAAQ;wCACtB,WAAU;;;;;;;;;;;;;;;;;;oBAMf,KAAK,MAAM,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE,MAAM,GAAG,mBAChD,6LAAC;kEAAc;kCACb,cAAA,6LAAC;4BAAK,MAAK;4BAAY,WAAU;;gCAAU;gCACpC,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAM7C,6LAAC,4KAAA,CAAA,SAAM;gBACL,MAAK;gBACL,UAAU,6JAAA,CAAA,WAAQ,CAAC,MAAM;gBACzB,WAAU;;;;;;;;;;;;;;;;AAUlB;KApJM;uCAsJS", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/workflow/WorkflowEditor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState, useMemo } from 'react';\nimport { Card, Button, Space, Typography, Divider, message, Modal, Form, Input, Select } from 'antd';\nimport {\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  SaveOutlined,\n  PlusOutlined,\n  SettingOutlined,\n  DeleteOutlined,\n  LinkOutlined,\n  RobotOutlined\n} from '@ant-design/icons';\nimport {\n  ReactFlow,\n  Background,\n  Controls,\n  MiniMap,\n  useNodesState,\n  useEdgesState,\n  addEdge,\n  Connection,\n  Edge,\n  Node,\n  NodeTypes\n} from '@xyflow/react';\nimport '@xyflow/react/dist/style.css';\nimport { useAppStore } from '@/store';\nimport CustomNode from './CustomNode';\nimport WorkflowAIAssistant from './WorkflowAIAssistant';\nimport { WorkflowTemplate } from '@/utils/workflowAI';\n\nconst { Title, Text } = Typography;\n\nconst WorkflowEditor: React.FC = () => {\n  const {\n    currentProject,\n    currentWorkflow,\n    addNode,\n    updateNode,\n    deleteNode,\n    connectNodes,\n    saveWorkflow,\n    executionContexts,\n    startExecution,\n    pauseExecution,\n    stopExecution\n  } = useAppStore();\n\n  const [selectedNode, setSelectedNode] = useState<string | null>(null);\n  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);\n  const [isAIAssistantVisible, setIsAIAssistantVisible] = useState(false);\n  const [editingNode, setEditingNode] = useState<any>(null);\n  const [form] = Form.useForm();\n\n  const executionContext = currentProject ? executionContexts[currentProject.id] : null;\n\n  // 转换工作流数据为React Flow格式\n  const initialNodes: Node[] = currentWorkflow.map(node => ({\n    id: node.id,\n    type: 'custom',\n    position: node.position,\n    data: {\n      label: node.data.label,\n      type: node.type,\n      status: node.data.status,\n      description: getNodeDescription(node.type),\n      config: node.data.config,\n      onEdit: () => handleEditNode(node),\n      onDelete: () => handleDeleteNode(node.id),\n    },\n  }));\n\n  const initialEdges: Edge[] = currentWorkflow.flatMap(node =>\n    node.connections.map(conn => ({\n      id: `${conn.sourceId}-${conn.targetId}`,\n      source: conn.sourceId,\n      target: conn.targetId,\n      type: 'smoothstep',\n      animated: node.data.status === 'running',\n    }))\n  );\n\n  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);\n  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n\n  const reactFlowNodeTypes: NodeTypes = useMemo(() => ({\n    custom: CustomNode,\n  }), []);\n\n  const getNodeDescription = (nodeType: string) => {\n    const descriptions: Record<string, string> = {\n      'input': '用户参数输入',\n      'title-generator': 'AI生成书名候选',\n      'detail-generator': '生成小说基础信息',\n      'character-creator': 'AI生成角色设定',\n      'worldbuilding': 'AI生成世界背景',\n      'plotline-planner': 'AI生成主要故事线',\n      'outline-generator': 'AI生成故事结构',\n      'chapter-count-input': '用户指定章节总数',\n      'detailed-outline': '生成详细情节要点',\n      'chapter-generator': 'AI生成具体章节内容',\n      'content-polisher': 'AI优化文本质量',\n      'consistency-checker': '检查内容一致性',\n      'condition': '条件分支',\n      'loop': '循环执行',\n      'output': '最终结果展示',\n    };\n    return descriptions[nodeType] || '未知节点类型';\n  };\n\n  const handleSaveWorkflow = useCallback(() => {\n    if (currentProject) {\n      saveWorkflow(currentProject.id);\n      message.success('工作流已保存');\n    }\n  }, [currentProject, saveWorkflow]);\n\n  const handleStartExecution = useCallback(() => {\n    if (currentProject) {\n      startExecution(currentProject.id, 'default');\n      message.info('工作流开始执行');\n    }\n  }, [currentProject, startExecution]);\n\n  const handlePauseExecution = useCallback(() => {\n    if (currentProject) {\n      pauseExecution(currentProject.id);\n      message.info('工作流已暂停');\n    }\n  }, [currentProject, pauseExecution]);\n\n  const handleStopExecution = useCallback(() => {\n    if (currentProject) {\n      stopExecution(currentProject.id);\n      message.info('工作流已停止');\n    }\n  }, [currentProject, stopExecution]);\n\n  const handleEditNode = useCallback((node: any) => {\n    setEditingNode(node);\n    form.setFieldsValue({\n      label: node.data.label,\n      ...node.data.config,\n    });\n    setIsConfigModalVisible(true);\n  }, [form]);\n\n  const handleDeleteNode = useCallback((nodeId: string) => {\n    deleteNode(nodeId);\n    setNodes(nodes => nodes.filter(n => n.id !== nodeId));\n    setEdges(edges => edges.filter(e => e.source !== nodeId && e.target !== nodeId));\n    message.success('节点已删除');\n  }, [deleteNode, setNodes, setEdges]);\n\n  const handleCreateSampleWorkflow = useCallback(() => {\n    // 清空现有节点和连接\n    setNodes([]);\n    setEdges([]);\n\n    // 创建示例节点\n    const sampleNodes = [\n      {\n        id: 'input-1',\n        type: 'custom',\n        position: { x: 100, y: 100 },\n        data: {\n          label: '用户输入',\n          type: 'input',\n          status: 'idle' as const,\n          description: '输入创作主题和风格',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('input-1'),\n        },\n      },\n      {\n        id: 'title-gen-1',\n        type: 'custom',\n        position: { x: 100, y: 250 },\n        data: {\n          label: '书名生成',\n          type: 'title-generator',\n          status: 'idle' as const,\n          description: 'AI生成书名候选',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('title-gen-1'),\n        },\n      },\n      {\n        id: 'character-1',\n        type: 'custom',\n        position: { x: 350, y: 250 },\n        data: {\n          label: '角色创建',\n          type: 'character-creator',\n          status: 'idle' as const,\n          description: 'AI生成角色设定',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('character-1'),\n        },\n      },\n      {\n        id: 'outline-1',\n        type: 'custom',\n        position: { x: 225, y: 400 },\n        data: {\n          label: '大纲生成',\n          type: 'outline-generator',\n          status: 'idle' as const,\n          description: 'AI生成故事结构',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('outline-1'),\n        },\n      },\n      {\n        id: 'output-1',\n        type: 'custom',\n        position: { x: 225, y: 550 },\n        data: {\n          label: '结果输出',\n          type: 'output',\n          status: 'idle' as const,\n          description: '最终结果展示',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('output-1'),\n        },\n      },\n    ];\n\n    // 创建示例连接\n    const sampleEdges: Edge[] = [\n      {\n        id: 'input-1-title-gen-1',\n        source: 'input-1',\n        target: 'title-gen-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'input-1-character-1',\n        source: 'input-1',\n        target: 'character-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'title-gen-1-outline-1',\n        source: 'title-gen-1',\n        target: 'outline-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'character-1-outline-1',\n        source: 'character-1',\n        target: 'outline-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'outline-1-output-1',\n        source: 'outline-1',\n        target: 'output-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n    ];\n\n    setNodes(sampleNodes);\n    setEdges(sampleEdges);\n\n    message.success('示例工作流已创建！您可以拖拽节点来连接它们');\n  }, [setNodes, setEdges, handleDeleteNode]);\n\n  // 处理AI工作流应用\n  const handleApplyAIWorkflow = useCallback((template: WorkflowTemplate) => {\n    // 清空现有节点和连接\n    setNodes([]);\n    setEdges([]);\n\n    // 转换模板节点为React Flow格式\n    const reactFlowNodes = template.nodes.map((node, index) => ({\n      id: `node-${index}`,\n      type: 'custom',\n      position: node.position,\n      data: {\n        label: node.label,\n        type: node.type,\n        status: 'idle' as const,\n        description: getNodeDescription(node.type),\n        config: node.config || {},\n        onEdit: () => {},\n        onDelete: () => handleDeleteNode(`node-${index}`),\n      },\n    }));\n\n    // 转换模板连接为React Flow格式\n    const reactFlowEdges = template.connections.map((conn, index) => ({\n      id: `edge-${index}`,\n      source: `node-${conn.source}`,\n      target: `node-${conn.target}`,\n      type: 'smoothstep',\n      animated: false,\n      style: { stroke: '#3b82f6', strokeWidth: 2 },\n    }));\n\n    setNodes(reactFlowNodes);\n    setEdges(reactFlowEdges);\n\n    message.success(`AI工作流\"${template.name}\"已应用成功！`);\n  }, [setNodes, setEdges, handleDeleteNode, getNodeDescription]);\n\n\n\n  const handleAddNode = useCallback((nodeType: string) => {\n    const nodeId = `node-${Date.now()}`;\n    const position = { x: Math.random() * 400, y: Math.random() * 300 };\n\n    // 同时添加到React Flow\n    const reactFlowNode: Node = {\n      id: nodeId,\n      type: 'custom',\n      position,\n      data: {\n        label: `新${nodeType}节点`,\n        type: nodeType,\n        status: 'idle' as const,\n        description: getNodeDescription(nodeType),\n        config: {},\n        onEdit: () => handleEditNode({ id: nodeId, type: nodeType, data: { label: `新${nodeType}节点`, config: {}, status: 'idle' as const } }),\n        onDelete: () => handleDeleteNode(nodeId),\n      },\n    };\n    setNodes(nodes => [...nodes, reactFlowNode]);\n    message.success('节点已添加');\n  }, [setNodes, handleEditNode, handleDeleteNode]);\n\n  const onConnect = useCallback((params: Connection) => {\n    // 检查连接的有效性\n    if (!params.source || !params.target) {\n      message.error('连接失败：源节点或目标节点无效');\n      return;\n    }\n\n    // 防止自连接\n    if (params.source === params.target) {\n      message.warning('不能连接到自身节点');\n      return;\n    }\n\n    // 创建带样式的连接\n    const newEdge = {\n      ...params,\n      type: 'smoothstep',\n      animated: false,\n      style: {\n        stroke: '#3b82f6',\n        strokeWidth: 2\n      },\n    };\n\n    setEdges((eds) => addEdge(newEdge, eds));\n\n    // 同时更新store中的连接信息\n    if (currentProject && params.source && params.target) {\n      connectNodes(params.source, params.target);\n    }\n\n    message.success('节点连接成功');\n  }, [setEdges, connectNodes, currentProject]);\n\n  const nodeTypeOptions = [\n    { key: 'input', label: '输入节点', description: '用户参数输入' },\n    { key: 'title-generator', label: '书名生成', description: 'AI生成书名候选' },\n    { key: 'detail-generator', label: '详情生成', description: '生成小说基础信息' },\n    { key: 'character-creator', label: '角色创建', description: 'AI生成角色设定' },\n    { key: 'worldbuilding', label: '世界观构建', description: 'AI生成世界背景' },\n    { key: 'plotline-planner', label: '主线规划', description: 'AI生成主要故事线' },\n    { key: 'outline-generator', label: '大纲规划', description: 'AI生成故事结构' },\n    { key: 'chapter-count-input', label: '章节数量', description: '用户指定章节总数' },\n    { key: 'detailed-outline', label: '细纲生成', description: '生成详细情节要点' },\n    { key: 'chapter-generator', label: '章节生成', description: 'AI生成具体章节内容' },\n    { key: 'content-polisher', label: '内容润色', description: 'AI优化文本质量' },\n    { key: 'consistency-checker', label: '一致性检查', description: '检查内容一致性' },\n    { key: 'output', label: '输出节点', description: '最终结果展示' },\n  ];\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能编辑工作流。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* 工具栏 */}\n      <div className=\"bg-white border-b border-gray-200 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <Title level={4} className=\"mb-0\">工作流编辑器</Title>\n            <Text type=\"secondary\">项目: {currentProject.name}</Text>\n          </div>\n          \n          <Space>\n            <Button\n              icon={<SaveOutlined />}\n              onClick={handleSaveWorkflow}\n            >\n              保存工作流\n            </Button>\n\n            <Button\n              onClick={handleCreateSampleWorkflow}\n            >\n              创建示例工作流\n            </Button>\n\n            <Button\n              type=\"primary\"\n              icon={<RobotOutlined />}\n              onClick={() => setIsAIAssistantVisible(true)}\n            >\n              AI工作流助手\n            </Button>\n            \n            {executionContext?.status === 'running' ? (\n              <>\n                <Button \n                  icon={<PauseCircleOutlined />} \n                  onClick={handlePauseExecution}\n                >\n                  暂停\n                </Button>\n                <Button \n                  icon={<StopOutlined />} \n                  danger\n                  onClick={handleStopExecution}\n                >\n                  停止\n                </Button>\n              </>\n            ) : (\n              <Button \n                type=\"primary\"\n                icon={<PlayCircleOutlined />} \n                onClick={handleStartExecution}\n                disabled={currentWorkflow.length === 0}\n              >\n                开始执行\n              </Button>\n            )}\n          </Space>\n        </div>\n\n        {/* 执行状态 */}\n        {executionContext && (\n          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <Text strong>执行状态: {\n                executionContext.status === 'running' ? '运行中' :\n                executionContext.status === 'paused' ? '已暂停' :\n                executionContext.status === 'completed' ? '已完成' :\n                executionContext.status === 'error' ? '错误' : '空闲'\n              }</Text>\n              <Text>进度: {executionContext.progress.percentage.toFixed(1)}%</Text>\n            </div>\n            {executionContext.progress.currentStep && (\n              <Text type=\"secondary\">当前步骤: {executionContext.progress.currentStep}</Text>\n            )}\n          </div>\n        )}\n      </div>\n\n      <div className=\"flex-1 flex\">\n        {/* 节点面板 */}\n        <div className=\"w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto\">\n          <Title level={5}>节点库</Title>\n          <div className=\"space-y-2\">\n            {nodeTypeOptions.map((nodeType) => (\n              <Card \n                key={nodeType.key}\n                size=\"small\" \n                className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                onClick={() => handleAddNode(nodeType.key)}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <Text strong className=\"text-sm\">{nodeType.label}</Text>\n                    <div className=\"text-xs text-gray-500 mt-1\">\n                      {nodeType.description}\n                    </div>\n                  </div>\n                  <PlusOutlined className=\"text-blue-500\" />\n                </div>\n              </Card>\n            ))}\n          </div>\n        </div>\n\n        {/* 工作流画布 */}\n        <div className=\"flex-1 bg-gray-50 relative\">\n          <div className=\"absolute inset-0\">\n            {nodes.length === 0 ? (\n              <div className=\"w-full h-full flex items-center justify-center bg-white\">\n                <div className=\"text-center\">\n                  <Title level={4} type=\"secondary\">工作流画布</Title>\n                  <Text type=\"secondary\">\n                    从左侧节点库中选择节点开始构建您的AI小说生成工作流\n                  </Text>\n                </div>\n              </div>\n            ) : (\n              <ReactFlow\n                nodes={nodes}\n                edges={edges}\n                onNodesChange={onNodesChange}\n                onEdgesChange={onEdgesChange}\n                onConnect={onConnect}\n                nodeTypes={reactFlowNodeTypes}\n                fitView\n                className=\"bg-gray-50\"\n              >\n                <Background />\n                <Controls />\n                <MiniMap\n                  nodeColor={(node) => {\n                    switch (node.data?.status) {\n                      case 'running': return '#3b82f6';\n                      case 'completed': return '#10b981';\n                      case 'error': return '#ef4444';\n                      default: return '#6b7280';\n                    }\n                  }}\n                />\n              </ReactFlow>\n            )}\n          </div>\n        </div>\n\n      </div>\n\n      {/* 节点配置模态框 */}\n      <Modal\n        title=\"节点配置\"\n        open={isConfigModalVisible}\n        onOk={async () => {\n          try {\n            const values = await form.validateFields();\n            if (editingNode) {\n              // 更新节点配置\n              setNodes(nodes =>\n                nodes.map(node =>\n                  node.id === editingNode.id\n                    ? {\n                        ...node,\n                        data: {\n                          ...node.data,\n                          label: values.label,\n                          config: { ...(node.data.config || {}), ...values }\n                        }\n                      }\n                    : node\n                )\n              );\n              message.success('节点配置已更新');\n            }\n            setIsConfigModalVisible(false);\n            form.resetFields();\n          } catch (error) {\n            console.error('配置验证失败:', error);\n          }\n        }}\n        onCancel={() => {\n          setIsConfigModalVisible(false);\n          form.resetFields();\n        }}\n        width={600}\n      >\n        <Form form={form} layout=\"vertical\">\n          <Form.Item\n            name=\"label\"\n            label=\"节点名称\"\n            rules={[{ required: true, message: '请输入节点名称' }]}\n          >\n            <Input placeholder=\"请输入节点名称\" />\n          </Form.Item>\n\n          {editingNode?.type === 'input' && (\n            <>\n              <Form.Item name=\"theme\" label=\"创作主题\">\n                <Input placeholder=\"请输入创作主题\" />\n              </Form.Item>\n              <Form.Item name=\"style\" label=\"写作风格\">\n                <Select placeholder=\"请选择写作风格\">\n                  <Select.Option value=\"轻松幽默\">轻松幽默</Select.Option>\n                  <Select.Option value=\"深沉严肃\">深沉严肃</Select.Option>\n                  <Select.Option value=\"浪漫温馨\">浪漫温馨</Select.Option>\n                </Select>\n              </Form.Item>\n            </>\n          )}\n\n          {editingNode?.type === 'chapter-count-input' && (\n            <Form.Item\n              name=\"chapterCount\"\n              label=\"章节数量\"\n              rules={[{ required: true, message: '请输入章节数量' }]}\n            >\n              <Input type=\"number\" min={1} max={500} placeholder=\"请输入章节数量\" />\n            </Form.Item>\n          )}\n\n          {editingNode?.type === 'title-generator' && (\n            <>\n              <Form.Item name=\"count\" label=\"生成数量\">\n                <Input type=\"number\" min={1} max={20} placeholder=\"生成书名数量\" />\n              </Form.Item>\n              <Form.Item name=\"genre\" label=\"小说类型\">\n                <Select placeholder=\"请选择小说类型\">\n                  <Select.Option value=\"现代都市\">现代都市</Select.Option>\n                  <Select.Option value=\"古代言情\">古代言情</Select.Option>\n                  <Select.Option value=\"玄幻修仙\">玄幻修仙</Select.Option>\n                </Select>\n              </Form.Item>\n            </>\n          )}\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default WorkflowEditor;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAcA;AACA;;;AA9BA;;;;;;;;AAkCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAElC,MAAM,iBAA2B;;IAC/B,MAAM,EACJ,cAAc,EACd,eAAe,EACf,OAAO,EACP,UAAU,EACV,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,aAAa,EACd,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,mBAAmB,iBAAiB,iBAAiB,CAAC,eAAe,EAAE,CAAC,GAAG;IAEjF,uBAAuB;IACvB,MAAM,eAAuB,gBAAgB,GAAG,CAAC,CAAA,OAAQ,CAAC;YACxD,IAAI,KAAK,EAAE;YACX,MAAM;YACN,UAAU,KAAK,QAAQ;YACvB,MAAM;gBACJ,OAAO,KAAK,IAAI,CAAC,KAAK;gBACtB,MAAM,KAAK,IAAI;gBACf,QAAQ,KAAK,IAAI,CAAC,MAAM;gBACxB,aAAa,mBAAmB,KAAK,IAAI;gBACzC,QAAQ,KAAK,IAAI,CAAC,MAAM;gBACxB,QAAQ,IAAM,eAAe;gBAC7B,UAAU,IAAM,iBAAiB,KAAK,EAAE;YAC1C;QACF,CAAC;IAED,MAAM,eAAuB,gBAAgB,OAAO,CAAC,CAAA,OACnD,KAAK,WAAW,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC5B,IAAI,AAAC,GAAmB,OAAjB,KAAK,QAAQ,EAAC,KAAiB,OAAd,KAAK,QAAQ;gBACrC,QAAQ,KAAK,QAAQ;gBACrB,QAAQ,KAAK,QAAQ;gBACrB,MAAM;gBACN,UAAU,KAAK,IAAI,CAAC,MAAM,KAAK;YACjC,CAAC;IAGH,MAAM,CAAC,OAAO,UAAU,cAAc,GAAG,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,UAAU,cAAc,GAAG,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAAE;IAEvD,MAAM,qBAAgC,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE,IAAM,CAAC;gBACnD,QAAQ,+IAAA,CAAA,UAAU;YACpB,CAAC;qDAAG,EAAE;IAEN,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAuC;YAC3C,SAAS;YACT,mBAAmB;YACnB,oBAAoB;YACpB,qBAAqB;YACrB,iBAAiB;YACjB,oBAAoB;YACpB,qBAAqB;YACrB,uBAAuB;YACvB,oBAAoB;YACpB,qBAAqB;YACrB,oBAAoB;YACpB,uBAAuB;YACvB,aAAa;YACb,QAAQ;YACR,UAAU;QACZ;QACA,OAAO,YAAY,CAAC,SAAS,IAAI;IACnC;IAEA,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,IAAI,gBAAgB;gBAClB,aAAa,eAAe,EAAE;gBAC9B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;QACF;yDAAG;QAAC;QAAgB;KAAa;IAEjC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACvC,IAAI,gBAAgB;gBAClB,eAAe,eAAe,EAAE,EAAE;gBAClC,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;YACf;QACF;2DAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACvC,IAAI,gBAAgB;gBAClB,eAAe,eAAe,EAAE;gBAChC,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;YACf;QACF;2DAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,IAAI,gBAAgB;gBAClB,cAAc,eAAe,EAAE;gBAC/B,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;YACf;QACF;0DAAG;QAAC;QAAgB;KAAc;IAElC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,eAAe;YACf,KAAK,cAAc,CAAC;gBAClB,OAAO,KAAK,IAAI,CAAC,KAAK;gBACtB,GAAG,KAAK,IAAI,CAAC,MAAM;YACrB;YACA,wBAAwB;QAC1B;qDAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACpC,WAAW;YACX;gEAAS,CAAA,QAAS,MAAM,MAAM;wEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;;YAC7C;gEAAS,CAAA,QAAS,MAAM,MAAM;wEAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK;;;YACxE,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;uDAAG;QAAC;QAAY;QAAU;KAAS;IAEnC,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE;YAC7C,YAAY;YACZ,SAAS,EAAE;YACX,SAAS,EAAE;YAEX,SAAS;YACT,MAAM,cAAc;gBAClB;oBACE,IAAI;oBACJ,MAAM;oBACN,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAI;oBAC3B,MAAM;wBACJ,OAAO;wBACP,MAAM;wBACN,QAAQ;wBACR,aAAa;wBACb,MAAM;sFAAE,KAAO;;wBACf,QAAQ;sFAAE,IAAM,iBAAiB;;oBACnC;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAI;oBAC3B,MAAM;wBACJ,OAAO;wBACP,MAAM;wBACN,QAAQ;wBACR,aAAa;wBACb,MAAM;sFAAE,KAAO;;wBACf,QAAQ;sFAAE,IAAM,iBAAiB;;oBACnC;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAI;oBAC3B,MAAM;wBACJ,OAAO;wBACP,MAAM;wBACN,QAAQ;wBACR,aAAa;wBACb,MAAM;sFAAE,KAAO;;wBACf,QAAQ;sFAAE,IAAM,iBAAiB;;oBACnC;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAI;oBAC3B,MAAM;wBACJ,OAAO;wBACP,MAAM;wBACN,QAAQ;wBACR,aAAa;wBACb,MAAM;sFAAE,KAAO;;wBACf,QAAQ;sFAAE,IAAM,iBAAiB;;oBACnC;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAI;oBAC3B,MAAM;wBACJ,OAAO;wBACP,MAAM;wBACN,QAAQ;wBACR,aAAa;wBACb,MAAM;sFAAE,KAAO;;wBACf,QAAQ;sFAAE,IAAM,iBAAiB;;oBACnC;gBACF;aACD;YAED,SAAS;YACT,MAAM,cAAsB;gBAC1B;oBACE,IAAI;oBACJ,QAAQ;oBACR,QAAQ;oBACR,MAAM;oBACN,UAAU;oBACV,OAAO;wBAAE,QAAQ;wBAAW,aAAa;oBAAE;gBAC7C;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,QAAQ;oBACR,MAAM;oBACN,UAAU;oBACV,OAAO;wBAAE,QAAQ;wBAAW,aAAa;oBAAE;gBAC7C;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,QAAQ;oBACR,MAAM;oBACN,UAAU;oBACV,OAAO;wBAAE,QAAQ;wBAAW,aAAa;oBAAE;gBAC7C;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,QAAQ;oBACR,MAAM;oBACN,UAAU;oBACV,OAAO;wBAAE,QAAQ;wBAAW,aAAa;oBAAE;gBAC7C;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,QAAQ;oBACR,MAAM;oBACN,UAAU;oBACV,OAAO;wBAAE,QAAQ;wBAAW,aAAa;oBAAE;gBAC7C;aACD;YAED,SAAS;YACT,SAAS;YAET,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;iEAAG;QAAC;QAAU;QAAU;KAAiB;IAEzC,YAAY;IACZ,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACzC,YAAY;YACZ,SAAS,EAAE;YACX,SAAS,EAAE;YAEX,sBAAsB;YACtB,MAAM,iBAAiB,SAAS,KAAK,CAAC,GAAG;oFAAC,CAAC,MAAM,QAAU,CAAC;wBAC1D,IAAI,AAAC,QAAa,OAAN;wBACZ,MAAM;wBACN,UAAU,KAAK,QAAQ;wBACvB,MAAM;4BACJ,OAAO,KAAK,KAAK;4BACjB,MAAM,KAAK,IAAI;4BACf,QAAQ;4BACR,aAAa,mBAAmB,KAAK,IAAI;4BACzC,QAAQ,KAAK,MAAM,IAAI,CAAC;4BACxB,MAAM;oGAAE,KAAO;;4BACf,QAAQ;oGAAE,IAAM,iBAAiB,AAAC,QAAa,OAAN;;wBAC3C;oBACF,CAAC;;YAED,sBAAsB;YACtB,MAAM,iBAAiB,SAAS,WAAW,CAAC,GAAG;oFAAC,CAAC,MAAM,QAAU,CAAC;wBAChE,IAAI,AAAC,QAAa,OAAN;wBACZ,QAAQ,AAAC,QAAmB,OAAZ,KAAK,MAAM;wBAC3B,QAAQ,AAAC,QAAmB,OAAZ,KAAK,MAAM;wBAC3B,MAAM;wBACN,UAAU;wBACV,OAAO;4BAAE,QAAQ;4BAAW,aAAa;wBAAE;oBAC7C,CAAC;;YAED,SAAS;YACT,SAAS;YAET,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,AAAC,SAAsB,OAAd,SAAS,IAAI,EAAC;QACzC;4DAAG;QAAC;QAAU;QAAU;QAAkB;KAAmB;IAI7D,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,MAAM,SAAS,AAAC,QAAkB,OAAX,KAAK,GAAG;YAC/B,MAAM,WAAW;gBAAE,GAAG,KAAK,MAAM,KAAK;gBAAK,GAAG,KAAK,MAAM,KAAK;YAAI;YAElE,kBAAkB;YAClB,MAAM,gBAAsB;gBAC1B,IAAI;gBACJ,MAAM;gBACN;gBACA,MAAM;oBACJ,OAAO,AAAC,IAAY,OAAT,UAAS;oBACpB,MAAM;oBACN,QAAQ;oBACR,aAAa,mBAAmB;oBAChC,QAAQ,CAAC;oBACT,MAAM;qEAAE,IAAM,eAAe;gCAAE,IAAI;gCAAQ,MAAM;gCAAU,MAAM;oCAAE,OAAO,AAAC,IAAY,OAAT,UAAS;oCAAK,QAAQ,CAAC;oCAAG,QAAQ;gCAAgB;4BAAE;;oBAClI,QAAQ;qEAAE,IAAM,iBAAiB;;gBACnC;YACF;YACA;6DAAS,CAAA,QAAS;2BAAI;wBAAO;qBAAc;;YAC3C,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;oDAAG;QAAC;QAAU;QAAgB;KAAiB;IAE/C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC7B,WAAW;YACX,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,EAAE;gBACpC,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBACd;YACF;YAEA,QAAQ;YACR,IAAI,OAAO,MAAM,KAAK,OAAO,MAAM,EAAE;gBACnC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB;YACF;YAEA,WAAW;YACX,MAAM,UAAU;gBACd,GAAG,MAAM;gBACT,MAAM;gBACN,UAAU;gBACV,OAAO;oBACL,QAAQ;oBACR,aAAa;gBACf;YACF;YAEA;yDAAS,CAAC,MAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,SAAS;;YAEnC,kBAAkB;YAClB,IAAI,kBAAkB,OAAO,MAAM,IAAI,OAAO,MAAM,EAAE;gBACpD,aAAa,OAAO,MAAM,EAAE,OAAO,MAAM;YAC3C;YAEA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;gDAAG;QAAC;QAAU;QAAc;KAAe;IAE3C,MAAM,kBAAkB;QACtB;YAAE,KAAK;YAAS,OAAO;YAAQ,aAAa;QAAS;QACrD;YAAE,KAAK;YAAmB,OAAO;YAAQ,aAAa;QAAW;QACjE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAW;QACnE;YAAE,KAAK;YAAiB,OAAO;YAAS,aAAa;QAAW;QAChE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAY;QACnE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAW;QACnE;YAAE,KAAK;YAAuB,OAAO;YAAQ,aAAa;QAAW;QACrE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAa;QACrE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAuB,OAAO;YAAS,aAAa;QAAU;QACrE;YAAE,KAAK;YAAU,OAAO;YAAQ,aAAa;QAAS;KACvD;IAED,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAO;;;;;;kDAClC,6LAAC;wCAAK,MAAK;;4CAAY;4CAAK,eAAe,IAAI;;;;;;;;;;;;;0CAGjD,6LAAC,mMAAA,CAAA,QAAK;;kDACJ,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,SAAS;kDACV;;;;;;kDAID,6LAAC,qMAAA,CAAA,SAAM;wCACL,SAAS;kDACV;;;;;;kDAID,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;wCACpB,SAAS,IAAM,wBAAwB;kDACxC;;;;;;oCAIA,CAAA,6BAAA,uCAAA,iBAAkB,MAAM,MAAK,0BAC5B;;0DACE,6LAAC,qMAAA,CAAA,SAAM;gDACL,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;gDAC1B,SAAS;0DACV;;;;;;0DAGD,6LAAC,qMAAA,CAAA,SAAM;gDACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gDACnB,MAAM;gDACN,SAAS;0DACV;;;;;;;qEAKH,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;wCACzB,SAAS;wCACT,UAAU,gBAAgB,MAAM,KAAK;kDACtC;;;;;;;;;;;;;;;;;;oBAQN,kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,MAAM;;4CAAC;4CACX,iBAAiB,MAAM,KAAK,YAAY,QACxC,iBAAiB,MAAM,KAAK,WAAW,QACvC,iBAAiB,MAAM,KAAK,cAAc,QAC1C,iBAAiB,MAAM,KAAK,UAAU,OAAO;;;;;;;kDAE/C,6LAAC;;4CAAK;4CAAK,iBAAiB,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;4BAE5D,iBAAiB,QAAQ,CAAC,WAAW,kBACpC,6LAAC;gCAAK,MAAK;;oCAAY;oCAAO,iBAAiB,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;;0BAM3E,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC,iLAAA,CAAA,OAAI;wCAEH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc,SAAS,GAAG;kDAEzC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAK,MAAM;4DAAC,WAAU;sEAAW,SAAS,KAAK;;;;;;sEAChD,6LAAC;4DAAI,WAAU;sEACZ,SAAS,WAAW;;;;;;;;;;;;8DAGzB,6LAAC,qNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;uCAZrB,SAAS,GAAG;;;;;;;;;;;;;;;;kCAoBzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,KAAK,kBAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,OAAO;4CAAG,MAAK;sDAAY;;;;;;sDAClC,6LAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;yFAM3B,6LAAC,4KAAA,CAAA,YAAS;gCACR,OAAO;gCACP,OAAO;gCACP,eAAe;gCACf,eAAe;gCACf,WAAW;gCACX,WAAW;gCACX,OAAO;gCACP,WAAU;;kDAEV,6LAAC,4KAAA,CAAA,aAAU;;;;;kDACX,6LAAC,4KAAA,CAAA,WAAQ;;;;;kDACT,6LAAC,4KAAA,CAAA,UAAO;wCACN,WAAW,CAAC;gDACF;4CAAR,QAAQ,aAAA,KAAK,IAAI,cAAT,iCAAA,WAAW,MAAM;gDACvB,KAAK;oDAAW,OAAO;gDACvB,KAAK;oDAAa,OAAO;gDACzB,KAAK;oDAAS,OAAO;gDACrB;oDAAS,OAAO;4CAClB;wCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUZ,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,MAAM;oBACJ,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBACxC,IAAI,aAAa;4BACf,SAAS;4BACT,SAAS,CAAA,QACP,MAAM,GAAG,CAAC,CAAA,OACR,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;wCACE,GAAG,IAAI;wCACP,MAAM;4CACJ,GAAG,KAAK,IAAI;4CACZ,OAAO,OAAO,KAAK;4CACnB,QAAQ;gDAAE,GAAI,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;gDAAG,GAAG,MAAM;4CAAC;wCACnD;oCACF,IACA;4BAGR,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;wBAClB;wBACA,wBAAwB;wBACxB,KAAK,WAAW;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B;gBACF;gBACA,UAAU;oBACR,wBAAwB;oBACxB,KAAK,WAAW;gBAClB;gBACA,OAAO;0BAEP,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;wBAGpB,CAAA,wBAAA,kCAAA,YAAa,IAAI,MAAK,yBACrB;;8CACE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;;;;;;8CAErB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;wBAMnC,CAAA,wBAAA,kCAAA,YAAa,IAAI,MAAK,uCACrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,MAAK;gCAAS,KAAK;gCAAG,KAAK;gCAAK,aAAY;;;;;;;;;;;wBAItD,CAAA,wBAAA,kCAAA,YAAa,IAAI,MAAK,mCACrB;;8CACE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,6LAAC,mLAAA,CAAA,QAAK;wCAAC,MAAK;wCAAS,KAAK;wCAAG,KAAK;wCAAI,aAAY;;;;;;;;;;;8CAEpD,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,6LAAC,qLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GA9lBM;;QAaA,wHAAA,CAAA,cAAW;QAMA,iLAAA,CAAA,OAAI,CAAC;QA8BqB,4KAAA,CAAA,gBAAa;QACb,4KAAA,CAAA,gBAAa;;;KAlDlD;uCAgmBS", "debugId": null}}, {"offset": {"line": 2575, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/project/ProjectOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Card, \n  Button, \n  Space, \n  Typography, \n  Modal, \n  Form, \n  Input, \n  Select, \n  InputNumber,\n  message,\n  Empty,\n  Tag,\n  Popconfirm\n} from 'antd';\nimport { \n  PlusOutlined, \n  EditOutlined, \n  DeleteOutlined,\n  PlayCircleOutlined,\n  FileTextOutlined,\n  CalendarOutlined,\n  SettingOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { Project } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\n\nconst ProjectOverview: React.FC = () => {\n  const { \n    projects, \n    currentProject,\n    createProject, \n    updateProject, \n    deleteProject, \n    setCurrentProject,\n    setActiveTab\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState<Project | null>(null);\n  const [form] = Form.useForm();\n\n  const handleCreateProject = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEditProject = (project: Project) => {\n    setEditingProject(project);\n    form.setFieldsValue({\n      name: project.name,\n      description: project.description,\n      genre: project.settings.genre,\n      style: project.settings.style,\n      targetWordCount: project.settings.targetWordCount,\n      chapterCount: project.settings.chapterCount,\n      language: project.settings.language,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteProject = (projectId: string) => {\n    deleteProject(projectId);\n    message.success('项目已删除');\n  };\n\n  const handleSelectProject = (project: Project) => {\n    setCurrentProject(project.id);\n    message.success(`已选择项目: ${project.name}`);\n  };\n\n  const handleStartWorkflow = (project: Project) => {\n    setCurrentProject(project.id);\n    setActiveTab('workflow');\n    message.info(`正在打开项目 ${project.name} 的工作流编辑器`);\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingProject) {\n        // 更新项目\n        updateProject(editingProject.id, {\n          name: values.name,\n          description: values.description,\n          settings: {\n            genre: values.genre,\n            style: values.style,\n            targetWordCount: values.targetWordCount,\n            chapterCount: values.chapterCount,\n            language: values.language,\n          }\n        });\n        message.success('项目已更新');\n      } else {\n        // 创建新项目\n        createProject({\n          name: values.name,\n          description: values.description,\n          status: 'draft',\n          settings: {\n            genre: values.genre,\n            style: values.style,\n            targetWordCount: values.targetWordCount,\n            chapterCount: values.chapterCount,\n            language: values.language,\n          }\n        });\n        message.success('项目已创建');\n      }\n      \n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n    setEditingProject(null);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'draft': return 'default';\n      case 'in-progress': return 'processing';\n      case 'completed': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'draft': return '草稿';\n      case 'in-progress': return '进行中';\n      case 'completed': return '已完成';\n      default: return '未知';\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>项目总览</Title>\n          <Text type=\"secondary\">管理您的AI小说创作项目</Text>\n        </div>\n        <Button \n          type=\"primary\" \n          icon={<PlusOutlined />}\n          onClick={handleCreateProject}\n        >\n          创建新项目\n        </Button>\n      </div>\n\n      {projects.length === 0 ? (\n        <Card className=\"text-center py-12\">\n          <Empty\n            description={\n              <div>\n                <Text type=\"secondary\">还没有任何项目</Text>\n                <br />\n                <Text type=\"secondary\">点击上方按钮创建您的第一个AI小说项目</Text>\n              </div>\n            }\n          />\n        </Card>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {projects.map((project) => (\n            <Card\n              key={project.id}\n              className={`hover:shadow-lg transition-shadow ${\n                currentProject?.id === project.id ? 'ring-2 ring-blue-500' : ''\n              }`}\n              actions={[\n                <Button \n                  key=\"select\"\n                  type=\"text\" \n                  icon={<SettingOutlined />}\n                  onClick={() => handleSelectProject(project)}\n                >\n                  选择\n                </Button>,\n                <Button \n                  key=\"workflow\"\n                  type=\"text\" \n                  icon={<PlayCircleOutlined />}\n                  onClick={() => handleStartWorkflow(project)}\n                >\n                  工作流\n                </Button>,\n                <Button \n                  key=\"edit\"\n                  type=\"text\" \n                  icon={<EditOutlined />}\n                  onClick={() => handleEditProject(project)}\n                >\n                  编辑\n                </Button>,\n                <Popconfirm\n                  key=\"delete\"\n                  title=\"确定要删除这个项目吗？\"\n                  description=\"删除后将无法恢复，请谨慎操作。\"\n                  onConfirm={() => handleDeleteProject(project.id)}\n                  okText=\"确定\"\n                  cancelText=\"取消\"\n                >\n                  <Button \n                    type=\"text\" \n                    danger\n                    icon={<DeleteOutlined />}\n                  >\n                    删除\n                  </Button>\n                </Popconfirm>\n              ]}\n            >\n              <div className=\"mb-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <Title level={4} className=\"mb-0\">{project.name}</Title>\n                  <Tag color={getStatusColor(project.status)}>\n                    {getStatusText(project.status)}\n                  </Tag>\n                </div>\n                \n                <Paragraph \n                  type=\"secondary\" \n                  ellipsis={{ rows: 2 }}\n                  className=\"mb-3\"\n                >\n                  {project.description}\n                </Paragraph>\n\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">类型:</Text>\n                    <Text>{project.settings.genre}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">风格:</Text>\n                    <Text>{project.settings.style}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">目标字数:</Text>\n                    <Text>{project.settings.targetWordCount.toLocaleString()}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">章节数:</Text>\n                    <Text>{project.settings.chapterCount}</Text>\n                  </div>\n                </div>\n\n                <div className=\"mt-4 pt-3 border-t border-gray-100\">\n                  <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                    <span className=\"flex items-center\">\n                      <CalendarOutlined className=\"mr-1\" />\n                      创建: {new Date(project.createdAt).toLocaleDateString()}\n                    </span>\n                    <span className=\"flex items-center\">\n                      <FileTextOutlined className=\"mr-1\" />\n                      更新: {new Date(project.updatedAt).toLocaleDateString()}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </Card>\n          ))}\n        </div>\n      )}\n\n      {/* 创建/编辑项目模态框 */}\n      <Modal\n        title={editingProject ? '编辑项目' : '创建新项目'}\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={600}\n        okText={editingProject ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            genre: '现代都市',\n            style: '轻松幽默',\n            targetWordCount: 100000,\n            chapterCount: 50,\n            language: 'zh-CN',\n          }}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"项目名称\"\n            rules={[{ required: true, message: '请输入项目名称' }]}\n          >\n            <Input placeholder=\"请输入项目名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"项目描述\"\n            rules={[{ required: true, message: '请输入项目描述' }]}\n          >\n            <Input.TextArea \n              rows={3} \n              placeholder=\"请简要描述您的小说项目...\" \n            />\n          </Form.Item>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"genre\"\n              label=\"小说类型\"\n              rules={[{ required: true, message: '请选择小说类型' }]}\n            >\n              <Select placeholder=\"请选择类型\">\n                <Option value=\"现代都市\">现代都市</Option>\n                <Option value=\"古代言情\">古代言情</Option>\n                <Option value=\"玄幻修仙\">玄幻修仙</Option>\n                <Option value=\"科幻未来\">科幻未来</Option>\n                <Option value=\"悬疑推理\">悬疑推理</Option>\n                <Option value=\"历史军事\">历史军事</Option>\n                <Option value=\"青春校园\">青春校园</Option>\n                <Option value=\"商战职场\">商战职场</Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item\n              name=\"style\"\n              label=\"写作风格\"\n              rules={[{ required: true, message: '请选择写作风格' }]}\n            >\n              <Select placeholder=\"请选择风格\">\n                <Option value=\"轻松幽默\">轻松幽默</Option>\n                <Option value=\"深沉严肃\">深沉严肃</Option>\n                <Option value=\"浪漫温馨\">浪漫温馨</Option>\n                <Option value=\"紧张刺激\">紧张刺激</Option>\n                <Option value=\"文艺清新\">文艺清新</Option>\n                <Option value=\"热血激昂\">热血激昂</Option>\n              </Select>\n            </Form.Item>\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"targetWordCount\"\n              label=\"目标字数\"\n              rules={[{ required: true, message: '请输入目标字数' }]}\n            >\n              <InputNumber\n                min={10000}\n                max={1000000}\n                step={10000}\n                placeholder=\"100000\"\n                className=\"w-full\"\n                formatter={(value) => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n                parser={(value) => value!.replace(/\\$\\s?|(,*)/g, '')}\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"chapterCount\"\n              label=\"预计章节数\"\n              rules={[{ required: true, message: '请输入章节数' }]}\n            >\n              <InputNumber\n                min={1}\n                max={500}\n                placeholder=\"50\"\n                className=\"w-full\"\n              />\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            name=\"language\"\n            label=\"语言\"\n            rules={[{ required: true, message: '请选择语言' }]}\n          >\n            <Select placeholder=\"请选择语言\">\n              <Option value=\"zh-CN\">简体中文</Option>\n              <Option value=\"en-US\">English</Option>\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectOverview;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AA3BA;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAEzB,MAAM,kBAA4B;;IAChC,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,aAAa,EACb,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,YAAY,EACb,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,KAAK,cAAc,CAAC;YAClB,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,OAAO,QAAQ,QAAQ,CAAC,KAAK;YAC7B,OAAO,QAAQ,QAAQ,CAAC,KAAK;YAC7B,iBAAiB,QAAQ,QAAQ,CAAC,eAAe;YACjD,cAAc,QAAQ,QAAQ,CAAC,YAAY;YAC3C,UAAU,QAAQ,QAAQ,CAAC,QAAQ;QACrC;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,QAAQ,EAAE;QAC5B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,AAAC,UAAsB,OAAb,QAAQ,IAAI;IACxC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,QAAQ,EAAE;QAC5B,aAAa;QACb,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,AAAC,UAAsB,OAAb,QAAQ,IAAI,EAAC;IACtC;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,gBAAgB;gBAClB,OAAO;gBACP,cAAc,eAAe,EAAE,EAAE;oBAC/B,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,UAAU;wBACR,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,iBAAiB,OAAO,eAAe;wBACvC,cAAc,OAAO,YAAY;wBACjC,UAAU,OAAO,QAAQ;oBAC3B;gBACF;gBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,QAAQ;gBACR,cAAc;oBACZ,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,QAAQ;oBACR,UAAU;wBACR,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,iBAAiB,OAAO,eAAe;wBACvC,cAAc,OAAO,YAAY;wBACjC,UAAU,OAAO,QAAQ;oBAC3B;gBACF;gBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,kBAAkB;YAClB,KAAK,WAAW;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;kCAEzB,6LAAC,qMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBACnB,SAAS;kCACV;;;;;;;;;;;;YAKF,SAAS,MAAM,KAAK,kBACnB,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mLAAA,CAAA,QAAK;oBACJ,2BACE,6LAAC;;0CACC,6LAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,6LAAC;;;;;0CACD,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;yEAM/B,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,iLAAA,CAAA,OAAI;wBAEH,WAAW,AAAC,qCAEX,OADC,CAAA,2BAAA,qCAAA,eAAgB,EAAE,MAAK,QAAQ,EAAE,GAAG,yBAAyB;wBAE/D,SAAS;0CACP,6LAAC,qMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;gCACtB,SAAS,IAAM,oBAAoB;0CACpC;+BAJK;;;;;0CAON,6LAAC,qMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;gCACzB,SAAS,IAAM,oBAAoB;0CACpC;+BAJK;;;;;0CAON,6LAAC,qMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,kBAAkB;0CAClC;+BAJK;;;;;0CAON,6LAAC,6LAAA,CAAA,aAAU;gCAET,OAAM;gCACN,aAAY;gCACZ,WAAW,IAAM,oBAAoB,QAAQ,EAAE;gCAC/C,QAAO;gCACP,YAAW;0CAEX,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,MAAM;oCACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;8CACtB;;;;;;+BAXG;;;;;yBAeP;kCAED,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAQ,QAAQ,IAAI;;;;;;sDAC/C,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAO,eAAe,QAAQ,MAAM;sDACtC,cAAc,QAAQ,MAAM;;;;;;;;;;;;8CAIjC,6LAAC;oCACC,MAAK;oCACL,UAAU;wCAAE,MAAM;oCAAE;oCACpB,WAAU;8CAET,QAAQ,WAAW;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;8DAAM,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sDAE/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;8DAAM,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sDAE/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;8DAAM,QAAQ,QAAQ,CAAC,eAAe,CAAC,cAAc;;;;;;;;;;;;sDAExD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;8DAAM,QAAQ,QAAQ,CAAC,YAAY;;;;;;;;;;;;;;;;;;8CAIxC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,6NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAAS;oDAChC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;0DAErD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,6NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAAS;oDAChC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;uBA1FtD,QAAQ,EAAE;;;;;;;;;;0BAqGvB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,iBAAiB,SAAS;gBACjC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ,iBAAiB,OAAO;gBAChC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;oBACP,eAAe;wBACb,OAAO;wBACP,OAAO;wBACP,iBAAiB;wBACjB,cAAc;wBACd,UAAU;oBACZ;;sCAEA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;gCACb,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;8CAIzB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;sCAK3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,6LAAC,mMAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,aAAY;wCACZ,WAAU;wCACV,WAAW,CAAC,QAAU,AAAC,GAAQ,OAAN,OAAQ,OAAO,CAAC,yBAAyB;wCAClE,QAAQ,CAAC,QAAU,MAAO,OAAO,CAAC,eAAe;;;;;;;;;;;8CAIrD,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;8CAE9C,cAAA,6LAAC,mMAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAKhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCAAC,aAAY;;kDAClB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GAhXM;;QASA,wHAAA,CAAA,cAAW;QAIA,iLAAA,CAAA,OAAI,CAAC;;;KAbhB;uCAkXS", "debugId": null}}, {"offset": {"line": 3457, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/outline/OutlineManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Tree,\n  Tabs,\n  List,\n  Tag,\n  Popconfirm,\n  message,\n  Row,\n  Col,\n  Divider,\n  Progress,\n  Tooltip,\n  InputNumber,\n  Badge\n} from 'antd';\nimport {\n  FileTextOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  FolderOutlined,\n  FileOutlined,\n  DownOutlined,\n  UpOutlined,\n  CopyOutlined,\n  HistoryOutlined,\n  CheckCircleOutlined,\n  ClockCircleOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { Outline, OutlineNode } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst OutlineManager: React.FC = () => {\n  const {\n    currentProject,\n    outlines,\n    addOutline,\n    updateOutline,\n    deleteOutline,\n    getOutlines\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isNodeModalVisible, setIsNodeModalVisible] = useState(false);\n  const [editingOutline, setEditingOutline] = useState<Outline | null>(null);\n  const [editingNode, setEditingNode] = useState<OutlineNode | null>(null);\n  const [selectedOutline, setSelectedOutline] = useState<Outline | null>(null);\n  const [selectedNode, setSelectedNode] = useState<OutlineNode | null>(null);\n  const [activeTab, setActiveTab] = useState('outlines');\n  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);\n  const [form] = Form.useForm();\n  const [nodeForm] = Form.useForm();\n\n  const projectOutlines = currentProject ? getOutlines(currentProject.id) : [];\n\n  // 将大纲节点转换为Tree组件需要的格式\n  const convertToTreeData = (nodes: OutlineNode[]): any[] => {\n    return nodes.map(node => ({\n      key: node.id,\n      title: (\n        <div className=\"flex items-center justify-between w-full\">\n          <div className=\"flex items-center space-x-2\">\n            {node.type === 'part' && <FolderOutlined className=\"text-blue-500\" />}\n            {node.type === 'chapter' && <FileOutlined className=\"text-green-500\" />}\n            {node.type === 'section' && <FileTextOutlined className=\"text-gray-500\" />}\n            <span className=\"font-medium\">{node.title}</span>\n            <Tag color={getStatusColor(node.status)}>\n              {getStatusText(node.status)}\n            </Tag>\n            {node.wordCount && (\n              <Text type=\"secondary\" className=\"text-xs\">\n                {node.wordCount.toLocaleString()}字\n              </Text>\n            )}\n          </div>\n          <div className=\"flex space-x-1\">\n            <Button\n              type=\"text\"\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={(e) => {\n                e.stopPropagation();\n                handleEditNode(node);\n              }}\n            />\n            <Button\n              type=\"text\"\n              size=\"small\"\n              icon={<PlusOutlined />}\n              onClick={(e) => {\n                e.stopPropagation();\n                handleAddChildNode(node);\n              }}\n            />\n            <Popconfirm\n              title=\"确定要删除这个节点吗？\"\n              onConfirm={(e) => {\n                e?.stopPropagation();\n                handleDeleteNode(node);\n              }}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button\n                type=\"text\"\n                size=\"small\"\n                danger\n                icon={<DeleteOutlined />}\n                onClick={(e) => e.stopPropagation()}\n              />\n            </Popconfirm>\n          </div>\n        </div>\n      ),\n      children: node.children.length > 0 ? convertToTreeData(node.children) : undefined,\n    }));\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'success';\n      case 'writing': return 'processing';\n      case 'revised': return 'warning';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'planned': return '计划中';\n      case 'writing': return '写作中';\n      case 'completed': return '已完成';\n      case 'revised': return '已修订';\n      default: return '未知';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed': return <CheckCircleOutlined className=\"text-green-500\" />;\n      case 'writing': return <ClockCircleOutlined className=\"text-blue-500\" />;\n      case 'revised': return <ExclamationCircleOutlined className=\"text-orange-500\" />;\n      default: return <ClockCircleOutlined className=\"text-gray-400\" />;\n    }\n  };\n\n  const calculateProgress = (nodes: OutlineNode[]): { completed: number; total: number } => {\n    let completed = 0;\n    let total = 0;\n\n    const traverse = (nodeList: OutlineNode[]) => {\n      nodeList.forEach(node => {\n        if (node.type === 'chapter') {\n          total++;\n          if (node.status === 'completed') {\n            completed++;\n          }\n        }\n        if (node.children.length > 0) {\n          traverse(node.children);\n        }\n      });\n    };\n\n    traverse(nodes);\n    return { completed, total };\n  };\n\n  const handleCreateOutline = () => {\n    setEditingOutline(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEditOutline = (outline: Outline) => {\n    setEditingOutline(outline);\n    form.setFieldsValue({\n      title: outline.title,\n      type: outline.type,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteOutline = (outlineId: string) => {\n    if (currentProject) {\n      deleteOutline(currentProject.id, outlineId);\n      message.success('大纲已删除');\n      if (selectedOutline?.id === outlineId) {\n        setSelectedOutline(null);\n      }\n    }\n  };\n\n  const handleAddChildNode = (parentNode?: OutlineNode) => {\n    setEditingNode(null);\n    nodeForm.resetFields();\n    if (parentNode) {\n      // 根据父节点类型设置子节点类型\n      const childType = parentNode.type === 'part' ? 'chapter' : 'section';\n      nodeForm.setFieldsValue({ type: childType });\n    }\n    setIsNodeModalVisible(true);\n  };\n\n  const handleEditNode = (node: OutlineNode) => {\n    setEditingNode(node);\n    nodeForm.setFieldsValue({\n      title: node.title,\n      summary: node.summary,\n      type: node.type,\n      wordCount: node.wordCount,\n      keyPoints: node.keyPoints,\n      status: node.status,\n    });\n    setIsNodeModalVisible(true);\n  };\n\n  const handleDeleteNode = (node: OutlineNode) => {\n    // 这里应该实现删除节点的逻辑\n    message.success('节点已删除');\n  };\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理大纲。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>大纲管理</Title>\n          <Text type=\"secondary\">管理故事大纲和章节结构 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={handleCreateOutline}\n          >\n            创建大纲\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'outlines',\n            label: (\n              <span>\n                <FileTextOutlined />\n                大纲列表 ({projectOutlines.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {projectOutlines.length === 0 ? (\n                  <Card className=\"text-center py-12\">\n                    <FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                    <div className=\"mt-4\">\n                      <Text type=\"secondary\">还没有创建任何大纲</Text>\n                      <br />\n                      <Text type=\"secondary\">点击上方按钮创建您的第一个故事大纲</Text>\n                    </div>\n                  </Card>\n                ) : (\n                  <Row gutter={[16, 16]}>\n                    {projectOutlines.map((outline) => {\n                      const progress = calculateProgress(outline.structure);\n                      const progressPercent = progress.total > 0 ? (progress.completed / progress.total) * 100 : 0;\n\n                      return (\n                        <Col span={8} key={outline.id}>\n                          <Card\n                            className=\"hover:shadow-lg transition-shadow cursor-pointer\"\n                            onClick={() => setSelectedOutline(outline)}\n                            actions={[\n                              <Tooltip title=\"编辑\" key=\"edit\">\n                                <EditOutlined onClick={(e) => {\n                                  e.stopPropagation();\n                                  handleEditOutline(outline);\n                                }} />\n                              </Tooltip>,\n                              <Tooltip title=\"复制\" key=\"copy\">\n                                <CopyOutlined onClick={(e) => {\n                                  e.stopPropagation();\n                                  message.info('复制功能开发中');\n                                }} />\n                              </Tooltip>,\n                              <Popconfirm\n                                key=\"delete\"\n                                title=\"确定要删除这个大纲吗？\"\n                                onConfirm={(e) => {\n                                  e?.stopPropagation();\n                                  handleDeleteOutline(outline.id);\n                                }}\n                                okText=\"确定\"\n                                cancelText=\"取消\"\n                              >\n                                <DeleteOutlined onClick={(e) => e.stopPropagation()} />\n                              </Popconfirm>\n                            ]}\n                          >\n                            <div className=\"mb-3\">\n                              <div className=\"flex items-center justify-between mb-2\">\n                                <Title level={5} className=\"mb-0\">{outline.title}</Title>\n                                <Tag color={outline.type === 'main' ? 'blue' : 'green'}>\n                                  {outline.type === 'main' ? '主大纲' : '详细大纲'}\n                                </Tag>\n                              </div>\n\n                              <div className=\"mb-3\">\n                                <Text type=\"secondary\" className=\"text-sm\">\n                                  版本 {outline.version} • {new Date(outline.updatedAt).toLocaleDateString()}\n                                </Text>\n                              </div>\n\n                              <div className=\"mb-3\">\n                                <div className=\"flex items-center justify-between mb-1\">\n                                  <Text type=\"secondary\" className=\"text-sm\">完成进度</Text>\n                                  <Text type=\"secondary\" className=\"text-sm\">\n                                    {progress.completed}/{progress.total}\n                                  </Text>\n                                </div>\n                                <Progress\n                                  percent={progressPercent}\n                                  size=\"small\"\n                                  status={progressPercent === 100 ? 'success' : 'active'}\n                                />\n                              </div>\n\n                              <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                                <span>{outline.structure.length} 个主要节点</span>\n                                <span>\n                                  {outline.structure.reduce((total, node) => {\n                                    const countNodes = (n: OutlineNode): number => {\n                                      return 1 + n.children.reduce((sum, child) => sum + countNodes(child), 0);\n                                    };\n                                    return total + countNodes(node);\n                                  }, 0)} 个总节点\n                                </span>\n                              </div>\n                            </div>\n                          </Card>\n                        </Col>\n                      );\n                    })}\n                  </Row>\n                )}\n              </div>\n            ),\n          },\n          {\n            key: 'structure',\n            label: (\n              <span>\n                <FolderOutlined />\n                大纲结构\n              </span>\n            ),\n            children: (\n              <div>\n                {selectedOutline ? (\n                  <Card\n                    title={`${selectedOutline.title} - 结构视图`}\n                    extra={\n                      <Space>\n                        <Button\n                          icon={<PlusOutlined />}\n                          onClick={() => handleAddChildNode()}\n                        >\n                          添加节点\n                        </Button>\n                      </Space>\n                    }\n                  >\n                    {selectedOutline.structure.length === 0 ? (\n                      <div className=\"text-center py-8\">\n                        <FolderOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />\n                        <div className=\"mt-4\">\n                          <Text type=\"secondary\">大纲结构为空</Text>\n                          <br />\n                          <Text type=\"secondary\">点击上方按钮添加第一个节点</Text>\n                        </div>\n                      </div>\n                    ) : (\n                      <Tree\n                        showLine\n                        switcherIcon={<DownOutlined />}\n                        treeData={convertToTreeData(selectedOutline.structure)}\n                        expandedKeys={expandedKeys}\n                        onExpand={(keys) => setExpandedKeys(keys as string[])}\n                        onSelect={(keys) => {\n                          if (keys.length > 0) {\n                            // 找到选中的节点\n                            const findNode = (nodes: OutlineNode[], id: string): OutlineNode | null => {\n                              for (const node of nodes) {\n                                if (node.id === id) return node;\n                                const found = findNode(node.children, id);\n                                if (found) return found;\n                              }\n                              return null;\n                            };\n                            const node = findNode(selectedOutline.structure, keys[0] as string);\n                            setSelectedNode(node);\n                          }\n                        }}\n                      />\n                    )}\n                  </Card>\n                ) : (\n                  <Card className=\"text-center py-12\">\n                    <FolderOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                    <div className=\"mt-4\">\n                      <Text type=\"secondary\">请先选择一个大纲</Text>\n                      <br />\n                      <Text type=\"secondary\">在左侧大纲列表中点击选择要查看的大纲</Text>\n                    </div>\n                  </Card>\n                )}\n              </div>\n            ),\n          },\n        ]}\n      />\n\n      {/* 节点详情面板 */}\n      {selectedNode && (\n        <Card\n          className=\"mt-6\"\n          title={`节点详情 - ${selectedNode.title}`}\n          extra={\n            <Button\n              type=\"primary\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditNode(selectedNode)}\n            >\n              编辑节点\n            </Button>\n          }\n        >\n          <Row gutter={24}>\n            <Col span={16}>\n              <div className=\"space-y-4\">\n                <div>\n                  <Text strong>节点摘要:</Text>\n                  <Paragraph className=\"mt-2\">{selectedNode.summary || '暂无摘要'}</Paragraph>\n                </div>\n\n                {selectedNode.keyPoints.length > 0 && (\n                  <div>\n                    <Text strong>关键要点:</Text>\n                    <List\n                      size=\"small\"\n                      className=\"mt-2\"\n                      dataSource={selectedNode.keyPoints}\n                      renderItem={(point, index) => (\n                        <List.Item>\n                          <Text>{index + 1}. {point}</Text>\n                        </List.Item>\n                      )}\n                    />\n                  </div>\n                )}\n              </div>\n            </Col>\n            <Col span={8}>\n              <div className=\"space-y-4\">\n                <div>\n                  <Text strong>节点类型:</Text>\n                  <div className=\"mt-1\">\n                    <Tag color=\"blue\">\n                      {selectedNode.type === 'part' ? '部分' :\n                       selectedNode.type === 'chapter' ? '章节' : '小节'}\n                    </Tag>\n                  </div>\n                </div>\n\n                <div>\n                  <Text strong>状态:</Text>\n                  <div className=\"mt-1 flex items-center space-x-2\">\n                    {getStatusIcon(selectedNode.status)}\n                    <Text>{getStatusText(selectedNode.status)}</Text>\n                  </div>\n                </div>\n\n                {selectedNode.wordCount && (\n                  <div>\n                    <Text strong>字数目标:</Text>\n                    <div className=\"mt-1\">\n                      <Text>{selectedNode.wordCount.toLocaleString()} 字</Text>\n                    </div>\n                  </div>\n                )}\n\n                <div>\n                  <Text strong>排序:</Text>\n                  <div className=\"mt-1\">\n                    <Text>第 {selectedNode.order} 位</Text>\n                  </div>\n                </div>\n\n                <div>\n                  <Text strong>子节点:</Text>\n                  <div className=\"mt-1\">\n                    <Badge count={selectedNode.children.length} showZero>\n                      <Text>包含子节点</Text>\n                    </Badge>\n                  </div>\n                </div>\n              </div>\n            </Col>\n          </Row>\n        </Card>\n      )}\n\n      {/* 创建/编辑大纲模态框 */}\n      <Modal\n        title={editingOutline ? '编辑大纲' : '创建大纲'}\n        open={isModalVisible}\n        onOk={async () => {\n          try {\n            const values = await form.validateFields();\n\n            if (editingOutline) {\n              // 更新大纲\n              if (currentProject) {\n                updateOutline(currentProject.id, editingOutline.id, {\n                  ...values,\n                  version: editingOutline.version + 1,\n                });\n                message.success('大纲已更新');\n              }\n            } else {\n              // 创建新大纲\n              if (currentProject) {\n                addOutline(currentProject.id, {\n                  ...values,\n                  structure: [],\n                  version: 1,\n                });\n                message.success('大纲已创建');\n              }\n            }\n\n            setIsModalVisible(false);\n            form.resetFields();\n          } catch (error) {\n            console.error('表单验证失败:', error);\n          }\n        }}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n        }}\n        okText={editingOutline ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Form.Item\n            name=\"title\"\n            label=\"大纲标题\"\n            rules={[{ required: true, message: '请输入大纲标题' }]}\n          >\n            <Input placeholder=\"请输入大纲标题\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"type\"\n            label=\"大纲类型\"\n            rules={[{ required: true, message: '请选择大纲类型' }]}\n          >\n            <Select placeholder=\"请选择大纲类型\">\n              <Option value=\"main\">主大纲</Option>\n              <Option value=\"detailed\">详细大纲</Option>\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 创建/编辑节点模态框 */}\n      <Modal\n        title={editingNode ? '编辑节点' : '创建节点'}\n        open={isNodeModalVisible}\n        onOk={async () => {\n          try {\n            const values = await nodeForm.validateFields();\n            // 这里应该实现节点的创建/更新逻辑\n            message.success(editingNode ? '节点已更新' : '节点已创建');\n            setIsNodeModalVisible(false);\n            nodeForm.resetFields();\n          } catch (error) {\n            console.error('表单验证失败:', error);\n          }\n        }}\n        onCancel={() => {\n          setIsNodeModalVisible(false);\n          nodeForm.resetFields();\n        }}\n        width={800}\n        okText={editingNode ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={nodeForm} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"title\"\n                label=\"节点标题\"\n                rules={[{ required: true, message: '请输入节点标题' }]}\n              >\n                <Input placeholder=\"请输入节点标题\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"type\"\n                label=\"节点类型\"\n                rules={[{ required: true, message: '请选择节点类型' }]}\n              >\n                <Select placeholder=\"请选择类型\">\n                  <Option value=\"part\">部分</Option>\n                  <Option value=\"chapter\">章节</Option>\n                  <Option value=\"section\">小节</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"summary\" label=\"节点摘要\">\n            <TextArea\n              rows={3}\n              placeholder=\"请输入节点的简要描述...\"\n            />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item name=\"wordCount\" label=\"字数目标\">\n                <InputNumber\n                  min={0}\n                  placeholder=\"预计字数\"\n                  className=\"w-full\"\n                  formatter={(value) => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n                  parser={(value) => (Number(value!.replace(/\\$\\s?|(,*)/g, '')) || 0) as any}\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item name=\"status\" label=\"状态\">\n                <Select placeholder=\"请选择状态\">\n                  <Option value=\"planned\">计划中</Option>\n                  <Option value=\"writing\">写作中</Option>\n                  <Option value=\"completed\">已完成</Option>\n                  <Option value=\"revised\">已修订</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"keyPoints\" label=\"关键要点\">\n            <Select\n              mode=\"tags\"\n              placeholder=\"请输入关键要点，按回车添加\"\n              tokenSeparators={[',']}\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default OutlineManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;;;AAzCA;;;;;AA4CA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAE1B,MAAM,iBAA2B;;IAC/B,MAAM,EACJ,cAAc,EACd,QAAQ,EACR,UAAU,EACV,aAAa,EACb,aAAa,EACb,WAAW,EACZ,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,SAAS,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE/B,MAAM,kBAAkB,iBAAiB,YAAY,eAAe,EAAE,IAAI,EAAE;IAE5E,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,KAAK,KAAK,EAAE;gBACZ,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,IAAI,KAAK,wBAAU,6LAAC,yNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;gCAClD,KAAK,IAAI,KAAK,2BAAa,6LAAC,qNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCACnD,KAAK,IAAI,KAAK,2BAAa,6LAAC,6NAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;8CACxD,6LAAC;oCAAK,WAAU;8CAAe,KAAK,KAAK;;;;;;8CACzC,6LAAC,+KAAA,CAAA,MAAG;oCAAC,OAAO,eAAe,KAAK,MAAM;8CACnC,cAAc,KAAK,MAAM;;;;;;gCAE3B,KAAK,SAAS,kBACb,6LAAC;oCAAK,MAAK;oCAAY,WAAU;;wCAC9B,KAAK,SAAS,CAAC,cAAc;wCAAG;;;;;;;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,MAAK;oCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,eAAe;oCACjB;;;;;;8CAEF,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,MAAK;oCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACnB,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB,mBAAmB;oCACrB;;;;;;8CAEF,6LAAC,6LAAA,CAAA,aAAU;oCACT,OAAM;oCACN,WAAW,CAAC;wCACV,cAAA,wBAAA,EAAG,eAAe;wCAClB,iBAAiB;oCACnB;oCACA,QAAO;oCACP,YAAW;8CAEX,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAK;wCACL,MAAM;wCACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;;;gBAM3C,UAAU,KAAK,QAAQ,CAAC,MAAM,GAAG,IAAI,kBAAkB,KAAK,QAAQ,IAAI;YAC1E,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACxD,KAAK;gBAAW,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACtD,KAAK;gBAAW,qBAAO,6LAAC,+OAAA,CAAA,4BAAyB;oBAAC,WAAU;;;;;;YAC5D;gBAAS,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;QACjD;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY;QAChB,IAAI,QAAQ;QAEZ,MAAM,WAAW,CAAC;YAChB,SAAS,OAAO,CAAC,CAAA;gBACf,IAAI,KAAK,IAAI,KAAK,WAAW;oBAC3B;oBACA,IAAI,KAAK,MAAM,KAAK,aAAa;wBAC/B;oBACF;gBACF;gBACA,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;oBAC5B,SAAS,KAAK,QAAQ;gBACxB;YACF;QACF;QAEA,SAAS;QACT,OAAO;YAAE;YAAW;QAAM;IAC5B;IAEA,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,KAAK,cAAc,CAAC;YAClB,OAAO,QAAQ,KAAK;YACpB,MAAM,QAAQ,IAAI;QACpB;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,gBAAgB;YAClB,cAAc,eAAe,EAAE,EAAE;YACjC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,IAAI,CAAA,4BAAA,sCAAA,gBAAiB,EAAE,MAAK,WAAW;gBACrC,mBAAmB;YACrB;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe;QACf,SAAS,WAAW;QACpB,IAAI,YAAY;YACd,iBAAiB;YACjB,MAAM,YAAY,WAAW,IAAI,KAAK,SAAS,YAAY;YAC3D,SAAS,cAAc,CAAC;gBAAE,MAAM;YAAU;QAC5C;QACA,sBAAsB;IACxB;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,SAAS,cAAc,CAAC;YACtB,OAAO,KAAK,KAAK;YACjB,SAAS,KAAK,OAAO;YACrB,MAAM,KAAK,IAAI;YACf,WAAW,KAAK,SAAS;YACzB,WAAW,KAAK,SAAS;YACzB,QAAQ,KAAK,MAAM;QACrB;QACA,sBAAsB;IACxB;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;;oCAAY;oCAAmB,eAAe,IAAI;;;;;;;;;;;;;kCAE/D,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS;sCACV;;;;;;;;;;;;;;;;;0BAML,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gCAAG;gCACb,gBAAgB,MAAM;gCAAC;;;;;;;wBAGlC,wBACE,6LAAC;sCACE,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC,iLAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,6NAAA,CAAA,mBAAgB;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;wCAAU;;;;;;kDAC1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,MAAK;0DAAY;;;;;;0DACvB,6LAAC;;;;;0DACD,6LAAC;gDAAK,MAAK;0DAAY;;;;;;;;;;;;;;;;;uDAI3B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,QAAQ;oCAAC;oCAAI;iCAAG;0CAClB,gBAAgB,GAAG,CAAC,CAAC;oCACpB,MAAM,WAAW,kBAAkB,QAAQ,SAAS;oCACpD,MAAM,kBAAkB,SAAS,KAAK,GAAG,IAAI,AAAC,SAAS,SAAS,GAAG,SAAS,KAAK,GAAI,MAAM;oCAE3F,qBACE,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,mBAAmB;4CAClC,SAAS;8DACP,6LAAC,uLAAA,CAAA,UAAO;oDAAC,OAAM;8DACb,cAAA,6LAAC,qNAAA,CAAA,eAAY;wDAAC,SAAS,CAAC;4DACtB,EAAE,eAAe;4DACjB,kBAAkB;wDACpB;;;;;;mDAJsB;;;;;8DAMxB,6LAAC,uLAAA,CAAA,UAAO;oDAAC,OAAM;8DACb,cAAA,6LAAC,qNAAA,CAAA,eAAY;wDAAC,SAAS,CAAC;4DACtB,EAAE,eAAe;4DACjB,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;wDACf;;;;;;mDAJsB;;;;;8DAMxB,6LAAC,6LAAA,CAAA,aAAU;oDAET,OAAM;oDACN,WAAW,CAAC;wDACV,cAAA,wBAAA,EAAG,eAAe;wDAClB,oBAAoB,QAAQ,EAAE;oDAChC;oDACA,QAAO;oDACP,YAAW;8DAEX,cAAA,6LAAC,yNAAA,CAAA,iBAAc;wDAAC,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;mDAT7C;;;;;6CAWP;sDAED,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,OAAO;gEAAG,WAAU;0EAAQ,QAAQ,KAAK;;;;;;0EAChD,6LAAC,+KAAA,CAAA,MAAG;gEAAC,OAAO,QAAQ,IAAI,KAAK,SAAS,SAAS;0EAC5C,QAAQ,IAAI,KAAK,SAAS,QAAQ;;;;;;;;;;;;kEAIvC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,MAAK;4DAAY,WAAU;;gEAAU;gEACrC,QAAQ,OAAO;gEAAC;gEAAI,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;kEAI1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAK;wEAAY,WAAU;kFAAU;;;;;;kFAC3C,6LAAC;wEAAK,MAAK;wEAAY,WAAU;;4EAC9B,SAAS,SAAS;4EAAC;4EAAE,SAAS,KAAK;;;;;;;;;;;;;0EAGxC,6LAAC,yLAAA,CAAA,WAAQ;gEACP,SAAS;gEACT,MAAK;gEACL,QAAQ,oBAAoB,MAAM,YAAY;;;;;;;;;;;;kEAIlD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,QAAQ,SAAS,CAAC,MAAM;oEAAC;;;;;;;0EAChC,6LAAC;;oEACE,QAAQ,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO;wEAChC,MAAM,aAAa,CAAC;4EAClB,OAAO,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,WAAW,QAAQ;wEACxE;wEACA,OAAO,QAAQ,WAAW;oEAC5B,GAAG;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;uCAnEG,QAAQ,EAAE;;;;;gCA0EjC;;;;;;;;;;;oBAKV;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCAAG;;;;;;;wBAItB,wBACE,6LAAC;sCACE,gCACC,6LAAC,iLAAA,CAAA,OAAI;gCACH,OAAO,AAAC,GAAwB,OAAtB,gBAAgB,KAAK,EAAC;gCAChC,qBACE,6LAAC,mMAAA,CAAA,QAAK;8CACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;wCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,SAAS,IAAM;kDAChB;;;;;;;;;;;0CAMJ,gBAAgB,SAAS,CAAC,MAAM,KAAK,kBACpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACxD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;;;;;8DACD,6LAAC;oDAAK,MAAK;8DAAY;;;;;;;;;;;;;;;;;2DAI3B,6LAAC,iLAAA,CAAA,OAAI;oCACH,QAAQ;oCACR,4BAAc,6LAAC,qNAAA,CAAA,eAAY;;;;;oCAC3B,UAAU,kBAAkB,gBAAgB,SAAS;oCACrD,cAAc;oCACd,UAAU,CAAC,OAAS,gBAAgB;oCACpC,UAAU,CAAC;wCACT,IAAI,KAAK,MAAM,GAAG,GAAG;4CACnB,UAAU;4CACV,MAAM,WAAW,CAAC,OAAsB;gDACtC,KAAK,MAAM,QAAQ,MAAO;oDACxB,IAAI,KAAK,EAAE,KAAK,IAAI,OAAO;oDAC3B,MAAM,QAAQ,SAAS,KAAK,QAAQ,EAAE;oDACtC,IAAI,OAAO,OAAO;gDACpB;gDACA,OAAO;4CACT;4CACA,MAAM,OAAO,SAAS,gBAAgB,SAAS,EAAE,IAAI,CAAC,EAAE;4CACxD,gBAAgB;wCAClB;oCACF;;;;;;;;;;uDAKN,6LAAC,iLAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,yNAAA,CAAA,iBAAc;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;wCAAU;;;;;;kDACxD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,MAAK;0DAAY;;;;;;0DACvB,6LAAC;;;;;0DACD,6LAAC;gDAAK,MAAK;0DAAY;;;;;;;;;;;;;;;;;;;;;;;oBAMnC;iBACD;;;;;;YAIF,8BACC,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAU;gBACV,OAAO,AAAC,UAA4B,OAAnB,aAAa,KAAK;gBACnC,qBACE,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,SAAS,IAAM,eAAe;8BAC/B;;;;;;0BAKH,cAAA,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;;sCACX,6LAAC,+KAAA,CAAA,MAAG;4BAAC,MAAM;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,aAAa,OAAO,IAAI;;;;;;;;;;;;oCAGtD,aAAa,SAAS,CAAC,MAAM,GAAG,mBAC/B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC,iLAAA,CAAA,OAAI;gDACH,MAAK;gDACL,WAAU;gDACV,YAAY,aAAa,SAAS;gDAClC,YAAY,CAAC,OAAO,sBAClB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEACR,cAAA,6LAAC;;gEAAM,QAAQ;gEAAE;gEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlC,6LAAC,+KAAA,CAAA,MAAG;4BAAC,MAAM;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAM;8DACR,aAAa,IAAI,KAAK,SAAS,OAC/B,aAAa,IAAI,KAAK,YAAY,OAAO;;;;;;;;;;;;;;;;;kDAKhD,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,aAAa,MAAM;kEAClC,6LAAC;kEAAM,cAAc,aAAa,MAAM;;;;;;;;;;;;;;;;;;oCAI3C,aAAa,SAAS,kBACrB,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;wDAAM,aAAa,SAAS,CAAC,cAAc;wDAAG;;;;;;;;;;;;;;;;;;kDAKrD,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;;wDAAK;wDAAG,aAAa,KAAK;wDAAC;;;;;;;;;;;;;;;;;;kDAIhC,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;oDAAC,OAAO,aAAa,QAAQ,CAAC,MAAM;oDAAE,QAAQ;8DAClD,cAAA,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,iBAAiB,SAAS;gBACjC,MAAM;gBACN,MAAM;oBACJ,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBAExC,IAAI,gBAAgB;4BAClB,OAAO;4BACP,IAAI,gBAAgB;gCAClB,cAAc,eAAe,EAAE,EAAE,eAAe,EAAE,EAAE;oCAClD,GAAG,MAAM;oCACT,SAAS,eAAe,OAAO,GAAG;gCACpC;gCACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;4BAClB;wBACF,OAAO;4BACL,QAAQ;4BACR,IAAI,gBAAgB;gCAClB,WAAW,eAAe,EAAE,EAAE;oCAC5B,GAAG,MAAM;oCACT,WAAW,EAAE;oCACb,SAAS;gCACX;gCACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;4BAClB;wBACF;wBAEA,kBAAkB;wBAClB,KAAK,WAAW;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B;gBACF;gBACA,UAAU;oBACR,kBAAkB;oBAClB,KAAK,WAAW;gBAClB;gBACA,QAAQ,iBAAiB,OAAO;gBAChC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCAAC,aAAY;;kDAClB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,cAAc,SAAS;gBAC9B,MAAM;gBACN,MAAM;oBACJ,IAAI;wBACF,MAAM,SAAS,MAAM,SAAS,cAAc;wBAC5C,mBAAmB;wBACnB,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,cAAc,UAAU;wBACxC,sBAAsB;wBACtB,SAAS,WAAW;oBACtB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B;gBACF;gBACA,UAAU;oBACR,sBAAsB;oBACtB,SAAS,WAAW;gBACtB;gBACA,OAAO;gBACP,QAAQ,cAAc,OAAO;gBAC7B,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAU,QAAO;;sCAC3B,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;;8DAClB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAU,OAAM;sCAC9B,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCAAC,MAAK;wCAAY,OAAM;kDAChC,cAAA,6LAAC,mMAAA,CAAA,cAAW;4CACV,KAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,WAAW,CAAC,QAAU,AAAC,GAAQ,OAAN,OAAQ,OAAO,CAAC,yBAAyB;4CAClE,QAAQ,CAAC,QAAW,OAAO,MAAO,OAAO,CAAC,eAAe,QAAQ;;;;;;;;;;;;;;;;8CAIvE,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCAAC,MAAK;wCAAS,OAAM;kDAC7B,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;;8DAClB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6LAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAY,OAAM;sCAChC,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,MAAK;gCACL,aAAY;gCACZ,iBAAiB;oCAAC;iCAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GAxoBM;;QAQA,wHAAA,CAAA,cAAW;QAUA,iLAAA,CAAA,OAAI,CAAC;QACD,iLAAA,CAAA,OAAI,CAAC;;;KAnBpB;uCA0oBS", "debugId": null}}, {"offset": {"line": 5056, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/character/CharacterManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useMemo } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Avatar,\n  Tag,\n  Tabs,\n  List,\n  Popconfirm,\n  message,\n  Row,\n  Col,\n  Divider,\n  Badge,\n  Tooltip\n} from 'antd';\nimport {\n  TeamOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  UserOutlined,\n  HeartOutlined,\n  ThunderboltOutlined,\n  CrownOutlined,\n  StarOutlined,\n  EyeOutlined,\n  LinkOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { Character } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst CharacterManager: React.FC = () => {\n  const {\n    currentProject,\n    characters,\n    addCharacter,\n    updateCharacter,\n    deleteCharacter,\n    getCharacters\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingCharacter, setEditingCharacter] = useState<Character | null>(null);\n  const [selected<PERSON>haracter, setSelectedCharacter] = useState<Character | null>(null);\n  const [activeTab, setActiveTab] = useState('list');\n  const [form] = Form.useForm();\n\n  const projectCharacters = currentProject ? getCharacters(currentProject.id) : [];\n\n  // 角色模板\n  const characterTemplates = [\n    {\n      name: '主角模板',\n      role: 'protagonist' as const,\n      personality: ['勇敢', '善良', '坚韧'],\n      description: '故事的主要角色，推动情节发展'\n    },\n    {\n      name: '反派模板',\n      role: 'antagonist' as const,\n      personality: ['狡猾', '野心勃勃', '冷酷'],\n      description: '与主角对立的角色'\n    },\n    {\n      name: '配角模板',\n      role: 'supporting' as const,\n      personality: ['忠诚', '幽默', '可靠'],\n      description: '支持主角的重要角色'\n    }\n  ];\n\n  const handleCreateCharacter = (template?: any) => {\n    setEditingCharacter(null);\n    if (template) {\n      form.setFieldsValue({\n        role: template.role,\n        personality: template.personality,\n      });\n    } else {\n      form.resetFields();\n    }\n    setIsModalVisible(true);\n  };\n\n  const handleEditCharacter = (character: Character) => {\n    setEditingCharacter(character);\n    form.setFieldsValue({\n      name: character.name,\n      age: character.age,\n      gender: character.gender,\n      role: character.role,\n      personality: character.personality,\n      background: character.background,\n      appearance: character.appearance,\n      dialogueStyle: character.dialogueStyle,\n      developmentArc: character.developmentArc,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteCharacter = (characterId: string) => {\n    if (currentProject) {\n      deleteCharacter(currentProject.id, characterId);\n      message.success('角色已删除');\n      if (selectedCharacter?.id === characterId) {\n        setSelectedCharacter(null);\n      }\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (editingCharacter) {\n        // 更新角色\n        if (currentProject) {\n          updateCharacter(currentProject.id, editingCharacter.id, {\n            ...values,\n            personality: values.personality || [],\n            relationships: editingCharacter.relationships || [],\n            appearances: editingCharacter.appearances || [],\n          });\n          message.success('角色已更新');\n        }\n      } else {\n        // 创建新角色\n        if (currentProject) {\n          addCharacter(currentProject.id, {\n            ...values,\n            personality: values.personality || [],\n            relationships: [],\n            appearances: [],\n          });\n          message.success('角色已创建');\n        }\n      }\n\n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n    setEditingCharacter(null);\n  };\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'protagonist': return <CrownOutlined className=\"text-yellow-500\" />;\n      case 'antagonist': return <ThunderboltOutlined className=\"text-red-500\" />;\n      case 'supporting': return <StarOutlined className=\"text-blue-500\" />;\n      default: return <UserOutlined className=\"text-gray-500\" />;\n    }\n  };\n\n  const getRoleText = (role: string) => {\n    switch (role) {\n      case 'protagonist': return '主角';\n      case 'antagonist': return '反派';\n      case 'supporting': return '配角';\n      case 'minor': return '次要角色';\n      default: return '未知';\n    }\n  };\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'protagonist': return 'gold';\n      case 'antagonist': return 'red';\n      case 'supporting': return 'blue';\n      case 'minor': return 'default';\n      default: return 'default';\n    }\n  };\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理角色。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>角色管理</Title>\n          <Text type=\"secondary\">管理小说角色设定和关系 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => handleCreateCharacter()}\n          >\n            创建角色\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'list',\n            label: (\n              <span>\n                <TeamOutlined />\n                角色列表 ({projectCharacters.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {/* 角色模板快速创建 */}\n                <Card className=\"mb-6\" title=\"快速创建\">\n                  <Row gutter={16}>\n                    {characterTemplates.map((template, index) => (\n                      <Col span={8} key={index}>\n                        <Card\n                          size=\"small\"\n                          className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                          onClick={() => handleCreateCharacter(template)}\n                        >\n                          <div className=\"text-center\">\n                            {getRoleIcon(template.role)}\n                            <div className=\"mt-2\">\n                              <Text strong>{template.name}</Text>\n                              <div className=\"text-xs text-gray-500 mt-1\">\n                                {template.description}\n                              </div>\n                            </div>\n                          </div>\n                        </Card>\n                      </Col>\n                    ))}\n                  </Row>\n                </Card>\n\n                {/* 角色列表 */}\n                {projectCharacters.length === 0 ? (\n                  <Card className=\"text-center py-12\">\n                    <TeamOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                    <div className=\"mt-4\">\n                      <Text type=\"secondary\">还没有创建任何角色</Text>\n                      <br />\n                      <Text type=\"secondary\">点击上方按钮或使用模板快速创建角色</Text>\n                    </div>\n                  </Card>\n                ) : (\n                  <Row gutter={[16, 16]}>\n                    {projectCharacters.map((character) => (\n                      <Col span={8} key={character.id}>\n                        <Card\n                          className=\"hover:shadow-lg transition-shadow cursor-pointer\"\n                          onClick={() => setSelectedCharacter(character)}\n                          actions={[\n                            <Tooltip title=\"查看详情\" key=\"view\">\n                              <EyeOutlined onClick={(e) => {\n                                e.stopPropagation();\n                                setSelectedCharacter(character);\n                              }} />\n                            </Tooltip>,\n                            <Tooltip title=\"编辑\" key=\"edit\">\n                              <EditOutlined onClick={(e) => {\n                                e.stopPropagation();\n                                handleEditCharacter(character);\n                              }} />\n                            </Tooltip>,\n                            <Popconfirm\n                              key=\"delete\"\n                              title=\"确定要删除这个角色吗？\"\n                              onConfirm={(e) => {\n                                e?.stopPropagation();\n                                handleDeleteCharacter(character.id);\n                              }}\n                              okText=\"确定\"\n                              cancelText=\"取消\"\n                            >\n                              <DeleteOutlined onClick={(e) => e.stopPropagation()} />\n                            </Popconfirm>\n                          ]}\n                        >\n                          <div className=\"text-center\">\n                            <Avatar\n                              size={64}\n                              icon={<UserOutlined />}\n                              src={character.avatar}\n                              className=\"mb-3\"\n                            />\n                            <div className=\"mb-2\">\n                              <Text strong className=\"text-lg\">{character.name}</Text>\n                              {character.age && (\n                                <Text type=\"secondary\" className=\"ml-2\">({character.age}岁)</Text>\n                              )}\n                            </div>\n                            <div className=\"mb-2\">\n                              <Tag color={getRoleColor(character.role)} icon={getRoleIcon(character.role)}>\n                                {getRoleText(character.role)}\n                              </Tag>\n                              {character.gender && (\n                                <Tag>{character.gender}</Tag>\n                              )}\n                            </div>\n                            <Paragraph\n                              ellipsis={{ rows: 2 }}\n                              type=\"secondary\"\n                              className=\"text-sm\"\n                            >\n                              {character.background || '暂无背景描述'}\n                            </Paragraph>\n                            {character.personality.length > 0 && (\n                              <div className=\"mt-2\">\n                                {character.personality.slice(0, 3).map((trait, index) => (\n                                  <Tag key={index} size=\"small\" color=\"blue\">\n                                    {trait}\n                                  </Tag>\n                                ))}\n                                {character.personality.length > 3 && (\n                                  <Tag size=\"small\">+{character.personality.length - 3}</Tag>\n                                )}\n                              </div>\n                            )}\n                          </div>\n                        </Card>\n                      </Col>\n                    ))}\n                  </Row>\n                )}\n              </div>\n            ),\n          },\n          {\n            key: 'relationships',\n            label: (\n              <span>\n                <LinkOutlined />\n                关系图谱\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <LinkOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">角色关系图谱功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持可视化角色关系网络</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n\n      {/* 角色详情侧边栏 */}\n      {selectedCharacter && (\n        <Modal\n          title={`角色详情 - ${selectedCharacter.name}`}\n          open={!!selectedCharacter}\n          onCancel={() => setSelectedCharacter(null)}\n          footer={[\n            <Button key=\"edit\" type=\"primary\" onClick={() => {\n              handleEditCharacter(selectedCharacter);\n              setSelectedCharacter(null);\n            }}>\n              编辑角色\n            </Button>,\n            <Button key=\"close\" onClick={() => setSelectedCharacter(null)}>\n              关闭\n            </Button>\n          ]}\n          width={800}\n        >\n          <Row gutter={24}>\n            <Col span={8}>\n              <div className=\"text-center\">\n                <Avatar\n                  size={120}\n                  icon={<UserOutlined />}\n                  src={selectedCharacter.avatar}\n                  className=\"mb-4\"\n                />\n                <div className=\"mb-2\">\n                  <Title level={4}>{selectedCharacter.name}</Title>\n                  {selectedCharacter.age && (\n                    <Text type=\"secondary\">年龄: {selectedCharacter.age}岁</Text>\n                  )}\n                </div>\n                <div className=\"mb-4\">\n                  <Tag color={getRoleColor(selectedCharacter.role)} icon={getRoleIcon(selectedCharacter.role)}>\n                    {getRoleText(selectedCharacter.role)}\n                  </Tag>\n                  {selectedCharacter.gender && (\n                    <Tag>{selectedCharacter.gender}</Tag>\n                  )}\n                </div>\n              </div>\n            </Col>\n            <Col span={16}>\n              <div className=\"space-y-4\">\n                <div>\n                  <Text strong>性格特征:</Text>\n                  <div className=\"mt-2\">\n                    {selectedCharacter.personality.map((trait, index) => (\n                      <Tag key={index} color=\"blue\">{trait}</Tag>\n                    ))}\n                  </div>\n                </div>\n\n                {selectedCharacter.background && (\n                  <div>\n                    <Text strong>角色背景:</Text>\n                    <Paragraph className=\"mt-2\">{selectedCharacter.background}</Paragraph>\n                  </div>\n                )}\n\n                {selectedCharacter.appearance && (\n                  <div>\n                    <Text strong>外貌描述:</Text>\n                    <Paragraph className=\"mt-2\">{selectedCharacter.appearance}</Paragraph>\n                  </div>\n                )}\n\n                {selectedCharacter.dialogueStyle && (\n                  <div>\n                    <Text strong>对话风格:</Text>\n                    <Paragraph className=\"mt-2\">{selectedCharacter.dialogueStyle}</Paragraph>\n                  </div>\n                )}\n\n                {selectedCharacter.developmentArc && (\n                  <div>\n                    <Text strong>发展轨迹:</Text>\n                    <Paragraph className=\"mt-2\">{selectedCharacter.developmentArc}</Paragraph>\n                  </div>\n                )}\n\n                <div>\n                  <Text strong>出场记录:</Text>\n                  <div className=\"mt-2\">\n                    <Badge count={selectedCharacter.appearances.length} showZero>\n                      <Text type=\"secondary\">章节出场次数</Text>\n                    </Badge>\n                  </div>\n                </div>\n              </div>\n            </Col>\n          </Row>\n        </Modal>\n      )}\n\n      {/* 创建/编辑角色模态框 */}\n      <Modal\n        title={editingCharacter ? '编辑角色' : '创建角色'}\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={800}\n        okText={editingCharacter ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"角色姓名\"\n                rules={[{ required: true, message: '请输入角色姓名' }]}\n              >\n                <Input placeholder=\"请输入角色姓名\" />\n              </Form.Item>\n            </Col>\n            <Col span={6}>\n              <Form.Item name=\"age\" label=\"年龄\">\n                <Input type=\"number\" placeholder=\"年龄\" />\n              </Form.Item>\n            </Col>\n            <Col span={6}>\n              <Form.Item name=\"gender\" label=\"性别\">\n                <Select placeholder=\"请选择性别\">\n                  <Option value=\"男\">男</Option>\n                  <Option value=\"女\">女</Option>\n                  <Option value=\"其他\">其他</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"role\"\n            label=\"角色定位\"\n            rules={[{ required: true, message: '请选择角色定位' }]}\n          >\n            <Select placeholder=\"请选择角色定位\">\n              <Option value=\"protagonist\">主角</Option>\n              <Option value=\"antagonist\">反派</Option>\n              <Option value=\"supporting\">配角</Option>\n              <Option value=\"minor\">次要角色</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"personality\"\n            label=\"性格特征\"\n            help=\"请输入角色的性格特征，用逗号分隔\"\n          >\n            <Select\n              mode=\"tags\"\n              placeholder=\"请输入性格特征，如：勇敢、善良、聪明\"\n              tokenSeparators={[',']}\n            >\n              <Option value=\"勇敢\">勇敢</Option>\n              <Option value=\"善良\">善良</Option>\n              <Option value=\"聪明\">聪明</Option>\n              <Option value=\"冷静\">冷静</Option>\n              <Option value=\"幽默\">幽默</Option>\n              <Option value=\"坚韧\">坚韧</Option>\n              <Option value=\"狡猾\">狡猾</Option>\n              <Option value=\"野心勃勃\">野心勃勃</Option>\n              <Option value=\"忠诚\">忠诚</Option>\n              <Option value=\"可靠\">可靠</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item name=\"background\" label=\"角色背景\">\n            <TextArea\n              rows={3}\n              placeholder=\"请描述角色的背景故事、成长经历等...\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"appearance\" label=\"外貌描述\">\n            <TextArea\n              rows={2}\n              placeholder=\"请描述角色的外貌特征...\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"dialogueStyle\" label=\"对话风格\">\n            <TextArea\n              rows={2}\n              placeholder=\"请描述角色的说话方式、语言特点...\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"developmentArc\" label=\"发展轨迹\">\n            <TextArea\n              rows={3}\n              placeholder=\"请描述角色在故事中的成长和变化...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default CharacterManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AArCA;;;;;AAwCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAE1B,MAAM,mBAA6B;;IACjC,MAAM,EACJ,cAAc,EACd,UAAU,EACV,YAAY,EACZ,eAAe,EACf,eAAe,EACf,aAAa,EACd,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,oBAAoB,iBAAiB,cAAc,eAAe,EAAE,IAAI,EAAE;IAEhF,OAAO;IACP,MAAM,qBAAqB;QACzB;YACE,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAM;gBAAM;aAAK;YAC/B,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAM;gBAAQ;aAAK;YACjC,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,aAAa;gBAAC;gBAAM;gBAAM;aAAK;YAC/B,aAAa;QACf;KACD;IAED,MAAM,wBAAwB,CAAC;QAC7B,oBAAoB;QACpB,IAAI,UAAU;YACZ,KAAK,cAAc,CAAC;gBAClB,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;YACnC;QACF,OAAO;YACL,KAAK,WAAW;QAClB;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB;QACpB,KAAK,cAAc,CAAC;YAClB,MAAM,UAAU,IAAI;YACpB,KAAK,UAAU,GAAG;YAClB,QAAQ,UAAU,MAAM;YACxB,MAAM,UAAU,IAAI;YACpB,aAAa,UAAU,WAAW;YAClC,YAAY,UAAU,UAAU;YAChC,YAAY,UAAU,UAAU;YAChC,eAAe,UAAU,aAAa;YACtC,gBAAgB,UAAU,cAAc;QAC1C;QACA,kBAAkB;IACpB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,gBAAgB;YAClB,gBAAgB,eAAe,EAAE,EAAE;YACnC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,IAAI,CAAA,8BAAA,wCAAA,kBAAmB,EAAE,MAAK,aAAa;gBACzC,qBAAqB;YACvB;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,kBAAkB;gBACpB,OAAO;gBACP,IAAI,gBAAgB;oBAClB,gBAAgB,eAAe,EAAE,EAAE,iBAAiB,EAAE,EAAE;wBACtD,GAAG,MAAM;wBACT,aAAa,OAAO,WAAW,IAAI,EAAE;wBACrC,eAAe,iBAAiB,aAAa,IAAI,EAAE;wBACnD,aAAa,iBAAiB,WAAW,IAAI,EAAE;oBACjD;oBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF,OAAO;gBACL,QAAQ;gBACR,IAAI,gBAAgB;oBAClB,aAAa,eAAe,EAAE,EAAE;wBAC9B,GAAG,MAAM;wBACT,aAAa,OAAO,WAAW,IAAI,EAAE;wBACrC,eAAe,EAAE;wBACjB,aAAa,EAAE;oBACjB;oBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF;YAEA,kBAAkB;YAClB,KAAK,WAAW;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,KAAK,WAAW;QAChB,oBAAoB;IACtB;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAe,qBAAO,6LAAC,uNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YACpD,KAAK;gBAAc,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACzD,KAAK;gBAAc,qBAAO,6LAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YAClD;gBAAS,qBAAO,6LAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;QAC1C;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;;oCAAY;oCAAmB,eAAe,IAAI;;;;;;;;;;;;;kCAE/D,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM;sCAChB;;;;;;;;;;;;;;;;;0BAML,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,qNAAA,CAAA,eAAY;;;;;gCAAG;gCACT,kBAAkB,MAAM;gCAAC;;;;;;;wBAGpC,wBACE,6LAAC;;8CAEC,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,OAAM;8CAC3B,cAAA,6LAAC,+KAAA,CAAA,MAAG;wCAAC,QAAQ;kDACV,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBACjC,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,sBAAsB;8DAErC,cAAA,6LAAC;wDAAI,WAAU;;4DACZ,YAAY,SAAS,IAAI;0EAC1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAM;kFAAE,SAAS,IAAI;;;;;;kFAC3B,6LAAC;wEAAI,WAAU;kFACZ,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;+CAXZ;;;;;;;;;;;;;;;gCAsBxB,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,qNAAA,CAAA,eAAY;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;;;;;8DACD,6LAAC;oDAAK,MAAK;8DAAY;;;;;;;;;;;;;;;;;2DAI3B,6LAAC,+KAAA,CAAA,MAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;8CAClB,kBAAkB,GAAG,CAAC,CAAC,0BACtB,6LAAC,+KAAA,CAAA,MAAG;4CAAC,MAAM;sDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDACH,WAAU;gDACV,SAAS,IAAM,qBAAqB;gDACpC,SAAS;kEACP,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,mNAAA,CAAA,cAAW;4DAAC,SAAS,CAAC;gEACrB,EAAE,eAAe;gEACjB,qBAAqB;4DACvB;;;;;;uDAJwB;;;;;kEAM1B,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,qNAAA,CAAA,eAAY;4DAAC,SAAS,CAAC;gEACtB,EAAE,eAAe;gEACjB,oBAAoB;4DACtB;;;;;;uDAJsB;;;;;kEAMxB,6LAAC,6LAAA,CAAA,aAAU;wDAET,OAAM;wDACN,WAAW,CAAC;4DACV,cAAA,wBAAA,EAAG,eAAe;4DAClB,sBAAsB,UAAU,EAAE;wDACpC;wDACA,QAAO;wDACP,YAAW;kEAEX,cAAA,6LAAC,yNAAA,CAAA,iBAAc;4DAAC,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;uDAT7C;;;;;iDAWP;0DAED,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qLAAA,CAAA,SAAM;4DACL,MAAM;4DACN,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4DACnB,KAAK,UAAU,MAAM;4DACrB,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,MAAM;oEAAC,WAAU;8EAAW,UAAU,IAAI;;;;;;gEAC/C,UAAU,GAAG,kBACZ,6LAAC;oEAAK,MAAK;oEAAY,WAAU;;wEAAO;wEAAE,UAAU,GAAG;wEAAC;;;;;;;;;;;;;sEAG5D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+KAAA,CAAA,MAAG;oEAAC,OAAO,aAAa,UAAU,IAAI;oEAAG,MAAM,YAAY,UAAU,IAAI;8EACvE,YAAY,UAAU,IAAI;;;;;;gEAE5B,UAAU,MAAM,kBACf,6LAAC,+KAAA,CAAA,MAAG;8EAAE,UAAU,MAAM;;;;;;;;;;;;sEAG1B,6LAAC;4DACC,UAAU;gEAAE,MAAM;4DAAE;4DACpB,MAAK;4DACL,WAAU;sEAET,UAAU,UAAU,IAAI;;;;;;wDAE1B,UAAU,WAAW,CAAC,MAAM,GAAG,mBAC9B,6LAAC;4DAAI,WAAU;;gEACZ,UAAU,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC7C,6LAAC,+KAAA,CAAA,MAAG;wEAAa,MAAK;wEAAQ,OAAM;kFACjC;uEADO;;;;;gEAIX,UAAU,WAAW,CAAC,MAAM,GAAG,mBAC9B,6LAAC,+KAAA,CAAA,MAAG;oEAAC,MAAK;;wEAAQ;wEAAE,UAAU,WAAW,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;2CAnE5C,UAAU,EAAE;;;;;;;;;;;;;;;;oBA+E3C;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,qNAAA,CAAA,eAAY;;;;;gCAAG;;;;;;;wBAIpB,wBACE,6LAAC,iLAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,qNAAA,CAAA,eAAY;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;YAIF,mCACC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,AAAC,UAAgC,OAAvB,kBAAkB,IAAI;gBACvC,MAAM,CAAC,CAAC;gBACR,UAAU,IAAM,qBAAqB;gBACrC,QAAQ;kCACN,6LAAC,qMAAA,CAAA,SAAM;wBAAY,MAAK;wBAAU,SAAS;4BACzC,oBAAoB;4BACpB,qBAAqB;wBACvB;kCAAG;uBAHS;;;;;kCAMZ,6LAAC,qMAAA,CAAA,SAAM;wBAAa,SAAS,IAAM,qBAAqB;kCAAO;uBAAnD;;;;;iBAGb;gBACD,OAAO;0BAEP,cAAA,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;;sCACX,6LAAC,+KAAA,CAAA,MAAG;4BAAC,MAAM;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qLAAA,CAAA,SAAM;wCACL,MAAM;wCACN,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wCACnB,KAAK,kBAAkB,MAAM;wCAC7B,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,OAAO;0DAAI,kBAAkB,IAAI;;;;;;4CACvC,kBAAkB,GAAG,kBACpB,6LAAC;gDAAK,MAAK;;oDAAY;oDAAK,kBAAkB,GAAG;oDAAC;;;;;;;;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+KAAA,CAAA,MAAG;gDAAC,OAAO,aAAa,kBAAkB,IAAI;gDAAG,MAAM,YAAY,kBAAkB,IAAI;0DACvF,YAAY,kBAAkB,IAAI;;;;;;4CAEpC,kBAAkB,MAAM,kBACvB,6LAAC,+KAAA,CAAA,MAAG;0DAAE,kBAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;;sCAKtC,6LAAC,+KAAA,CAAA,MAAG;4BAAC,MAAM;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,kBAAkB,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzC,6LAAC,+KAAA,CAAA,MAAG;wDAAa,OAAM;kEAAQ;uDAArB;;;;;;;;;;;;;;;;oCAKf,kBAAkB,UAAU,kBAC3B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,kBAAkB,UAAU;;;;;;;;;;;;oCAI5D,kBAAkB,UAAU,kBAC3B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,kBAAkB,UAAU;;;;;;;;;;;;oCAI5D,kBAAkB,aAAa,kBAC9B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,kBAAkB,aAAa;;;;;;;;;;;;oCAI/D,kBAAkB,cAAc,kBAC/B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,kBAAkB,cAAc;;;;;;;;;;;;kDAIjE,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;oDAAC,OAAO,kBAAkB,WAAW,CAAC,MAAM;oDAAE,QAAQ;8DAC1D,cAAA,6LAAC;wDAAK,MAAK;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWvC,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,mBAAmB,SAAS;gBACnC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ,mBAAmB,OAAO;gBAClC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCAAC,MAAK;wCAAM,OAAM;kDAC1B,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,MAAK;4CAAS,aAAY;;;;;;;;;;;;;;;;8CAGrC,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCAAC,MAAK;wCAAS,OAAM;kDAC7B,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM3B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCAAC,aAAY;;kDAClB,6LAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,6LAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,6LAAC;wCAAO,OAAM;kDAAa;;;;;;kDAC3B,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;sCAI1B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,MAAK;sCAEL,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,MAAK;gCACL,aAAY;gCACZ,iBAAiB;oCAAC;iCAAI;;kDAEtB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;;;;;;;;;;;;sCAIvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAa,OAAM;sCACjC,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAa,OAAM;sCACjC,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAgB,OAAM;sCACpC,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAiB,OAAM;sCACrC,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GArhBM;;QAQA,wHAAA,CAAA,cAAW;QAMA,iLAAA,CAAA,OAAI,CAAC;;;KAdhB;uCAuhBS", "debugId": null}}, {"offset": {"line": 6467, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/worldbuilding/WorldBuildingManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Tabs,\n  List,\n  Tag,\n  Popconfirm,\n  message,\n  Row,\n  Col,\n  Divider,\n  Tree,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  GlobalOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EnvironmentOutlined,\n  BookOutlined,\n  SettingOutlined,\n  ThunderboltOutlined,\n  CrownOutlined,\n  HomeOutlined,\n  TeamOutlined,\n  DollarOutlined,\n  HistoryOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { WorldBuilding, WorldCategory } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst WorldBuildingManager: React.FC = () => {\n  const {\n    currentProject,\n    worldBuilding,\n    addWorldElement,\n    updateWorldElement,\n    deleteWorldElement,\n    getWorldElements\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingElement, setEditingElement] = useState<WorldBuilding | null>(null);\n  const [selectedCategory, setSelectedCategory] = useState<WorldCategory | 'all'>('all');\n  const [activeTab, setActiveTab] = useState('elements');\n  const [form] = Form.useForm();\n\n  const projectWorldElements = currentProject ? getWorldElements(currentProject.id) : [];\n\n  // 世界观分类配置\n  const worldCategories = [\n    {\n      key: 'setting' as WorldCategory,\n      label: '环境设定',\n      icon: <EnvironmentOutlined />,\n      color: 'green',\n      description: '地理环境、气候、地形等自然环境设定'\n    },\n    {\n      key: 'magic-system' as WorldCategory,\n      label: '魔法体系',\n      icon: <ThunderboltOutlined />,\n      color: 'purple',\n      description: '魔法规则、能力体系、超自然力量设定'\n    },\n    {\n      key: 'technology' as WorldCategory,\n      label: '科技水平',\n      icon: <SettingOutlined />,\n      color: 'blue',\n      description: '科技发展程度、工具、武器、交通等'\n    },\n    {\n      key: 'culture' as WorldCategory,\n      label: '文化背景',\n      icon: <BookOutlined />,\n      color: 'orange',\n      description: '宗教信仰、传统习俗、艺术文化等'\n    },\n    {\n      key: 'geography' as WorldCategory,\n      label: '地理结构',\n      icon: <GlobalOutlined />,\n      color: 'cyan',\n      description: '大陆分布、城市布局、重要地标等'\n    },\n    {\n      key: 'history' as WorldCategory,\n      label: '历史背景',\n      icon: <HistoryOutlined />,\n      color: 'gold',\n      description: '重大历史事件、时代变迁、传说故事等'\n    },\n    {\n      key: 'society' as WorldCategory,\n      label: '社会制度',\n      icon: <CrownOutlined />,\n      color: 'red',\n      description: '政治体制、社会阶层、法律制度等'\n    },\n    {\n      key: 'economy' as WorldCategory,\n      label: '经济体系',\n      icon: <DollarOutlined />,\n      color: 'lime',\n      description: '货币制度、贸易体系、经济结构等'\n    }\n  ];\n\n  const filteredElements = selectedCategory === 'all'\n    ? projectWorldElements\n    : projectWorldElements.filter(element => element.category === selectedCategory);\n\n  const handleCreateElement = (category?: WorldCategory) => {\n    setEditingElement(null);\n    if (category) {\n      form.setFieldsValue({ category });\n    } else {\n      form.resetFields();\n    }\n    setIsModalVisible(true);\n  };\n\n  const handleEditElement = (element: WorldBuilding) => {\n    setEditingElement(element);\n    form.setFieldsValue({\n      name: element.name,\n      category: element.category,\n      description: element.description,\n      details: element.details,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteElement = (elementId: string) => {\n    if (currentProject) {\n      deleteWorldElement(currentProject.id, elementId);\n      message.success('世界观元素已删除');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (editingElement) {\n        // 更新元素\n        if (currentProject) {\n          updateWorldElement(currentProject.id, editingElement.id, {\n            ...values,\n            relationships: editingElement.relationships || [],\n            consistency: editingElement.consistency || [],\n          });\n          message.success('世界观元素已更新');\n        }\n      } else {\n        // 创建新元素\n        if (currentProject) {\n          addWorldElement(currentProject.id, {\n            ...values,\n            relationships: [],\n            consistency: [],\n          });\n          message.success('世界观元素已创建');\n        }\n      }\n\n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n    setEditingElement(null);\n  };\n\n  const getCategoryConfig = (category: WorldCategory) => {\n    return worldCategories.find(cat => cat.key === category) || worldCategories[0];\n  };\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理世界观。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>世界观管理</Title>\n          <Text type=\"secondary\">管理故事世界观设定和背景 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => handleCreateElement()}\n          >\n            添加设定\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'elements',\n            label: (\n              <span>\n                <GlobalOutlined />\n                世界观元素 ({projectWorldElements.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {/* 分类过滤器 */}\n                <Card className=\"mb-6\" title=\"分类浏览\">\n                  <Row gutter={[16, 16]}>\n                    <Col span={3}>\n                      <Card\n                        size=\"small\"\n                        className={`cursor-pointer transition-all ${\n                          selectedCategory === 'all' ? 'ring-2 ring-blue-500' : ''\n                        }`}\n                        onClick={() => setSelectedCategory('all')}\n                      >\n                        <div className=\"text-center\">\n                          <GlobalOutlined className=\"text-2xl text-gray-500\" />\n                          <div className=\"mt-2\">\n                            <Text strong>全部</Text>\n                            <div className=\"text-xs text-gray-500\">\n                              {projectWorldElements.length} 个元素\n                            </div>\n                          </div>\n                        </div>\n                      </Card>\n                    </Col>\n                    {worldCategories.map((category) => {\n                      const count = projectWorldElements.filter(e => e.category === category.key).length;\n                      return (\n                        <Col span={3} key={category.key}>\n                          <Card\n                            size=\"small\"\n                            className={`cursor-pointer transition-all ${\n                              selectedCategory === category.key ? 'ring-2 ring-blue-500' : ''\n                            }`}\n                            onClick={() => setSelectedCategory(category.key)}\n                          >\n                            <div className=\"text-center\">\n                              <div className={`text-2xl text-${category.color}-500`}>\n                                {category.icon}\n                              </div>\n                              <div className=\"mt-2\">\n                                <Text strong>{category.label}</Text>\n                                <div className=\"text-xs text-gray-500\">\n                                  {count} 个元素\n                                </div>\n                              </div>\n                            </div>\n                          </Card>\n                        </Col>\n                      );\n                    })}\n                  </Row>\n                </Card>\n\n                {/* 快速创建模板 */}\n                <Card className=\"mb-6\" title=\"快速创建\">\n                  <Row gutter={16}>\n                    {worldCategories.slice(0, 4).map((category) => (\n                      <Col span={6} key={category.key}>\n                        <Card\n                          size=\"small\"\n                          className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                          onClick={() => handleCreateElement(category.key)}\n                        >\n                          <div className=\"text-center\">\n                            <div className={`text-xl text-${category.color}-500`}>\n                              {category.icon}\n                            </div>\n                            <div className=\"mt-2\">\n                              <Text strong>{category.label}</Text>\n                              <div className=\"text-xs text-gray-500 mt-1\">\n                                {category.description}\n                              </div>\n                            </div>\n                          </div>\n                        </Card>\n                      </Col>\n                    ))}\n                  </Row>\n                </Card>\n\n                {/* 世界观元素列表 */}\n                {filteredElements.length === 0 ? (\n                  <Card className=\"text-center py-12\">\n                    <GlobalOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                    <div className=\"mt-4\">\n                      <Text type=\"secondary\">\n                        {selectedCategory === 'all' ? '还没有创建任何世界观元素' : `还没有创建${getCategoryConfig(selectedCategory as WorldCategory).label}相关的元素`}\n                      </Text>\n                      <br />\n                      <Text type=\"secondary\">点击上方按钮或使用模板快速创建</Text>\n                    </div>\n                  </Card>\n                ) : (\n                  <List\n                    grid={{ gutter: 16, column: 2 }}\n                    dataSource={filteredElements}\n                    renderItem={(element) => {\n                      const categoryConfig = getCategoryConfig(element.category);\n                      return (\n                        <List.Item>\n                          <Card\n                            className=\"hover:shadow-lg transition-shadow\"\n                            actions={[\n                              <Tooltip title=\"编辑\" key=\"edit\">\n                                <EditOutlined onClick={() => handleEditElement(element)} />\n                              </Tooltip>,\n                              <Popconfirm\n                                key=\"delete\"\n                                title=\"确定要删除这个世界观元素吗？\"\n                                onConfirm={() => handleDeleteElement(element.id)}\n                                okText=\"确定\"\n                                cancelText=\"取消\"\n                              >\n                                <DeleteOutlined />\n                              </Popconfirm>\n                            ]}\n                          >\n                            <div className=\"mb-3\">\n                              <div className=\"flex items-center justify-between mb-2\">\n                                <Title level={5} className=\"mb-0\">{element.name}</Title>\n                                <Tag\n                                  color={categoryConfig.color}\n                                  icon={categoryConfig.icon}\n                                >\n                                  {categoryConfig.label}\n                                </Tag>\n                              </div>\n                              <Paragraph\n                                ellipsis={{ rows: 3 }}\n                                type=\"secondary\"\n                              >\n                                {element.description}\n                              </Paragraph>\n                            </div>\n\n                            {element.details && Object.keys(element.details).length > 0 && (\n                              <div className=\"mt-3 pt-3 border-t border-gray-100\">\n                                <Text type=\"secondary\" className=\"text-sm\">\n                                  包含 {Object.keys(element.details).length} 个详细设定\n                                </Text>\n                              </div>\n                            )}\n\n                            {element.relationships.length > 0 && (\n                              <div className=\"mt-2\">\n                                <Badge count={element.relationships.length} showZero={false}>\n                                  <Text type=\"secondary\" className=\"text-sm\">关联元素</Text>\n                                </Badge>\n                              </div>\n                            )}\n                          </Card>\n                        </List.Item>\n                      );\n                    }}\n                  />\n                )}\n              </div>\n            ),\n          },\n          {\n            key: 'consistency',\n            label: (\n              <span>\n                <CheckCircleOutlined />\n                一致性检查\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <CheckCircleOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">一致性检查功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持世界观元素间的逻辑一致性验证</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n\n      {/* 创建/编辑世界观元素模态框 */}\n      <Modal\n        title={editingElement ? '编辑世界观元素' : '创建世界观元素'}\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={800}\n        okText={editingElement ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"name\"\n                label=\"元素名称\"\n                rules={[{ required: true, message: '请输入元素名称' }]}\n              >\n                <Input placeholder=\"请输入世界观元素名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"category\"\n                label=\"分类\"\n                rules={[{ required: true, message: '请选择分类' }]}\n              >\n                <Select placeholder=\"请选择分类\">\n                  {worldCategories.map((category) => (\n                    <Option key={category.key} value={category.key}>\n                      <Space>\n                        {category.icon}\n                        {category.label}\n                      </Space>\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"基本描述\"\n            rules={[{ required: true, message: '请输入基本描述' }]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请描述这个世界观元素的基本信息...\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"details\" label=\"详细设定\">\n            <TextArea\n              rows={6}\n              placeholder=\"请输入更详细的设定信息，如规则、特点、影响等...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default WorldBuildingManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;;;AAzCA;;;;;AA4CA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAE1B,MAAM,uBAAiC;;IACrC,MAAM,EACJ,cAAc,EACd,aAAa,EACb,eAAe,EACf,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EACjB,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,uBAAuB,iBAAiB,iBAAiB,eAAe,EAAE,IAAI,EAAE;IAEtF,UAAU;IACV,MAAM,kBAAkB;QACtB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;YACpB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,mBAAmB,qBAAqB,QAC1C,uBACA,qBAAqB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEhE,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,IAAI,UAAU;YACZ,KAAK,cAAc,CAAC;gBAAE;YAAS;QACjC,OAAO;YACL,KAAK,WAAW;QAClB;QACA,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,KAAK,cAAc,CAAC;YAClB,MAAM,QAAQ,IAAI;YAClB,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,SAAS,QAAQ,OAAO;QAC1B;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,gBAAgB;YAClB,mBAAmB,eAAe,EAAE,EAAE;YACtC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,gBAAgB;gBAClB,OAAO;gBACP,IAAI,gBAAgB;oBAClB,mBAAmB,eAAe,EAAE,EAAE,eAAe,EAAE,EAAE;wBACvD,GAAG,MAAM;wBACT,eAAe,eAAe,aAAa,IAAI,EAAE;wBACjD,aAAa,eAAe,WAAW,IAAI,EAAE;oBAC/C;oBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF,OAAO;gBACL,QAAQ;gBACR,IAAI,gBAAgB;oBAClB,gBAAgB,eAAe,EAAE,EAAE;wBACjC,GAAG,MAAM;wBACT,eAAe,EAAE;wBACjB,aAAa,EAAE;oBACjB;oBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF;YAEA,kBAAkB;YAClB,KAAK,WAAW;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,aAAa,eAAe,CAAC,EAAE;IAChF;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;;oCAAY;oCAAoB,eAAe,IAAI;;;;;;;;;;;;;kCAEhE,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM;sCAChB;;;;;;;;;;;;;;;;;0BAML,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCAAG;gCACV,qBAAqB,MAAM;gCAAC;;;;;;;wBAGxC,wBACE,6LAAC;;8CAEC,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,OAAM;8CAC3B,cAAA,6LAAC,+KAAA,CAAA,MAAG;wCAAC,QAAQ;4CAAC;4CAAI;yCAAG;;0DACnB,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAW,AAAC,iCAEX,OADC,qBAAqB,QAAQ,yBAAyB;oDAExD,SAAS,IAAM,oBAAoB;8DAEnC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,yNAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;0EAC1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAM;kFAAC;;;;;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,qBAAqB,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAMtC,gBAAgB,GAAG,CAAC,CAAC;gDACpB,MAAM,QAAQ,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,GAAG,EAAE,MAAM;gDAClF,qBACE,6LAAC,+KAAA,CAAA,MAAG;oDAAC,MAAM;8DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAW,AAAC,iCAEX,OADC,qBAAqB,SAAS,GAAG,GAAG,yBAAyB;wDAE/D,SAAS,IAAM,oBAAoB,SAAS,GAAG;kEAE/C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,AAAC,iBAA+B,OAAf,SAAS,KAAK,EAAC;8EAC7C,SAAS,IAAI;;;;;;8EAEhB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,MAAM;sFAAE,SAAS,KAAK;;;;;;sFAC5B,6LAAC;4EAAI,WAAU;;gFACZ;gFAAM;;;;;;;;;;;;;;;;;;;;;;;;mDAfE,SAAS,GAAG;;;;;4CAsBnC;;;;;;;;;;;;8CAKJ,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,OAAM;8CAC3B,cAAA,6LAAC,+KAAA,CAAA,MAAG;wCAAC,QAAQ;kDACV,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAChC,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,oBAAoB,SAAS,GAAG;8DAE/C,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,AAAC,gBAA8B,OAAf,SAAS,KAAK,EAAC;0EAC5C,SAAS,IAAI;;;;;;0EAEhB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAM;kFAAE,SAAS,KAAK;;;;;;kFAC5B,6LAAC;wEAAI,WAAU;kFACZ,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;+CAbZ,SAAS,GAAG;;;;;;;;;;;;;;;gCAwBpC,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACxD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DACR,qBAAqB,QAAQ,iBAAiB,AAAC,QAAkE,OAA3D,kBAAkB,kBAAmC,KAAK,EAAC;;;;;;8DAEpH,6LAAC;;;;;8DACD,6LAAC;oDAAK,MAAK;8DAAY;;;;;;;;;;;;;;;;;2DAI3B,6LAAC,iLAAA,CAAA,OAAI;oCACH,MAAM;wCAAE,QAAQ;wCAAI,QAAQ;oCAAE;oCAC9B,YAAY;oCACZ,YAAY,CAAC;wCACX,MAAM,iBAAiB,kBAAkB,QAAQ,QAAQ;wCACzD,qBACE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;sDACR,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDACH,WAAU;gDACV,SAAS;kEACP,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,qNAAA,CAAA,eAAY;4DAAC,SAAS,IAAM,kBAAkB;;;;;;uDADzB;;;;;kEAGxB,6LAAC,6LAAA,CAAA,aAAU;wDAET,OAAM;wDACN,WAAW,IAAM,oBAAoB,QAAQ,EAAE;wDAC/C,QAAO;wDACP,YAAW;kEAEX,cAAA,6LAAC,yNAAA,CAAA,iBAAc;;;;;uDANX;;;;;iDAQP;;kEAED,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAM,OAAO;wEAAG,WAAU;kFAAQ,QAAQ,IAAI;;;;;;kFAC/C,6LAAC,+KAAA,CAAA,MAAG;wEACF,OAAO,eAAe,KAAK;wEAC3B,MAAM,eAAe,IAAI;kFAExB,eAAe,KAAK;;;;;;;;;;;;0EAGzB,6LAAC;gEACC,UAAU;oEAAE,MAAM;gEAAE;gEACpB,MAAK;0EAEJ,QAAQ,WAAW;;;;;;;;;;;;oDAIvB,QAAQ,OAAO,IAAI,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE,MAAM,GAAG,mBACxD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,MAAK;4DAAY,WAAU;;gEAAU;gEACrC,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE,MAAM;gEAAC;;;;;;;;;;;;oDAK7C,QAAQ,aAAa,CAAC,MAAM,GAAG,mBAC9B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;4DAAC,OAAO,QAAQ,aAAa,CAAC,MAAM;4DAAE,UAAU;sEACpD,cAAA,6LAAC;gEAAK,MAAK;gEAAY,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAOzD;;;;;;;;;;;;oBAKV;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,mOAAA,CAAA,sBAAmB;;;;;gCAAG;;;;;;;wBAI3B,wBACE,6LAAC,iLAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mOAAA,CAAA,sBAAmB;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CAC7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;0BAIH,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,iBAAiB,YAAY;gBACpC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ,iBAAiB,OAAO;gBAChC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;yCAAE;kDAE7C,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;sDACjB,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC;oDAA0B,OAAO,SAAS,GAAG;8DAC5C,cAAA,6LAAC,mMAAA,CAAA,QAAK;;4DACH,SAAS,IAAI;4DACb,SAAS,KAAK;;;;;;;mDAHN,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYnC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAU,OAAM;sCAC9B,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GArbM;;QAQA,wHAAA,CAAA,cAAW;QAMA,iLAAA,CAAA,OAAI,CAAC;;;KAdhB;uCAubS", "debugId": null}}, {"offset": {"line": 7452, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/plotline/PlotLineManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Tabs,\n  List,\n  Tag,\n  Popconfirm,\n  message,\n  Row,\n  Col,\n  Timeline,\n  Tooltip,\n  Badge,\n  Divider,\n  Slider\n} from 'antd';\nimport {\n  BranchesOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  ClockCircleOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined,\n  FireOutlined,\n  HeartOutlined,\n  ThunderboltOutlined,\n  TeamOutlined,\n  BookOutlined,\n  WarningOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { PlotLine, PlotEvent, Conflict } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst PlotLineManager: React.FC = () => {\n  const {\n    currentProject,\n    plotLines,\n    addPlotLine,\n    updatePlotLine,\n    deletePlotLine,\n    getPlotLines\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isEventModalVisible, setIsEventModalVisible] = useState(false);\n  const [isConflictModalVisible, setIsConflictModalVisible] = useState(false);\n  const [editingPlotLine, setEditingPlotLine] = useState<PlotLine | null>(null);\n  const [editingEvent, setEditingEvent] = useState<PlotEvent | null>(null);\n  const [editingConflict, setEditingConflict] = useState<Conflict | null>(null);\n  const [selectedPlotLine, setSelectedPlotLine] = useState<PlotLine | null>(null);\n  const [activeTab, setActiveTab] = useState('plotlines');\n  const [timelineFilter, setTimelineFilter] = useState<'all' | 'main' | 'subplot'>('all');\n  const [form] = Form.useForm();\n  const [eventForm] = Form.useForm();\n  const [conflictForm] = Form.useForm();\n\n  const projectPlotLines = currentProject ? getPlotLines(currentProject.id) : [];\n  const filteredPlotLines = timelineFilter === 'all'\n    ? projectPlotLines\n    : projectPlotLines.filter(line => line.type === timelineFilter);\n\n  // 获取所有事件并按时间排序\n  const allEvents = projectPlotLines.flatMap(line =>\n    line.timeline.map(event => ({ ...event, plotLineId: line.id, plotLineName: line.name }))\n  ).sort((a, b) => a.timestamp - b.timestamp);\n\n  const handleCreatePlotLine = () => {\n    setEditingPlotLine(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEditPlotLine = (plotLine: PlotLine) => {\n    setEditingPlotLine(plotLine);\n    form.setFieldsValue({\n      name: plotLine.name,\n      type: plotLine.type,\n      description: plotLine.description,\n      characters: plotLine.characters,\n      resolution: plotLine.resolution,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeletePlotLine = (plotLineId: string) => {\n    if (currentProject) {\n      deletePlotLine(currentProject.id, plotLineId);\n      message.success('故事线已删除');\n      if (selectedPlotLine?.id === plotLineId) {\n        setSelectedPlotLine(null);\n      }\n    }\n  };\n\n  const handleCreateEvent = (plotLine?: PlotLine) => {\n    setEditingEvent(null);\n    eventForm.resetFields();\n    if (plotLine) {\n      eventForm.setFieldsValue({ plotLineId: plotLine.id });\n    }\n    setIsEventModalVisible(true);\n  };\n\n  const handleCreateConflict = (plotLine?: PlotLine) => {\n    setEditingConflict(null);\n    conflictForm.resetFields();\n    if (plotLine) {\n      conflictForm.setFieldsValue({ plotLineId: plotLine.id });\n    }\n    setIsConflictModalVisible(true);\n  };\n\n  const getImportanceColor = (importance: string) => {\n    switch (importance) {\n      case 'critical': return 'red';\n      case 'major': return 'orange';\n      case 'minor': return 'blue';\n      default: return 'default';\n    }\n  };\n\n  const getImportanceIcon = (importance: string) => {\n    switch (importance) {\n      case 'critical': return <FireOutlined className=\"text-red-500\" />;\n      case 'major': return <ExclamationCircleOutlined className=\"text-orange-500\" />;\n      case 'minor': return <ClockCircleOutlined className=\"text-blue-500\" />;\n      default: return <ClockCircleOutlined className=\"text-gray-500\" />;\n    }\n  };\n\n  const getConflictTypeIcon = (type: string) => {\n    switch (type) {\n      case 'internal': return <HeartOutlined className=\"text-pink-500\" />;\n      case 'external': return <ThunderboltOutlined className=\"text-yellow-500\" />;\n      case 'interpersonal': return <TeamOutlined className=\"text-blue-500\" />;\n      default: return <ExclamationCircleOutlined className=\"text-gray-500\" />;\n    }\n  };\n\n  const getConflictTypeText = (type: string) => {\n    switch (type) {\n      case 'internal': return '内心冲突';\n      case 'external': return '外部冲突';\n      case 'interpersonal': return '人际冲突';\n      default: return '未知冲突';\n    }\n  };\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理故事线。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>主线管理</Title>\n          <Text type=\"secondary\">管理故事主线和支线情节 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={handleCreatePlotLine}\n          >\n            创建故事线\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'plotlines',\n            label: (\n              <span>\n                <BranchesOutlined />\n                故事线 ({projectPlotLines.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {projectPlotLines.length === 0 ? (\n                  <Card className=\"text-center py-12\">\n                    <BranchesOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                    <div className=\"mt-4\">\n                      <Text type=\"secondary\">还没有创建任何故事线</Text>\n                      <br />\n                      <Text type=\"secondary\">点击上方按钮创建您的第一条故事线</Text>\n                    </div>\n                  </Card>\n                ) : (\n                  <Row gutter={[16, 16]}>\n                    {projectPlotLines.map((plotLine) => (\n                      <Col span={12} key={plotLine.id}>\n                        <Card\n                          className=\"hover:shadow-lg transition-shadow cursor-pointer\"\n                          onClick={() => setSelectedPlotLine(plotLine)}\n                          actions={[\n                            <Tooltip title=\"添加事件\" key=\"event\">\n                              <ClockCircleOutlined onClick={(e) => {\n                                e.stopPropagation();\n                                handleCreateEvent(plotLine);\n                              }} />\n                            </Tooltip>,\n                            <Tooltip title=\"添加冲突\" key=\"conflict\">\n                              <ExclamationCircleOutlined onClick={(e) => {\n                                e.stopPropagation();\n                                handleCreateConflict(plotLine);\n                              }} />\n                            </Tooltip>,\n                            <Tooltip title=\"编辑\" key=\"edit\">\n                              <EditOutlined onClick={(e) => {\n                                e.stopPropagation();\n                                handleEditPlotLine(plotLine);\n                              }} />\n                            </Tooltip>,\n                            <Popconfirm\n                              key=\"delete\"\n                              title=\"确定要删除这条故事线吗？\"\n                              onConfirm={(e) => {\n                                e?.stopPropagation();\n                                handleDeletePlotLine(plotLine.id);\n                              }}\n                              okText=\"确定\"\n                              cancelText=\"取消\"\n                            >\n                              <DeleteOutlined onClick={(e) => e.stopPropagation()} />\n                            </Popconfirm>\n                          ]}\n                        >\n                          <div className=\"mb-3\">\n                            <div className=\"flex items-center justify-between mb-2\">\n                              <Title level={5} className=\"mb-0\">{plotLine.name}</Title>\n                              <Tag color={plotLine.type === 'main' ? 'red' : 'blue'}>\n                                {plotLine.type === 'main' ? '主线' : '支线'}\n                              </Tag>\n                            </div>\n\n                            <Paragraph\n                              ellipsis={{ rows: 2 }}\n                              type=\"secondary\"\n                              className=\"mb-3\"\n                            >\n                              {plotLine.description}\n                            </Paragraph>\n\n                            <div className=\"space-y-2\">\n                              <div className=\"flex items-center justify-between text-sm\">\n                                <span className=\"flex items-center space-x-1\">\n                                  <ClockCircleOutlined />\n                                  <Text type=\"secondary\">事件</Text>\n                                </span>\n                                <Badge count={plotLine.timeline.length} showZero />\n                              </div>\n\n                              <div className=\"flex items-center justify-between text-sm\">\n                                <span className=\"flex items-center space-x-1\">\n                                  <ExclamationCircleOutlined />\n                                  <Text type=\"secondary\">冲突</Text>\n                                </span>\n                                <Badge count={plotLine.conflicts.length} showZero />\n                              </div>\n\n                              <div className=\"flex items-center justify-between text-sm\">\n                                <span className=\"flex items-center space-x-1\">\n                                  <TeamOutlined />\n                                  <Text type=\"secondary\">角色</Text>\n                                </span>\n                                <Badge count={plotLine.characters.length} showZero />\n                              </div>\n                            </div>\n\n                            {plotLine.resolution && (\n                              <div className=\"mt-3 pt-3 border-t border-gray-100\">\n                                <Text type=\"secondary\" className=\"text-sm\">\n                                  <CheckCircleOutlined className=\"mr-1\" />\n                                  已设定结局\n                                </Text>\n                              </div>\n                            )}\n                          </div>\n                        </Card>\n                      </Col>\n                    ))}\n                  </Row>\n                )}\n              </div>\n            ),\n          },\n          {\n            key: 'timeline',\n            label: (\n              <span>\n                <ClockCircleOutlined />\n                时间轴\n              </span>\n            ),\n            children: (\n              <div>\n                <Card\n                  title=\"故事时间轴\"\n                  extra={\n                    <Space>\n                      <Select\n                        value={timelineFilter}\n                        onChange={setTimelineFilter}\n                        style={{ width: 120 }}\n                      >\n                        <Option value=\"all\">全部</Option>\n                        <Option value=\"main\">主线</Option>\n                        <Option value=\"subplot\">支线</Option>\n                      </Select>\n                    </Space>\n                  }\n                >\n                  {allEvents.length === 0 ? (\n                    <div className=\"text-center py-8\">\n                      <ClockCircleOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />\n                      <div className=\"mt-4\">\n                        <Text type=\"secondary\">还没有任何事件</Text>\n                        <br />\n                        <Text type=\"secondary\">在故事线中添加事件来构建时间轴</Text>\n                      </div>\n                    </div>\n                  ) : (\n                    <Timeline mode=\"left\">\n                      {allEvents.map((event, index) => (\n                        <Timeline.Item\n                          key={`${event.plotLineId}-${event.id}`}\n                          dot={getImportanceIcon(event.importance)}\n                          color={getImportanceColor(event.importance)}\n                        >\n                          <div className=\"pb-4\">\n                            <div className=\"flex items-center justify-between mb-2\">\n                              <Title level={5} className=\"mb-0\">{event.title}</Title>\n                              <Space>\n                                <Tag color=\"blue\">{event.plotLineName}</Tag>\n                                <Tag color={getImportanceColor(event.importance)}>\n                                  {event.importance === 'critical' ? '关键' :\n                                   event.importance === 'major' ? '重要' : '次要'}\n                                </Tag>\n                              </Space>\n                            </div>\n                            <Paragraph type=\"secondary\" className=\"mb-2\">\n                              {event.description}\n                            </Paragraph>\n                            {event.consequences.length > 0 && (\n                              <div>\n                                <Text strong className=\"text-sm\">后果影响:</Text>\n                                <ul className=\"mt-1 ml-4\">\n                                  {event.consequences.map((consequence, idx) => (\n                                    <li key={idx} className=\"text-sm text-gray-600\">\n                                      {consequence}\n                                    </li>\n                                  ))}\n                                </ul>\n                              </div>\n                            )}\n                          </div>\n                        </Timeline.Item>\n                      ))}\n                    </Timeline>\n                  )}\n                </Card>\n              </div>\n            ),\n          },\n          {\n            key: 'conflicts',\n            label: (\n              <span>\n                <ExclamationCircleOutlined />\n                冲突分析\n              </span>\n            ),\n            children: (\n              <div>\n                <Card title=\"冲突总览\">\n                  {projectPlotLines.length === 0 ? (\n                    <div className=\"text-center py-8\">\n                      <ExclamationCircleOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />\n                      <div className=\"mt-4\">\n                        <Text type=\"secondary\">还没有任何冲突</Text>\n                        <br />\n                        <Text type=\"secondary\">在故事线中添加冲突来分析情节张力</Text>\n                      </div>\n                    </div>\n                  ) : (\n                    <Row gutter={[16, 16]}>\n                      {projectPlotLines.map((plotLine) => (\n                        plotLine.conflicts.map((conflict) => (\n                          <Col span={8} key={conflict.id}>\n                            <Card size=\"small\">\n                              <div className=\"mb-2\">\n                                <div className=\"flex items-center justify-between\">\n                                  <Text strong>{conflict.description}</Text>\n                                  <div className=\"flex items-center space-x-1\">\n                                    {getConflictTypeIcon(conflict.type)}\n                                    <Text type=\"secondary\" className=\"text-xs\">\n                                      {getConflictTypeText(conflict.type)}\n                                    </Text>\n                                  </div>\n                                </div>\n                              </div>\n                              <div className=\"mb-2\">\n                                <Text type=\"secondary\" className=\"text-sm\">\n                                  故事线: {plotLine.name}\n                                </Text>\n                              </div>\n                              {conflict.participants.length > 0 && (\n                                <div className=\"mb-2\">\n                                  <Text type=\"secondary\" className=\"text-xs\">参与者:</Text>\n                                  <div className=\"mt-1\">\n                                    {conflict.participants.map((participant, idx) => (\n                                      <Tag key={idx} size=\"small\">{participant}</Tag>\n                                    ))}\n                                  </div>\n                                </div>\n                              )}\n                              {conflict.resolution && (\n                                <div className=\"mt-2 pt-2 border-t border-gray-100\">\n                                  <Text type=\"secondary\" className=\"text-xs\">\n                                    <CheckCircleOutlined className=\"mr-1\" />\n                                    已解决\n                                  </Text>\n                                </div>\n                              )}\n                            </Card>\n                          </Col>\n                        ))\n                      ))}\n                    </Row>\n                  )}\n                </Card>\n              </div>\n            ),\n          },\n        ]}\n      />\n\n      {/* 故事线详情 */}\n      {selectedPlotLine && (\n        <Card\n          className=\"mt-6\"\n          title={`故事线详情 - ${selectedPlotLine.name}`}\n          extra={\n            <Button\n              type=\"primary\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditPlotLine(selectedPlotLine)}\n            >\n              编辑故事线\n            </Button>\n          }\n        >\n          <Row gutter={24}>\n            <Col span={16}>\n              <div className=\"space-y-4\">\n                <div>\n                  <Text strong>故事线描述:</Text>\n                  <Paragraph className=\"mt-2\">{selectedPlotLine.description}</Paragraph>\n                </div>\n\n                {selectedPlotLine.resolution && (\n                  <div>\n                    <Text strong>结局设定:</Text>\n                    <Paragraph className=\"mt-2\">{selectedPlotLine.resolution}</Paragraph>\n                  </div>\n                )}\n\n                {selectedPlotLine.timeline.length > 0 && (\n                  <div>\n                    <Text strong>关键事件:</Text>\n                    <List\n                      size=\"small\"\n                      className=\"mt-2\"\n                      dataSource={selectedPlotLine.timeline}\n                      renderItem={(event) => (\n                        <List.Item>\n                          <div className=\"flex items-center justify-between w-full\">\n                            <div className=\"flex items-center space-x-2\">\n                              {getImportanceIcon(event.importance)}\n                              <Text>{event.title}</Text>\n                            </div>\n                            <Tag color={getImportanceColor(event.importance)}>\n                              {event.importance === 'critical' ? '关键' :\n                               event.importance === 'major' ? '重要' : '次要'}\n                            </Tag>\n                          </div>\n                        </List.Item>\n                      )}\n                    />\n                  </div>\n                )}\n              </div>\n            </Col>\n            <Col span={8}>\n              <div className=\"space-y-4\">\n                <div>\n                  <Text strong>故事线类型:</Text>\n                  <div className=\"mt-1\">\n                    <Tag color={selectedPlotLine.type === 'main' ? 'red' : 'blue'}>\n                      {selectedPlotLine.type === 'main' ? '主线' : '支线'}\n                    </Tag>\n                  </div>\n                </div>\n\n                <div>\n                  <Text strong>涉及角色:</Text>\n                  <div className=\"mt-2\">\n                    {selectedPlotLine.characters.map((character, index) => (\n                      <Tag key={index}>{character}</Tag>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <Text strong>事件数量:</Text>\n                  <div className=\"mt-1\">\n                    <Badge count={selectedPlotLine.timeline.length} showZero>\n                      <Text>个事件</Text>\n                    </Badge>\n                  </div>\n                </div>\n\n                <div>\n                  <Text strong>冲突数量:</Text>\n                  <div className=\"mt-1\">\n                    <Badge count={selectedPlotLine.conflicts.length} showZero>\n                      <Text>个冲突</Text>\n                    </Badge>\n                  </div>\n                </div>\n              </div>\n            </Col>\n          </Row>\n        </Card>\n      )}\n\n      {/* 创建/编辑故事线模态框 */}\n      <Modal\n        title={editingPlotLine ? '编辑故事线' : '创建故事线'}\n        open={isModalVisible}\n        onOk={async () => {\n          try {\n            const values = await form.validateFields();\n\n            if (editingPlotLine) {\n              // 更新故事线\n              if (currentProject) {\n                updatePlotLine(currentProject.id, editingPlotLine.id, {\n                  ...values,\n                  timeline: editingPlotLine.timeline || [],\n                  conflicts: editingPlotLine.conflicts || [],\n                });\n                message.success('故事线已更新');\n              }\n            } else {\n              // 创建新故事线\n              if (currentProject) {\n                addPlotLine(currentProject.id, {\n                  ...values,\n                  timeline: [],\n                  conflicts: [],\n                });\n                message.success('故事线已创建');\n              }\n            }\n\n            setIsModalVisible(false);\n            form.resetFields();\n          } catch (error) {\n            console.error('表单验证失败:', error);\n          }\n        }}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n        }}\n        width={800}\n        okText={editingPlotLine ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"name\"\n                label=\"故事线名称\"\n                rules={[{ required: true, message: '请输入故事线名称' }]}\n              >\n                <Input placeholder=\"请输入故事线名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"type\"\n                label=\"故事线类型\"\n                rules={[{ required: true, message: '请选择故事线类型' }]}\n              >\n                <Select placeholder=\"请选择类型\">\n                  <Option value=\"main\">主线</Option>\n                  <Option value=\"subplot\">支线</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"故事线描述\"\n            rules={[{ required: true, message: '请输入故事线描述' }]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请描述这条故事线的主要内容和发展...\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"characters\"\n            label=\"涉及角色\"\n            help=\"请输入参与这条故事线的角色名称\"\n          >\n            <Select\n              mode=\"tags\"\n              placeholder=\"请输入角色名称，按回车添加\"\n              tokenSeparators={[',']}\n            />\n          </Form.Item>\n\n          <Form.Item name=\"resolution\" label=\"结局设定\">\n            <TextArea\n              rows={3}\n              placeholder=\"请描述这条故事线的结局或解决方案...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 其他模态框占位符 */}\n      <Modal\n        title=\"添加事件\"\n        open={isEventModalVisible}\n        onCancel={() => setIsEventModalVisible(false)}\n        footer={null}\n      >\n        <div className=\"text-center py-8\">\n          <ClockCircleOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />\n          <div className=\"mt-4\">\n            <Text type=\"secondary\">事件编辑功能开发中</Text>\n          </div>\n        </div>\n      </Modal>\n\n      <Modal\n        title=\"添加冲突\"\n        open={isConflictModalVisible}\n        onCancel={() => setIsConflictModalVisible(false)}\n        footer={null}\n      >\n        <div className=\"text-center py-8\">\n          <ExclamationCircleOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />\n          <div className=\"mt-4\">\n            <Text type=\"secondary\">冲突编辑功能开发中</Text>\n          </div>\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default PlotLineManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;;;AAxCA;;;;;AA2CA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAE1B,MAAM,kBAA4B;;IAChC,MAAM,EACJ,cAAc,EACd,SAAS,EACT,WAAW,EACX,cAAc,EACd,cAAc,EACd,YAAY,EACb,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACjF,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,UAAU,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAChC,MAAM,CAAC,aAAa,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAEnC,MAAM,mBAAmB,iBAAiB,aAAa,eAAe,EAAE,IAAI,EAAE;IAC9E,MAAM,oBAAoB,mBAAmB,QACzC,mBACA,iBAAiB,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;IAElD,eAAe;IACf,MAAM,YAAY,iBAAiB,OAAO,CAAC,CAAA,OACzC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;gBAAE,GAAG,KAAK;gBAAE,YAAY,KAAK,EAAE;gBAAE,cAAc,KAAK,IAAI;YAAC,CAAC,IACtF,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IAE1C,MAAM,uBAAuB;QAC3B,mBAAmB;QACnB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,KAAK,cAAc,CAAC;YAClB,MAAM,SAAS,IAAI;YACnB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW;YACjC,YAAY,SAAS,UAAU;YAC/B,YAAY,SAAS,UAAU;QACjC;QACA,kBAAkB;IACpB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,gBAAgB;YAClB,eAAe,eAAe,EAAE,EAAE;YAClC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,IAAI,CAAA,6BAAA,uCAAA,iBAAkB,EAAE,MAAK,YAAY;gBACvC,oBAAoB;YACtB;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,UAAU,WAAW;QACrB,IAAI,UAAU;YACZ,UAAU,cAAc,CAAC;gBAAE,YAAY,SAAS,EAAE;YAAC;QACrD;QACA,uBAAuB;IACzB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,aAAa,WAAW;QACxB,IAAI,UAAU;YACZ,aAAa,cAAc,CAAC;gBAAE,YAAY,SAAS,EAAE;YAAC;QACxD;QACA,0BAA0B;IAC5B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAY,qBAAO,6LAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YAChD,KAAK;gBAAS,qBAAO,6LAAC,+OAAA,CAAA,4BAAyB;oBAAC,WAAU;;;;;;YAC1D,KAAK;gBAAS,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACpD;gBAAS,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;QACjD;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAY,qBAAO,6LAAC,uNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YACjD,KAAK;gBAAY,qBAAO,6LAAC,mOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACvD,KAAK;gBAAiB,qBAAO,6LAAC,qNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACrD;gBAAS,qBAAO,6LAAC,+OAAA,CAAA,4BAAyB;oBAAC,WAAU;;;;;;QACvD;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAiB,OAAO;YAC7B;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;;oCAAY;oCAAmB,eAAe,IAAI;;;;;;;;;;;;;kCAE/D,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS;sCACV;;;;;;;;;;;;;;;;;0BAML,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gCAAG;gCACd,iBAAiB,MAAM;gCAAC;;;;;;;wBAGlC,wBACE,6LAAC;sCACE,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC,iLAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,6NAAA,CAAA,mBAAgB;wCAAC,OAAO;4CAAE,UAAU;4CAAI,OAAO;wCAAU;;;;;;kDAC1D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,MAAK;0DAAY;;;;;;0DACvB,6LAAC;;;;;0DACD,6LAAC;gDAAK,MAAK;0DAAY;;;;;;;;;;;;;;;;;uDAI3B,6LAAC,+KAAA,CAAA,MAAG;gCAAC,QAAQ;oCAAC;oCAAI;iCAAG;0CAClB,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CACH,WAAU;4CACV,SAAS,IAAM,oBAAoB;4CACnC,SAAS;8DACP,6LAAC,uLAAA,CAAA,UAAO;oDAAC,OAAM;8DACb,cAAA,6LAAC,mOAAA,CAAA,sBAAmB;wDAAC,SAAS,CAAC;4DAC7B,EAAE,eAAe;4DACjB,kBAAkB;wDACpB;;;;;;mDAJwB;;;;;8DAM1B,6LAAC,uLAAA,CAAA,UAAO;oDAAC,OAAM;8DACb,cAAA,6LAAC,+OAAA,CAAA,4BAAyB;wDAAC,SAAS,CAAC;4DACnC,EAAE,eAAe;4DACjB,qBAAqB;wDACvB;;;;;;mDAJwB;;;;;8DAM1B,6LAAC,uLAAA,CAAA,UAAO;oDAAC,OAAM;8DACb,cAAA,6LAAC,qNAAA,CAAA,eAAY;wDAAC,SAAS,CAAC;4DACtB,EAAE,eAAe;4DACjB,mBAAmB;wDACrB;;;;;;mDAJsB;;;;;8DAMxB,6LAAC,6LAAA,CAAA,aAAU;oDAET,OAAM;oDACN,WAAW,CAAC;wDACV,cAAA,wBAAA,EAAG,eAAe;wDAClB,qBAAqB,SAAS,EAAE;oDAClC;oDACA,QAAO;oDACP,YAAW;8DAEX,cAAA,6LAAC,yNAAA,CAAA,iBAAc;wDAAC,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;mDAT7C;;;;;6CAWP;sDAED,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,OAAO;gEAAG,WAAU;0EAAQ,SAAS,IAAI;;;;;;0EAChD,6LAAC,+KAAA,CAAA,MAAG;gEAAC,OAAO,SAAS,IAAI,KAAK,SAAS,QAAQ;0EAC5C,SAAS,IAAI,KAAK,SAAS,OAAO;;;;;;;;;;;;kEAIvC,6LAAC;wDACC,UAAU;4DAAE,MAAM;wDAAE;wDACpB,MAAK;wDACL,WAAU;kEAET,SAAS,WAAW;;;;;;kEAGvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,mOAAA,CAAA,sBAAmB;;;;;0FACpB,6LAAC;gFAAK,MAAK;0FAAY;;;;;;;;;;;;kFAEzB,6LAAC,mLAAA,CAAA,QAAK;wEAAC,OAAO,SAAS,QAAQ,CAAC,MAAM;wEAAE,QAAQ;;;;;;;;;;;;0EAGlD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,+OAAA,CAAA,4BAAyB;;;;;0FAC1B,6LAAC;gFAAK,MAAK;0FAAY;;;;;;;;;;;;kFAEzB,6LAAC,mLAAA,CAAA,QAAK;wEAAC,OAAO,SAAS,SAAS,CAAC,MAAM;wEAAE,QAAQ;;;;;;;;;;;;0EAGnD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;;0FACd,6LAAC,qNAAA,CAAA,eAAY;;;;;0FACb,6LAAC;gFAAK,MAAK;0FAAY;;;;;;;;;;;;kFAEzB,6LAAC,mLAAA,CAAA,QAAK;wEAAC,OAAO,SAAS,UAAU,CAAC,MAAM;wEAAE,QAAQ;;;;;;;;;;;;;;;;;;oDAIrD,SAAS,UAAU,kBAClB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,MAAK;4DAAY,WAAU;;8EAC/B,6LAAC,mOAAA,CAAA,sBAAmB;oEAAC,WAAU;;;;;;gEAAS;;;;;;;;;;;;;;;;;;;;;;;uCAlFhC,SAAS,EAAE;;;;;;;;;;;;;;;oBA+F3C;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,mOAAA,CAAA,sBAAmB;;;;;gCAAG;;;;;;;wBAI3B,wBACE,6LAAC;sCACC,cAAA,6LAAC,iLAAA,CAAA,OAAI;gCACH,OAAM;gCACN,qBACE,6LAAC,mMAAA,CAAA,QAAK;8CACJ,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCACL,OAAO;wCACP,UAAU;wCACV,OAAO;4CAAE,OAAO;wCAAI;;0DAEpB,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;;;;;;0CAK7B,UAAU,MAAM,KAAK,kBACpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mOAAA,CAAA,sBAAmB;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDAC7D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;;;;;8DACD,6LAAC;oDAAK,MAAK;8DAAY;;;;;;;;;;;;;;;;;2DAI3B,6LAAC,yLAAA,CAAA,WAAQ;oCAAC,MAAK;8CACZ,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,6LAAC,yLAAA,CAAA,WAAQ,CAAC,IAAI;4CAEZ,KAAK,kBAAkB,MAAM,UAAU;4CACvC,OAAO,mBAAmB,MAAM,UAAU;sDAE1C,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAM,OAAO;gEAAG,WAAU;0EAAQ,MAAM,KAAK;;;;;;0EAC9C,6LAAC,mMAAA,CAAA,QAAK;;kFACJ,6LAAC,+KAAA,CAAA,MAAG;wEAAC,OAAM;kFAAQ,MAAM,YAAY;;;;;;kFACrC,6LAAC,+KAAA,CAAA,MAAG;wEAAC,OAAO,mBAAmB,MAAM,UAAU;kFAC5C,MAAM,UAAU,KAAK,aAAa,OAClC,MAAM,UAAU,KAAK,UAAU,OAAO;;;;;;;;;;;;;;;;;;kEAI7C,6LAAC;wDAAU,MAAK;wDAAY,WAAU;kEACnC,MAAM,WAAW;;;;;;oDAEnB,MAAM,YAAY,CAAC,MAAM,GAAG,mBAC3B,6LAAC;;0EACC,6LAAC;gEAAK,MAAM;gEAAC,WAAU;0EAAU;;;;;;0EACjC,6LAAC;gEAAG,WAAU;0EACX,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,oBACpC,6LAAC;wEAAa,WAAU;kFACrB;uEADM;;;;;;;;;;;;;;;;;;;;;;2CAvBd,AAAC,GAAsB,OAApB,MAAM,UAAU,EAAC,KAAY,OAAT,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;oBAsCpD;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,+OAAA,CAAA,4BAAyB;;;;;gCAAG;;;;;;;wBAIjC,wBACE,6LAAC;sCACC,cAAA,6LAAC,iLAAA,CAAA,OAAI;gCAAC,OAAM;0CACT,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+OAAA,CAAA,4BAAyB;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACnE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;;;;;8DACD,6LAAC;oDAAK,MAAK;8DAAY;;;;;;;;;;;;;;;;;2DAI3B,6LAAC,+KAAA,CAAA,MAAG;oCAAC,QAAQ;wCAAC;wCAAI;qCAAG;8CAClB,iBAAiB,GAAG,CAAC,CAAC,WACrB,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,yBACtB,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;oDAAC,MAAK;;sEACT,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAM;kFAAE,SAAS,WAAW;;;;;;kFAClC,6LAAC;wEAAI,WAAU;;4EACZ,oBAAoB,SAAS,IAAI;0FAClC,6LAAC;gFAAK,MAAK;gFAAY,WAAU;0FAC9B,oBAAoB,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;sEAK1C,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,MAAK;gEAAY,WAAU;;oEAAU;oEACnC,SAAS,IAAI;;;;;;;;;;;;wDAGtB,SAAS,YAAY,CAAC,MAAM,GAAG,mBAC9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,MAAK;oEAAY,WAAU;8EAAU;;;;;;8EAC3C,6LAAC;oEAAI,WAAU;8EACZ,SAAS,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,oBACvC,6LAAC,+KAAA,CAAA,MAAG;4EAAW,MAAK;sFAAS;2EAAnB;;;;;;;;;;;;;;;;wDAKjB,SAAS,UAAU,kBAClB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,MAAK;gEAAY,WAAU;;kFAC/B,6LAAC,mOAAA,CAAA,sBAAmB;wEAAC,WAAU;;;;;;oEAAS;;;;;;;;;;;;;;;;;;+CA/B/B,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;oBA6C9C;iBACD;;;;;;YAIF,kCACC,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAU;gBACV,OAAO,AAAC,WAAgC,OAAtB,iBAAiB,IAAI;gBACvC,qBACE,6LAAC,qMAAA,CAAA,SAAM;oBACL,MAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,SAAS,IAAM,mBAAmB;8BACnC;;;;;;0BAKH,cAAA,6LAAC,+KAAA,CAAA,MAAG;oBAAC,QAAQ;;sCACX,6LAAC,+KAAA,CAAA,MAAG;4BAAC,MAAM;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,iBAAiB,WAAW;;;;;;;;;;;;oCAG1D,iBAAiB,UAAU,kBAC1B,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAU,WAAU;0DAAQ,iBAAiB,UAAU;;;;;;;;;;;;oCAI3D,iBAAiB,QAAQ,CAAC,MAAM,GAAG,mBAClC,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC,iLAAA,CAAA,OAAI;gDACH,MAAK;gDACL,WAAU;gDACV,YAAY,iBAAiB,QAAQ;gDACrC,YAAY,CAAC,sBACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEACZ,kBAAkB,MAAM,UAAU;sFACnC,6LAAC;sFAAM,MAAM,KAAK;;;;;;;;;;;;8EAEpB,6LAAC,+KAAA,CAAA,MAAG;oEAAC,OAAO,mBAAmB,MAAM,UAAU;8EAC5C,MAAM,UAAU,KAAK,aAAa,OAClC,MAAM,UAAU,KAAK,UAAU,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUzD,6LAAC,+KAAA,CAAA,MAAG;4BAAC,MAAM;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+KAAA,CAAA,MAAG;oDAAC,OAAO,iBAAiB,IAAI,KAAK,SAAS,QAAQ;8DACpD,iBAAiB,IAAI,KAAK,SAAS,OAAO;;;;;;;;;;;;;;;;;kDAKjD,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,iBAAiB,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC3C,6LAAC,+KAAA,CAAA,MAAG;kEAAc;uDAAR;;;;;;;;;;;;;;;;kDAKhB,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;oDAAC,OAAO,iBAAiB,QAAQ,CAAC,MAAM;oDAAE,QAAQ;8DACtD,cAAA,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;kDAKZ,6LAAC;;0DACC,6LAAC;gDAAK,MAAM;0DAAC;;;;;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;oDAAC,OAAO,iBAAiB,SAAS,CAAC,MAAM;oDAAE,QAAQ;8DACvD,cAAA,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,kBAAkB,UAAU;gBACnC,MAAM;gBACN,MAAM;oBACJ,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBAExC,IAAI,iBAAiB;4BACnB,QAAQ;4BACR,IAAI,gBAAgB;gCAClB,eAAe,eAAe,EAAE,EAAE,gBAAgB,EAAE,EAAE;oCACpD,GAAG,MAAM;oCACT,UAAU,gBAAgB,QAAQ,IAAI,EAAE;oCACxC,WAAW,gBAAgB,SAAS,IAAI,EAAE;gCAC5C;gCACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;4BAClB;wBACF,OAAO;4BACL,SAAS;4BACT,IAAI,gBAAgB;gCAClB,YAAY,eAAe,EAAE,EAAE;oCAC7B,GAAG,MAAM;oCACT,UAAU,EAAE;oCACZ,WAAW,EAAE;gCACf;gCACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;4BAClB;wBACF;wBAEA,kBAAkB;wBAClB,KAAK,WAAW;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B;gBACF;gBACA,UAAU;oBACR,kBAAkB;oBAClB,KAAK,WAAW;gBAClB;gBACA,OAAO;gBACP,QAAQ,kBAAkB,OAAO;gBACjC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAW;yCAAE;kDAEhD,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAW;yCAAE;kDAEhD,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;;8DAClB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAW;6BAAE;sCAEhD,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,MAAK;sCAEL,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,MAAK;gCACL,aAAY;gCACZ,iBAAiB;oCAAC;iCAAI;;;;;;;;;;;sCAI1B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAa,OAAM;sCACjC,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;0BAOpB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,uBAAuB;gBACvC,QAAQ;0BAER,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mOAAA,CAAA,sBAAmB;4BAAC,OAAO;gCAAE,UAAU;gCAAI,OAAO;4BAAU;;;;;;sCAC7D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;0BAK7B,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU,IAAM,0BAA0B;gBAC1C,QAAQ;0BAER,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+OAAA,CAAA,4BAAyB;4BAAC,OAAO;gCAAE,UAAU;gCAAI,OAAO;4BAAU;;;;;;sCACnE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnC;GAvoBM;;QAQA,wHAAA,CAAA,cAAW;QAWA,iLAAA,CAAA,OAAI,CAAC;QACA,iLAAA,CAAA,OAAI,CAAC;QACF,iLAAA,CAAA,OAAI,CAAC;;;KArBxB;uCAyoBS", "debugId": null}}, {"offset": {"line": 9203, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/title/TitleManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Tabs,\n  List,\n  Tag,\n  Rate,\n  Progress,\n  message,\n  Row,\n  Col,\n  Divider,\n  Tooltip,\n  Badge,\n  Slider,\n  Switch\n} from 'antd';\nimport {\n  BookOutlined,\n  PlusOutlined,\n  HeartOutlined,\n  HeartFilled,\n  ThunderboltOutlined,\n  StarOutlined,\n  EyeOutlined,\n  TrophyOutlined,\n  FireOutlined,\n  CheckCircleOutlined,\n  ReloadOutlined,\n  DownloadOutlined,\n  ShareAltOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { BookTitle, TitleAnalysis } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\n\nconst TitleManager: React.FC = () => {\n  const {\n    currentProject,\n    bookTitles,\n    addBookTitle,\n    updateBookTitle,\n    deleteBookTitle,\n    toggleTitleFavorite,\n    getBookTitles\n  } = useAppStore();\n\n  const [isGeneratorVisible, setIsGeneratorVisible] = useState(false);\n  const [isAnalysisVisible, setIsAnalysisVisible] = useState(false);\n  const [selectedTitle, setSelectedTitle] = useState<BookTitle | null>(null);\n  const [activeTab, setActiveTab] = useState('titles');\n  const [filterFavorites, setFilterFavorites] = useState(false);\n  const [sortBy, setSortBy] = useState<'score' | 'created' | 'name'>('score');\n  const [generatorForm] = Form.useForm();\n\n  const projectTitles = currentProject ? getBookTitles(currentProject.id) : [];\n\n  // 过滤和排序书名\n  const filteredTitles = projectTitles\n    .filter(title => !filterFavorites || title.isFavorite)\n    .sort((a, b) => {\n      switch (sortBy) {\n        case 'score': return b.score - a.score;\n        case 'created': return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n        case 'name': return a.title.localeCompare(b.title);\n        default: return 0;\n      }\n    });\n\n  // 模拟书名生成器\n  const generateTitles = async (params: any) => {\n    const { genre, style, keywords, count } = params;\n\n    // 模拟生成的书名\n    const sampleTitles = [\n      '星辰大海的征途', '时光倒流的秘密', '梦境中的王国', '失落的古老传说',\n      '永恒之光的守护者', '命运交响曲', '幻想世界的冒险', '心灵深处的呼唤',\n      '未来科技的奇迹', '爱情与战争的史诗', '神秘力量的觉醒', '英雄的传奇故事'\n    ];\n\n    const generatedTitles = [];\n    for (let i = 0; i < count; i++) {\n      const title = sampleTitles[Math.floor(Math.random() * sampleTitles.length)];\n      const analysis: TitleAnalysis = {\n        appeal: Math.floor(Math.random() * 40) + 60,\n        memorability: Math.floor(Math.random() * 40) + 60,\n        genreMatch: Math.floor(Math.random() * 30) + 70,\n        uniqueness: Math.floor(Math.random() * 50) + 50,\n        marketability: Math.floor(Math.random() * 40) + 60,\n        suggestions: [\n          '考虑增加更具体的元素描述',\n          '可以尝试更简洁的表达方式',\n          '建议突出主角或核心冲突'\n        ]\n      };\n\n      const score = Math.round((analysis.appeal + analysis.memorability + analysis.genreMatch + analysis.uniqueness + analysis.marketability) / 5);\n\n      generatedTitles.push({\n        title: `${title}${i > 0 ? ` ${i + 1}` : ''}`,\n        genre,\n        style,\n        score,\n        analysis,\n        isFavorite: false,\n        createdAt: new Date(),\n      });\n    }\n\n    return generatedTitles;\n  };\n\n  const handleGenerate = async () => {\n    try {\n      const values = await generatorForm.validateFields();\n      const generatedTitles = await generateTitles(values);\n\n      if (currentProject) {\n        generatedTitles.forEach(titleData => {\n          addBookTitle(currentProject.id, titleData);\n        });\n        message.success(`成功生成 ${generatedTitles.length} 个书名`);\n      }\n\n      setIsGeneratorVisible(false);\n      generatorForm.resetFields();\n    } catch (error) {\n      console.error('生成失败:', error);\n    }\n  };\n\n  const handleToggleFavorite = (title: BookTitle) => {\n    if (currentProject) {\n      toggleTitleFavorite(currentProject.id, title.id);\n      message.success(title.isFavorite ? '已取消收藏' : '已添加到收藏');\n    }\n  };\n\n  const getScoreColor = (score: number) => {\n    if (score >= 80) return 'success';\n    if (score >= 60) return 'warning';\n    return 'exception';\n  };\n\n  const getScoreIcon = (score: number) => {\n    if (score >= 90) return <TrophyOutlined className=\"text-yellow-500\" />;\n    if (score >= 80) return <StarOutlined className=\"text-blue-500\" />;\n    if (score >= 60) return <FireOutlined className=\"text-orange-500\" />;\n    return <EyeOutlined className=\"text-gray-500\" />;\n  };\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理书名。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>书名管理</Title>\n          <Text type=\"secondary\">管理书名生成和选择 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<ThunderboltOutlined />}\n            onClick={() => setIsGeneratorVisible(true)}\n          >\n            AI生成书名\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'titles',\n            label: (\n              <span>\n                <BookOutlined />\n                书名库 ({filteredTitles.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {/* 过滤和排序控制 */}\n                <Card className=\"mb-6\">\n                  <Row gutter={16} align=\"middle\">\n                    <Col span={6}>\n                      <div className=\"flex items-center space-x-2\">\n                        <Text>只看收藏:</Text>\n                        <Switch\n                          checked={filterFavorites}\n                          onChange={setFilterFavorites}\n                          checkedChildren={<HeartFilled />}\n                          unCheckedChildren={<HeartOutlined />}\n                        />\n                      </div>\n                    </Col>\n                    <Col span={6}>\n                      <div className=\"flex items-center space-x-2\">\n                        <Text>排序方式:</Text>\n                        <Select\n                          value={sortBy}\n                          onChange={setSortBy}\n                          style={{ width: 120 }}\n                        >\n                          <Option value=\"score\">评分</Option>\n                          <Option value=\"created\">创建时间</Option>\n                          <Option value=\"name\">书名</Option>\n                        </Select>\n                      </div>\n                    </Col>\n                    <Col span={12}>\n                      <div className=\"text-right\">\n                        <Space>\n                          <Text type=\"secondary\">\n                            共 {projectTitles.length} 个书名，\n                            收藏 {projectTitles.filter(t => t.isFavorite).length} 个\n                          </Text>\n                        </Space>\n                      </div>\n                    </Col>\n                  </Row>\n                </Card>\n\n                {/* 书名列表 */}\n                {filteredTitles.length === 0 ? (\n                  <Card className=\"text-center py-12\">\n                    <BookOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                    <div className=\"mt-4\">\n                      <Text type=\"secondary\">\n                        {projectTitles.length === 0 ? '还没有任何书名' : '没有符合条件的书名'}\n                      </Text>\n                      <br />\n                      <Text type=\"secondary\">\n                        {projectTitles.length === 0 ? '点击上方按钮使用AI生成书名' : '尝试调整过滤条件'}\n                      </Text>\n                    </div>\n                  </Card>\n                ) : (\n                  <List\n                    grid={{ gutter: 16, column: 2 }}\n                    dataSource={filteredTitles}\n                    renderItem={(title) => (\n                      <List.Item>\n                        <Card\n                          className=\"hover:shadow-lg transition-shadow cursor-pointer\"\n                          onClick={() => {\n                            setSelectedTitle(title);\n                            setIsAnalysisVisible(true);\n                          }}\n                          actions={[\n                            <Tooltip title={title.isFavorite ? \"取消收藏\" : \"添加收藏\"} key=\"favorite\">\n                              <Button\n                                type=\"text\"\n                                icon={title.isFavorite ? <HeartFilled className=\"text-red-500\" /> : <HeartOutlined />}\n                                onClick={(e) => {\n                                  e.stopPropagation();\n                                  handleToggleFavorite(title);\n                                }}\n                              />\n                            </Tooltip>,\n                            <Tooltip title=\"查看分析\" key=\"analysis\">\n                              <EyeOutlined onClick={(e) => {\n                                e.stopPropagation();\n                                setSelectedTitle(title);\n                                setIsAnalysisVisible(true);\n                              }} />\n                            </Tooltip>,\n                            <Tooltip title=\"分享\" key=\"share\">\n                              <ShareAltOutlined onClick={(e) => {\n                                e.stopPropagation();\n                                navigator.clipboard.writeText(title.title);\n                                message.success('书名已复制到剪贴板');\n                              }} />\n                            </Tooltip>\n                          ]}\n                        >\n                          <div className=\"mb-3\">\n                            <div className=\"flex items-center justify-between mb-2\">\n                              <Title level={5} className=\"mb-0 flex-1 mr-2\">\n                                {title.title}\n                              </Title>\n                              <div className=\"flex items-center space-x-1\">\n                                {getScoreIcon(title.score)}\n                                <Text strong className=\"text-lg\">{title.score}</Text>\n                              </div>\n                            </div>\n\n                            <div className=\"mb-3\">\n                              <Progress\n                                percent={title.score}\n                                size=\"small\"\n                                status={getScoreColor(title.score)}\n                                showInfo={false}\n                              />\n                            </div>\n\n                            <div className=\"flex items-center justify-between mb-2\">\n                              <Space>\n                                <Tag color=\"blue\">{title.genre}</Tag>\n                                <Tag color=\"green\">{title.style}</Tag>\n                              </Space>\n                              {title.isFavorite && (\n                                <HeartFilled className=\"text-red-500\" />\n                              )}\n                            </div>\n\n                            <div className=\"text-sm text-gray-500\">\n                              创建于 {new Date(title.createdAt).toLocaleDateString()}\n                            </div>\n                          </div>\n                        </Card>\n                      </List.Item>\n                    )}\n                  />\n                )}\n              </div>\n            ),\n          },\n          {\n            key: 'analytics',\n            label: (\n              <span>\n                <StarOutlined />\n                数据分析\n              </span>\n            ),\n            children: (\n              <div>\n                <Row gutter={[16, 16]}>\n                  <Col span={8}>\n                    <Card title=\"评分分布\">\n                      <div className=\"space-y-3\">\n                        <div className=\"flex items-center justify-between\">\n                          <Text>优秀 (80+)</Text>\n                          <Badge count={projectTitles.filter(t => t.score >= 80).length} showZero />\n                        </div>\n                        <div className=\"flex items-center justify-between\">\n                          <Text>良好 (60-79)</Text>\n                          <Badge count={projectTitles.filter(t => t.score >= 60 && t.score < 80).length} showZero />\n                        </div>\n                        <div className=\"flex items-center justify-between\">\n                          <Text>一般 (60以下)</Text>\n                          <Badge count={projectTitles.filter(t => t.score < 60).length} showZero />\n                        </div>\n                      </div>\n                    </Card>\n                  </Col>\n                  <Col span={8}>\n                    <Card title=\"类型分布\">\n                      <div className=\"space-y-3\">\n                        {Array.from(new Set(projectTitles.map(t => t.genre))).map(genre => (\n                          <div key={genre} className=\"flex items-center justify-between\">\n                            <Text>{genre}</Text>\n                            <Badge count={projectTitles.filter(t => t.genre === genre).length} showZero />\n                          </div>\n                        ))}\n                      </div>\n                    </Card>\n                  </Col>\n                  <Col span={8}>\n                    <Card title=\"收藏统计\">\n                      <div className=\"space-y-3\">\n                        <div className=\"flex items-center justify-between\">\n                          <Text>已收藏</Text>\n                          <Badge count={projectTitles.filter(t => t.isFavorite).length} showZero />\n                        </div>\n                        <div className=\"flex items-center justify-between\">\n                          <Text>未收藏</Text>\n                          <Badge count={projectTitles.filter(t => !t.isFavorite).length} showZero />\n                        </div>\n                        <div className=\"flex items-center justify-between\">\n                          <Text>收藏率</Text>\n                          <Text>\n                            {projectTitles.length > 0\n                              ? Math.round((projectTitles.filter(t => t.isFavorite).length / projectTitles.length) * 100)\n                              : 0}%\n                          </Text>\n                        </div>\n                      </div>\n                    </Card>\n                  </Col>\n                </Row>\n              </div>\n            ),\n          },\n        ]}\n      />\n\n      {/* AI书名生成器模态框 */}\n      <Modal\n        title=\"AI书名生成器\"\n        open={isGeneratorVisible}\n        onOk={handleGenerate}\n        onCancel={() => {\n          setIsGeneratorVisible(false);\n          generatorForm.resetFields();\n        }}\n        width={600}\n        okText=\"生成书名\"\n        cancelText=\"取消\"\n      >\n        <Form form={generatorForm} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"genre\"\n                label=\"小说类型\"\n                rules={[{ required: true, message: '请选择小说类型' }]}\n              >\n                <Select placeholder=\"请选择类型\">\n                  <Option value=\"现代都市\">现代都市</Option>\n                  <Option value=\"古代言情\">古代言情</Option>\n                  <Option value=\"玄幻修仙\">玄幻修仙</Option>\n                  <Option value=\"科幻未来\">科幻未来</Option>\n                  <Option value=\"悬疑推理\">悬疑推理</Option>\n                  <Option value=\"历史军事\">历史军事</Option>\n                  <Option value=\"青春校园\">青春校园</Option>\n                  <Option value=\"商战职场\">商战职场</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"style\"\n                label=\"写作风格\"\n                rules={[{ required: true, message: '请选择写作风格' }]}\n              >\n                <Select placeholder=\"请选择风格\">\n                  <Option value=\"轻松幽默\">轻松幽默</Option>\n                  <Option value=\"深沉严肃\">深沉严肃</Option>\n                  <Option value=\"浪漫温馨\">浪漫温馨</Option>\n                  <Option value=\"紧张刺激\">紧张刺激</Option>\n                  <Option value=\"文艺清新\">文艺清新</Option>\n                  <Option value=\"热血激昂\">热血激昂</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"keywords\" label=\"关键词\">\n            <Select\n              mode=\"tags\"\n              placeholder=\"请输入关键词，如：爱情、冒险、成长等\"\n              tokenSeparators={[',']}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"count\"\n            label=\"生成数量\"\n            initialValue={5}\n            rules={[{ required: true, message: '请选择生成数量' }]}\n          >\n            <Slider\n              min={1}\n              max={20}\n              marks={{\n                1: '1个',\n                5: '5个',\n                10: '10个',\n                20: '20个'\n              }}\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 书名分析模态框 */}\n      <Modal\n        title={`书名分析 - ${selectedTitle?.title}`}\n        open={isAnalysisVisible}\n        onCancel={() => setIsAnalysisVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setIsAnalysisVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {selectedTitle && (\n          <div>\n            <Row gutter={24}>\n              <Col span={16}>\n                <div className=\"space-y-4\">\n                  <div>\n                    <Title level={4}>{selectedTitle.title}</Title>\n                    <Space>\n                      <Tag color=\"blue\">{selectedTitle.genre}</Tag>\n                      <Tag color=\"green\">{selectedTitle.style}</Tag>\n                      <Text type=\"secondary\">\n                        创建于 {new Date(selectedTitle.createdAt).toLocaleDateString()}\n                      </Text>\n                    </Space>\n                  </div>\n\n                  <Divider />\n\n                  <div>\n                    <Title level={5}>详细评分</Title>\n                    <div className=\"space-y-3\">\n                      <div>\n                        <div className=\"flex items-center justify-between mb-1\">\n                          <Text>吸引力</Text>\n                          <Text strong>{selectedTitle.analysis.appeal}/100</Text>\n                        </div>\n                        <Progress percent={selectedTitle.analysis.appeal} size=\"small\" />\n                      </div>\n\n                      <div>\n                        <div className=\"flex items-center justify-between mb-1\">\n                          <Text>记忆度</Text>\n                          <Text strong>{selectedTitle.analysis.memorability}/100</Text>\n                        </div>\n                        <Progress percent={selectedTitle.analysis.memorability} size=\"small\" />\n                      </div>\n\n                      <div>\n                        <div className=\"flex items-center justify-between mb-1\">\n                          <Text>类型匹配</Text>\n                          <Text strong>{selectedTitle.analysis.genreMatch}/100</Text>\n                        </div>\n                        <Progress percent={selectedTitle.analysis.genreMatch} size=\"small\" />\n                      </div>\n\n                      <div>\n                        <div className=\"flex items-center justify-between mb-1\">\n                          <Text>独特性</Text>\n                          <Text strong>{selectedTitle.analysis.uniqueness}/100</Text>\n                        </div>\n                        <Progress percent={selectedTitle.analysis.uniqueness} size=\"small\" />\n                      </div>\n\n                      <div>\n                        <div className=\"flex items-center justify-between mb-1\">\n                          <Text>市场性</Text>\n                          <Text strong>{selectedTitle.analysis.marketability}/100</Text>\n                        </div>\n                        <Progress percent={selectedTitle.analysis.marketability} size=\"small\" />\n                      </div>\n                    </div>\n                  </div>\n\n                  <Divider />\n\n                  <div>\n                    <Title level={5}>优化建议</Title>\n                    <List\n                      size=\"small\"\n                      dataSource={selectedTitle.analysis.suggestions}\n                      renderItem={(suggestion, index) => (\n                        <List.Item>\n                          <Text>{index + 1}. {suggestion}</Text>\n                        </List.Item>\n                      )}\n                    />\n                  </div>\n                </div>\n              </Col>\n              <Col span={8}>\n                <div className=\"text-center\">\n                  <div className=\"mb-4\">\n                    <div className=\"text-6xl font-bold text-blue-500 mb-2\">\n                      {selectedTitle.score}\n                    </div>\n                    <Text type=\"secondary\">综合评分</Text>\n                  </div>\n\n                  <div className=\"mb-4\">\n                    <Progress\n                      type=\"circle\"\n                      percent={selectedTitle.score}\n                      status={getScoreColor(selectedTitle.score)}\n                      width={120}\n                    />\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <Button\n                      type={selectedTitle.isFavorite ? \"default\" : \"primary\"}\n                      icon={selectedTitle.isFavorite ? <HeartFilled /> : <HeartOutlined />}\n                      onClick={() => handleToggleFavorite(selectedTitle)}\n                      block\n                    >\n                      {selectedTitle.isFavorite ? '取消收藏' : '添加收藏'}\n                    </Button>\n\n                    <Button\n                      icon={<ShareAltOutlined />}\n                      onClick={() => {\n                        navigator.clipboard.writeText(selectedTitle.title);\n                        message.success('书名已复制到剪贴板');\n                      }}\n                      block\n                    >\n                      复制书名\n                    </Button>\n                  </div>\n                </div>\n              </Col>\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default TitleManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;;;AAzCA;;;;;AA4CA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAEzB,MAAM,eAAyB;;IAC7B,MAAM,EACJ,cAAc,EACd,UAAU,EACV,YAAY,EACZ,eAAe,EACf,eAAe,EACf,mBAAmB,EACnB,aAAa,EACd,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACnE,MAAM,CAAC,cAAc,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAEpC,MAAM,gBAAgB,iBAAiB,cAAc,eAAe,EAAE,IAAI,EAAE;IAE5E,UAAU;IACV,MAAM,iBAAiB,cACpB,MAAM,CAAC,CAAA,QAAS,CAAC,mBAAmB,MAAM,UAAU,EACpD,IAAI,CAAC,CAAC,GAAG;QACR,OAAQ;YACN,KAAK;gBAAS,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YACtC,KAAK;gBAAW,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACtF,KAAK;gBAAQ,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,KAAK;YACjD;gBAAS,OAAO;QAClB;IACF;IAEF,UAAU;IACV,MAAM,iBAAiB,OAAO;QAC5B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;QAE1C,UAAU;QACV,MAAM,eAAe;YACnB;YAAW;YAAW;YAAU;YAChC;YAAY;YAAS;YAAW;YAChC;YAAW;YAAY;YAAW;SACnC;QAED,MAAM,kBAAkB,EAAE;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,QAAQ,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;YAC3E,MAAM,WAA0B;gBAC9B,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBACzC,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC/C,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC7C,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAC7C,eAAe,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;gBAChD,aAAa;oBACX;oBACA;oBACA;iBACD;YACH;YAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,CAAC,SAAS,MAAM,GAAG,SAAS,YAAY,GAAG,SAAS,UAAU,GAAG,SAAS,UAAU,GAAG,SAAS,aAAa,IAAI;YAE1I,gBAAgB,IAAI,CAAC;gBACnB,OAAO,AAAC,GAAU,OAAR,OAAiC,OAAzB,IAAI,IAAI,AAAC,IAAS,OAAN,IAAI,KAAM;gBACxC;gBACA;gBACA;gBACA;gBACA,YAAY;gBACZ,WAAW,IAAI;YACjB;QACF;QAEA,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,cAAc,cAAc;YACjD,MAAM,kBAAkB,MAAM,eAAe;YAE7C,IAAI,gBAAgB;gBAClB,gBAAgB,OAAO,CAAC,CAAA;oBACtB,aAAa,eAAe,EAAE,EAAE;gBAClC;gBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,AAAC,QAA8B,OAAvB,gBAAgB,MAAM,EAAC;YACjD;YAEA,sBAAsB;YACtB,cAAc,WAAW;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,gBAAgB;YAClB,oBAAoB,eAAe,EAAE,EAAE,MAAM,EAAE;YAC/C,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,MAAM,UAAU,GAAG,UAAU;QAC/C;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,SAAS,IAAI,qBAAO,6LAAC,yNAAA,CAAA,iBAAc;YAAC,WAAU;;;;;;QAClD,IAAI,SAAS,IAAI,qBAAO,6LAAC,qNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;QAChD,IAAI,SAAS,IAAI,qBAAO,6LAAC,qNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;QAChD,qBAAO,6LAAC,mNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;IAChC;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;;oCAAY;oCAAiB,eAAe,IAAI;;;;;;;;;;;;;kCAE7D,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;4BAC1B,SAAS,IAAM,sBAAsB;sCACtC;;;;;;;;;;;;;;;;;0BAML,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,qNAAA,CAAA,eAAY;;;;;gCAAG;gCACV,eAAe,MAAM;gCAAC;;;;;;;wBAGhC,wBACE,6LAAC;;8CAEC,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,+KAAA,CAAA,MAAG;wCAAC,QAAQ;wCAAI,OAAM;;0DACrB,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC,qLAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU;4DACV,+BAAiB,6LAAC,mNAAA,CAAA,cAAW;;;;;4DAC7B,iCAAmB,6LAAC,uNAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;0DAIvC,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC,qLAAA,CAAA,SAAM;4DACL,OAAO;4DACP,UAAU;4DACV,OAAO;gEAAE,OAAO;4DAAI;;8EAEpB,6LAAC;oEAAO,OAAM;8EAAQ;;;;;;8EACtB,6LAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,6LAAC;oEAAO,OAAM;8EAAO;;;;;;;;;;;;;;;;;;;;;;;0DAI3B,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mMAAA,CAAA,QAAK;kEACJ,cAAA,6LAAC;4DAAK,MAAK;;gEAAY;gEAClB,cAAc,MAAM;gEAAC;gEACpB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAS9D,eAAe,MAAM,KAAK,kBACzB,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,qNAAA,CAAA,eAAY;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DACR,cAAc,MAAM,KAAK,IAAI,YAAY;;;;;;8DAE5C,6LAAC;;;;;8DACD,6LAAC;oDAAK,MAAK;8DACR,cAAc,MAAM,KAAK,IAAI,mBAAmB;;;;;;;;;;;;;;;;;2DAKvD,6LAAC,iLAAA,CAAA,OAAI;oCACH,MAAM;wCAAE,QAAQ;wCAAI,QAAQ;oCAAE;oCAC9B,YAAY;oCACZ,YAAY,CAAC,sBACX,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;sDACR,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDACH,WAAU;gDACV,SAAS;oDACP,iBAAiB;oDACjB,qBAAqB;gDACvB;gDACA,SAAS;kEACP,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAO,MAAM,UAAU,GAAG,SAAS;kEAC1C,cAAA,6LAAC,qMAAA,CAAA,SAAM;4DACL,MAAK;4DACL,MAAM,MAAM,UAAU,iBAAG,6LAAC,mNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;uFAAoB,6LAAC,uNAAA,CAAA,gBAAa;;;;;4DAClF,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,qBAAqB;4DACvB;;;;;;uDAPoD;;;;;kEAUxD,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,mNAAA,CAAA,cAAW;4DAAC,SAAS,CAAC;gEACrB,EAAE,eAAe;gEACjB,iBAAiB;gEACjB,qBAAqB;4DACvB;;;;;;uDALwB;;;;;kEAO1B,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;4DAAC,SAAS,CAAC;gEAC1B,EAAE,eAAe;gEACjB,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,KAAK;gEACzC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;4DAClB;;;;;;uDALsB;;;;;iDAOzB;0DAED,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,OAAO;oEAAG,WAAU;8EACxB,MAAM,KAAK;;;;;;8EAEd,6LAAC;oEAAI,WAAU;;wEACZ,aAAa,MAAM,KAAK;sFACzB,6LAAC;4EAAK,MAAM;4EAAC,WAAU;sFAAW,MAAM,KAAK;;;;;;;;;;;;;;;;;;sEAIjD,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,yLAAA,CAAA,WAAQ;gEACP,SAAS,MAAM,KAAK;gEACpB,MAAK;gEACL,QAAQ,cAAc,MAAM,KAAK;gEACjC,UAAU;;;;;;;;;;;sEAId,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,mMAAA,CAAA,QAAK;;sFACJ,6LAAC,+KAAA,CAAA,MAAG;4EAAC,OAAM;sFAAQ,MAAM,KAAK;;;;;;sFAC9B,6LAAC,+KAAA,CAAA,MAAG;4EAAC,OAAM;sFAAS,MAAM,KAAK;;;;;;;;;;;;gEAEhC,MAAM,UAAU,kBACf,6LAAC,mNAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;;;;;;sEAI3B,6LAAC;4DAAI,WAAU;;gEAAwB;gEAChC,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUrE;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,qNAAA,CAAA,eAAY;;;;;gCAAG;;;;;;;wBAIpB,wBACE,6LAAC;sCACC,cAAA,6LAAC,+KAAA,CAAA,MAAG;gCAAC,QAAQ;oCAAC;oCAAI;iCAAG;;kDACnB,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,OAAM;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC,mLAAA,CAAA,QAAK;gEAAC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,IAAI,MAAM;gEAAE,QAAQ;;;;;;;;;;;;kEAEzE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC,mLAAA,CAAA,QAAK;gEAAC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,MAAM,EAAE,KAAK,GAAG,IAAI,MAAM;gEAAE,QAAQ;;;;;;;;;;;;kEAEzF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC,mLAAA,CAAA,QAAK;gEAAC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,GAAG,IAAI,MAAM;gEAAE,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAK9E,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,OAAM;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI,CAAC,IAAI,IAAI,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,GAAG,CAAC,CAAA,sBACxD,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;0EAAM;;;;;;0EACP,6LAAC,mLAAA,CAAA,QAAK;gEAAC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,OAAO,MAAM;gEAAE,QAAQ;;;;;;;uDAFnE;;;;;;;;;;;;;;;;;;;;kDAQlB,6LAAC,+KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CAAC,OAAM;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC,mLAAA,CAAA,QAAK;gEAAC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;gEAAE,QAAQ;;;;;;;;;;;;kEAExE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC,mLAAA,CAAA,QAAK;gEAAC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,MAAM;gEAAE,QAAQ;;;;;;;;;;;;kEAEzE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;0EAAK;;;;;;0EACN,6LAAC;;oEACE,cAAc,MAAM,GAAG,IACpB,KAAK,KAAK,CAAC,AAAC,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM,GAAG,cAAc,MAAM,GAAI,OACrF;oEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASxB;iBACD;;;;;;0BAIH,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,cAAc,WAAW;gBAC3B;gBACA,OAAO;gBACP,QAAO;gBACP,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAe,QAAO;;sCAChC,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;;8DAClB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;;;;;8CAI3B,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;;8DAClB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM7B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAW,OAAM;sCAC/B,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,MAAK;gCACL,aAAY;gCACZ,iBAAiB;oCAAC;iCAAI;;;;;;;;;;;sCAI1B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,cAAc;4BACd,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,KAAK;gCACL,KAAK;gCACL,OAAO;oCACL,GAAG;oCACH,GAAG;oCACH,IAAI;oCACJ,IAAI;gCACN;;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,AAAC,UAA8B,OAArB,0BAAA,oCAAA,cAAe,KAAK;gBACrC,MAAM;gBACN,UAAU,IAAM,qBAAqB;gBACrC,QAAQ;kCACN,6LAAC,qMAAA,CAAA,SAAM;wBAAa,SAAS,IAAM,qBAAqB;kCAAQ;uBAApD;;;;;iBAGb;gBACD,OAAO;0BAEN,+BACC,6LAAC;8BACC,cAAA,6LAAC,+KAAA,CAAA,MAAG;wBAAC,QAAQ;;0CACX,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,OAAO;8DAAI,cAAc,KAAK;;;;;;8DACrC,6LAAC,mMAAA,CAAA,QAAK;;sEACJ,6LAAC,+KAAA,CAAA,MAAG;4DAAC,OAAM;sEAAQ,cAAc,KAAK;;;;;;sEACtC,6LAAC,+KAAA,CAAA,MAAG;4DAAC,OAAM;sEAAS,cAAc,KAAK;;;;;;sEACvC,6LAAC;4DAAK,MAAK;;gEAAY;gEAChB,IAAI,KAAK,cAAc,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;sDAK/D,6LAAC,uLAAA,CAAA,UAAO;;;;;sDAER,6LAAC;;8DACC,6LAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,MAAM;;gFAAE,cAAc,QAAQ,CAAC,MAAM;gFAAC;;;;;;;;;;;;;8EAE9C,6LAAC,yLAAA,CAAA,WAAQ;oEAAC,SAAS,cAAc,QAAQ,CAAC,MAAM;oEAAE,MAAK;;;;;;;;;;;;sEAGzD,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,MAAM;;gFAAE,cAAc,QAAQ,CAAC,YAAY;gFAAC;;;;;;;;;;;;;8EAEpD,6LAAC,yLAAA,CAAA,WAAQ;oEAAC,SAAS,cAAc,QAAQ,CAAC,YAAY;oEAAE,MAAK;;;;;;;;;;;;sEAG/D,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,MAAM;;gFAAE,cAAc,QAAQ,CAAC,UAAU;gFAAC;;;;;;;;;;;;;8EAElD,6LAAC,yLAAA,CAAA,WAAQ;oEAAC,SAAS,cAAc,QAAQ,CAAC,UAAU;oEAAE,MAAK;;;;;;;;;;;;sEAG7D,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,MAAM;;gFAAE,cAAc,QAAQ,CAAC,UAAU;gFAAC;;;;;;;;;;;;;8EAElD,6LAAC,yLAAA,CAAA,WAAQ;oEAAC,SAAS,cAAc,QAAQ,CAAC,UAAU;oEAAE,MAAK;;;;;;;;;;;;sEAG7D,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;sFAAK;;;;;;sFACN,6LAAC;4EAAK,MAAM;;gFAAE,cAAc,QAAQ,CAAC,aAAa;gFAAC;;;;;;;;;;;;;8EAErD,6LAAC,yLAAA,CAAA,WAAQ;oEAAC,SAAS,cAAc,QAAQ,CAAC,aAAa;oEAAE,MAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAKpE,6LAAC,uLAAA,CAAA,UAAO;;;;;sDAER,6LAAC;;8DACC,6LAAC;oDAAM,OAAO;8DAAG;;;;;;8DACjB,6LAAC,iLAAA,CAAA,OAAI;oDACH,MAAK;oDACL,YAAY,cAAc,QAAQ,CAAC,WAAW;oDAC9C,YAAY,CAAC,YAAY,sBACvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;sEACR,cAAA,6LAAC;;oEAAM,QAAQ;oEAAE;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhC,6LAAC,+KAAA,CAAA,MAAG;gCAAC,MAAM;0CACT,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,cAAc,KAAK;;;;;;8DAEtB,6LAAC;oDAAK,MAAK;8DAAY;;;;;;;;;;;;sDAGzB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yLAAA,CAAA,WAAQ;gDACP,MAAK;gDACL,SAAS,cAAc,KAAK;gDAC5B,QAAQ,cAAc,cAAc,KAAK;gDACzC,OAAO;;;;;;;;;;;sDAIX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,SAAM;oDACL,MAAM,cAAc,UAAU,GAAG,YAAY;oDAC7C,MAAM,cAAc,UAAU,iBAAG,6LAAC,mNAAA,CAAA,cAAW;;;;+EAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;oDACjE,SAAS,IAAM,qBAAqB;oDACpC,KAAK;8DAEJ,cAAc,UAAU,GAAG,SAAS;;;;;;8DAGvC,6LAAC,qMAAA,CAAA,SAAM;oDACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oDACvB,SAAS;wDACP,UAAU,SAAS,CAAC,SAAS,CAAC,cAAc,KAAK;wDACjD,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;oDAClB;oDACA,KAAK;8DACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;GArkBM;;QASA,wHAAA,CAAA,cAAW;QAQS,iLAAA,CAAA,OAAI,CAAC;;;KAjBzB;uCAukBS", "debugId": null}}, {"offset": {"line": 10954, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/document/DocumentManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Tree,\n  Tabs,\n  List,\n  Tag,\n  message,\n  Row,\n  Col,\n  Progress,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  FolderOutlined,\n  FileOutlined,\n  DownloadOutlined,\n  UploadOutlined,\n  SearchOutlined,\n  HistoryOutlined,\n  FileTextOutlined,\n  FilePdfOutlined,\n  FileWordOutlined,\n  FileExcelOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\n\nconst { Title, Text } = Typography;\n\nconst DocumentManager: React.FC = () => {\n  const { currentProject } = useAppStore();\n  const [activeTab, setActiveTab] = useState('structure');\n\n  // 模拟文档结构\n  const documentStructure = [\n    {\n      title: 'workflow-configs',\n      key: 'workflow-configs',\n      icon: <FolderOutlined />,\n      children: [\n        { title: 'main-workflow.json', key: 'main-workflow', icon: <FileOutlined /> },\n        { title: 'backup-workflow.json', key: 'backup-workflow', icon: <FileOutlined /> },\n      ]\n    },\n    {\n      title: 'prompts',\n      key: 'prompts',\n      icon: <FolderOutlined />,\n      children: [\n        { title: 'character-prompts.md', key: 'character-prompts', icon: <FileTextOutlined /> },\n        { title: 'plot-prompts.md', key: 'plot-prompts', icon: <FileTextOutlined /> },\n      ]\n    },\n    {\n      title: 'generated-content',\n      key: 'generated-content',\n      icon: <FolderOutlined />,\n      children: [\n        {\n          title: 'outlines',\n          key: 'outlines',\n          icon: <FolderOutlined />,\n          children: [\n            { title: 'main-outline.md', key: 'main-outline', icon: <FileTextOutlined /> },\n            { title: 'detailed-outline.md', key: 'detailed-outline', icon: <FileTextOutlined /> },\n          ]\n        },\n        {\n          title: 'characters',\n          key: 'characters',\n          icon: <FolderOutlined />,\n          children: [\n            { title: 'protagonist.json', key: 'protagonist', icon: <FileOutlined /> },\n            { title: 'supporting-characters.json', key: 'supporting', icon: <FileOutlined /> },\n          ]\n        },\n        {\n          title: 'chapters',\n          key: 'chapters',\n          icon: <FolderOutlined />,\n          children: [\n            { title: 'chapter-01.md', key: 'chapter-01', icon: <FileTextOutlined /> },\n            { title: 'chapter-02.md', key: 'chapter-02', icon: <FileTextOutlined /> },\n            { title: 'chapter-03.md', key: 'chapter-03', icon: <FileTextOutlined /> },\n          ]\n        }\n      ]\n    },\n    {\n      title: 'exports',\n      key: 'exports',\n      icon: <FolderOutlined />,\n      children: [\n        { title: 'novel-draft.docx', key: 'novel-docx', icon: <FileWordOutlined /> },\n        { title: 'novel-draft.pdf', key: 'novel-pdf', icon: <FilePdfOutlined /> },\n      ]\n    }\n  ];\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理文档。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>文档管理</Title>\n          <Text type=\"secondary\">管理项目文档和文件 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button icon={<UploadOutlined />}>\n            导入文档\n          </Button>\n          <Button type=\"primary\" icon={<DownloadOutlined />}>\n            导出项目\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'structure',\n            label: (\n              <span>\n                <FolderOutlined />\n                文档结构\n              </span>\n            ),\n            children: (\n              <Row gutter={16}>\n                <Col span={8}>\n                  <Card title=\"项目文档树\">\n                    <Tree\n                      showIcon\n                      defaultExpandAll\n                      treeData={documentStructure}\n                      onSelect={(keys, info) => {\n                        if (keys.length > 0) {\n                          message.info(`选中文件: ${info.node.title}`);\n                        }\n                      }}\n                    />\n                  </Card>\n                </Col>\n                <Col span={16}>\n                  <Card title=\"文档预览\">\n                    <div className=\"text-center py-12\">\n                      <FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                      <div className=\"mt-4\">\n                        <Text type=\"secondary\">选择左侧文档查看内容</Text>\n                        <br />\n                        <Text type=\"secondary\">支持Markdown、JSON、文本文件预览</Text>\n                      </div>\n                    </div>\n                  </Card>\n                </Col>\n              </Row>\n            ),\n          },\n          {\n            key: 'versions',\n            label: (\n              <span>\n                <HistoryOutlined />\n                版本历史\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <HistoryOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">版本控制功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持文档版本管理和历史对比</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n    </div>\n  );\n};\n\nexport default DocumentManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AA/BA;;;;;AAiCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAElC,MAAM,kBAA4B;;IAChC,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,MAAM,oBAAoB;QACxB;YACE,OAAO;YACP,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAsB,KAAK;oBAAiB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBAAI;gBAC5E;oBAAE,OAAO;oBAAwB,KAAK;oBAAmB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBAAI;aACjF;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAwB,KAAK;oBAAqB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBAAI;gBACtF;oBAAE,OAAO;oBAAmB,KAAK;oBAAgB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBAAI;aAC7E;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBACE,OAAO;oBACP,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,UAAU;wBACR;4BAAE,OAAO;4BAAmB,KAAK;4BAAgB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;wBAC5E;4BAAE,OAAO;4BAAuB,KAAK;4BAAoB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;qBACrF;gBACH;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,UAAU;wBACR;4BAAE,OAAO;4BAAoB,KAAK;4BAAe,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBAAI;wBACxE;4BAAE,OAAO;4BAA8B,KAAK;4BAAc,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBAAI;qBAClF;gBACH;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,UAAU;wBACR;4BAAE,OAAO;4BAAiB,KAAK;4BAAc,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;wBACxE;4BAAE,OAAO;4BAAiB,KAAK;4BAAc,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;wBACxE;4BAAE,OAAO;4BAAiB,KAAK;4BAAc,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;qBACzE;gBACH;aACD;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAoB,KAAK;oBAAc,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBAAI;gBAC3E;oBAAE,OAAO;oBAAmB,KAAK;oBAAa,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;gBAAI;aACzE;QACH;KACD;IAED,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;;oCAAY;oCAAiB,eAAe,IAAI;;;;;;;;;;;;;kCAE7D,6LAAC,mMAAA,CAAA,QAAK;;0CACJ,6LAAC,qMAAA,CAAA,SAAM;gCAAC,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;0CAAK;;;;;;0CAGlC,6LAAC,qMAAA,CAAA,SAAM;gCAAC,MAAK;gCAAU,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;0CAAK;;;;;;;;;;;;;;;;;;0BAMvD,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCAAG;;;;;;;wBAItB,wBACE,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;wCAAC,OAAM;kDACV,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CACH,QAAQ;4CACR,gBAAgB;4CAChB,UAAU;4CACV,UAAU,CAAC,MAAM;gDACf,IAAI,KAAK,MAAM,GAAG,GAAG;oDACnB,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,AAAC,SAAwB,OAAhB,KAAK,IAAI,CAAC,KAAK;gDACvC;4CACF;;;;;;;;;;;;;;;;8CAIN,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;wCAAC,OAAM;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6NAAA,CAAA,mBAAgB;oDAAC,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAU;;;;;;8DAC1D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,MAAK;sEAAY;;;;;;sEACvB,6LAAC;;;;;sEACD,6LAAC;4DAAK,MAAK;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOrC;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,2NAAA,CAAA,kBAAe;;;;;gCAAG;;;;;;;wBAIvB,wBACE,6LAAC,iLAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,2NAAA,CAAA,kBAAe;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;;;;;;;AAIT;GApKM;;QACuB,wHAAA,CAAA,cAAW;;;KADlC;uCAsKS", "debugId": null}}, {"offset": {"line": 11503, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/prompt/PromptManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Tabs,\n  List,\n  Tag,\n  Rate,\n  message,\n  Row,\n  Col,\n  Divider,\n  Badge,\n  Progress\n} from 'antd';\nimport {\n  FileTextOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  CopyOutlined,\n  StarOutlined,\n  ExperimentOutlined,\n  Bar<PERSON>hartOutlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { PromptTemplate } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst PromptManager: React.FC = () => {\n  const {\n    promptTemplates,\n    addPromptTemplate,\n    updatePromptTemplate,\n    deletePromptTemplate,\n    getPromptTemplatesByCategory\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);\n  const [activeTab, setActiveTab] = useState('templates');\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [form] = Form.useForm();\n\n  // 提示词分类\n  const categories = [\n    { key: 'character', label: '角色生成', color: 'blue' },\n    { key: 'plot', label: '情节规划', color: 'green' },\n    { key: 'dialogue', label: '对话生成', color: 'orange' },\n    { key: 'description', label: '场景描述', color: 'purple' },\n    { key: 'title', label: '书名生成', color: 'red' },\n    { key: 'outline', label: '大纲规划', color: 'cyan' },\n    { key: 'worldbuilding', label: '世界观构建', color: 'gold' },\n    { key: 'polish', label: '内容润色', color: 'lime' },\n  ];\n\n  // 模拟提示词模板数据\n  const sampleTemplates: PromptTemplate[] = [\n    {\n      id: '1',\n      name: '主角角色生成',\n      category: 'character',\n      template: '请为一部{genre}小说创建一个{age}岁的{gender}主角。角色应该具有{personality}的性格特征，背景设定为{background}。请详细描述角色的外貌、性格、能力和成长经历。',\n      variables: [\n        { name: 'genre', type: 'string', description: '小说类型', defaultValue: '现代都市', required: true },\n        { name: 'age', type: 'number', description: '角色年龄', defaultValue: 25, required: true },\n        { name: 'gender', type: 'string', description: '角色性别', defaultValue: '男', required: true },\n        { name: 'personality', type: 'string', description: '性格特征', defaultValue: '勇敢、善良', required: true },\n        { name: 'background', type: 'string', description: '背景设定', defaultValue: '普通家庭', required: false },\n      ],\n      version: 1,\n      isActive: true,\n      performance: {\n        usage: 156,\n        rating: 4.5,\n        feedback: ['生成的角色很有特色', '背景设定合理', '可以增加更多细节'],\n      }\n    },\n    {\n      id: '2',\n      name: '情节冲突生成',\n      category: 'plot',\n      template: '为{genre}类型的小说设计一个{conflict_type}冲突。冲突应该涉及{characters}，发生在{setting}环境中。请描述冲突的起因、发展过程和可能的解决方案。',\n      variables: [\n        { name: 'genre', type: 'string', description: '小说类型', required: true },\n        { name: 'conflict_type', type: 'string', description: '冲突类型', required: true },\n        { name: 'characters', type: 'array', description: '涉及角色', required: true },\n        { name: 'setting', type: 'string', description: '场景设定', required: true },\n      ],\n      version: 2,\n      isActive: true,\n      performance: {\n        usage: 89,\n        rating: 4.2,\n        feedback: ['冲突设计有趣', '逻辑性强'],\n      }\n    }\n  ];\n\n  const filteredTemplates = selectedCategory === 'all'\n    ? sampleTemplates\n    : sampleTemplates.filter(template => template.category === selectedCategory);\n\n  const handleCreateTemplate = () => {\n    setEditingTemplate(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEditTemplate = (template: PromptTemplate) => {\n    setEditingTemplate(template);\n    form.setFieldsValue({\n      name: template.name,\n      category: template.category,\n      template: template.template,\n    });\n    setIsModalVisible(true);\n  };\n\n  const getCategoryConfig = (category: string) => {\n    return categories.find(cat => cat.key === category) || categories[0];\n  };\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>提示词管理</Title>\n          <Text type=\"secondary\">管理AI提示词模板和配置</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={handleCreateTemplate}\n          >\n            创建模板\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'templates',\n            label: (\n              <span>\n                <FileTextOutlined />\n                提示词模板 ({filteredTemplates.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {/* 分类过滤器 */}\n                <Card className=\"mb-6\">\n                  <Row gutter={16}>\n                    <Col span={3}>\n                      <Card\n                        size=\"small\"\n                        className={`cursor-pointer transition-all ${\n                          selectedCategory === 'all' ? 'ring-2 ring-blue-500' : ''\n                        }`}\n                        onClick={() => setSelectedCategory('all')}\n                      >\n                        <div className=\"text-center\">\n                          <FileTextOutlined className=\"text-2xl text-gray-500\" />\n                          <div className=\"mt-2\">\n                            <Text strong>全部</Text>\n                            <div className=\"text-xs text-gray-500\">\n                              {sampleTemplates.length} 个模板\n                            </div>\n                          </div>\n                        </div>\n                      </Card>\n                    </Col>\n                    {categories.map((category) => {\n                      const count = sampleTemplates.filter(t => t.category === category.key).length;\n                      return (\n                        <Col span={3} key={category.key}>\n                          <Card\n                            size=\"small\"\n                            className={`cursor-pointer transition-all ${\n                              selectedCategory === category.key ? 'ring-2 ring-blue-500' : ''\n                            }`}\n                            onClick={() => setSelectedCategory(category.key)}\n                          >\n                            <div className=\"text-center\">\n                              <FileTextOutlined className={`text-2xl text-${category.color}-500`} />\n                              <div className=\"mt-2\">\n                                <Text strong>{category.label}</Text>\n                                <div className=\"text-xs text-gray-500\">\n                                  {count} 个模板\n                                </div>\n                              </div>\n                            </div>\n                          </Card>\n                        </Col>\n                      );\n                    })}\n                  </Row>\n                </Card>\n\n                {/* 模板列表 */}\n                <List\n                  grid={{ gutter: 16, column: 1 }}\n                  dataSource={filteredTemplates}\n                  renderItem={(template) => {\n                    const categoryConfig = getCategoryConfig(template.category);\n                    return (\n                      <List.Item>\n                        <Card\n                          className=\"hover:shadow-lg transition-shadow\"\n                          actions={[\n                            <Button\n                              key=\"copy\"\n                              type=\"text\"\n                              icon={<CopyOutlined />}\n                              onClick={() => {\n                                navigator.clipboard.writeText(template.template);\n                                message.success('模板已复制到剪贴板');\n                              }}\n                            >\n                              复制\n                            </Button>,\n                            <Button\n                              key=\"edit\"\n                              type=\"text\"\n                              icon={<EditOutlined />}\n                              onClick={() => handleEditTemplate(template)}\n                            >\n                              编辑\n                            </Button>,\n                            <Button\n                              key=\"delete\"\n                              type=\"text\"\n                              danger\n                              icon={<DeleteOutlined />}\n                              onClick={() => {\n                                message.info('删除功能开发中');\n                              }}\n                            >\n                              删除\n                            </Button>\n                          ]}\n                        >\n                          <Row gutter={24}>\n                            <Col span={16}>\n                              <div className=\"mb-3\">\n                                <div className=\"flex items-center justify-between mb-2\">\n                                  <Title level={5} className=\"mb-0\">{template.name}</Title>\n                                  <Space>\n                                    <Tag color={categoryConfig.color}>\n                                      {categoryConfig.label}\n                                    </Tag>\n                                    <Tag color={template.isActive ? 'green' : 'default'}>\n                                      {template.isActive ? '启用' : '禁用'}\n                                    </Tag>\n                                    <Text type=\"secondary\">v{template.version}</Text>\n                                  </Space>\n                                </div>\n\n                                <Paragraph\n                                  ellipsis={{ rows: 2 }}\n                                  type=\"secondary\"\n                                  className=\"mb-3\"\n                                >\n                                  {template.template}\n                                </Paragraph>\n\n                                <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                                  <span>变量: {template.variables.length} 个</span>\n                                  <span>使用: {template.performance.usage} 次</span>\n                                  <div className=\"flex items-center space-x-1\">\n                                    <Rate\n                                      disabled\n                                      value={template.performance.rating}\n                                      allowHalf\n                                      style={{ fontSize: 12 }}\n                                    />\n                                    <Text type=\"secondary\">({template.performance.rating})</Text>\n                                  </div>\n                                </div>\n                              </div>\n                            </Col>\n                            <Col span={8}>\n                              <div className=\"text-center\">\n                                <div className=\"mb-2\">\n                                  <Text strong className=\"text-lg\">{template.performance.rating}</Text>\n                                  <Text type=\"secondary\" className=\"ml-1\">/5.0</Text>\n                                </div>\n                                <Progress\n                                  percent={template.performance.rating * 20}\n                                  size=\"small\"\n                                  showInfo={false}\n                                />\n                                <div className=\"mt-2\">\n                                  <Badge count={template.performance.usage} showZero>\n                                    <Text type=\"secondary\" className=\"text-sm\">使用次数</Text>\n                                  </Badge>\n                                </div>\n                              </div>\n                            </Col>\n                          </Row>\n                        </Card>\n                      </List.Item>\n                    );\n                  }}\n                />\n              </div>\n            ),\n          },\n          {\n            key: 'analytics',\n            label: (\n              <span>\n                <BarChartOutlined />\n                性能分析\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <BarChartOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">性能分析功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持模板使用统计和效果分析</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n\n      {/* 创建/编辑模板模态框 */}\n      <Modal\n        title={editingTemplate ? '编辑提示词模板' : '创建提示词模板'}\n        open={isModalVisible}\n        onOk={async () => {\n          try {\n            const values = await form.validateFields();\n            message.success(editingTemplate ? '模板已更新' : '模板已创建');\n            setIsModalVisible(false);\n            form.resetFields();\n          } catch (error) {\n            console.error('表单验证失败:', error);\n          }\n        }}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n        }}\n        width={800}\n        okText={editingTemplate ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"name\"\n                label=\"模板名称\"\n                rules={[{ required: true, message: '请输入模板名称' }]}\n              >\n                <Input placeholder=\"请输入模板名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"category\"\n                label=\"分类\"\n                rules={[{ required: true, message: '请选择分类' }]}\n              >\n                <Select placeholder=\"请选择分类\">\n                  {categories.map((category) => (\n                    <Option key={category.key} value={category.key}>\n                      {category.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"template\"\n            label=\"提示词模板\"\n            rules={[{ required: true, message: '请输入提示词模板' }]}\n            help=\"使用 {变量名} 的格式定义变量，如 {genre}、{character_name} 等\"\n          >\n            <TextArea\n              rows={8}\n              placeholder=\"请输入提示词模板，使用 {变量名} 定义可替换的变量...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default PromptManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAlCA;;;;;AAqCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAE1B,MAAM,gBAA0B;;IAC9B,MAAM,EACJ,eAAe,EACf,iBAAiB,EACjB,oBAAoB,EACpB,oBAAoB,EACpB,4BAA4B,EAC7B,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,QAAQ;IACR,MAAM,aAAa;QACjB;YAAE,KAAK;YAAa,OAAO;YAAQ,OAAO;QAAO;QACjD;YAAE,KAAK;YAAQ,OAAO;YAAQ,OAAO;QAAQ;QAC7C;YAAE,KAAK;YAAY,OAAO;YAAQ,OAAO;QAAS;QAClD;YAAE,KAAK;YAAe,OAAO;YAAQ,OAAO;QAAS;QACrD;YAAE,KAAK;YAAS,OAAO;YAAQ,OAAO;QAAM;QAC5C;YAAE,KAAK;YAAW,OAAO;YAAQ,OAAO;QAAO;QAC/C;YAAE,KAAK;YAAiB,OAAO;YAAS,OAAO;QAAO;QACtD;YAAE,KAAK;YAAU,OAAO;YAAQ,OAAO;QAAO;KAC/C;IAED,YAAY;IACZ,MAAM,kBAAoC;QACxC;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;gBACT;oBAAE,MAAM;oBAAS,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAQ,UAAU;gBAAK;gBAC3F;oBAAE,MAAM;oBAAO,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAI,UAAU;gBAAK;gBACrF;oBAAE,MAAM;oBAAU,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAK,UAAU;gBAAK;gBACzF;oBAAE,MAAM;oBAAe,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAS,UAAU;gBAAK;gBAClG;oBAAE,MAAM;oBAAc,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAQ,UAAU;gBAAM;aAClG;YACD,SAAS;YACT,UAAU;YACV,aAAa;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;oBAAC;oBAAa;oBAAU;iBAAW;YAC/C;QACF;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;gBACT;oBAAE,MAAM;oBAAS,MAAM;oBAAU,aAAa;oBAAQ,UAAU;gBAAK;gBACrE;oBAAE,MAAM;oBAAiB,MAAM;oBAAU,aAAa;oBAAQ,UAAU;gBAAK;gBAC7E;oBAAE,MAAM;oBAAc,MAAM;oBAAS,aAAa;oBAAQ,UAAU;gBAAK;gBACzE;oBAAE,MAAM;oBAAW,MAAM;oBAAU,aAAa;oBAAQ,UAAU;gBAAK;aACxE;YACD,SAAS;YACT,UAAU;YACV,aAAa;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;oBAAC;oBAAU;iBAAO;YAC9B;QACF;KACD;IAED,MAAM,oBAAoB,qBAAqB,QAC3C,kBACA,gBAAgB,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;IAE7D,MAAM,uBAAuB;QAC3B,mBAAmB;QACnB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,KAAK,cAAc,CAAC;YAClB,MAAM,SAAS,IAAI;YACnB,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ;QAC7B;QACA,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,aAAa,UAAU,CAAC,EAAE;IACtE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;kCAEzB,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS;sCACV;;;;;;;;;;;;;;;;;0BAML,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gCAAG;gCACZ,kBAAkB,MAAM;gCAAC;;;;;;;wBAGrC,wBACE,6LAAC;;8CAEC,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,+KAAA,CAAA,MAAG;wCAAC,QAAQ;;0DACX,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAW,AAAC,iCAEX,OADC,qBAAqB,QAAQ,yBAAyB;oDAExD,SAAS,IAAM,oBAAoB;8DAEnC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6NAAA,CAAA,mBAAgB;gEAAC,WAAU;;;;;;0EAC5B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAM;kFAAC;;;;;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,gBAAgB,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAMjC,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,GAAG,EAAE,MAAM;gDAC7E,qBACE,6LAAC,+KAAA,CAAA,MAAG;oDAAC,MAAM;8DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAW,AAAC,iCAEX,OADC,qBAAqB,SAAS,GAAG,GAAG,yBAAyB;wDAE/D,SAAS,IAAM,oBAAoB,SAAS,GAAG;kEAE/C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6NAAA,CAAA,mBAAgB;oEAAC,WAAW,AAAC,iBAA+B,OAAf,SAAS,KAAK,EAAC;;;;;;8EAC7D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,MAAM;sFAAE,SAAS,KAAK;;;;;;sFAC5B,6LAAC;4EAAI,WAAU;;gFACZ;gFAAM;;;;;;;;;;;;;;;;;;;;;;;;mDAbE,SAAS,GAAG;;;;;4CAoBnC;;;;;;;;;;;;8CAKJ,6LAAC,iLAAA,CAAA,OAAI;oCACH,MAAM;wCAAE,QAAQ;wCAAI,QAAQ;oCAAE;oCAC9B,YAAY;oCACZ,YAAY,CAAC;wCACX,MAAM,iBAAiB,kBAAkB,SAAS,QAAQ;wCAC1D,qBACE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;sDACR,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDACH,WAAU;gDACV,SAAS;kEACP,6LAAC,qMAAA,CAAA,SAAM;wDAEL,MAAK;wDACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wDACnB,SAAS;4DACP,UAAU,SAAS,CAAC,SAAS,CAAC,SAAS,QAAQ;4DAC/C,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;wDAClB;kEACD;uDAPK;;;;;kEAUN,6LAAC,qMAAA,CAAA,SAAM;wDAEL,MAAK;wDACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wDACnB,SAAS,IAAM,mBAAmB;kEACnC;uDAJK;;;;;kEAON,6LAAC,qMAAA,CAAA,SAAM;wDAEL,MAAK;wDACL,MAAM;wDACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wDACrB,SAAS;4DACP,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;wDACf;kEACD;uDAPK;;;;;iDAUP;0DAED,cAAA,6LAAC,+KAAA,CAAA,MAAG;oDAAC,QAAQ;;sEACX,6LAAC,+KAAA,CAAA,MAAG;4DAAC,MAAM;sEACT,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAM,OAAO;gFAAG,WAAU;0FAAQ,SAAS,IAAI;;;;;;0FAChD,6LAAC,mMAAA,CAAA,QAAK;;kGACJ,6LAAC,+KAAA,CAAA,MAAG;wFAAC,OAAO,eAAe,KAAK;kGAC7B,eAAe,KAAK;;;;;;kGAEvB,6LAAC,+KAAA,CAAA,MAAG;wFAAC,OAAO,SAAS,QAAQ,GAAG,UAAU;kGACvC,SAAS,QAAQ,GAAG,OAAO;;;;;;kGAE9B,6LAAC;wFAAK,MAAK;;4FAAY;4FAAE,SAAS,OAAO;;;;;;;;;;;;;;;;;;;kFAI7C,6LAAC;wEACC,UAAU;4EAAE,MAAM;wEAAE;wEACpB,MAAK;wEACL,WAAU;kFAET,SAAS,QAAQ;;;;;;kFAGpB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAK;oFAAK,SAAS,SAAS,CAAC,MAAM;oFAAC;;;;;;;0FACrC,6LAAC;;oFAAK;oFAAK,SAAS,WAAW,CAAC,KAAK;oFAAC;;;;;;;0FACtC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,iLAAA,CAAA,OAAI;wFACH,QAAQ;wFACR,OAAO,SAAS,WAAW,CAAC,MAAM;wFAClC,SAAS;wFACT,OAAO;4FAAE,UAAU;wFAAG;;;;;;kGAExB,6LAAC;wFAAK,MAAK;;4FAAY;4FAAE,SAAS,WAAW,CAAC,MAAM;4FAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAK7D,6LAAC,+KAAA,CAAA,MAAG;4DAAC,MAAM;sEACT,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,MAAM;gFAAC,WAAU;0FAAW,SAAS,WAAW,CAAC,MAAM;;;;;;0FAC7D,6LAAC;gFAAK,MAAK;gFAAY,WAAU;0FAAO;;;;;;;;;;;;kFAE1C,6LAAC,yLAAA,CAAA,WAAQ;wEACP,SAAS,SAAS,WAAW,CAAC,MAAM,GAAG;wEACvC,MAAK;wEACL,UAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;4EAAC,OAAO,SAAS,WAAW,CAAC,KAAK;4EAAE,QAAQ;sFAChD,cAAA,6LAAC;gFAAK,MAAK;gFAAY,WAAU;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAS7D;;;;;;;;;;;;oBAIR;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gCAAG;;;;;;;wBAIxB,wBACE,6LAAC,iLAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,6NAAA,CAAA,mBAAgB;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CAC1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;0BAIH,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,kBAAkB,YAAY;gBACrC,MAAM;gBACN,MAAM;oBACJ,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBACxC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,kBAAkB,UAAU;wBAC5C,kBAAkB;wBAClB,KAAK,WAAW;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B;gBACF;gBACA,UAAU;oBACR,kBAAkB;oBAClB,KAAK,WAAW;gBAClB;gBACA,OAAO;gBACP,QAAQ,kBAAkB,OAAO;gBACjC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;yCAAE;kDAE7C,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;sDACjB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oDAA0B,OAAO,SAAS,GAAG;8DAC3C,SAAS,KAAK;mDADJ,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;sCASnC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAW;6BAAE;4BAChD,MAAK;sCAEL,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GAnXM;;QAOA,wHAAA,CAAA,cAAW;QAMA,iLAAA,CAAA,OAAI,CAAC;;;KAbhB;uCAqXS", "debugId": null}}, {"offset": {"line": 12472, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport WorkflowEditor from '@/components/workflow/WorkflowEditor';\nimport ProjectOverview from '@/components/project/ProjectOverview';\nimport OutlineManager from '@/components/outline/OutlineManager';\nimport CharacterManager from '@/components/character/CharacterManager';\nimport WorldBuildingManager from '@/components/worldbuilding/WorldBuildingManager';\nimport PlotLineManager from '@/components/plotline/PlotLineManager';\nimport TitleManager from '@/components/title/TitleManager';\nimport DocumentManager from '@/components/document/DocumentManager';\nimport PromptManager from '@/components/prompt/PromptManager';\nimport { useAppStore } from '@/store';\n\nexport default function Home() {\n  const { ui } = useAppStore();\n\n  const renderContent = () => {\n    switch (ui.activeTab) {\n      case 'workflow':\n        return <WorkflowEditor />;\n      case 'projects':\n        return <ProjectOverview />;\n      case 'outlines':\n        return <OutlineManager />;\n      case 'characters':\n        return <CharacterManager />;\n      case 'worldbuilding':\n        return <WorldBuildingManager />;\n      case 'plotlines':\n        return <PlotLineManager />;\n      case 'titles':\n        return <TitleManager />;\n      case 'documents':\n        return <DocumentManager />;\n      case 'prompts':\n        return <PromptManager />;\n      default:\n        return <WorkflowEditor />;\n    }\n  };\n\n  return (\n    <MainLayout>\n      {renderContent()}\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;AAee,SAAS;;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEzB,MAAM,gBAAgB;QACpB,OAAQ,GAAG,SAAS;YAClB,KAAK;gBACH,qBAAO,6LAAC,mJAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,mJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,kJAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,sJAAA,CAAA,UAAgB;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,8JAAA,CAAA,UAAoB;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,oJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,UAAY;;;;;YACtB,KAAK;gBACH,qBAAO,6LAAC,oJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,gJAAA,CAAA,UAAa;;;;;YACvB;gBACE,qBAAO,6LAAC,mJAAA,CAAA,UAAc;;;;;QAC1B;IACF;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;kBACR;;;;;;AAGP;GAjCwB;;QACP,wHAAA,CAAA,cAAW;;;KADJ", "debugId": null}}]}