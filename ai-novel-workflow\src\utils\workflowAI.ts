// AI工作流生成器
import { WorkflowNode } from '@/types';

// 工作流模板定义
interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  nodes: Array<{
    type: string;
    label: string;
    position: { x: number; y: number };
    config?: Record<string, any>;
  }>;
  connections: Array<{
    source: number;
    target: number;
  }>;
  tags: string[];
  complexity: 'simple' | 'medium' | 'complex';
}

// 预定义工作流模板
const WORKFLOW_TEMPLATES: WorkflowTemplate[] = [
  {
    id: 'quick-novel',
    name: '快速小说生成',
    description: '适合新手的简单工作流，快速生成短篇小说',
    nodes: [
      { type: 'input', label: '创作输入', position: { x: 100, y: 100 } },
      { type: 'title-generator', label: '生成书名', position: { x: 100, y: 250 } },
      { type: 'character-creator', label: '创建角色', position: { x: 350, y: 250 } },
      { type: 'outline-generator', label: '生成大纲', position: { x: 225, y: 400 } },
      { type: 'chapter-generator', label: '生成章节', position: { x: 225, y: 550 } },
      { type: 'output', label: '完成输出', position: { x: 225, y: 700 } }
    ],
    connections: [
      { source: 0, target: 1 },
      { source: 0, target: 2 },
      { source: 1, target: 3 },
      { source: 2, target: 3 },
      { source: 3, target: 4 },
      { source: 4, target: 5 }
    ],
    tags: ['简单', '快速', '新手'],
    complexity: 'simple'
  },
  {
    id: 'professional-novel',
    name: '专业小说创作',
    description: '完整的专业级工作流，包含详细的世界观和角色设定',
    nodes: [
      { type: 'input', label: '创作参数', position: { x: 100, y: 50 } },
      { type: 'title-generator', label: '书名生成', position: { x: 50, y: 200 } },
      { type: 'detail-generator', label: '详情生成', position: { x: 200, y: 200 } },
      { type: 'character-creator', label: '角色创建', position: { x: 350, y: 200 } },
      { type: 'worldbuilding', label: '世界观构建', position: { x: 500, y: 200 } },
      { type: 'plotline-planner', label: '主线规划', position: { x: 150, y: 350 } },
      { type: 'outline-generator', label: '大纲生成', position: { x: 350, y: 350 } },
      { type: 'detailed-outline', label: '详细大纲', position: { x: 250, y: 500 } },
      { type: 'chapter-generator', label: '章节生成', position: { x: 250, y: 650 } },
      { type: 'content-polisher', label: '内容润色', position: { x: 100, y: 800 } },
      { type: 'consistency-checker', label: '一致性检查', position: { x: 400, y: 800 } },
      { type: 'output', label: '最终输出', position: { x: 250, y: 950 } }
    ],
    connections: [
      { source: 0, target: 1 }, { source: 0, target: 2 }, { source: 0, target: 3 }, { source: 0, target: 4 },
      { source: 1, target: 5 }, { source: 2, target: 5 }, { source: 3, target: 5 }, { source: 4, target: 5 },
      { source: 3, target: 6 }, { source: 4, target: 6 }, { source: 5, target: 6 },
      { source: 6, target: 7 }, { source: 7, target: 8 }, { source: 8, target: 9 }, { source: 8, target: 10 },
      { source: 9, target: 11 }, { source: 10, target: 11 }
    ],
    tags: ['专业', '完整', '高质量'],
    complexity: 'complex'
  },
  {
    id: 'fantasy-novel',
    name: '奇幻小说专用',
    description: '专为奇幻类小说设计的工作流，重点关注世界观和魔法体系',
    nodes: [
      { type: 'input', label: '奇幻设定', position: { x: 100, y: 100 } },
      { type: 'worldbuilding', label: '魔法世界', position: { x: 100, y: 250 } },
      { type: 'character-creator', label: '角色种族', position: { x: 300, y: 250 } },
      { type: 'plotline-planner', label: '冒险主线', position: { x: 200, y: 400 } },
      { type: 'outline-generator', label: '冒险大纲', position: { x: 200, y: 550 } },
      { type: 'chapter-generator', label: '章节创作', position: { x: 200, y: 700 } },
      { type: 'consistency-checker', label: '设定检查', position: { x: 200, y: 850 } },
      { type: 'output', label: '奇幻小说', position: { x: 200, y: 1000 } }
    ],
    connections: [
      { source: 0, target: 1 }, { source: 0, target: 2 },
      { source: 1, target: 3 }, { source: 2, target: 3 },
      { source: 3, target: 4 }, { source: 4, target: 5 },
      { source: 5, target: 6 }, { source: 6, target: 7 }
    ],
    tags: ['奇幻', '魔法', '冒险'],
    complexity: 'medium'
  },
  {
    id: 'romance-novel',
    name: '言情小说工作流',
    description: '专注于情感描写和角色关系的言情小说创作流程',
    nodes: [
      { type: 'input', label: '言情设定', position: { x: 100, y: 100 } },
      { type: 'character-creator', label: '主角设定', position: { x: 50, y: 250 } },
      { type: 'character-creator', label: '配角设定', position: { x: 200, y: 250 } },
      { type: 'plotline-planner', label: '情感主线', position: { x: 125, y: 400 } },
      { type: 'outline-generator', label: '情节大纲', position: { x: 125, y: 550 } },
      { type: 'chapter-generator', label: '章节创作', position: { x: 125, y: 700 } },
      { type: 'content-polisher', label: '情感润色', position: { x: 125, y: 850 } },
      { type: 'output', label: '言情小说', position: { x: 125, y: 1000 } }
    ],
    connections: [
      { source: 0, target: 1 }, { source: 0, target: 2 },
      { source: 1, target: 3 }, { source: 2, target: 3 },
      { source: 3, target: 4 }, { source: 4, target: 5 },
      { source: 5, target: 6 }, { source: 6, target: 7 }
    ],
    tags: ['言情', '情感', '关系'],
    complexity: 'medium'
  }
];

// AI工作流分析器
export class WorkflowAI {
  // 分析用户需求并推荐工作流
  static analyzeRequirements(requirements: {
    genre?: string;
    style?: string;
    length?: string;
    experience?: string;
    features?: string[];
  }): WorkflowTemplate[] {
    const { genre, style, length, experience, features = [] } = requirements;
    
    let recommendations: Array<{ template: WorkflowTemplate; score: number }> = [];
    
    WORKFLOW_TEMPLATES.forEach(template => {
      let score = 0;
      
      // 根据类型匹配
      if (genre) {
        if (genre.includes('奇幻') && template.tags.includes('奇幻')) score += 30;
        if (genre.includes('言情') && template.tags.includes('言情')) score += 30;
        if (genre.includes('现代') && template.tags.includes('简单')) score += 20;
      }
      
      // 根据经验水平匹配
      if (experience) {
        if (experience === '新手' && template.complexity === 'simple') score += 25;
        if (experience === '进阶' && template.complexity === 'medium') score += 25;
        if (experience === '专业' && template.complexity === 'complex') score += 25;
      }
      
      // 根据长度匹配
      if (length) {
        if (length === '短篇' && template.complexity === 'simple') score += 20;
        if (length === '中篇' && template.complexity === 'medium') score += 20;
        if (length === '长篇' && template.complexity === 'complex') score += 20;
      }
      
      // 根据特殊需求匹配
      features.forEach(feature => {
        if (template.tags.some(tag => tag.includes(feature))) {
          score += 15;
        }
      });
      
      recommendations.push({ template, score });
    });
    
    // 按分数排序并返回前3个
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, 3)
      .map(r => r.template);
  }
  
  // 生成自定义工作流
  static generateCustomWorkflow(requirements: {
    genre: string;
    style: string;
    length: string;
    features: string[];
  }): WorkflowTemplate {
    const { genre, style, length, features } = requirements;
    
    // 基础节点
    const nodes = [
      { type: 'input', label: '创作输入', position: { x: 200, y: 100 } }
    ];
    
    const connections: Array<{ source: number; target: number }> = [];
    let currentY = 250;
    let nodeIndex = 1;
    
    // 根据需求添加节点
    const addNode = (type: string, label: string, x: number = 200) => {
      nodes.push({ type, label, position: { x, y: currentY } });
      return nodeIndex++;
    };
    
    // 书名生成（总是需要）
    const titleNode = addNode('title-generator', '书名生成', 100);
    connections.push({ source: 0, target: titleNode });
    
    // 根据类型添加特定节点
    if (genre.includes('奇幻') || features.includes('世界观')) {
      const worldNode = addNode('worldbuilding', '世界观构建', 300);
      connections.push({ source: 0, target: worldNode });
    }
    
    // 角色创建（总是需要）
    currentY += 150;
    const charNode = addNode('character-creator', '角色创建');
    connections.push({ source: 0, target: charNode });
    
    // 主线规划
    currentY += 150;
    const plotNode = addNode('plotline-planner', '主线规划');
    connections.push({ source: titleNode, target: plotNode });
    connections.push({ source: charNode, target: plotNode });
    
    // 大纲生成
    currentY += 150;
    const outlineNode = addNode('outline-generator', '大纲生成');
    connections.push({ source: plotNode, target: outlineNode });
    
    // 根据长度决定是否需要详细大纲
    if (length === '长篇') {
      currentY += 150;
      const detailOutlineNode = addNode('detailed-outline', '详细大纲');
      connections.push({ source: outlineNode, target: detailOutlineNode });
    }
    
    // 章节生成
    currentY += 150;
    const chapterNode = addNode('chapter-generator', '章节生成');
    const prevNode = length === '长篇' ? nodeIndex - 2 : outlineNode;
    connections.push({ source: prevNode, target: chapterNode });
    
    // 根据需求添加润色和检查
    if (features.includes('高质量') || style.includes('文艺')) {
      currentY += 150;
      const polishNode = addNode('content-polisher', '内容润色', 100);
      connections.push({ source: chapterNode, target: polishNode });
      
      const checkNode = addNode('consistency-checker', '一致性检查', 300);
      connections.push({ source: chapterNode, target: checkNode });
      
      // 最终输出
      currentY += 150;
      const outputNode = addNode('output', '最终输出');
      connections.push({ source: polishNode, target: outputNode });
      connections.push({ source: checkNode, target: outputNode });
    } else {
      // 简单输出
      currentY += 150;
      const outputNode = addNode('output', '最终输出');
      connections.push({ source: chapterNode, target: outputNode });
    }
    
    return {
      id: 'custom-' + Date.now(),
      name: '自定义工作流',
      description: `为${genre}类型的${length}小说定制的工作流`,
      nodes,
      connections,
      tags: ['自定义', genre, length],
      complexity: length === '短篇' ? 'simple' : length === '中篇' ? 'medium' : 'complex'
    };
  }
  
  // 优化现有工作流
  static optimizeWorkflow(currentNodes: any[], requirements: any): WorkflowTemplate {
    // 分析现有节点
    const nodeTypes = currentNodes.map(node => node.data.type);
    
    // 检查缺失的关键节点
    const missingNodes = [];
    if (!nodeTypes.includes('input')) missingNodes.push('input');
    if (!nodeTypes.includes('output')) missingNodes.push('output');
    if (!nodeTypes.includes('character-creator')) missingNodes.push('character-creator');
    if (!nodeTypes.includes('outline-generator')) missingNodes.push('outline-generator');
    
    // 生成优化建议
    const optimizedNodes = [...currentNodes];
    const connections: Array<{ source: number; target: number }> = [];
    
    // 添加缺失的节点
    missingNodes.forEach((nodeType, index) => {
      optimizedNodes.push({
        type: nodeType,
        label: this.getNodeLabel(nodeType),
        position: { x: 200 + index * 150, y: 100 + index * 150 }
      });
    });
    
    return {
      id: 'optimized-' + Date.now(),
      name: '优化工作流',
      description: '基于现有工作流的优化版本',
      nodes: optimizedNodes,
      connections,
      tags: ['优化', '改进'],
      complexity: 'medium'
    };
  }
  
  private static getNodeLabel(nodeType: string): string {
    const labels: Record<string, string> = {
      'input': '输入节点',
      'title-generator': '书名生成',
      'detail-generator': '详情生成',
      'character-creator': '角色创建',
      'worldbuilding': '世界观构建',
      'plotline-planner': '主线规划',
      'outline-generator': '大纲生成',
      'chapter-count-input': '章节数设定',
      'detailed-outline': '详细大纲',
      'chapter-generator': '章节生成',
      'content-polisher': '内容润色',
      'consistency-checker': '一致性检查',
      'condition': '条件分支',
      'loop': '循环执行',
      'output': '结果输出'
    };
    return labels[nodeType] || nodeType;
  }
}

export { WORKFLOW_TEMPLATES };
export type { WorkflowTemplate };
