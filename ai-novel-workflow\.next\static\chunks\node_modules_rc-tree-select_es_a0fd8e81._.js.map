{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/hooks/useCache.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\n/**\n * This function will try to call requestIdleCallback if available to save performance.\n * No need `getLabel` here since already fetch on `rawLabeledValue`.\n */\nexport default (function (values) {\n  var cacheRef = React.useRef({\n    valueLabels: new Map()\n  });\n  return React.useMemo(function () {\n    var valueLabels = cacheRef.current.valueLabels;\n    var valueLabelsCache = new Map();\n    var filledValues = values.map(function (item) {\n      var value = item.value,\n        label = item.label;\n      var mergedLabel = label !== null && label !== void 0 ? label : valueLabels.get(value);\n\n      // Save in cache\n      valueLabelsCache.set(value, mergedLabel);\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: mergedLabel\n      });\n    });\n    cacheRef.current.valueLabels = valueLabelsCache;\n    return [filledValues];\n  }, [values]);\n});"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAKgB,SAAU,MAAM;IAC9B,IAAI,WAAW,6JAAA,CAAA,SAAY,CAAC;QAC1B,aAAa,IAAI;IACnB;IACA,OAAO,6JAAA,CAAA,UAAa;mBAAC;YACnB,IAAI,cAAc,SAAS,OAAO,CAAC,WAAW;YAC9C,IAAI,mBAAmB,IAAI;YAC3B,IAAI,eAAe,OAAO,GAAG;wCAAC,SAAU,IAAI;oBAC1C,IAAI,QAAQ,KAAK,KAAK,EACpB,QAAQ,KAAK,KAAK;oBACpB,IAAI,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,QAAQ,YAAY,GAAG,CAAC;oBAE/E,gBAAgB;oBAChB,iBAAiB,GAAG,CAAC,OAAO;oBAC5B,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG;wBAChD,OAAO;oBACT;gBACF;;YACA,SAAS,OAAO,CAAC,WAAW,GAAG;YAC/B,OAAO;gBAAC;aAAa;QACvB;kBAAG;QAAC;KAAO;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/hooks/useCheckedKeys.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nvar useCheckedKeys = function useCheckedKeys(rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities) {\n  return React.useMemo(function () {\n    var extractValues = function extractValues(values) {\n      return values.map(function (_ref) {\n        var value = _ref.value;\n        return value;\n      });\n    };\n    var checkedKeys = extractValues(rawLabeledValues);\n    var halfCheckedKeys = extractValues(rawHalfCheckedValues);\n    var missingValues = checkedKeys.filter(function (key) {\n      return !keyEntities[key];\n    });\n    var finalCheckedKeys = checkedKeys;\n    var finalHalfCheckedKeys = halfCheckedKeys;\n    if (treeConduction) {\n      var conductResult = conductCheck(checkedKeys, true, keyEntities);\n      finalCheckedKeys = conductResult.checkedKeys;\n      finalHalfCheckedKeys = conductResult.halfCheckedKeys;\n    }\n    return [Array.from(new Set([].concat(_toConsumableArray(missingValues), _toConsumableArray(finalCheckedKeys)))), finalHalfCheckedKeys];\n  }, [rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities]);\n};\nexport default useCheckedKeys;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,IAAI,iBAAiB,SAAS,eAAe,gBAAgB,EAAE,oBAAoB,EAAE,cAAc,EAAE,WAAW;IAC9G,OAAO,6JAAA,CAAA,UAAa;kCAAC;YACnB,IAAI,gBAAgB,SAAS,cAAc,MAAM;gBAC/C,OAAO,OAAO,GAAG;4DAAC,SAAU,IAAI;wBAC9B,IAAI,QAAQ,KAAK,KAAK;wBACtB,OAAO;oBACT;;YACF;YACA,IAAI,cAAc,cAAc;YAChC,IAAI,kBAAkB,cAAc;YACpC,IAAI,gBAAgB,YAAY,MAAM;wDAAC,SAAU,GAAG;oBAClD,OAAO,CAAC,WAAW,CAAC,IAAI;gBAC1B;;YACA,IAAI,mBAAmB;YACvB,IAAI,uBAAuB;YAC3B,IAAI,gBAAgB;gBAClB,IAAI,gBAAgB,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE,aAAa,MAAM;gBACpD,mBAAmB,cAAc,WAAW;gBAC5C,uBAAuB,cAAc,eAAe;YACtD;YACA,OAAO;gBAAC,MAAM,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,gBAAgB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;gBAAsB;aAAqB;QACxI;iCAAG;QAAC;QAAkB;QAAsB;QAAgB;KAAY;AAC1E;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/utils/valueUtil.js"], "sourcesContent": ["export var toArray = function toArray(value) {\n  return Array.isArray(value) ? value : value !== undefined ? [value] : [];\n};\nexport var fillFieldNames = function fillFieldNames(fieldNames) {\n  var _ref = fieldNames || {},\n    label = _ref.label,\n    value = _ref.value,\n    children = _ref.children;\n  return {\n    _title: label ? [label] : ['title', 'label'],\n    value: value || 'value',\n    key: value || 'value',\n    children: children || 'children'\n  };\n};\nexport var isCheckDisabled = function isCheckDisabled(node) {\n  return !node || node.disabled || node.disableCheckbox || node.checkable === false;\n};\nexport var getAllKeys = function getAllKeys(treeData, fieldNames) {\n  var keys = [];\n  var dig = function dig(list) {\n    list.forEach(function (item) {\n      var children = item[fieldNames.children];\n      if (children) {\n        keys.push(item[fieldNames.value]);\n        dig(children);\n      }\n    });\n  };\n  dig(treeData);\n  return keys;\n};\nexport var isNil = function isNil(val) {\n  return val === null || val === undefined;\n};"], "names": [], "mappings": ";;;;;;;AAAO,IAAI,UAAU,SAAS,QAAQ,KAAK;IACzC,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ,UAAU,YAAY;QAAC;KAAM,GAAG,EAAE;AAC1E;AACO,IAAI,iBAAiB,SAAS,eAAe,UAAU;IAC5D,IAAI,OAAO,cAAc,CAAC,GACxB,QAAQ,KAAK,KAAK,EAClB,QAAQ,KAAK,KAAK,EAClB,WAAW,KAAK,QAAQ;IAC1B,OAAO;QACL,QAAQ,QAAQ;YAAC;SAAM,GAAG;YAAC;YAAS;SAAQ;QAC5C,OAAO,SAAS;QAChB,KAAK,SAAS;QACd,UAAU,YAAY;IACxB;AACF;AACO,IAAI,kBAAkB,SAAS,gBAAgB,IAAI;IACxD,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,KAAK,eAAe,IAAI,KAAK,SAAS,KAAK;AAC9E;AACO,IAAI,aAAa,SAAS,WAAW,QAAQ,EAAE,UAAU;IAC9D,IAAI,OAAO,EAAE;IACb,IAAI,MAAM,SAAS,IAAI,IAAI;QACzB,KAAK,OAAO,CAAC,SAAU,IAAI;YACzB,IAAI,WAAW,IAAI,CAAC,WAAW,QAAQ,CAAC;YACxC,IAAI,UAAU;gBACZ,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC;gBAChC,IAAI;YACN;QACF;IACF;IACA,IAAI;IACJ,OAAO;AACT;AACO,IAAI,QAAQ,SAAS,MAAM,GAAG;IACnC,OAAO,QAAQ,QAAQ,QAAQ;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/hooks/useDataEntities.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport warning from \"rc-util/es/warning\";\nimport { isNil } from \"../utils/valueUtil\";\nexport default (function (treeData, fieldNames) {\n  return React.useMemo(function () {\n    var collection = convertDataToEntities(treeData, {\n      fieldNames: fieldNames,\n      initWrapper: function initWrapper(wrapper) {\n        return _objectSpread(_objectSpread({}, wrapper), {}, {\n          valueEntities: new Map()\n        });\n      },\n      processEntity: function processEntity(entity, wrapper) {\n        var val = entity.node[fieldNames.value];\n\n        // Check if exist same value\n        if (process.env.NODE_ENV !== 'production') {\n          var key = entity.node.key;\n          warning(!isNil(val), 'TreeNode `value` is invalidate: undefined');\n          warning(!wrapper.valueEntities.has(val), \"Same `value` exist in the tree: \".concat(val));\n          warning(!key || String(key) === String(val), \"`key` or `value` with TreeNode must be the same or you can remove one of them. key: \".concat(key, \", value: \").concat(val, \".\"));\n        }\n        wrapper.valueEntities.set(val, entity);\n      }\n    });\n    return collection;\n  }, [treeData, fieldNames]);\n});"], "names": [], "mappings": ";;;AAkBY;AAlBZ;AACA;AACA;AACA;AACA;;;;;;uCACgB,SAAU,QAAQ,EAAE,UAAU;IAC5C,OAAO,6JAAA,CAAA,UAAa;mBAAC;YACnB,IAAI,aAAa,CAAA,GAAA,wJAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU;gBAC/C,YAAY;gBACZ,aAAa,SAAS,YAAY,OAAO;oBACvC,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,UAAU,CAAC,GAAG;wBACnD,eAAe,IAAI;oBACrB;gBACF;gBACA,eAAe,SAAS,cAAc,MAAM,EAAE,OAAO;oBACnD,IAAI,MAAM,OAAO,IAAI,CAAC,WAAW,KAAK,CAAC;oBAEvC,4BAA4B;oBAC5B,wCAA2C;wBACzC,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG;wBACzB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,CAAA,GAAA,mKAAA,CAAA,QAAK,AAAD,EAAE,MAAM;wBACrB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,QAAQ,aAAa,CAAC,GAAG,CAAC,MAAM,mCAAmC,MAAM,CAAC;wBACnF,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,OAAO,OAAO,SAAS,OAAO,MAAM,uFAAuF,MAAM,CAAC,KAAK,aAAa,MAAM,CAAC,KAAK;oBAC3K;oBACA,QAAQ,aAAa,CAAC,GAAG,CAAC,KAAK;gBACjC;YACF;YACA,OAAO;QACT;kBAAG;QAAC;QAAU;KAAW;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/TreeNode.js"], "sourcesContent": ["/* istanbul ignore file */\n\n/** This is a placeholder, not real render in dom */\nvar TreeNode = function TreeNode() {\n  return null;\n};\nexport default TreeNode;"], "names": [], "mappings": "AAAA,wBAAwB,GAExB,kDAAkD;;;AAClD,IAAI,WAAW,SAAS;IACtB,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/utils/legacyUtil.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"children\", \"value\"];\nimport * as React from 'react';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport warning from \"rc-util/es/warning\";\nimport TreeNode from \"../TreeNode\";\nexport function convertChildrenToData(nodes) {\n  return toArray(nodes).map(function (node) {\n    if (! /*#__PURE__*/React.isValidElement(node) || !node.type) {\n      return null;\n    }\n    var _ref = node,\n      key = _ref.key,\n      _ref$props = _ref.props,\n      children = _ref$props.children,\n      value = _ref$props.value,\n      restProps = _objectWithoutProperties(_ref$props, _excluded);\n    var data = _objectSpread({\n      key: key,\n      value: value\n    }, restProps);\n    var childData = convertChildrenToData(children);\n    if (childData.length) {\n      data.children = childData;\n    }\n    return data;\n  }).filter(function (data) {\n    return data;\n  });\n}\nexport function fillLegacyProps(dataNode) {\n  if (!dataNode) {\n    return dataNode;\n  }\n  var cloneNode = _objectSpread({}, dataNode);\n  if (!('props' in cloneNode)) {\n    Object.defineProperty(cloneNode, 'props', {\n      get: function get() {\n        warning(false, 'New `rc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access.');\n        return cloneNode;\n      }\n    });\n  }\n  return cloneNode;\n}\nexport function fillAdditionalInfo(extra, triggerValue, checkedValues, treeData, showPosition, fieldNames) {\n  var triggerNode = null;\n  var nodeList = null;\n  function generateMap() {\n    function dig(list) {\n      var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '0';\n      var parentIncluded = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n      return list.map(function (option, index) {\n        var pos = \"\".concat(level, \"-\").concat(index);\n        var value = option[fieldNames.value];\n        var included = checkedValues.includes(value);\n        var children = dig(option[fieldNames.children] || [], pos, included);\n        var node = /*#__PURE__*/React.createElement(TreeNode, option, children.map(function (child) {\n          return child.node;\n        }));\n\n        // Link with trigger node\n        if (triggerValue === value) {\n          triggerNode = node;\n        }\n        if (included) {\n          var checkedNode = {\n            pos: pos,\n            node: node,\n            children: children\n          };\n          if (!parentIncluded) {\n            nodeList.push(checkedNode);\n          }\n          return checkedNode;\n        }\n        return null;\n      }).filter(function (node) {\n        return node;\n      });\n    }\n    if (!nodeList) {\n      nodeList = [];\n      dig(treeData);\n\n      // Sort to keep the checked node length\n      nodeList.sort(function (_ref2, _ref3) {\n        var val1 = _ref2.node.props.value;\n        var val2 = _ref3.node.props.value;\n        var index1 = checkedValues.indexOf(val1);\n        var index2 = checkedValues.indexOf(val2);\n        return index1 - index2;\n      });\n    }\n  }\n  Object.defineProperty(extra, 'triggerNode', {\n    get: function get() {\n      warning(false, '`triggerNode` is deprecated. Please consider decoupling data with node.');\n      generateMap();\n      return triggerNode;\n    }\n  });\n  Object.defineProperty(extra, 'allCheckedNodes', {\n    get: function get() {\n      warning(false, '`allCheckedNodes` is deprecated. Please consider decoupling data with node.');\n      generateMap();\n      if (showPosition) {\n        return nodeList;\n      }\n      return nodeList.map(function (_ref4) {\n        var node = _ref4.node;\n        return node;\n      });\n    }\n  });\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AACA;AACA;AACA;;;AAJA,IAAI,YAAY;IAAC;IAAY;CAAQ;;;;;AAK9B,SAAS,sBAAsB,KAAK;IACzC,OAAO,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,OAAO,GAAG,CAAC,SAAU,IAAI;QACtC,IAAI,CAAE,WAAW,GAAE,6JAAA,CAAA,iBAAoB,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC3D,OAAO;QACT;QACA,IAAI,OAAO,MACT,MAAM,KAAK,GAAG,EACd,aAAa,KAAK,KAAK,EACvB,WAAW,WAAW,QAAQ,EAC9B,QAAQ,WAAW,KAAK,EACxB,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,YAAY;QACnD,IAAI,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE;YACvB,KAAK;YACL,OAAO;QACT,GAAG;QACH,IAAI,YAAY,sBAAsB;QACtC,IAAI,UAAU,MAAM,EAAE;YACpB,KAAK,QAAQ,GAAG;QAClB;QACA,OAAO;IACT,GAAG,MAAM,CAAC,SAAU,IAAI;QACtB,OAAO;IACT;AACF;AACO,SAAS,gBAAgB,QAAQ;IACtC,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IACA,IAAI,YAAY,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG;IAClC,IAAI,CAAC,CAAC,WAAW,SAAS,GAAG;QAC3B,OAAO,cAAc,CAAC,WAAW,SAAS;YACxC,KAAK,SAAS;gBACZ,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACf,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AACO,SAAS,mBAAmB,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU;IACvG,IAAI,cAAc;IAClB,IAAI,WAAW;IACf,SAAS;QACP,SAAS,IAAI,IAAI;YACf,IAAI,QAAQ,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;YAChF,IAAI,iBAAiB,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;YACzF,OAAO,KAAK,GAAG,CAAC,SAAU,MAAM,EAAE,KAAK;gBACrC,IAAI,MAAM,GAAG,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC;gBACvC,IAAI,QAAQ,MAAM,CAAC,WAAW,KAAK,CAAC;gBACpC,IAAI,WAAW,cAAc,QAAQ,CAAC;gBACtC,IAAI,WAAW,IAAI,MAAM,CAAC,WAAW,QAAQ,CAAC,IAAI,EAAE,EAAE,KAAK;gBAC3D,IAAI,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,yJAAA,CAAA,UAAQ,EAAE,QAAQ,SAAS,GAAG,CAAC,SAAU,KAAK;oBACxF,OAAO,MAAM,IAAI;gBACnB;gBAEA,yBAAyB;gBACzB,IAAI,iBAAiB,OAAO;oBAC1B,cAAc;gBAChB;gBACA,IAAI,UAAU;oBACZ,IAAI,cAAc;wBAChB,KAAK;wBACL,MAAM;wBACN,UAAU;oBACZ;oBACA,IAAI,CAAC,gBAAgB;wBACnB,SAAS,IAAI,CAAC;oBAChB;oBACA,OAAO;gBACT;gBACA,OAAO;YACT,GAAG,MAAM,CAAC,SAAU,IAAI;gBACtB,OAAO;YACT;QACF;QACA,IAAI,CAAC,UAAU;YACb,WAAW,EAAE;YACb,IAAI;YAEJ,uCAAuC;YACvC,SAAS,IAAI,CAAC,SAAU,KAAK,EAAE,KAAK;gBAClC,IAAI,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK;gBACjC,IAAI,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK;gBACjC,IAAI,SAAS,cAAc,OAAO,CAAC;gBACnC,IAAI,SAAS,cAAc,OAAO,CAAC;gBACnC,OAAO,SAAS;YAClB;QACF;IACF;IACA,OAAO,cAAc,CAAC,OAAO,eAAe;QAC1C,KAAK,SAAS;YACZ,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;YACf;YACA,OAAO;QACT;IACF;IACA,OAAO,cAAc,CAAC,OAAO,mBAAmB;QAC9C,KAAK,SAAS;YACZ,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;YACf;YACA,IAAI,cAAc;gBAChB,OAAO;YACT;YACA,OAAO,SAAS,GAAG,CAAC,SAAU,KAAK;gBACjC,IAAI,OAAO,MAAM,IAAI;gBACrB,OAAO;YACT;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/hooks/useFilterTreeData.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { fillLegacyProps } from \"../utils/legacyUtil\";\nvar useFilterTreeData = function useFilterTreeData(treeData, searchValue, options) {\n  var fieldNames = options.fieldNames,\n    treeNodeFilterProp = options.treeNodeFilterProp,\n    filterTreeNode = options.filterTreeNode;\n  var fieldChildren = fieldNames.children;\n  return React.useMemo(function () {\n    if (!searchValue || filterTreeNode === false) {\n      return treeData;\n    }\n    var filterOptionFunc = typeof filterTreeNode === 'function' ? filterTreeNode : function (_, dataNode) {\n      return String(dataNode[treeNodeFilterProp]).toUpperCase().includes(searchValue.toUpperCase());\n    };\n    var filterTreeNodes = function filterTreeNodes(nodes) {\n      var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      return nodes.reduce(function (filtered, node) {\n        var children = node[fieldChildren];\n        var isMatch = keepAll || filterOptionFunc(searchValue, fillLegacyProps(node));\n        var filteredChildren = filterTreeNodes(children || [], isMatch);\n        if (isMatch || filteredChildren.length) {\n          filtered.push(_objectSpread(_objectSpread({}, node), {}, _defineProperty({\n            isLeaf: undefined\n          }, fieldChildren, filteredChildren)));\n        }\n        return filtered;\n      }, []);\n    };\n    return filterTreeNodes(treeData);\n  }, [treeData, searchValue, fieldChildren, treeNodeFilterProp, filterTreeNode]);\n};\nexport default useFilterTreeData;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,IAAI,oBAAoB,SAAS,kBAAkB,QAAQ,EAAE,WAAW,EAAE,OAAO;IAC/E,IAAI,aAAa,QAAQ,UAAU,EACjC,qBAAqB,QAAQ,kBAAkB,EAC/C,iBAAiB,QAAQ,cAAc;IACzC,IAAI,gBAAgB,WAAW,QAAQ;IACvC,OAAO,6JAAA,CAAA,UAAa;qCAAC;YACnB,IAAI,CAAC,eAAe,mBAAmB,OAAO;gBAC5C,OAAO;YACT;YACA,IAAI,mBAAmB,OAAO,mBAAmB,aAAa;6CAAiB,SAAU,CAAC,EAAE,QAAQ;oBAClG,OAAO,OAAO,QAAQ,CAAC,mBAAmB,EAAE,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;gBAC5F;;YACA,IAAI,kBAAkB,SAAS,gBAAgB,KAAK;gBAClD,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,SAAS,CAAC,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,GAAG;gBAClF,OAAO,MAAM,MAAM;iEAAC,SAAU,QAAQ,EAAE,IAAI;wBAC1C,IAAI,WAAW,IAAI,CAAC,cAAc;wBAClC,IAAI,UAAU,WAAW,iBAAiB,aAAa,CAAA,GAAA,oKAAA,CAAA,kBAAe,AAAD,EAAE;wBACvE,IAAI,mBAAmB,gBAAgB,YAAY,EAAE,EAAE;wBACvD,IAAI,WAAW,iBAAiB,MAAM,EAAE;4BACtC,SAAS,IAAI,CAAC,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE;gCACvE,QAAQ;4BACV,GAAG,eAAe;wBACpB;wBACA,OAAO;oBACT;gEAAG,EAAE;YACP;YACA,OAAO,gBAAgB;QACzB;oCAAG;QAAC;QAAU;QAAa;QAAe;QAAoB;KAAe;AAC/E;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/hooks/useRefFunc.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Same as `React.useCallback` but always return a memoized function\n * but redirect to real function.\n */\nexport default function useRefFunc(callback) {\n  var funcRef = React.useRef();\n  funcRef.current = callback;\n  var cacheFn = React.useCallback(function () {\n    return funcRef.current.apply(funcRef, arguments);\n  }, []);\n  return cacheFn;\n}"], "names": [], "mappings": ";;;AAAA;;AAMe,SAAS,WAAW,QAAQ;IACzC,IAAI,UAAU,6JAAA,CAAA,SAAY;IAC1B,QAAQ,OAAO,GAAG;IAClB,IAAI,UAAU,6JAAA,CAAA,cAAiB;2CAAC;YAC9B,OAAO,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS;QACxC;0CAAG,EAAE;IACL,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/hooks/useTreeData.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertChildrenToData } from \"../utils/legacyUtil\";\nfunction buildTreeStructure(nodes, config) {\n  var id = config.id,\n    pId = config.pId,\n    rootPId = config.rootPId;\n  var nodeMap = new Map();\n  var rootNodes = [];\n  nodes.forEach(function (node) {\n    var nodeKey = node[id];\n    var clonedNode = _objectSpread(_objectSpread({}, node), {}, {\n      key: node.key || nodeKey\n    });\n    nodeMap.set(nodeKey, clonedNode);\n  });\n  nodeMap.forEach(function (node) {\n    var parentKey = node[pId];\n    var parent = nodeMap.get(parentKey);\n    if (parent) {\n      parent.children = parent.children || [];\n      parent.children.push(node);\n    } else if (parentKey === rootPId || rootPId === null) {\n      rootNodes.push(node);\n    }\n  });\n  return rootNodes;\n}\n\n/**\n * 将 `treeData` 或 `children` 转换为格式化的 `treeData`。\n * 如果 `treeData` 或 `children` 没有变化，则不会重新计算。\n */\nexport default function useTreeData(treeData, children, simpleMode) {\n  return React.useMemo(function () {\n    if (treeData) {\n      if (simpleMode) {\n        var config = _objectSpread({\n          id: 'id',\n          pId: 'pId',\n          rootPId: null\n        }, _typeof(simpleMode) === 'object' ? simpleMode : {});\n        return buildTreeStructure(treeData, config);\n      }\n      return treeData;\n    }\n    return convertChildrenToData(children);\n  }, [children, simpleMode, treeData]);\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,SAAS,mBAAmB,KAAK,EAAE,MAAM;IACvC,IAAI,KAAK,OAAO,EAAE,EAChB,MAAM,OAAO,GAAG,EAChB,UAAU,OAAO,OAAO;IAC1B,IAAI,UAAU,IAAI;IAClB,IAAI,YAAY,EAAE;IAClB,MAAM,OAAO,CAAC,SAAU,IAAI;QAC1B,IAAI,UAAU,IAAI,CAAC,GAAG;QACtB,IAAI,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG;YAC1D,KAAK,KAAK,GAAG,IAAI;QACnB;QACA,QAAQ,GAAG,CAAC,SAAS;IACvB;IACA,QAAQ,OAAO,CAAC,SAAU,IAAI;QAC5B,IAAI,YAAY,IAAI,CAAC,IAAI;QACzB,IAAI,SAAS,QAAQ,GAAG,CAAC;QACzB,IAAI,QAAQ;YACV,OAAO,QAAQ,GAAG,OAAO,QAAQ,IAAI,EAAE;YACvC,OAAO,QAAQ,CAAC,IAAI,CAAC;QACvB,OAAO,IAAI,cAAc,WAAW,YAAY,MAAM;YACpD,UAAU,IAAI,CAAC;QACjB;IACF;IACA,OAAO;AACT;AAMe,SAAS,YAAY,QAAQ,EAAE,QAAQ,EAAE,UAAU;IAChE,OAAO,6JAAA,CAAA,UAAa;+BAAC;YACnB,IAAI,UAAU;gBACZ,IAAI,YAAY;oBACd,IAAI,SAAS,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE;wBACzB,IAAI;wBACJ,KAAK;wBACL,SAAS;oBACX,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,WAAW,aAAa,CAAC;oBACpD,OAAO,mBAAmB,UAAU;gBACtC;gBACA,OAAO;YACT;YACA,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;QAC/B;8BAAG;QAAC;QAAU;QAAY;KAAS;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/LegacyContext.js"], "sourcesContent": ["import * as React from 'react';\nvar LegacySelectContext = /*#__PURE__*/React.createContext(null);\nexport default LegacySelectContext;"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI,sBAAsB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC;uCAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/TreeSelectContext.js"], "sourcesContent": ["import * as React from 'react';\nvar TreeSelectContext = /*#__PURE__*/React.createContext(null);\nexport default TreeSelectContext;"], "names": [], "mappings": ";;;AAAA;;AACA,IAAI,oBAAoB,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC;uCAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/OptionList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _createForOfIteratorHelper from \"@babel/runtime/helpers/esm/createForOfIteratorHelper\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useBaseProps } from 'rc-select';\nimport Tree from 'rc-tree';\nimport { UnstableContext } from 'rc-tree';\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport useMemo from \"rc-util/es/hooks/useMemo\";\nimport * as React from 'react';\nimport LegacyContext from \"./LegacyContext\";\nimport TreeSelectContext from \"./TreeSelectContext\";\nimport { getAllKeys, isCheckDisabled } from \"./utils/valueUtil\";\nimport { useEvent } from 'rc-util';\nvar HIDDEN_STYLE = {\n  width: 0,\n  height: 0,\n  display: 'flex',\n  overflow: 'hidden',\n  opacity: 0,\n  border: 0,\n  padding: 0,\n  margin: 0\n};\nvar OptionList = function OptionList(_, ref) {\n  var _useBaseProps = useBaseProps(),\n    prefixCls = _useBaseProps.prefixCls,\n    multiple = _useBaseProps.multiple,\n    searchValue = _useBaseProps.searchValue,\n    toggleOpen = _useBaseProps.toggleOpen,\n    open = _useBaseProps.open,\n    notFoundContent = _useBaseProps.notFoundContent;\n  var _React$useContext = React.useContext(TreeSelectContext),\n    virtual = _React$useContext.virtual,\n    listHeight = _React$useContext.listHeight,\n    listItemHeight = _React$useContext.listItemHeight,\n    listItemScrollOffset = _React$useContext.listItemScrollOffset,\n    treeData = _React$useContext.treeData,\n    fieldNames = _React$useContext.fieldNames,\n    onSelect = _React$useContext.onSelect,\n    dropdownMatchSelectWidth = _React$useContext.dropdownMatchSelectWidth,\n    treeExpandAction = _React$useContext.treeExpandAction,\n    treeTitleRender = _React$useContext.treeTitleRender,\n    onPopupScroll = _React$useContext.onPopupScroll,\n    leftMaxCount = _React$useContext.leftMaxCount,\n    leafCountOnly = _React$useContext.leafCountOnly,\n    valueEntities = _React$useContext.valueEntities;\n  var _React$useContext2 = React.useContext(LegacyContext),\n    checkable = _React$useContext2.checkable,\n    checkedKeys = _React$useContext2.checkedKeys,\n    halfCheckedKeys = _React$useContext2.halfCheckedKeys,\n    treeExpandedKeys = _React$useContext2.treeExpandedKeys,\n    treeDefaultExpandAll = _React$useContext2.treeDefaultExpandAll,\n    treeDefaultExpandedKeys = _React$useContext2.treeDefaultExpandedKeys,\n    onTreeExpand = _React$useContext2.onTreeExpand,\n    treeIcon = _React$useContext2.treeIcon,\n    showTreeIcon = _React$useContext2.showTreeIcon,\n    switcherIcon = _React$useContext2.switcherIcon,\n    treeLine = _React$useContext2.treeLine,\n    treeNodeFilterProp = _React$useContext2.treeNodeFilterProp,\n    loadData = _React$useContext2.loadData,\n    treeLoadedKeys = _React$useContext2.treeLoadedKeys,\n    treeMotion = _React$useContext2.treeMotion,\n    onTreeLoad = _React$useContext2.onTreeLoad,\n    keyEntities = _React$useContext2.keyEntities;\n  var treeRef = React.useRef();\n  var memoTreeData = useMemo(function () {\n    return treeData;\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [open, treeData], function (prev, next) {\n    return next[0] && prev[1] !== next[1];\n  });\n\n  // ========================== Values ==========================\n  var mergedCheckedKeys = React.useMemo(function () {\n    if (!checkable) {\n      return null;\n    }\n    return {\n      checked: checkedKeys,\n      halfChecked: halfCheckedKeys\n    };\n  }, [checkable, checkedKeys, halfCheckedKeys]);\n\n  // ========================== Scroll ==========================\n  React.useEffect(function () {\n    // Single mode should scroll to current key\n    if (open && !multiple && checkedKeys.length) {\n      var _treeRef$current;\n      (_treeRef$current = treeRef.current) === null || _treeRef$current === void 0 || _treeRef$current.scrollTo({\n        key: checkedKeys[0]\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [open]);\n\n  // ========================== Events ==========================\n  var onListMouseDown = function onListMouseDown(event) {\n    event.preventDefault();\n  };\n  var onInternalSelect = function onInternalSelect(__, info) {\n    var node = info.node;\n    if (checkable && isCheckDisabled(node)) {\n      return;\n    }\n    onSelect(node.key, {\n      selected: !checkedKeys.includes(node.key)\n    });\n    if (!multiple) {\n      toggleOpen(false);\n    }\n  };\n\n  // =========================== Keys ===========================\n  var _React$useState = React.useState(treeDefaultExpandedKeys),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    expandedKeys = _React$useState2[0],\n    setExpandedKeys = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    searchExpandedKeys = _React$useState4[0],\n    setSearchExpandedKeys = _React$useState4[1];\n  var mergedExpandedKeys = React.useMemo(function () {\n    if (treeExpandedKeys) {\n      return _toConsumableArray(treeExpandedKeys);\n    }\n    return searchValue ? searchExpandedKeys : expandedKeys;\n  }, [expandedKeys, searchExpandedKeys, treeExpandedKeys, searchValue]);\n  var onInternalExpand = function onInternalExpand(keys) {\n    setExpandedKeys(keys);\n    setSearchExpandedKeys(keys);\n    if (onTreeExpand) {\n      onTreeExpand(keys);\n    }\n  };\n\n  // ========================== Search ==========================\n  var lowerSearchValue = String(searchValue).toLowerCase();\n  var filterTreeNode = function filterTreeNode(treeNode) {\n    if (!lowerSearchValue) {\n      return false;\n    }\n    return String(treeNode[treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue);\n  };\n  React.useEffect(function () {\n    if (searchValue) {\n      setSearchExpandedKeys(getAllKeys(treeData, fieldNames));\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [searchValue]);\n\n  // ========================= Disabled =========================\n  // Cache disabled states in React state to ensure re-render when cache updates\n  var _React$useState5 = React.useState(function () {\n      return new Map();\n    }),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    disabledCache = _React$useState6[0],\n    setDisabledCache = _React$useState6[1];\n  React.useEffect(function () {\n    if (leftMaxCount) {\n      setDisabledCache(new Map());\n    }\n  }, [leftMaxCount]);\n  function getDisabledWithCache(node) {\n    var value = node[fieldNames.value];\n    if (!disabledCache.has(value)) {\n      var entity = valueEntities.get(value);\n      var isLeaf = (entity.children || []).length === 0;\n      if (!isLeaf) {\n        var checkableChildren = entity.children.filter(function (childTreeNode) {\n          return !childTreeNode.node.disabled && !childTreeNode.node.disableCheckbox && !checkedKeys.includes(childTreeNode.node[fieldNames.value]);\n        });\n        var checkableChildrenCount = checkableChildren.length;\n        disabledCache.set(value, checkableChildrenCount > leftMaxCount);\n      } else {\n        disabledCache.set(value, false);\n      }\n    }\n    return disabledCache.get(value);\n  }\n  var nodeDisabled = useEvent(function (node) {\n    var nodeValue = node[fieldNames.value];\n    if (checkedKeys.includes(nodeValue)) {\n      return false;\n    }\n    if (leftMaxCount === null) {\n      return false;\n    }\n    if (leftMaxCount <= 0) {\n      return true;\n    }\n\n    // This is a low performance calculation\n    if (leafCountOnly && leftMaxCount) {\n      return getDisabledWithCache(node);\n    }\n    return false;\n  });\n\n  // ========================== Get First Selectable Node ==========================\n  var getFirstMatchingNode = function getFirstMatchingNode(nodes) {\n    var _iterator = _createForOfIteratorHelper(nodes),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var node = _step.value;\n        if (node.disabled || node.selectable === false) {\n          continue;\n        }\n        if (searchValue) {\n          if (filterTreeNode(node)) {\n            return node;\n          }\n        } else {\n          return node;\n        }\n        if (node[fieldNames.children]) {\n          var matchInChildren = getFirstMatchingNode(node[fieldNames.children]);\n          if (matchInChildren) {\n            return matchInChildren;\n          }\n        }\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n    return null;\n  };\n\n  // ========================== Active ==========================\n  var _React$useState7 = React.useState(null),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    activeKey = _React$useState8[0],\n    setActiveKey = _React$useState8[1];\n  var activeEntity = keyEntities[activeKey];\n  React.useEffect(function () {\n    if (!open) {\n      return;\n    }\n    var nextActiveKey = null;\n    var getFirstNode = function getFirstNode() {\n      var firstNode = getFirstMatchingNode(memoTreeData);\n      return firstNode ? firstNode[fieldNames.value] : null;\n    };\n\n    // single mode active first checked node\n    if (!multiple && checkedKeys.length && !searchValue) {\n      nextActiveKey = checkedKeys[0];\n    } else {\n      nextActiveKey = getFirstNode();\n    }\n    setActiveKey(nextActiveKey);\n  }, [open, searchValue]);\n\n  // ========================= Keyboard =========================\n  React.useImperativeHandle(ref, function () {\n    var _treeRef$current2;\n    return {\n      scrollTo: (_treeRef$current2 = treeRef.current) === null || _treeRef$current2 === void 0 ? void 0 : _treeRef$current2.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var _treeRef$current3;\n        var which = event.which;\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n          case KeyCode.LEFT:\n          case KeyCode.RIGHT:\n            (_treeRef$current3 = treeRef.current) === null || _treeRef$current3 === void 0 || _treeRef$current3.onKeyDown(event);\n            break;\n\n          // >>> Select item\n          case KeyCode.ENTER:\n            {\n              if (activeEntity) {\n                var isNodeDisabled = nodeDisabled(activeEntity.node);\n                var _ref = (activeEntity === null || activeEntity === void 0 ? void 0 : activeEntity.node) || {},\n                  selectable = _ref.selectable,\n                  value = _ref.value,\n                  disabled = _ref.disabled;\n                if (selectable !== false && !disabled && !isNodeDisabled) {\n                  onInternalSelect(null, {\n                    node: {\n                      key: activeKey\n                    },\n                    selected: !checkedKeys.includes(value)\n                  });\n                }\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  });\n  var hasLoadDataFn = useMemo(function () {\n    return searchValue ? false : true;\n  }, [searchValue, treeExpandedKeys || expandedKeys], function (_ref2, _ref3) {\n    var _ref4 = _slicedToArray(_ref2, 1),\n      preSearchValue = _ref4[0];\n    var _ref5 = _slicedToArray(_ref3, 2),\n      nextSearchValue = _ref5[0],\n      nextExcludeSearchExpandedKeys = _ref5[1];\n    return preSearchValue !== nextSearchValue && !!(nextSearchValue || nextExcludeSearchExpandedKeys);\n  });\n  var syncLoadData = hasLoadDataFn ? loadData : null;\n\n  // ========================== Render ==========================\n  if (memoTreeData.length === 0) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      role: \"listbox\",\n      className: \"\".concat(prefixCls, \"-empty\"),\n      onMouseDown: onListMouseDown\n    }, notFoundContent);\n  }\n  var treeProps = {\n    fieldNames: fieldNames\n  };\n  if (treeLoadedKeys) {\n    treeProps.loadedKeys = treeLoadedKeys;\n  }\n  if (mergedExpandedKeys) {\n    treeProps.expandedKeys = mergedExpandedKeys;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onListMouseDown\n  }, activeEntity && open && /*#__PURE__*/React.createElement(\"span\", {\n    style: HIDDEN_STYLE,\n    \"aria-live\": \"assertive\"\n  }, activeEntity.node.value), /*#__PURE__*/React.createElement(UnstableContext.Provider, {\n    value: {\n      nodeDisabled: nodeDisabled\n    }\n  }, /*#__PURE__*/React.createElement(Tree, _extends({\n    ref: treeRef,\n    focusable: false,\n    prefixCls: \"\".concat(prefixCls, \"-tree\"),\n    treeData: memoTreeData,\n    height: listHeight,\n    itemHeight: listItemHeight,\n    itemScrollOffset: listItemScrollOffset,\n    virtual: virtual !== false && dropdownMatchSelectWidth !== false,\n    multiple: multiple,\n    icon: treeIcon,\n    showIcon: showTreeIcon,\n    switcherIcon: switcherIcon,\n    showLine: treeLine,\n    loadData: syncLoadData,\n    motion: treeMotion,\n    activeKey: activeKey\n    // We handle keys by out instead tree self\n    ,\n    checkable: checkable,\n    checkStrictly: true,\n    checkedKeys: mergedCheckedKeys,\n    selectedKeys: !checkable ? checkedKeys : [],\n    defaultExpandAll: treeDefaultExpandAll,\n    titleRender: treeTitleRender\n  }, treeProps, {\n    // Proxy event out\n    onActiveChange: setActiveKey,\n    onSelect: onInternalSelect,\n    onCheck: onInternalSelect,\n    onExpand: onInternalExpand,\n    onLoad: onTreeLoad,\n    filterTreeNode: filterTreeNode,\n    expandAction: treeExpandAction,\n    onScroll: onPopupScroll\n  }))));\n};\nvar RefOptionList = /*#__PURE__*/React.forwardRef(OptionList);\nif (process.env.NODE_ENV !== 'production') {\n  RefOptionList.displayName = 'OptionList';\n}\nexport default RefOptionList;"], "names": [], "mappings": ";;;AA8XI;AA9XJ;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;;;;;;;AACA,IAAI,eAAe;IACjB,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,SAAS;IACT,QAAQ;IACR,SAAS;IACT,QAAQ;AACV;AACA,IAAI,aAAa,SAAS,WAAW,CAAC,EAAE,GAAG;IACzC,IAAI,gBAAgB,CAAA,GAAA,yMAAA,CAAA,eAAY,AAAD,KAC7B,YAAY,cAAc,SAAS,EACnC,WAAW,cAAc,QAAQ,EACjC,cAAc,cAAc,WAAW,EACvC,aAAa,cAAc,UAAU,EACrC,OAAO,cAAc,IAAI,EACzB,kBAAkB,cAAc,eAAe;IACjD,IAAI,oBAAoB,6JAAA,CAAA,aAAgB,CAAC,kKAAA,CAAA,UAAiB,GACxD,UAAU,kBAAkB,OAAO,EACnC,aAAa,kBAAkB,UAAU,EACzC,iBAAiB,kBAAkB,cAAc,EACjD,uBAAuB,kBAAkB,oBAAoB,EAC7D,WAAW,kBAAkB,QAAQ,EACrC,aAAa,kBAAkB,UAAU,EACzC,WAAW,kBAAkB,QAAQ,EACrC,2BAA2B,kBAAkB,wBAAwB,EACrE,mBAAmB,kBAAkB,gBAAgB,EACrD,kBAAkB,kBAAkB,eAAe,EACnD,gBAAgB,kBAAkB,aAAa,EAC/C,eAAe,kBAAkB,YAAY,EAC7C,gBAAgB,kBAAkB,aAAa,EAC/C,gBAAgB,kBAAkB,aAAa;IACjD,IAAI,qBAAqB,6JAAA,CAAA,aAAgB,CAAC,8JAAA,CAAA,UAAa,GACrD,YAAY,mBAAmB,SAAS,EACxC,cAAc,mBAAmB,WAAW,EAC5C,kBAAkB,mBAAmB,eAAe,EACpD,mBAAmB,mBAAmB,gBAAgB,EACtD,uBAAuB,mBAAmB,oBAAoB,EAC9D,0BAA0B,mBAAmB,uBAAuB,EACpE,eAAe,mBAAmB,YAAY,EAC9C,WAAW,mBAAmB,QAAQ,EACtC,eAAe,mBAAmB,YAAY,EAC9C,eAAe,mBAAmB,YAAY,EAC9C,WAAW,mBAAmB,QAAQ,EACtC,qBAAqB,mBAAmB,kBAAkB,EAC1D,WAAW,mBAAmB,QAAQ,EACtC,iBAAiB,mBAAmB,cAAc,EAClD,aAAa,mBAAmB,UAAU,EAC1C,aAAa,mBAAmB,UAAU,EAC1C,cAAc,mBAAmB,WAAW;IAC9C,IAAI,UAAU,6JAAA,CAAA,SAAY;IAC1B,IAAI,eAAe,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD;4CAAE;YACzB,OAAO;QACT;2CACA,uDAAuD;IACvD;QAAC;QAAM;KAAS;4CAAE,SAAU,IAAI,EAAE,IAAI;YACpC,OAAO,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;QACvC;;IAEA,+DAA+D;IAC/D,IAAI,oBAAoB,6JAAA,CAAA,UAAa;iDAAC;YACpC,IAAI,CAAC,WAAW;gBACd,OAAO;YACT;YACA,OAAO;gBACL,SAAS;gBACT,aAAa;YACf;QACF;gDAAG;QAAC;QAAW;QAAa;KAAgB;IAE5C,+DAA+D;IAC/D,6JAAA,CAAA,YAAe;gCAAC;YACd,2CAA2C;YAC3C,IAAI,QAAQ,CAAC,YAAY,YAAY,MAAM,EAAE;gBAC3C,IAAI;gBACJ,CAAC,mBAAmB,QAAQ,OAAO,MAAM,QAAQ,qBAAqB,KAAK,KAAK,iBAAiB,QAAQ,CAAC;oBACxG,KAAK,WAAW,CAAC,EAAE;gBACrB;YACF;QACA,uDAAuD;QACzD;+BAAG;QAAC;KAAK;IAET,+DAA+D;IAC/D,IAAI,kBAAkB,SAAS,gBAAgB,KAAK;QAClD,MAAM,cAAc;IACtB;IACA,IAAI,mBAAmB,SAAS,iBAAiB,EAAE,EAAE,IAAI;QACvD,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,aAAa,CAAA,GAAA,mKAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YACtC;QACF;QACA,SAAS,KAAK,GAAG,EAAE;YACjB,UAAU,CAAC,YAAY,QAAQ,CAAC,KAAK,GAAG;QAC1C;QACA,IAAI,CAAC,UAAU;YACb,WAAW;QACb;IACF;IAEA,+DAA+D;IAC/D,IAAI,kBAAkB,6JAAA,CAAA,WAAc,CAAC,0BACnC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,eAAe,gBAAgB,CAAC,EAAE,EAClC,kBAAkB,gBAAgB,CAAC,EAAE;IACvC,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,OACpC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,qBAAqB,gBAAgB,CAAC,EAAE,EACxC,wBAAwB,gBAAgB,CAAC,EAAE;IAC7C,IAAI,qBAAqB,6JAAA,CAAA,UAAa;kDAAC;YACrC,IAAI,kBAAkB;gBACpB,OAAO,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;YAC5B;YACA,OAAO,cAAc,qBAAqB;QAC5C;iDAAG;QAAC;QAAc;QAAoB;QAAkB;KAAY;IACpE,IAAI,mBAAmB,SAAS,iBAAiB,IAAI;QACnD,gBAAgB;QAChB,sBAAsB;QACtB,IAAI,cAAc;YAChB,aAAa;QACf;IACF;IAEA,+DAA+D;IAC/D,IAAI,mBAAmB,OAAO,aAAa,WAAW;IACtD,IAAI,iBAAiB,SAAS,eAAe,QAAQ;QACnD,IAAI,CAAC,kBAAkB;YACrB,OAAO;QACT;QACA,OAAO,OAAO,QAAQ,CAAC,mBAAmB,EAAE,WAAW,GAAG,QAAQ,CAAC;IACrE;IACA,6JAAA,CAAA,YAAe;gCAAC;YACd,IAAI,aAAa;gBACf,sBAAsB,CAAA,GAAA,mKAAA,CAAA,aAAU,AAAD,EAAE,UAAU;YAC7C;QACA,uDAAuD;QACzD;+BAAG;QAAC;KAAY;IAEhB,+DAA+D;IAC/D,8EAA8E;IAC9E,IAAI,mBAAmB,6JAAA,CAAA,WAAc;iDAAC;YAClC,OAAO,IAAI;QACb;iDACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,gBAAgB,gBAAgB,CAAC,EAAE,EACnC,mBAAmB,gBAAgB,CAAC,EAAE;IACxC,6JAAA,CAAA,YAAe;gCAAC;YACd,IAAI,cAAc;gBAChB,iBAAiB,IAAI;YACvB;QACF;+BAAG;QAAC;KAAa;IACjB,SAAS,qBAAqB,IAAI;QAChC,IAAI,QAAQ,IAAI,CAAC,WAAW,KAAK,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,CAAC,QAAQ;YAC7B,IAAI,SAAS,cAAc,GAAG,CAAC;YAC/B,IAAI,SAAS,CAAC,OAAO,QAAQ,IAAI,EAAE,EAAE,MAAM,KAAK;YAChD,IAAI,CAAC,QAAQ;gBACX,IAAI,oBAAoB,OAAO,QAAQ,CAAC,MAAM,CAAC,SAAU,aAAa;oBACpE,OAAO,CAAC,cAAc,IAAI,CAAC,QAAQ,IAAI,CAAC,cAAc,IAAI,CAAC,eAAe,IAAI,CAAC,YAAY,QAAQ,CAAC,cAAc,IAAI,CAAC,WAAW,KAAK,CAAC;gBAC1I;gBACA,IAAI,yBAAyB,kBAAkB,MAAM;gBACrD,cAAc,GAAG,CAAC,OAAO,yBAAyB;YACpD,OAAO;gBACL,cAAc,GAAG,CAAC,OAAO;YAC3B;QACF;QACA,OAAO,cAAc,GAAG,CAAC;IAC3B;IACA,IAAI,eAAe,CAAA,GAAA,+LAAA,CAAA,WAAQ,AAAD;6CAAE,SAAU,IAAI;YACxC,IAAI,YAAY,IAAI,CAAC,WAAW,KAAK,CAAC;YACtC,IAAI,YAAY,QAAQ,CAAC,YAAY;gBACnC,OAAO;YACT;YACA,IAAI,iBAAiB,MAAM;gBACzB,OAAO;YACT;YACA,IAAI,gBAAgB,GAAG;gBACrB,OAAO;YACT;YAEA,wCAAwC;YACxC,IAAI,iBAAiB,cAAc;gBACjC,OAAO,qBAAqB;YAC9B;YACA,OAAO;QACT;;IAEA,kFAAkF;IAClF,IAAI,uBAAuB,SAAS,qBAAqB,KAAK;QAC5D,IAAI,YAAY,CAAA,GAAA,oLAAA,CAAA,UAA0B,AAAD,EAAE,QACzC;QACF,IAAI;YACF,IAAK,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,UAAU,CAAC,EAAE,EAAE,IAAI,EAAG;gBAClD,IAAI,OAAO,MAAM,KAAK;gBACtB,IAAI,KAAK,QAAQ,IAAI,KAAK,UAAU,KAAK,OAAO;oBAC9C;gBACF;gBACA,IAAI,aAAa;oBACf,IAAI,eAAe,OAAO;wBACxB,OAAO;oBACT;gBACF,OAAO;oBACL,OAAO;gBACT;gBACA,IAAI,IAAI,CAAC,WAAW,QAAQ,CAAC,EAAE;oBAC7B,IAAI,kBAAkB,qBAAqB,IAAI,CAAC,WAAW,QAAQ,CAAC;oBACpE,IAAI,iBAAiB;wBACnB,OAAO;oBACT;gBACF;YACF;QACF,EAAE,OAAO,KAAK;YACZ,UAAU,CAAC,CAAC;QACd,SAAU;YACR,UAAU,CAAC;QACb;QACA,OAAO;IACT;IAEA,+DAA+D;IAC/D,IAAI,mBAAmB,6JAAA,CAAA,WAAc,CAAC,OACpC,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,YAAY,gBAAgB,CAAC,EAAE,EAC/B,eAAe,gBAAgB,CAAC,EAAE;IACpC,IAAI,eAAe,WAAW,CAAC,UAAU;IACzC,6JAAA,CAAA,YAAe;gCAAC;YACd,IAAI,CAAC,MAAM;gBACT;YACF;YACA,IAAI,gBAAgB;YACpB,IAAI,eAAe,SAAS;gBAC1B,IAAI,YAAY,qBAAqB;gBACrC,OAAO,YAAY,SAAS,CAAC,WAAW,KAAK,CAAC,GAAG;YACnD;YAEA,wCAAwC;YACxC,IAAI,CAAC,YAAY,YAAY,MAAM,IAAI,CAAC,aAAa;gBACnD,gBAAgB,WAAW,CAAC,EAAE;YAChC,OAAO;gBACL,gBAAgB;YAClB;YACA,aAAa;QACf;+BAAG;QAAC;QAAM;KAAY;IAEtB,+DAA+D;IAC/D,6JAAA,CAAA,sBAAyB,CAAC;0CAAK;YAC7B,IAAI;YACJ,OAAO;gBACL,UAAU,CAAC,oBAAoB,QAAQ,OAAO,MAAM,QAAQ,sBAAsB,KAAK,IAAI,KAAK,IAAI,kBAAkB,QAAQ;gBAC9H,WAAW,SAAS,UAAU,KAAK;oBACjC,IAAI;oBACJ,IAAI,QAAQ,MAAM,KAAK;oBACvB,OAAQ;wBACN,iBAAiB;wBACjB,KAAK,8IAAA,CAAA,UAAO,CAAC,EAAE;wBACf,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;wBACjB,KAAK,8IAAA,CAAA,UAAO,CAAC,IAAI;wBACjB,KAAK,8IAAA,CAAA,UAAO,CAAC,KAAK;4BAChB,CAAC,oBAAoB,QAAQ,OAAO,MAAM,QAAQ,sBAAsB,KAAK,KAAK,kBAAkB,SAAS,CAAC;4BAC9G;wBAEF,kBAAkB;wBAClB,KAAK,8IAAA,CAAA,UAAO,CAAC,KAAK;4BAChB;gCACE,IAAI,cAAc;oCAChB,IAAI,iBAAiB,aAAa,aAAa,IAAI;oCACnD,IAAI,OAAO,CAAC,iBAAiB,QAAQ,iBAAiB,KAAK,IAAI,KAAK,IAAI,aAAa,IAAI,KAAK,CAAC,GAC7F,aAAa,KAAK,UAAU,EAC5B,QAAQ,KAAK,KAAK,EAClB,WAAW,KAAK,QAAQ;oCAC1B,IAAI,eAAe,SAAS,CAAC,YAAY,CAAC,gBAAgB;wCACxD,iBAAiB,MAAM;4CACrB,MAAM;gDACJ,KAAK;4CACP;4CACA,UAAU,CAAC,YAAY,QAAQ,CAAC;wCAClC;oCACF;gCACF;gCACA;4BACF;wBAEF,YAAY;wBACZ,KAAK,8IAAA,CAAA,UAAO,CAAC,GAAG;4BACd;gCACE,WAAW;4BACb;oBACJ;gBACF;gBACA,SAAS,SAAS,WAAW;YAC/B;QACF;;IACA,IAAI,gBAAgB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD;6CAAE;YAC1B,OAAO,cAAc,QAAQ;QAC/B;4CAAG;QAAC;QAAa,oBAAoB;KAAa;6CAAE,SAAU,KAAK,EAAE,KAAK;YACxE,IAAI,QAAQ,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,IAChC,iBAAiB,KAAK,CAAC,EAAE;YAC3B,IAAI,QAAQ,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,OAAO,IAChC,kBAAkB,KAAK,CAAC,EAAE,EAC1B,gCAAgC,KAAK,CAAC,EAAE;YAC1C,OAAO,mBAAmB,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,6BAA6B;QAClG;;IACA,IAAI,eAAe,gBAAgB,WAAW;IAE9C,+DAA+D;IAC/D,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;YAC7C,MAAM;YACN,WAAW,GAAG,MAAM,CAAC,WAAW;YAChC,aAAa;QACf,GAAG;IACL;IACA,IAAI,YAAY;QACd,YAAY;IACd;IACA,IAAI,gBAAgB;QAClB,UAAU,UAAU,GAAG;IACzB;IACA,IAAI,oBAAoB;QACtB,UAAU,YAAY,GAAG;IAC3B;IACA,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAC7C,aAAa;IACf,GAAG,gBAAgB,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QAClE,OAAO;QACP,aAAa;IACf,GAAG,aAAa,IAAI,CAAC,KAAK,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,mJAAA,CAAA,kBAAe,CAAC,QAAQ,EAAE;QACtF,OAAO;YACL,cAAc;QAChB;IACF,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,4JAAA,CAAA,UAAI,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACjD,KAAK;QACL,WAAW;QACX,WAAW,GAAG,MAAM,CAAC,WAAW;QAChC,UAAU;QACV,QAAQ;QACR,YAAY;QACZ,kBAAkB;QAClB,SAAS,YAAY,SAAS,6BAA6B;QAC3D,UAAU;QACV,MAAM;QACN,UAAU;QACV,cAAc;QACd,UAAU;QACV,UAAU;QACV,QAAQ;QACR,WAAW;QAGX,WAAW;QACX,eAAe;QACf,aAAa;QACb,cAAc,CAAC,YAAY,cAAc,EAAE;QAC3C,kBAAkB;QAClB,aAAa;IACf,GAAG,WAAW;QACZ,kBAAkB;QAClB,gBAAgB;QAChB,UAAU;QACV,SAAS;QACT,UAAU;QACV,QAAQ;QACR,gBAAgB;QAChB,cAAc;QACd,UAAU;IACZ;AACF;AACA,IAAI,gBAAgB,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC;AAClD,wCAA2C;IACzC,cAAc,WAAW,GAAG;AAC9B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/utils/strategyUtil.js"], "sourcesContent": ["import { isCheckDisabled } from \"./valueUtil\";\nexport var SHOW_ALL = 'SHOW_ALL';\nexport var SHOW_PARENT = 'SHOW_PARENT';\nexport var SHOW_CHILD = 'SHOW_CHILD';\nexport function formatStrategyValues(values, strategy, keyEntities, fieldNames) {\n  var valueSet = new Set(values);\n  if (strategy === SHOW_CHILD) {\n    return values.filter(function (key) {\n      var entity = keyEntities[key];\n      return !entity || !entity.children || !entity.children.some(function (_ref) {\n        var node = _ref.node;\n        return valueSet.has(node[fieldNames.value]);\n      }) || !entity.children.every(function (_ref2) {\n        var node = _ref2.node;\n        return isCheckDisabled(node) || valueSet.has(node[fieldNames.value]);\n      });\n    });\n  }\n  if (strategy === SHOW_PARENT) {\n    return values.filter(function (key) {\n      var entity = keyEntities[key];\n      var parent = entity ? entity.parent : null;\n      return !parent || isCheckDisabled(parent.node) || !valueSet.has(parent.key);\n    });\n  }\n  return values;\n}"], "names": [], "mappings": ";;;;;;AAAA;;AACO,IAAI,WAAW;AACf,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,SAAS,qBAAqB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU;IAC5E,IAAI,WAAW,IAAI,IAAI;IACvB,IAAI,aAAa,YAAY;QAC3B,OAAO,OAAO,MAAM,CAAC,SAAU,GAAG;YAChC,IAAI,SAAS,WAAW,CAAC,IAAI;YAC7B,OAAO,CAAC,UAAU,CAAC,OAAO,QAAQ,IAAI,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAU,IAAI;gBACxE,IAAI,OAAO,KAAK,IAAI;gBACpB,OAAO,SAAS,GAAG,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC;YAC5C,MAAM,CAAC,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAU,KAAK;gBAC1C,IAAI,OAAO,MAAM,IAAI;gBACrB,OAAO,CAAA,GAAA,mKAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,SAAS,GAAG,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC;YACrE;QACF;IACF;IACA,IAAI,aAAa,aAAa;QAC5B,OAAO,OAAO,MAAM,CAAC,SAAU,GAAG;YAChC,IAAI,SAAS,WAAW,CAAC,IAAI;YAC7B,IAAI,SAAS,SAAS,OAAO,MAAM,GAAG;YACtC,OAAO,CAAC,UAAU,CAAA,GAAA,mKAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,OAAO,GAAG;QAC5E;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/utils/warningPropsUtil.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport warning from \"rc-util/es/warning\";\nimport { toArray } from \"./valueUtil\";\nfunction warningProps(props) {\n  var searchPlaceholder = props.searchPlaceholder,\n    treeCheckStrictly = props.treeCheckStrictly,\n    treeCheckable = props.treeCheckable,\n    labelInValue = props.labelInValue,\n    value = props.value,\n    multiple = props.multiple,\n    showCheckedStrategy = props.showCheckedStrategy,\n    maxCount = props.maxCount;\n  warning(!searchPlaceholder, '`searchPlaceholder` has been removed.');\n  if (treeCheckStrictly && labelInValue === false) {\n    warning(false, '`treeCheckStrictly` will force set `labelInValue` to `true`.');\n  }\n  if (labelInValue || treeCheckStrictly) {\n    warning(toArray(value).every(function (val) {\n      return val && _typeof(val) === 'object' && 'value' in val;\n    }), 'Invalid prop `value` supplied to `TreeSelect`. You should use { label: string, value: string | number } or [{ label: string, value: string | number }] instead.');\n  }\n  if (treeCheckStrictly || multiple || treeCheckable) {\n    warning(!value || Array.isArray(value), '`value` should be an array when `TreeSelect` is checkable or multiple.');\n  } else {\n    warning(!Array.isArray(value), '`value` should not be array when `TreeSelect` is single mode.');\n  }\n  if (maxCount && (showCheckedStrategy === 'SHOW_ALL' && !treeCheckStrictly || showCheckedStrategy === 'SHOW_PARENT')) {\n    warning(false, '`maxCount` not work with `showCheckedStrategy=SHOW_ALL` (when `treeCheckStrictly=false`) or `showCheckedStrategy=SHOW_PARENT`.');\n  }\n}\nexport default warningProps;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,aAAa,KAAK;IACzB,IAAI,oBAAoB,MAAM,iBAAiB,EAC7C,oBAAoB,MAAM,iBAAiB,EAC3C,gBAAgB,MAAM,aAAa,EACnC,eAAe,MAAM,YAAY,EACjC,QAAQ,MAAM,KAAK,EACnB,WAAW,MAAM,QAAQ,EACzB,sBAAsB,MAAM,mBAAmB,EAC/C,WAAW,MAAM,QAAQ;IAC3B,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,mBAAmB;IAC5B,IAAI,qBAAqB,iBAAiB,OAAO;QAC/C,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IACjB;IACA,IAAI,gBAAgB,mBAAmB;QACrC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,mKAAA,CAAA,UAAO,AAAD,EAAE,OAAO,KAAK,CAAC,SAAU,GAAG;YACxC,OAAO,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,YAAY,WAAW;QACxD,IAAI;IACN;IACA,IAAI,qBAAqB,YAAY,eAAe;QAClD,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,SAAS,MAAM,OAAO,CAAC,QAAQ;IAC1C,OAAO;QACL,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAC,MAAM,OAAO,CAAC,QAAQ;IACjC;IACA,IAAI,YAAY,CAAC,wBAAwB,cAAc,CAAC,qBAAqB,wBAAwB,aAAa,GAAG;QACnH,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IACjB;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/TreeSelect.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar _excluded = [\"id\", \"prefixCls\", \"value\", \"defaultValue\", \"onChange\", \"onSelect\", \"onDeselect\", \"searchValue\", \"inputValue\", \"onSearch\", \"autoClearSearchValue\", \"filterTreeNode\", \"treeNodeFilterProp\", \"showCheckedStrategy\", \"treeNodeLabelProp\", \"multiple\", \"treeCheckable\", \"treeCheckStrictly\", \"labelInValue\", \"maxCount\", \"fieldNames\", \"treeDataSimpleMode\", \"treeData\", \"children\", \"loadData\", \"treeLoadedKeys\", \"onTreeLoad\", \"treeDefaultExpandAll\", \"treeExpandedKeys\", \"treeDefaultExpandedKeys\", \"onTreeExpand\", \"treeExpandAction\", \"virtual\", \"listHeight\", \"listItemHeight\", \"listItemScrollOffset\", \"onDropdownVisibleChange\", \"dropdownMatchSelectWidth\", \"treeLine\", \"treeIcon\", \"showTreeIcon\", \"switcherIcon\", \"treeMotion\", \"treeTitleRender\", \"onPopupScroll\"];\nimport { BaseSelect } from 'rc-select';\nimport useId from \"rc-select/es/hooks/useId\";\nimport { conductCheck } from \"rc-tree/es/utils/conductUtil\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport * as React from 'react';\nimport useCache from \"./hooks/useCache\";\nimport useCheckedKeys from \"./hooks/useCheckedKeys\";\nimport useDataEntities from \"./hooks/useDataEntities\";\nimport useFilterTreeData from \"./hooks/useFilterTreeData\";\nimport useRefFunc from \"./hooks/useRefFunc\";\nimport useTreeData from \"./hooks/useTreeData\";\nimport LegacyContext from \"./LegacyContext\";\nimport OptionList from \"./OptionList\";\nimport TreeNode from \"./TreeNode\";\nimport TreeSelectContext from \"./TreeSelectContext\";\nimport { fillAdditionalInfo, fillLegacyProps } from \"./utils/legacyUtil\";\nimport { formatStrategyValues, SHOW_ALL, SHOW_CHILD, SHOW_PARENT } from \"./utils/strategyUtil\";\nimport { fillFieldNames, isNil, toArray } from \"./utils/valueUtil\";\nimport warningProps from \"./utils/warningPropsUtil\";\nfunction isRawValue(value) {\n  return !value || _typeof(value) !== 'object';\n}\nvar TreeSelect = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var id = props.id,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-tree-select' : _props$prefixCls,\n    value = props.value,\n    defaultValue = props.defaultValue,\n    onChange = props.onChange,\n    onSelect = props.onSelect,\n    onDeselect = props.onDeselect,\n    searchValue = props.searchValue,\n    inputValue = props.inputValue,\n    onSearch = props.onSearch,\n    _props$autoClearSearc = props.autoClearSearchValue,\n    autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc,\n    filterTreeNode = props.filterTreeNode,\n    _props$treeNodeFilter = props.treeNodeFilterProp,\n    treeNodeFilterProp = _props$treeNodeFilter === void 0 ? 'value' : _props$treeNodeFilter,\n    showCheckedStrategy = props.showCheckedStrategy,\n    treeNodeLabelProp = props.treeNodeLabelProp,\n    multiple = props.multiple,\n    treeCheckable = props.treeCheckable,\n    treeCheckStrictly = props.treeCheckStrictly,\n    labelInValue = props.labelInValue,\n    maxCount = props.maxCount,\n    fieldNames = props.fieldNames,\n    treeDataSimpleMode = props.treeDataSimpleMode,\n    treeData = props.treeData,\n    children = props.children,\n    loadData = props.loadData,\n    treeLoadedKeys = props.treeLoadedKeys,\n    onTreeLoad = props.onTreeLoad,\n    treeDefaultExpandAll = props.treeDefaultExpandAll,\n    treeExpandedKeys = props.treeExpandedKeys,\n    treeDefaultExpandedKeys = props.treeDefaultExpandedKeys,\n    onTreeExpand = props.onTreeExpand,\n    treeExpandAction = props.treeExpandAction,\n    virtual = props.virtual,\n    _props$listHeight = props.listHeight,\n    listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight,\n    _props$listItemHeight = props.listItemHeight,\n    listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight,\n    _props$listItemScroll = props.listItemScrollOffset,\n    listItemScrollOffset = _props$listItemScroll === void 0 ? 0 : _props$listItemScroll,\n    onDropdownVisibleChange = props.onDropdownVisibleChange,\n    _props$dropdownMatchS = props.dropdownMatchSelectWidth,\n    dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS,\n    treeLine = props.treeLine,\n    treeIcon = props.treeIcon,\n    showTreeIcon = props.showTreeIcon,\n    switcherIcon = props.switcherIcon,\n    treeMotion = props.treeMotion,\n    treeTitleRender = props.treeTitleRender,\n    onPopupScroll = props.onPopupScroll,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var mergedId = useId(id);\n  var treeConduction = treeCheckable && !treeCheckStrictly;\n  var mergedCheckable = treeCheckable || treeCheckStrictly;\n  var mergedLabelInValue = treeCheckStrictly || labelInValue;\n  var mergedMultiple = mergedCheckable || multiple;\n  var _useMergedState = useMergedState(defaultValue, {\n      value: value\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    internalValue = _useMergedState2[0],\n    setInternalValue = _useMergedState2[1];\n\n  // `multiple` && `!treeCheckable` should be show all\n  var mergedShowCheckedStrategy = React.useMemo(function () {\n    if (!treeCheckable) {\n      return SHOW_ALL;\n    }\n    return showCheckedStrategy || SHOW_CHILD;\n  }, [showCheckedStrategy, treeCheckable]);\n\n  // ========================== Warning ===========================\n  if (process.env.NODE_ENV !== 'production') {\n    warningProps(props);\n  }\n\n  // ========================= FieldNames =========================\n  var mergedFieldNames = React.useMemo(function () {\n    return fillFieldNames(fieldNames);\n  }, /* eslint-disable react-hooks/exhaustive-deps */\n  [JSON.stringify(fieldNames)]\n  /* eslint-enable react-hooks/exhaustive-deps */);\n\n  // =========================== Search ===========================\n  var _useMergedState3 = useMergedState('', {\n      value: searchValue !== undefined ? searchValue : inputValue,\n      postState: function postState(search) {\n        return search || '';\n      }\n    }),\n    _useMergedState4 = _slicedToArray(_useMergedState3, 2),\n    mergedSearchValue = _useMergedState4[0],\n    setSearchValue = _useMergedState4[1];\n  var onInternalSearch = function onInternalSearch(searchText) {\n    setSearchValue(searchText);\n    onSearch === null || onSearch === void 0 || onSearch(searchText);\n  };\n\n  // ============================ Data ============================\n  // `useTreeData` only do convert of `children` or `simpleMode`.\n  // Else will return origin `treeData` for perf consideration.\n  // Do not do anything to loop the data.\n  var mergedTreeData = useTreeData(treeData, children, treeDataSimpleMode);\n  var _useDataEntities = useDataEntities(mergedTreeData, mergedFieldNames),\n    keyEntities = _useDataEntities.keyEntities,\n    valueEntities = _useDataEntities.valueEntities;\n\n  /** Get `missingRawValues` which not exist in the tree yet */\n  var splitRawValues = React.useCallback(function (newRawValues) {\n    var missingRawValues = [];\n    var existRawValues = [];\n\n    // Keep missing value in the cache\n    newRawValues.forEach(function (val) {\n      if (valueEntities.has(val)) {\n        existRawValues.push(val);\n      } else {\n        missingRawValues.push(val);\n      }\n    });\n    return {\n      missingRawValues: missingRawValues,\n      existRawValues: existRawValues\n    };\n  }, [valueEntities]);\n\n  // Filtered Tree\n  var filteredTreeData = useFilterTreeData(mergedTreeData, mergedSearchValue, {\n    fieldNames: mergedFieldNames,\n    treeNodeFilterProp: treeNodeFilterProp,\n    filterTreeNode: filterTreeNode\n  });\n\n  // =========================== Label ============================\n  var getLabel = React.useCallback(function (item) {\n    if (item) {\n      if (treeNodeLabelProp) {\n        return item[treeNodeLabelProp];\n      }\n\n      // Loop from fieldNames\n      var titleList = mergedFieldNames._title;\n      for (var i = 0; i < titleList.length; i += 1) {\n        var title = item[titleList[i]];\n        if (title !== undefined) {\n          return title;\n        }\n      }\n    }\n  }, [mergedFieldNames, treeNodeLabelProp]);\n\n  // ========================= Wrap Value =========================\n  var toLabeledValues = React.useCallback(function (draftValues) {\n    var values = toArray(draftValues);\n    return values.map(function (val) {\n      if (isRawValue(val)) {\n        return {\n          value: val\n        };\n      }\n      return val;\n    });\n  }, []);\n  var convert2LabelValues = React.useCallback(function (draftValues) {\n    var values = toLabeledValues(draftValues);\n    return values.map(function (item) {\n      var rawLabel = item.label;\n      var rawValue = item.value,\n        rawHalfChecked = item.halfChecked;\n      var rawDisabled;\n      var entity = valueEntities.get(rawValue);\n\n      // Fill missing label & status\n      if (entity) {\n        var _rawLabel;\n        rawLabel = treeTitleRender ? treeTitleRender(entity.node) : (_rawLabel = rawLabel) !== null && _rawLabel !== void 0 ? _rawLabel : getLabel(entity.node);\n        rawDisabled = entity.node.disabled;\n      } else if (rawLabel === undefined) {\n        // We try to find in current `labelInValue` value\n        var labelInValueItem = toLabeledValues(internalValue).find(function (labeledItem) {\n          return labeledItem.value === rawValue;\n        });\n        rawLabel = labelInValueItem.label;\n      }\n      return {\n        label: rawLabel,\n        value: rawValue,\n        halfChecked: rawHalfChecked,\n        disabled: rawDisabled\n      };\n    });\n  }, [valueEntities, getLabel, toLabeledValues, internalValue]);\n\n  // =========================== Values ===========================\n  var rawMixedLabeledValues = React.useMemo(function () {\n    return toLabeledValues(internalValue === null ? [] : internalValue);\n  }, [toLabeledValues, internalValue]);\n\n  // Split value into full check and half check\n  var _React$useMemo = React.useMemo(function () {\n      var fullCheckValues = [];\n      var halfCheckValues = [];\n      rawMixedLabeledValues.forEach(function (item) {\n        if (item.halfChecked) {\n          halfCheckValues.push(item);\n        } else {\n          fullCheckValues.push(item);\n        }\n      });\n      return [fullCheckValues, halfCheckValues];\n    }, [rawMixedLabeledValues]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    rawLabeledValues = _React$useMemo2[0],\n    rawHalfLabeledValues = _React$useMemo2[1];\n\n  // const [mergedValues] = useCache(rawLabeledValues);\n  var rawValues = React.useMemo(function () {\n    return rawLabeledValues.map(function (item) {\n      return item.value;\n    });\n  }, [rawLabeledValues]);\n\n  // Convert value to key. Will fill missed keys for conduct check.\n  var _useCheckedKeys = useCheckedKeys(rawLabeledValues, rawHalfLabeledValues, treeConduction, keyEntities),\n    _useCheckedKeys2 = _slicedToArray(_useCheckedKeys, 2),\n    rawCheckedValues = _useCheckedKeys2[0],\n    rawHalfCheckedValues = _useCheckedKeys2[1];\n\n  // Convert rawCheckedKeys to check strategy related values\n  var displayValues = React.useMemo(function () {\n    // Collect keys which need to show\n    var displayKeys = formatStrategyValues(rawCheckedValues, mergedShowCheckedStrategy, keyEntities, mergedFieldNames);\n\n    // Convert to value and filled with label\n    var values = displayKeys.map(function (key) {\n      var _keyEntities$key$node, _keyEntities$key;\n      return (_keyEntities$key$node = (_keyEntities$key = keyEntities[key]) === null || _keyEntities$key === void 0 || (_keyEntities$key = _keyEntities$key.node) === null || _keyEntities$key === void 0 ? void 0 : _keyEntities$key[mergedFieldNames.value]) !== null && _keyEntities$key$node !== void 0 ? _keyEntities$key$node : key;\n    });\n\n    // Back fill with origin label\n    var labeledValues = values.map(function (val) {\n      var targetItem = rawLabeledValues.find(function (item) {\n        return item.value === val;\n      });\n      var label = labelInValue ? targetItem === null || targetItem === void 0 ? void 0 : targetItem.label : treeTitleRender === null || treeTitleRender === void 0 ? void 0 : treeTitleRender(targetItem);\n      return {\n        value: val,\n        label: label\n      };\n    });\n    var rawDisplayValues = convert2LabelValues(labeledValues);\n    var firstVal = rawDisplayValues[0];\n    if (!mergedMultiple && firstVal && isNil(firstVal.value) && isNil(firstVal.label)) {\n      return [];\n    }\n    return rawDisplayValues.map(function (item) {\n      var _item$label;\n      return _objectSpread(_objectSpread({}, item), {}, {\n        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [mergedFieldNames, mergedMultiple, rawCheckedValues, rawLabeledValues, convert2LabelValues, mergedShowCheckedStrategy, keyEntities]);\n  var _useCache = useCache(displayValues),\n    _useCache2 = _slicedToArray(_useCache, 1),\n    cachedDisplayValues = _useCache2[0];\n\n  // ========================== MaxCount ==========================\n  var mergedMaxCount = React.useMemo(function () {\n    if (mergedMultiple && (mergedShowCheckedStrategy === 'SHOW_CHILD' || treeCheckStrictly || !treeCheckable)) {\n      return maxCount;\n    }\n    return null;\n  }, [maxCount, mergedMultiple, treeCheckStrictly, mergedShowCheckedStrategy, treeCheckable]);\n\n  // =========================== Change ===========================\n  var triggerChange = useRefFunc(function (newRawValues, extra, source) {\n    var formattedKeyList = formatStrategyValues(newRawValues, mergedShowCheckedStrategy, keyEntities, mergedFieldNames);\n\n    // Not allow pass with `maxCount`\n    if (mergedMaxCount && formattedKeyList.length > mergedMaxCount) {\n      return;\n    }\n    var labeledValues = convert2LabelValues(newRawValues);\n    setInternalValue(labeledValues);\n\n    // Clean up if needed\n    if (autoClearSearchValue) {\n      setSearchValue('');\n    }\n\n    // Generate rest parameters is costly, so only do it when necessary\n    if (onChange) {\n      var eventValues = newRawValues;\n      if (treeConduction) {\n        eventValues = formattedKeyList.map(function (key) {\n          var entity = valueEntities.get(key);\n          return entity ? entity.node[mergedFieldNames.value] : key;\n        });\n      }\n      var _ref = extra || {\n          triggerValue: undefined,\n          selected: undefined\n        },\n        triggerValue = _ref.triggerValue,\n        selected = _ref.selected;\n      var returnRawValues = eventValues;\n\n      // We need fill half check back\n      if (treeCheckStrictly) {\n        var halfValues = rawHalfLabeledValues.filter(function (item) {\n          return !eventValues.includes(item.value);\n        });\n        returnRawValues = [].concat(_toConsumableArray(returnRawValues), _toConsumableArray(halfValues));\n      }\n      var returnLabeledValues = convert2LabelValues(returnRawValues);\n      var additionalInfo = {\n        // [Legacy] Always return as array contains label & value\n        preValue: rawLabeledValues,\n        triggerValue: triggerValue\n      };\n\n      // [Legacy] Fill legacy data if user query.\n      // This is expansive that we only fill when user query\n      // https://github.com/react-component/tree-select/blob/fe33eb7c27830c9ac70cd1fdb1ebbe7bc679c16a/src/Select.jsx\n      var showPosition = true;\n      if (treeCheckStrictly || source === 'selection' && !selected) {\n        showPosition = false;\n      }\n      fillAdditionalInfo(additionalInfo, triggerValue, newRawValues, mergedTreeData, showPosition, mergedFieldNames);\n      if (mergedCheckable) {\n        additionalInfo.checked = selected;\n      } else {\n        additionalInfo.selected = selected;\n      }\n      var returnValues = mergedLabelInValue ? returnLabeledValues : returnLabeledValues.map(function (item) {\n        return item.value;\n      });\n      onChange(mergedMultiple ? returnValues : returnValues[0], mergedLabelInValue ? null : returnLabeledValues.map(function (item) {\n        return item.label;\n      }), additionalInfo);\n    }\n  });\n\n  // ========================== Options ===========================\n  /** Trigger by option list */\n  var onOptionSelect = React.useCallback(function (selectedKey, _ref2) {\n    var _node$mergedFieldName;\n    var selected = _ref2.selected,\n      source = _ref2.source;\n    var entity = keyEntities[selectedKey];\n    var node = entity === null || entity === void 0 ? void 0 : entity.node;\n    var selectedValue = (_node$mergedFieldName = node === null || node === void 0 ? void 0 : node[mergedFieldNames.value]) !== null && _node$mergedFieldName !== void 0 ? _node$mergedFieldName : selectedKey;\n\n    // Never be falsy but keep it safe\n    if (!mergedMultiple) {\n      // Single mode always set value\n      triggerChange([selectedValue], {\n        selected: true,\n        triggerValue: selectedValue\n      }, 'option');\n    } else {\n      var newRawValues = selected ? [].concat(_toConsumableArray(rawValues), [selectedValue]) : rawCheckedValues.filter(function (v) {\n        return v !== selectedValue;\n      });\n\n      // Add keys if tree conduction\n      if (treeConduction) {\n        // Should keep missing values\n        var _splitRawValues = splitRawValues(newRawValues),\n          missingRawValues = _splitRawValues.missingRawValues,\n          existRawValues = _splitRawValues.existRawValues;\n        var keyList = existRawValues.map(function (val) {\n          return valueEntities.get(val).key;\n        });\n\n        // Conduction by selected or not\n        var checkedKeys;\n        if (selected) {\n          var _conductCheck = conductCheck(keyList, true, keyEntities);\n          checkedKeys = _conductCheck.checkedKeys;\n        } else {\n          var _conductCheck2 = conductCheck(keyList, {\n            checked: false,\n            halfCheckedKeys: rawHalfCheckedValues\n          }, keyEntities);\n          checkedKeys = _conductCheck2.checkedKeys;\n        }\n\n        // Fill back of keys\n        newRawValues = [].concat(_toConsumableArray(missingRawValues), _toConsumableArray(checkedKeys.map(function (key) {\n          return keyEntities[key].node[mergedFieldNames.value];\n        })));\n      }\n      triggerChange(newRawValues, {\n        selected: selected,\n        triggerValue: selectedValue\n      }, source || 'option');\n    }\n\n    // Trigger select event\n    if (selected || !mergedMultiple) {\n      onSelect === null || onSelect === void 0 || onSelect(selectedValue, fillLegacyProps(node));\n    } else {\n      onDeselect === null || onDeselect === void 0 || onDeselect(selectedValue, fillLegacyProps(node));\n    }\n  }, [splitRawValues, valueEntities, keyEntities, mergedFieldNames, mergedMultiple, rawValues, triggerChange, treeConduction, onSelect, onDeselect, rawCheckedValues, rawHalfCheckedValues, maxCount]);\n\n  // ========================== Dropdown ==========================\n  var onInternalDropdownVisibleChange = React.useCallback(function (open) {\n    if (onDropdownVisibleChange) {\n      var legacyParam = {};\n      Object.defineProperty(legacyParam, 'documentClickClose', {\n        get: function get() {\n          warning(false, 'Second param of `onDropdownVisibleChange` has been removed.');\n          return false;\n        }\n      });\n      onDropdownVisibleChange(open, legacyParam);\n    }\n  }, [onDropdownVisibleChange]);\n\n  // ====================== Display Change ========================\n  var onDisplayValuesChange = useRefFunc(function (newValues, info) {\n    var newRawValues = newValues.map(function (item) {\n      return item.value;\n    });\n    if (info.type === 'clear') {\n      triggerChange(newRawValues, {}, 'selection');\n      return;\n    }\n\n    // TreeSelect only have multiple mode which means display change only has remove\n    if (info.values.length) {\n      onOptionSelect(info.values[0].value, {\n        selected: false,\n        source: 'selection'\n      });\n    }\n  });\n\n  // ========================== Context ===========================\n  var treeSelectContext = React.useMemo(function () {\n    return {\n      virtual: virtual,\n      dropdownMatchSelectWidth: dropdownMatchSelectWidth,\n      listHeight: listHeight,\n      listItemHeight: listItemHeight,\n      listItemScrollOffset: listItemScrollOffset,\n      treeData: filteredTreeData,\n      fieldNames: mergedFieldNames,\n      onSelect: onOptionSelect,\n      treeExpandAction: treeExpandAction,\n      treeTitleRender: treeTitleRender,\n      onPopupScroll: onPopupScroll,\n      leftMaxCount: maxCount === undefined ? null : maxCount - cachedDisplayValues.length,\n      leafCountOnly: mergedShowCheckedStrategy === 'SHOW_CHILD' && !treeCheckStrictly && !!treeCheckable,\n      valueEntities: valueEntities\n    };\n  }, [virtual, dropdownMatchSelectWidth, listHeight, listItemHeight, listItemScrollOffset, filteredTreeData, mergedFieldNames, onOptionSelect, treeExpandAction, treeTitleRender, onPopupScroll, maxCount, cachedDisplayValues.length, mergedShowCheckedStrategy, treeCheckStrictly, treeCheckable, valueEntities]);\n\n  // ======================= Legacy Context =======================\n  var legacyContext = React.useMemo(function () {\n    return {\n      checkable: mergedCheckable,\n      loadData: loadData,\n      treeLoadedKeys: treeLoadedKeys,\n      onTreeLoad: onTreeLoad,\n      checkedKeys: rawCheckedValues,\n      halfCheckedKeys: rawHalfCheckedValues,\n      treeDefaultExpandAll: treeDefaultExpandAll,\n      treeExpandedKeys: treeExpandedKeys,\n      treeDefaultExpandedKeys: treeDefaultExpandedKeys,\n      onTreeExpand: onTreeExpand,\n      treeIcon: treeIcon,\n      treeMotion: treeMotion,\n      showTreeIcon: showTreeIcon,\n      switcherIcon: switcherIcon,\n      treeLine: treeLine,\n      treeNodeFilterProp: treeNodeFilterProp,\n      keyEntities: keyEntities\n    };\n  }, [mergedCheckable, loadData, treeLoadedKeys, onTreeLoad, rawCheckedValues, rawHalfCheckedValues, treeDefaultExpandAll, treeExpandedKeys, treeDefaultExpandedKeys, onTreeExpand, treeIcon, treeMotion, showTreeIcon, switcherIcon, treeLine, treeNodeFilterProp, keyEntities]);\n\n  // =========================== Render ===========================\n  return /*#__PURE__*/React.createElement(TreeSelectContext.Provider, {\n    value: treeSelectContext\n  }, /*#__PURE__*/React.createElement(LegacyContext.Provider, {\n    value: legacyContext\n  }, /*#__PURE__*/React.createElement(BaseSelect, _extends({\n    ref: ref\n  }, restProps, {\n    // >>> MISC\n    id: mergedId,\n    prefixCls: prefixCls,\n    mode: mergedMultiple ? 'multiple' : undefined\n    // >>> Display Value\n    ,\n    displayValues: cachedDisplayValues,\n    onDisplayValuesChange: onDisplayValuesChange\n    // >>> Search\n    ,\n    searchValue: mergedSearchValue,\n    onSearch: onInternalSearch\n    // >>> Options\n    ,\n    OptionList: OptionList,\n    emptyOptions: !mergedTreeData.length,\n    onDropdownVisibleChange: onInternalDropdownVisibleChange,\n    dropdownMatchSelectWidth: dropdownMatchSelectWidth\n  }))));\n});\n\n// Assign name for Debug\nif (process.env.NODE_ENV !== 'production') {\n  TreeSelect.displayName = 'TreeSelect';\n}\nvar GenericTreeSelect = TreeSelect;\nGenericTreeSelect.TreeNode = TreeNode;\nGenericTreeSelect.SHOW_ALL = SHOW_ALL;\nGenericTreeSelect.SHOW_PARENT = SHOW_PARENT;\nGenericTreeSelect.SHOW_CHILD = SHOW_CHILD;\nexport default GenericTreeSelect;"], "names": [], "mappings": ";;;AAyGM;AAzGN;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AApBA,IAAI,YAAY;IAAC;IAAM;IAAa;IAAS;IAAgB;IAAY;IAAY;IAAc;IAAe;IAAc;IAAY;IAAwB;IAAkB;IAAsB;IAAuB;IAAqB;IAAY;IAAiB;IAAqB;IAAgB;IAAY;IAAc;IAAsB;IAAY;IAAY;IAAY;IAAkB;IAAc;IAAwB;IAAoB;IAA2B;IAAgB;IAAoB;IAAW;IAAc;IAAkB;IAAwB;IAA2B;IAA4B;IAAY;IAAY;IAAgB;IAAgB;IAAc;IAAmB;CAAgB;;;;;;;;;;;;;;;;;;;;;AAqB5vB,SAAS,WAAW,KAAK;IACvB,OAAO,CAAC,SAAS,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE,WAAW;AACtC;AACA,IAAI,aAAa,WAAW,GAAE,6JAAA,CAAA,aAAgB,CAAC,SAAU,KAAK,EAAE,GAAG;IACjE,IAAI,KAAK,MAAM,EAAE,EACf,mBAAmB,MAAM,SAAS,EAClC,YAAY,qBAAqB,KAAK,IAAI,mBAAmB,kBAC7D,QAAQ,MAAM,KAAK,EACnB,eAAe,MAAM,YAAY,EACjC,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ,EACzB,aAAa,MAAM,UAAU,EAC7B,cAAc,MAAM,WAAW,EAC/B,aAAa,MAAM,UAAU,EAC7B,WAAW,MAAM,QAAQ,EACzB,wBAAwB,MAAM,oBAAoB,EAClD,uBAAuB,0BAA0B,KAAK,IAAI,OAAO,uBACjE,iBAAiB,MAAM,cAAc,EACrC,wBAAwB,MAAM,kBAAkB,EAChD,qBAAqB,0BAA0B,KAAK,IAAI,UAAU,uBAClE,sBAAsB,MAAM,mBAAmB,EAC/C,oBAAoB,MAAM,iBAAiB,EAC3C,WAAW,MAAM,QAAQ,EACzB,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,MAAM,iBAAiB,EAC3C,eAAe,MAAM,YAAY,EACjC,WAAW,MAAM,QAAQ,EACzB,aAAa,MAAM,UAAU,EAC7B,qBAAqB,MAAM,kBAAkB,EAC7C,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ,EACzB,iBAAiB,MAAM,cAAc,EACrC,aAAa,MAAM,UAAU,EAC7B,uBAAuB,MAAM,oBAAoB,EACjD,mBAAmB,MAAM,gBAAgB,EACzC,0BAA0B,MAAM,uBAAuB,EACvD,eAAe,MAAM,YAAY,EACjC,mBAAmB,MAAM,gBAAgB,EACzC,UAAU,MAAM,OAAO,EACvB,oBAAoB,MAAM,UAAU,EACpC,aAAa,sBAAsB,KAAK,IAAI,MAAM,mBAClD,wBAAwB,MAAM,cAAc,EAC5C,iBAAiB,0BAA0B,KAAK,IAAI,KAAK,uBACzD,wBAAwB,MAAM,oBAAoB,EAClD,uBAAuB,0BAA0B,KAAK,IAAI,IAAI,uBAC9D,0BAA0B,MAAM,uBAAuB,EACvD,wBAAwB,MAAM,wBAAwB,EACtD,2BAA2B,0BAA0B,KAAK,IAAI,OAAO,uBACrE,WAAW,MAAM,QAAQ,EACzB,WAAW,MAAM,QAAQ,EACzB,eAAe,MAAM,YAAY,EACjC,eAAe,MAAM,YAAY,EACjC,aAAa,MAAM,UAAU,EAC7B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,YAAY,CAAA,GAAA,kLAAA,CAAA,UAAwB,AAAD,EAAE,OAAO;IAC9C,IAAI,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAK,AAAD,EAAE;IACrB,IAAI,iBAAiB,iBAAiB,CAAC;IACvC,IAAI,kBAAkB,iBAAiB;IACvC,IAAI,qBAAqB,qBAAqB;IAC9C,IAAI,iBAAiB,mBAAmB;IACxC,IAAI,kBAAkB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD,EAAE,cAAc;QAC/C,OAAO;IACT,IACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,gBAAgB,gBAAgB,CAAC,EAAE,EACnC,mBAAmB,gBAAgB,CAAC,EAAE;IAExC,oDAAoD;IACpD,IAAI,4BAA4B,6JAAA,CAAA,UAAa;yDAAC;YAC5C,IAAI,CAAC,eAAe;gBAClB,OAAO,sKAAA,CAAA,WAAQ;YACjB;YACA,OAAO,uBAAuB,sKAAA,CAAA,aAAU;QAC1C;wDAAG;QAAC;QAAqB;KAAc;IAEvC,iEAAiE;IACjE,wCAA2C;QACzC,CAAA,GAAA,0KAAA,CAAA,UAAY,AAAD,EAAE;IACf;IAEA,iEAAiE;IACjE,IAAI,mBAAmB,6JAAA,CAAA,UAAa;gDAAC;YACnC,OAAO,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE;QACxB;+CAAG,8CAA8C,GACjD;QAAC,KAAK,SAAS,CAAC;KAAY;IAG5B,iEAAiE;IACjE,IAAI,mBAAmB,CAAA,GAAA,8JAAA,CAAA,UAAc,AAAD,EAAE,IAAI;QACtC,OAAO,gBAAgB,YAAY,cAAc;QACjD,WAAW,SAAS,UAAU,MAAM;YAClC,OAAO,UAAU;QACnB;IACF,IACA,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,IACpD,oBAAoB,gBAAgB,CAAC,EAAE,EACvC,iBAAiB,gBAAgB,CAAC,EAAE;IACtC,IAAI,mBAAmB,SAAS,iBAAiB,UAAU;QACzD,eAAe;QACf,aAAa,QAAQ,aAAa,KAAK,KAAK,SAAS;IACvD;IAEA,iEAAiE;IACjE,+DAA+D;IAC/D,6DAA6D;IAC7D,uCAAuC;IACvC,IAAI,iBAAiB,CAAA,GAAA,qKAAA,CAAA,UAAW,AAAD,EAAE,UAAU,UAAU;IACrD,IAAI,mBAAmB,CAAA,GAAA,yKAAA,CAAA,UAAe,AAAD,EAAE,gBAAgB,mBACrD,cAAc,iBAAiB,WAAW,EAC1C,gBAAgB,iBAAiB,aAAa;IAEhD,2DAA2D,GAC3D,IAAI,iBAAiB,6JAAA,CAAA,cAAiB;kDAAC,SAAU,YAAY;YAC3D,IAAI,mBAAmB,EAAE;YACzB,IAAI,iBAAiB,EAAE;YAEvB,kCAAkC;YAClC,aAAa,OAAO;0DAAC,SAAU,GAAG;oBAChC,IAAI,cAAc,GAAG,CAAC,MAAM;wBAC1B,eAAe,IAAI,CAAC;oBACtB,OAAO;wBACL,iBAAiB,IAAI,CAAC;oBACxB;gBACF;;YACA,OAAO;gBACL,kBAAkB;gBAClB,gBAAgB;YAClB;QACF;iDAAG;QAAC;KAAc;IAElB,gBAAgB;IAChB,IAAI,mBAAmB,CAAA,GAAA,2KAAA,CAAA,UAAiB,AAAD,EAAE,gBAAgB,mBAAmB;QAC1E,YAAY;QACZ,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,iEAAiE;IACjE,IAAI,WAAW,6JAAA,CAAA,cAAiB;4CAAC,SAAU,IAAI;YAC7C,IAAI,MAAM;gBACR,IAAI,mBAAmB;oBACrB,OAAO,IAAI,CAAC,kBAAkB;gBAChC;gBAEA,uBAAuB;gBACvB,IAAI,YAAY,iBAAiB,MAAM;gBACvC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;oBAC5C,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC9B,IAAI,UAAU,WAAW;wBACvB,OAAO;oBACT;gBACF;YACF;QACF;2CAAG;QAAC;QAAkB;KAAkB;IAExC,iEAAiE;IACjE,IAAI,kBAAkB,6JAAA,CAAA,cAAiB;mDAAC,SAAU,WAAW;YAC3D,IAAI,SAAS,CAAA,GAAA,mKAAA,CAAA,UAAO,AAAD,EAAE;YACrB,OAAO,OAAO,GAAG;2DAAC,SAAU,GAAG;oBAC7B,IAAI,WAAW,MAAM;wBACnB,OAAO;4BACL,OAAO;wBACT;oBACF;oBACA,OAAO;gBACT;;QACF;kDAAG,EAAE;IACL,IAAI,sBAAsB,6JAAA,CAAA,cAAiB;uDAAC,SAAU,WAAW;YAC/D,IAAI,SAAS,gBAAgB;YAC7B,OAAO,OAAO,GAAG;+DAAC,SAAU,IAAI;oBAC9B,IAAI,WAAW,KAAK,KAAK;oBACzB,IAAI,WAAW,KAAK,KAAK,EACvB,iBAAiB,KAAK,WAAW;oBACnC,IAAI;oBACJ,IAAI,SAAS,cAAc,GAAG,CAAC;oBAE/B,8BAA8B;oBAC9B,IAAI,QAAQ;wBACV,IAAI;wBACJ,WAAW,kBAAkB,gBAAgB,OAAO,IAAI,IAAI,CAAC,YAAY,QAAQ,MAAM,QAAQ,cAAc,KAAK,IAAI,YAAY,SAAS,OAAO,IAAI;wBACtJ,cAAc,OAAO,IAAI,CAAC,QAAQ;oBACpC,OAAO,IAAI,aAAa,WAAW;wBACjC,iDAAiD;wBACjD,IAAI,mBAAmB,gBAAgB,eAAe,IAAI;4FAAC,SAAU,WAAW;gCAC9E,OAAO,YAAY,KAAK,KAAK;4BAC/B;;wBACA,WAAW,iBAAiB,KAAK;oBACnC;oBACA,OAAO;wBACL,OAAO;wBACP,OAAO;wBACP,aAAa;wBACb,UAAU;oBACZ;gBACF;;QACF;sDAAG;QAAC;QAAe;QAAU;QAAiB;KAAc;IAE5D,iEAAiE;IACjE,IAAI,wBAAwB,6JAAA,CAAA,UAAa;qDAAC;YACxC,OAAO,gBAAgB,kBAAkB,OAAO,EAAE,GAAG;QACvD;oDAAG;QAAC;QAAiB;KAAc;IAEnC,6CAA6C;IAC7C,IAAI,iBAAiB,6JAAA,CAAA,UAAa;8CAAC;YAC/B,IAAI,kBAAkB,EAAE;YACxB,IAAI,kBAAkB,EAAE;YACxB,sBAAsB,OAAO;sDAAC,SAAU,IAAI;oBAC1C,IAAI,KAAK,WAAW,EAAE;wBACpB,gBAAgB,IAAI,CAAC;oBACvB,OAAO;wBACL,gBAAgB,IAAI,CAAC;oBACvB;gBACF;;YACA,OAAO;gBAAC;gBAAiB;aAAgB;QAC3C;6CAAG;QAAC;KAAsB,GAC1B,kBAAkB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,gBAAgB,IACjD,mBAAmB,eAAe,CAAC,EAAE,EACrC,uBAAuB,eAAe,CAAC,EAAE;IAE3C,qDAAqD;IACrD,IAAI,YAAY,6JAAA,CAAA,UAAa;yCAAC;YAC5B,OAAO,iBAAiB,GAAG;iDAAC,SAAU,IAAI;oBACxC,OAAO,KAAK,KAAK;gBACnB;;QACF;wCAAG;QAAC;KAAiB;IAErB,iEAAiE;IACjE,IAAI,kBAAkB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,kBAAkB,sBAAsB,gBAAgB,cAC3F,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,iBAAiB,IACnD,mBAAmB,gBAAgB,CAAC,EAAE,EACtC,uBAAuB,gBAAgB,CAAC,EAAE;IAE5C,0DAA0D;IAC1D,IAAI,gBAAgB,6JAAA,CAAA,UAAa;6CAAC;YAChC,kCAAkC;YAClC,IAAI,cAAc,CAAA,GAAA,sKAAA,CAAA,uBAAoB,AAAD,EAAE,kBAAkB,2BAA2B,aAAa;YAEjG,yCAAyC;YACzC,IAAI,SAAS,YAAY,GAAG;4DAAC,SAAU,GAAG;oBACxC,IAAI,uBAAuB;oBAC3B,OAAO,CAAC,wBAAwB,CAAC,mBAAmB,WAAW,CAAC,IAAI,MAAM,QAAQ,qBAAqB,KAAK,KAAK,CAAC,mBAAmB,iBAAiB,IAAI,MAAM,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,gBAAgB,CAAC,iBAAiB,KAAK,CAAC,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;gBAClU;;YAEA,8BAA8B;YAC9B,IAAI,gBAAgB,OAAO,GAAG;mEAAC,SAAU,GAAG;oBAC1C,IAAI,aAAa,iBAAiB,IAAI;sFAAC,SAAU,IAAI;4BACnD,OAAO,KAAK,KAAK,KAAK;wBACxB;;oBACA,IAAI,QAAQ,eAAe,eAAe,QAAQ,eAAe,KAAK,IAAI,KAAK,IAAI,WAAW,KAAK,GAAG,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,gBAAgB;oBACxL,OAAO;wBACL,OAAO;wBACP,OAAO;oBACT;gBACF;;YACA,IAAI,mBAAmB,oBAAoB;YAC3C,IAAI,WAAW,gBAAgB,CAAC,EAAE;YAClC,IAAI,CAAC,kBAAkB,YAAY,CAAA,GAAA,mKAAA,CAAA,QAAK,AAAD,EAAE,SAAS,KAAK,KAAK,CAAA,GAAA,mKAAA,CAAA,QAAK,AAAD,EAAE,SAAS,KAAK,GAAG;gBACjF,OAAO,EAAE;YACX;YACA,OAAO,iBAAiB,GAAG;qDAAC,SAAU,IAAI;oBACxC,IAAI;oBACJ,OAAO,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,wKAAA,CAAA,UAAa,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG;wBAChD,OAAO,CAAC,cAAc,KAAK,KAAK,MAAM,QAAQ,gBAAgB,KAAK,IAAI,cAAc,KAAK,KAAK;oBACjG;gBACF;;QACA,uDAAuD;QACzD;4CAAG;QAAC;QAAkB;QAAgB;QAAkB;QAAkB;QAAqB;QAA2B;KAAY;IACtI,IAAI,YAAY,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,gBACvB,aAAa,CAAA,GAAA,wKAAA,CAAA,UAAc,AAAD,EAAE,WAAW,IACvC,sBAAsB,UAAU,CAAC,EAAE;IAErC,iEAAiE;IACjE,IAAI,iBAAiB,6JAAA,CAAA,UAAa;8CAAC;YACjC,IAAI,kBAAkB,CAAC,8BAA8B,gBAAgB,qBAAqB,CAAC,aAAa,GAAG;gBACzG,OAAO;YACT;YACA,OAAO;QACT;6CAAG;QAAC;QAAU;QAAgB;QAAmB;QAA2B;KAAc;IAE1F,iEAAiE;IACjE,IAAI,gBAAgB,CAAA,GAAA,oKAAA,CAAA,UAAU,AAAD;gDAAE,SAAU,YAAY,EAAE,KAAK,EAAE,MAAM;YAClE,IAAI,mBAAmB,CAAA,GAAA,sKAAA,CAAA,uBAAoB,AAAD,EAAE,cAAc,2BAA2B,aAAa;YAElG,iCAAiC;YACjC,IAAI,kBAAkB,iBAAiB,MAAM,GAAG,gBAAgB;gBAC9D;YACF;YACA,IAAI,gBAAgB,oBAAoB;YACxC,iBAAiB;YAEjB,qBAAqB;YACrB,IAAI,sBAAsB;gBACxB,eAAe;YACjB;YAEA,mEAAmE;YACnE,IAAI,UAAU;gBACZ,IAAI,cAAc;gBAClB,IAAI,gBAAgB;oBAClB,cAAc,iBAAiB,GAAG;gEAAC,SAAU,GAAG;4BAC9C,IAAI,SAAS,cAAc,GAAG,CAAC;4BAC/B,OAAO,SAAS,OAAO,IAAI,CAAC,iBAAiB,KAAK,CAAC,GAAG;wBACxD;;gBACF;gBACA,IAAI,OAAO,SAAS;oBAChB,cAAc;oBACd,UAAU;gBACZ,GACA,eAAe,KAAK,YAAY,EAChC,WAAW,KAAK,QAAQ;gBAC1B,IAAI,kBAAkB;gBAEtB,+BAA+B;gBAC/B,IAAI,mBAAmB;oBACrB,IAAI,aAAa,qBAAqB,MAAM;2EAAC,SAAU,IAAI;4BACzD,OAAO,CAAC,YAAY,QAAQ,CAAC,KAAK,KAAK;wBACzC;;oBACA,kBAAkB,EAAE,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,kBAAkB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;gBACtF;gBACA,IAAI,sBAAsB,oBAAoB;gBAC9C,IAAI,iBAAiB;oBACnB,yDAAyD;oBACzD,UAAU;oBACV,cAAc;gBAChB;gBAEA,2CAA2C;gBAC3C,sDAAsD;gBACtD,8GAA8G;gBAC9G,IAAI,eAAe;gBACnB,IAAI,qBAAqB,WAAW,eAAe,CAAC,UAAU;oBAC5D,eAAe;gBACjB;gBACA,CAAA,GAAA,oKAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB,cAAc,cAAc,gBAAgB,cAAc;gBAC7F,IAAI,iBAAiB;oBACnB,eAAe,OAAO,GAAG;gBAC3B,OAAO;oBACL,eAAe,QAAQ,GAAG;gBAC5B;gBACA,IAAI,eAAe,qBAAqB,sBAAsB,oBAAoB,GAAG;4DAAC,SAAU,IAAI;wBAClG,OAAO,KAAK,KAAK;oBACnB;;gBACA,SAAS,iBAAiB,eAAe,YAAY,CAAC,EAAE,EAAE,qBAAqB,OAAO,oBAAoB,GAAG;4DAAC,SAAU,IAAI;wBAC1H,OAAO,KAAK,KAAK;oBACnB;4DAAI;YACN;QACF;;IAEA,iEAAiE;IACjE,2BAA2B,GAC3B,IAAI,iBAAiB,6JAAA,CAAA,cAAiB;kDAAC,SAAU,WAAW,EAAE,KAAK;YACjE,IAAI;YACJ,IAAI,WAAW,MAAM,QAAQ,EAC3B,SAAS,MAAM,MAAM;YACvB,IAAI,SAAS,WAAW,CAAC,YAAY;YACrC,IAAI,OAAO,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI;YACtE,IAAI,gBAAgB,CAAC,wBAAwB,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;YAE9L,kCAAkC;YAClC,IAAI,CAAC,gBAAgB;gBACnB,+BAA+B;gBAC/B,cAAc;oBAAC;iBAAc,EAAE;oBAC7B,UAAU;oBACV,cAAc;gBAChB,GAAG;YACL,OAAO;gBACL,IAAI,eAAe,WAAW,EAAE,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,YAAY;oBAAC;iBAAc,IAAI,iBAAiB,MAAM;8DAAC,SAAU,CAAC;wBAC3H,OAAO,MAAM;oBACf;;gBAEA,8BAA8B;gBAC9B,IAAI,gBAAgB;oBAClB,6BAA6B;oBAC7B,IAAI,kBAAkB,eAAe,eACnC,mBAAmB,gBAAgB,gBAAgB,EACnD,iBAAiB,gBAAgB,cAAc;oBACjD,IAAI,UAAU,eAAe,GAAG;0EAAC,SAAU,GAAG;4BAC5C,OAAO,cAAc,GAAG,CAAC,KAAK,GAAG;wBACnC;;oBAEA,gCAAgC;oBAChC,IAAI;oBACJ,IAAI,UAAU;wBACZ,IAAI,gBAAgB,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,MAAM;wBAChD,cAAc,cAAc,WAAW;oBACzC,OAAO;wBACL,IAAI,iBAAiB,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE,SAAS;4BACzC,SAAS;4BACT,iBAAiB;wBACnB,GAAG;wBACH,cAAc,eAAe,WAAW;oBAC1C;oBAEA,oBAAoB;oBACpB,eAAe,EAAE,CAAC,MAAM,CAAC,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,mBAAmB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE,YAAY,GAAG;kEAAC,SAAU,GAAG;4BAC7G,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,KAAK,CAAC;wBACtD;;gBACF;gBACA,cAAc,cAAc;oBAC1B,UAAU;oBACV,cAAc;gBAChB,GAAG,UAAU;YACf;YAEA,uBAAuB;YACvB,IAAI,YAAY,CAAC,gBAAgB;gBAC/B,aAAa,QAAQ,aAAa,KAAK,KAAK,SAAS,eAAe,CAAA,GAAA,oKAAA,CAAA,kBAAe,AAAD,EAAE;YACtF,OAAO;gBACL,eAAe,QAAQ,eAAe,KAAK,KAAK,WAAW,eAAe,CAAA,GAAA,oKAAA,CAAA,kBAAe,AAAD,EAAE;YAC5F;QACF;iDAAG;QAAC;QAAgB;QAAe;QAAa;QAAkB;QAAgB;QAAW;QAAe;QAAgB;QAAU;QAAY;QAAkB;QAAsB;KAAS;IAEnM,iEAAiE;IACjE,IAAI,kCAAkC,6JAAA,CAAA,cAAiB;mEAAC,SAAU,IAAI;YACpE,IAAI,yBAAyB;gBAC3B,IAAI,cAAc,CAAC;gBACnB,OAAO,cAAc,CAAC,aAAa,sBAAsB;oBACvD,KAAK,SAAS;wBACZ,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;wBACf,OAAO;oBACT;gBACF;gBACA,wBAAwB,MAAM;YAChC;QACF;kEAAG;QAAC;KAAwB;IAE5B,iEAAiE;IACjE,IAAI,wBAAwB,CAAA,GAAA,oKAAA,CAAA,UAAU,AAAD;wDAAE,SAAU,SAAS,EAAE,IAAI;YAC9D,IAAI,eAAe,UAAU,GAAG;6EAAC,SAAU,IAAI;oBAC7C,OAAO,KAAK,KAAK;gBACnB;;YACA,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,cAAc,cAAc,CAAC,GAAG;gBAChC;YACF;YAEA,gFAAgF;YAChF,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE;gBACtB,eAAe,KAAK,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE;oBACnC,UAAU;oBACV,QAAQ;gBACV;YACF;QACF;;IAEA,iEAAiE;IACjE,IAAI,oBAAoB,6JAAA,CAAA,UAAa;iDAAC;YACpC,OAAO;gBACL,SAAS;gBACT,0BAA0B;gBAC1B,YAAY;gBACZ,gBAAgB;gBAChB,sBAAsB;gBACtB,UAAU;gBACV,YAAY;gBACZ,UAAU;gBACV,kBAAkB;gBAClB,iBAAiB;gBACjB,eAAe;gBACf,cAAc,aAAa,YAAY,OAAO,WAAW,oBAAoB,MAAM;gBACnF,eAAe,8BAA8B,gBAAgB,CAAC,qBAAqB,CAAC,CAAC;gBACrF,eAAe;YACjB;QACF;gDAAG;QAAC;QAAS;QAA0B;QAAY;QAAgB;QAAsB;QAAkB;QAAkB;QAAgB;QAAkB;QAAiB;QAAe;QAAU,oBAAoB,MAAM;QAAE;QAA2B;QAAmB;QAAe;KAAc;IAEhT,iEAAiE;IACjE,IAAI,gBAAgB,6JAAA,CAAA,UAAa;6CAAC;YAChC,OAAO;gBACL,WAAW;gBACX,UAAU;gBACV,gBAAgB;gBAChB,YAAY;gBACZ,aAAa;gBACb,iBAAiB;gBACjB,sBAAsB;gBACtB,kBAAkB;gBAClB,yBAAyB;gBACzB,cAAc;gBACd,UAAU;gBACV,YAAY;gBACZ,cAAc;gBACd,cAAc;gBACd,UAAU;gBACV,oBAAoB;gBACpB,aAAa;YACf;QACF;4CAAG;QAAC;QAAiB;QAAU;QAAgB;QAAY;QAAkB;QAAsB;QAAsB;QAAkB;QAAyB;QAAc;QAAU;QAAY;QAAc;QAAc;QAAU;QAAoB;KAAY;IAE9Q,iEAAiE;IACjE,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,kKAAA,CAAA,UAAiB,CAAC,QAAQ,EAAE;QAClE,OAAO;IACT,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,8JAAA,CAAA,UAAa,CAAC,QAAQ,EAAE;QAC1D,OAAO;IACT,GAAG,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,qMAAA,CAAA,aAAU,EAAE,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;QACvD,KAAK;IACP,GAAG,WAAW;QACZ,WAAW;QACX,IAAI;QACJ,WAAW;QACX,MAAM,iBAAiB,aAAa;QAGpC,eAAe;QACf,uBAAuB;QAGvB,aAAa;QACb,UAAU;QAGV,YAAY,2JAAA,CAAA,UAAU;QACtB,cAAc,CAAC,eAAe,MAAM;QACpC,yBAAyB;QACzB,0BAA0B;IAC5B;AACF;AAEA,wBAAwB;AACxB,wCAA2C;IACzC,WAAW,WAAW,GAAG;AAC3B;AACA,IAAI,oBAAoB;AACxB,kBAAkB,QAAQ,GAAG,yJAAA,CAAA,UAAQ;AACrC,kBAAkB,QAAQ,GAAG,sKAAA,CAAA,WAAQ;AACrC,kBAAkB,WAAW,GAAG,sKAAA,CAAA,cAAW;AAC3C,kBAAkB,UAAU,GAAG,sKAAA,CAAA,aAAU;uCAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1626, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/rc-tree-select/es/index.js"], "sourcesContent": ["import TreeSelect from \"./TreeSelect\";\nimport TreeNode from \"./TreeNode\";\nimport { SHOW_ALL, SHOW_CHILD, SHOW_PARENT } from \"./utils/strategyUtil\";\nexport { TreeNode, SHOW_ALL, SHOW_CHILD, SHOW_PARENT };\nexport default TreeSelect;"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;;uCAEe,2JAAA,CAAA,UAAU", "ignoreList": [0], "debugId": null}}]}