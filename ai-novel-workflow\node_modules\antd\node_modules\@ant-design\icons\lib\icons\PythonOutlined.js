"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _PythonOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/PythonOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var PythonOutlined = function PythonOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _PythonOutlined.default
  }));
};

/**![python](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTU1IDc5MC41YTI4LjUgMjguNSAwIDEwNTcgMCAyOC41IDI4LjUgMCAwMC01NyAwbS0xNDMtNTU3YTI4LjUgMjguNSAwIDEwNTcgMCAyOC41IDI4LjUgMCAwMC01NyAwIiAvPjxwYXRoIGQ9Ik04MjEuNTIgMjk3LjcxSDcyNi4zdi05NS4yM2MwLTQ5LjktNDAuNTgtOTAuNDgtOTAuNDgtOTAuNDhIMzg4LjE5Yy00OS45IDAtOTAuNDggNDAuNTctOTAuNDggOTAuNDh2OTUuMjNoLTk1LjIzYy00OS45IDAtOTAuNDggNDAuNTgtOTAuNDggOTAuNDh2MjQ3LjYyYzAgNDkuOSA0MC41NyA5MC40OCA5MC40OCA5MC40OGg5NS4yM3Y5NS4yM2MwIDQ5LjkgNDAuNTggOTAuNDggOTAuNDggOTAuNDhoMjQ3LjYyYzQ5LjkgMCA5MC40OC00MC41NyA5MC40OC05MC40OFY3MjYuM2g5NS4yM2M0OS45IDAgOTAuNDgtNDAuNTggOTAuNDgtOTAuNDhWMzg4LjE5YzAtNDkuOS00MC41Ny05MC40OC05MC40OC05MC40OE0yMDIuNDggNjY5LjE0YTMzLjM3IDMzLjM3IDAgMDEtMzMuMzQtMzMuMzNWMzg4LjE5YTMzLjM3IDMzLjM3IDAgMDEzMy4zNC0zMy4zM2gyNzguNTdhMjguNTMgMjguNTMgMCAwMDI4LjU3LTI4LjU3IDI4LjUzIDI4LjUzIDAgMDAtMjguNTctMjguNThoLTEyNi4ydi05NS4yM2EzMy4zNyAzMy4zNyAwIDAxMzMuMzQtMzMuMzRoMjQ3LjYyYTMzLjM3IDMzLjM3IDAgMDEzMy4zMyAzMy4zNHYyNTYuNDdhMjQuNDcgMjQuNDcgMCAwMS0yNC40NyAyNC40OEgzNzkuMzNjLTQ1LjA0IDAtODEuNjIgMzYuNjYtODEuNjIgODEuNjJ2MTA0LjF6bTY1Mi4zOC0zMy4zM2EzMy4zNyAzMy4zNyAwIDAxLTMzLjM0IDMzLjMzSDU0Mi45NWEyOC41MyAyOC41MyAwIDAwLTI4LjU3IDI4LjU3IDI4LjUzIDI4LjUzIDAgMDAyOC41NyAyOC41OGgxMjYuMnY5NS4yM2EzMy4zNyAzMy4zNyAwIDAxLTMzLjM0IDMzLjM0SDM4OC4xOWEzMy4zNyAzMy4zNyAwIDAxLTMzLjMzLTMzLjM0VjU2NS4wNWEyNC40NyAyNC40NyAwIDAxMjQuNDctMjQuNDhoMjY1LjM0YzQ1LjA0IDAgODEuNjItMzYuNjcgODEuNjItODEuNjJ2LTEwNC4xaDk1LjIzYTMzLjM3IDMzLjM3IDAgMDEzMy4zNCAzMy4zNHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(PythonOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PythonOutlined';
}
var _default = exports.default = RefIcon;