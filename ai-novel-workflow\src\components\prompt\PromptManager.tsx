'use client';

import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tabs,
  List,
  Tag,
  Rate,
  message,
  Row,
  Col,
  Divider,
  Badge,
  Progress
} from 'antd';
import {
  FileTextOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  StarOutlined,
  ExperimentOutlined,
  Bar<PERSON>hartOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/store';
import type { PromptTemplate } from '@/types';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const PromptManager: React.FC = () => {
  const {
    promptTemplates,
    addPromptTemplate,
    updatePromptTemplate,
    deletePromptTemplate,
    getPromptTemplatesByCategory
  } = useAppStore();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);
  const [activeTab, setActiveTab] = useState('templates');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [form] = Form.useForm();

  // 提示词分类
  const categories = [
    { key: 'character', label: '角色生成', color: 'blue' },
    { key: 'plot', label: '情节规划', color: 'green' },
    { key: 'dialogue', label: '对话生成', color: 'orange' },
    { key: 'description', label: '场景描述', color: 'purple' },
    { key: 'title', label: '书名生成', color: 'red' },
    { key: 'outline', label: '大纲规划', color: 'cyan' },
    { key: 'worldbuilding', label: '世界观构建', color: 'gold' },
    { key: 'polish', label: '内容润色', color: 'lime' },
  ];

  // 模拟提示词模板数据
  const sampleTemplates: PromptTemplate[] = [
    {
      id: '1',
      name: '主角角色生成',
      category: 'character',
      template: '请为一部{genre}小说创建一个{age}岁的{gender}主角。角色应该具有{personality}的性格特征，背景设定为{background}。请详细描述角色的外貌、性格、能力和成长经历。',
      variables: [
        { name: 'genre', type: 'string', description: '小说类型', defaultValue: '现代都市', required: true },
        { name: 'age', type: 'number', description: '角色年龄', defaultValue: 25, required: true },
        { name: 'gender', type: 'string', description: '角色性别', defaultValue: '男', required: true },
        { name: 'personality', type: 'string', description: '性格特征', defaultValue: '勇敢、善良', required: true },
        { name: 'background', type: 'string', description: '背景设定', defaultValue: '普通家庭', required: false },
      ],
      version: 1,
      isActive: true,
      performance: {
        usage: 156,
        rating: 4.5,
        feedback: ['生成的角色很有特色', '背景设定合理', '可以增加更多细节'],
      }
    },
    {
      id: '2',
      name: '情节冲突生成',
      category: 'plot',
      template: '为{genre}类型的小说设计一个{conflict_type}冲突。冲突应该涉及{characters}，发生在{setting}环境中。请描述冲突的起因、发展过程和可能的解决方案。',
      variables: [
        { name: 'genre', type: 'string', description: '小说类型', required: true },
        { name: 'conflict_type', type: 'string', description: '冲突类型', required: true },
        { name: 'characters', type: 'array', description: '涉及角色', required: true },
        { name: 'setting', type: 'string', description: '场景设定', required: true },
      ],
      version: 2,
      isActive: true,
      performance: {
        usage: 89,
        rating: 4.2,
        feedback: ['冲突设计有趣', '逻辑性强'],
      }
    }
  ];

  const filteredTemplates = selectedCategory === 'all'
    ? sampleTemplates
    : sampleTemplates.filter(template => template.category === selectedCategory);

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditTemplate = (template: PromptTemplate) => {
    setEditingTemplate(template);
    form.setFieldsValue({
      name: template.name,
      category: template.category,
      template: template.template,
    });
    setIsModalVisible(true);
  };

  const getCategoryConfig = (category: string) => {
    return categories.find(cat => cat.key === category) || categories[0];
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Title level={2}>提示词管理</Title>
          <Text type="secondary">管理AI提示词模板和配置</Text>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreateTemplate}
          >
            创建模板
          </Button>
        </Space>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'templates',
            label: (
              <span>
                <FileTextOutlined />
                提示词模板 ({filteredTemplates.length})
              </span>
            ),
            children: (
              <div>
                {/* 分类过滤器 */}
                <Card className="mb-6">
                  <Row gutter={16}>
                    <Col span={3}>
                      <Card
                        size="small"
                        className={`cursor-pointer transition-all ${
                          selectedCategory === 'all' ? 'ring-2 ring-blue-500' : ''
                        }`}
                        onClick={() => setSelectedCategory('all')}
                      >
                        <div className="text-center">
                          <FileTextOutlined className="text-2xl text-gray-500" />
                          <div className="mt-2">
                            <Text strong>全部</Text>
                            <div className="text-xs text-gray-500">
                              {sampleTemplates.length} 个模板
                            </div>
                          </div>
                        </div>
                      </Card>
                    </Col>
                    {categories.map((category) => {
                      const count = sampleTemplates.filter(t => t.category === category.key).length;
                      return (
                        <Col span={3} key={category.key}>
                          <Card
                            size="small"
                            className={`cursor-pointer transition-all ${
                              selectedCategory === category.key ? 'ring-2 ring-blue-500' : ''
                            }`}
                            onClick={() => setSelectedCategory(category.key)}
                          >
                            <div className="text-center">
                              <FileTextOutlined className={`text-2xl text-${category.color}-500`} />
                              <div className="mt-2">
                                <Text strong>{category.label}</Text>
                                <div className="text-xs text-gray-500">
                                  {count} 个模板
                                </div>
                              </div>
                            </div>
                          </Card>
                        </Col>
                      );
                    })}
                  </Row>
                </Card>

                {/* 模板列表 */}
                <List
                  grid={{ gutter: 16, column: 1 }}
                  dataSource={filteredTemplates}
                  renderItem={(template) => {
                    const categoryConfig = getCategoryConfig(template.category);
                    return (
                      <List.Item>
                        <Card
                          className="hover:shadow-lg transition-shadow"
                          actions={[
                            <Button
                              key="copy"
                              type="text"
                              icon={<CopyOutlined />}
                              onClick={() => {
                                navigator.clipboard.writeText(template.template);
                                message.success('模板已复制到剪贴板');
                              }}
                            >
                              复制
                            </Button>,
                            <Button
                              key="edit"
                              type="text"
                              icon={<EditOutlined />}
                              onClick={() => handleEditTemplate(template)}
                            >
                              编辑
                            </Button>,
                            <Button
                              key="delete"
                              type="text"
                              danger
                              icon={<DeleteOutlined />}
                              onClick={() => {
                                message.info('删除功能开发中');
                              }}
                            >
                              删除
                            </Button>
                          ]}
                        >
                          <Row gutter={24}>
                            <Col span={16}>
                              <div className="mb-3">
                                <div className="flex items-center justify-between mb-2">
                                  <Title level={5} className="mb-0">{template.name}</Title>
                                  <Space>
                                    <Tag color={categoryConfig.color}>
                                      {categoryConfig.label}
                                    </Tag>
                                    <Tag color={template.isActive ? 'green' : 'default'}>
                                      {template.isActive ? '启用' : '禁用'}
                                    </Tag>
                                    <Text type="secondary">v{template.version}</Text>
                                  </Space>
                                </div>

                                <Paragraph
                                  ellipsis={{ rows: 2 }}
                                  type="secondary"
                                  className="mb-3"
                                >
                                  {template.template}
                                </Paragraph>

                                <div className="flex items-center space-x-4 text-sm text-gray-500">
                                  <span>变量: {template.variables.length} 个</span>
                                  <span>使用: {template.performance.usage} 次</span>
                                  <div className="flex items-center space-x-1">
                                    <Rate
                                      disabled
                                      value={template.performance.rating}
                                      allowHalf
                                      style={{ fontSize: 12 }}
                                    />
                                    <Text type="secondary">({template.performance.rating})</Text>
                                  </div>
                                </div>
                              </div>
                            </Col>
                            <Col span={8}>
                              <div className="text-center">
                                <div className="mb-2">
                                  <Text strong className="text-lg">{template.performance.rating}</Text>
                                  <Text type="secondary" className="ml-1">/5.0</Text>
                                </div>
                                <Progress
                                  percent={template.performance.rating * 20}
                                  size="small"
                                  showInfo={false}
                                />
                                <div className="mt-2">
                                  <Badge count={template.performance.usage} showZero>
                                    <Text type="secondary" className="text-sm">使用次数</Text>
                                  </Badge>
                                </div>
                              </div>
                            </Col>
                          </Row>
                        </Card>
                      </List.Item>
                    );
                  }}
                />
              </div>
            ),
          },
          {
            key: 'analytics',
            label: (
              <span>
                <BarChartOutlined />
                性能分析
              </span>
            ),
            children: (
              <Card className="text-center py-12">
                <BarChartOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                <div className="mt-4">
                  <Text type="secondary">性能分析功能开发中</Text>
                  <br />
                  <Text type="secondary">即将支持模板使用统计和效果分析</Text>
                </div>
              </Card>
            ),
          },
        ]}
      />

      {/* 创建/编辑模板模态框 */}
      <Modal
        title={editingTemplate ? '编辑提示词模板' : '创建提示词模板'}
        open={isModalVisible}
        onOk={async () => {
          try {
            const values = await form.validateFields();
            message.success(editingTemplate ? '模板已更新' : '模板已创建');
            setIsModalVisible(false);
            form.resetFields();
          } catch (error) {
            console.error('表单验证失败:', error);
          }
        }}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={800}
        okText={editingTemplate ? '更新' : '创建'}
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item
                name="name"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input placeholder="请输入模板名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  {categories.map((category) => (
                    <Option key={category.key} value={category.key}>
                      {category.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="template"
            label="提示词模板"
            rules={[{ required: true, message: '请输入提示词模板' }]}
            help="使用 {变量名} 的格式定义变量，如 {genre}、{character_name} 等"
          >
            <TextArea
              rows={8}
              placeholder="请输入提示词模板，使用 {变量名} 定义可替换的变量..."
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PromptManager;
