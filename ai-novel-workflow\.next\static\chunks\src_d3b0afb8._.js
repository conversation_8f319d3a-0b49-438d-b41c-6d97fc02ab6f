(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/store/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "useAppStore": ()=>useAppStore
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
// 生成唯一ID的工具函数
const generateId = ()=>Math.random().toString(36).substr(2, 9);
const useAppStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // 初始UI状态
        ui: {
            theme: 'light',
            language: 'zh-CN',
            sidebarCollapsed: false,
            activeTab: 'workflow',
            selectedProject: undefined,
            notifications: []
        },
        // UI状态管理
        setTheme: (theme)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        theme
                    }
                })),
        setLanguage: (language)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        language
                    }
                })),
        toggleSidebar: ()=>set((state)=>({
                    ui: {
                        ...state.ui,
                        sidebarCollapsed: !state.ui.sidebarCollapsed
                    }
                })),
        setActiveTab: (activeTab)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        activeTab
                    }
                })),
        addNotification: (notification)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        notifications: [
                            ...state.ui.notifications,
                            {
                                ...notification,
                                id: generateId(),
                                timestamp: new Date(),
                                read: false
                            }
                        ]
                    }
                })),
        markNotificationRead: (id)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        notifications: state.ui.notifications.map((n)=>n.id === id ? {
                                ...n,
                                read: true
                            } : n)
                    }
                })),
        clearNotifications: ()=>set((state)=>({
                    ui: {
                        ...state.ui,
                        notifications: []
                    }
                })),
        // 项目管理
        projects: [],
        currentProject: null,
        createProject: (project)=>set((state)=>{
                const newProject = {
                    ...project,
                    id: generateId(),
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                return {
                    projects: [
                        ...state.projects,
                        newProject
                    ],
                    currentProject: newProject,
                    ui: {
                        ...state.ui,
                        selectedProject: newProject.id
                    }
                };
            }),
        updateProject: (id, updates)=>set((state)=>{
                var _state_currentProject;
                return {
                    projects: state.projects.map((p)=>p.id === id ? {
                            ...p,
                            ...updates,
                            updatedAt: new Date()
                        } : p),
                    currentProject: ((_state_currentProject = state.currentProject) === null || _state_currentProject === void 0 ? void 0 : _state_currentProject.id) === id ? {
                        ...state.currentProject,
                        ...updates,
                        updatedAt: new Date()
                    } : state.currentProject
                };
            }),
        deleteProject: (id)=>set((state)=>{
                var _state_currentProject;
                return {
                    projects: state.projects.filter((p)=>p.id !== id),
                    currentProject: ((_state_currentProject = state.currentProject) === null || _state_currentProject === void 0 ? void 0 : _state_currentProject.id) === id ? null : state.currentProject,
                    ui: {
                        ...state.ui,
                        selectedProject: state.ui.selectedProject === id ? undefined : state.ui.selectedProject
                    }
                };
            }),
        setCurrentProject: (id)=>set((state)=>{
                const project = state.projects.find((p)=>p.id === id);
                return {
                    currentProject: project || null,
                    ui: {
                        ...state.ui,
                        selectedProject: id
                    }
                };
            }),
        // 工作流管理
        workflows: {},
        currentWorkflow: [],
        addNode: (node)=>set((state)=>({
                    currentWorkflow: [
                        ...state.currentWorkflow,
                        {
                            ...node,
                            id: generateId()
                        }
                    ]
                })),
        updateNode: (id, updates)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.map((n)=>n.id === id ? {
                            ...n,
                            ...updates
                        } : n)
                })),
        deleteNode: (id)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.filter((n)=>n.id !== id)
                })),
        connectNodes: (sourceId, targetId)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.map((n)=>n.id === sourceId ? {
                            ...n,
                            connections: [
                                ...n.connections,
                                {
                                    sourceId,
                                    targetId
                                }
                            ]
                        } : n)
                })),
        disconnectNodes: (sourceId, targetId)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.map((n)=>n.id === sourceId ? {
                            ...n,
                            connections: n.connections.filter((c)=>!(c.sourceId === sourceId && c.targetId === targetId))
                        } : n)
                })),
        loadWorkflow: (projectId)=>set((state)=>({
                    currentWorkflow: state.workflows[projectId] || []
                })),
        saveWorkflow: (projectId)=>set((state)=>({
                    workflows: {
                        ...state.workflows,
                        [projectId]: state.currentWorkflow
                    }
                })),
        // 角色管理
        characters: {},
        addCharacter: (projectId, character)=>set((state)=>({
                    characters: {
                        ...state.characters,
                        [projectId]: [
                            ...state.characters[projectId] || [],
                            {
                                ...character,
                                id: generateId()
                            }
                        ]
                    }
                })),
        updateCharacter: (projectId, id, updates)=>set((state)=>({
                    characters: {
                        ...state.characters,
                        [projectId]: (state.characters[projectId] || []).map((c)=>c.id === id ? {
                                ...c,
                                ...updates
                            } : c)
                    }
                })),
        deleteCharacter: (projectId, id)=>set((state)=>({
                    characters: {
                        ...state.characters,
                        [projectId]: (state.characters[projectId] || []).filter((c)=>c.id !== id)
                    }
                })),
        getCharacters: (projectId)=>get().characters[projectId] || [],
        // 世界观管理
        worldBuilding: {},
        addWorldElement: (projectId, element)=>set((state)=>({
                    worldBuilding: {
                        ...state.worldBuilding,
                        [projectId]: [
                            ...state.worldBuilding[projectId] || [],
                            {
                                ...element,
                                id: generateId()
                            }
                        ]
                    }
                })),
        updateWorldElement: (projectId, id, updates)=>set((state)=>({
                    worldBuilding: {
                        ...state.worldBuilding,
                        [projectId]: (state.worldBuilding[projectId] || []).map((w)=>w.id === id ? {
                                ...w,
                                ...updates
                            } : w)
                    }
                })),
        deleteWorldElement: (projectId, id)=>set((state)=>({
                    worldBuilding: {
                        ...state.worldBuilding,
                        [projectId]: (state.worldBuilding[projectId] || []).filter((w)=>w.id !== id)
                    }
                })),
        getWorldElements: (projectId)=>get().worldBuilding[projectId] || [],
        // 其他管理功能的占位符实现
        outlines: {},
        addOutline: (projectId, outline)=>set((state)=>({
                    outlines: {
                        ...state.outlines,
                        [projectId]: [
                            ...state.outlines[projectId] || [],
                            {
                                ...outline,
                                id: generateId(),
                                createdAt: new Date(),
                                updatedAt: new Date()
                            }
                        ]
                    }
                })),
        updateOutline: (projectId, id, updates)=>set((state)=>({
                    outlines: {
                        ...state.outlines,
                        [projectId]: (state.outlines[projectId] || []).map((o)=>o.id === id ? {
                                ...o,
                                ...updates,
                                updatedAt: new Date()
                            } : o)
                    }
                })),
        deleteOutline: (projectId, id)=>set((state)=>({
                    outlines: {
                        ...state.outlines,
                        [projectId]: (state.outlines[projectId] || []).filter((o)=>o.id !== id)
                    }
                })),
        getOutlines: (projectId)=>get().outlines[projectId] || [],
        plotLines: {},
        addPlotLine: (projectId, plotLine)=>set((state)=>({
                    plotLines: {
                        ...state.plotLines,
                        [projectId]: [
                            ...state.plotLines[projectId] || [],
                            {
                                ...plotLine,
                                id: generateId()
                            }
                        ]
                    }
                })),
        updatePlotLine: (projectId, id, updates)=>set((state)=>({
                    plotLines: {
                        ...state.plotLines,
                        [projectId]: (state.plotLines[projectId] || []).map((p)=>p.id === id ? {
                                ...p,
                                ...updates
                            } : p)
                    }
                })),
        deletePlotLine: (projectId, id)=>set((state)=>({
                    plotLines: {
                        ...state.plotLines,
                        [projectId]: (state.plotLines[projectId] || []).filter((p)=>p.id !== id)
                    }
                })),
        getPlotLines: (projectId)=>get().plotLines[projectId] || [],
        bookTitles: {},
        addBookTitle: (projectId, title)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: [
                            ...state.bookTitles[projectId] || [],
                            {
                                ...title,
                                id: generateId(),
                                createdAt: new Date()
                            }
                        ]
                    }
                })),
        updateBookTitle: (projectId, id, updates)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: (state.bookTitles[projectId] || []).map((t)=>t.id === id ? {
                                ...t,
                                ...updates
                            } : t)
                    }
                })),
        deleteBookTitle: (projectId, id)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: (state.bookTitles[projectId] || []).filter((t)=>t.id !== id)
                    }
                })),
        toggleTitleFavorite: (projectId, id)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: (state.bookTitles[projectId] || []).map((t)=>t.id === id ? {
                                ...t,
                                isFavorite: !t.isFavorite
                            } : t)
                    }
                })),
        getBookTitles: (projectId)=>get().bookTitles[projectId] || [],
        chapters: {},
        addChapter: (projectId, chapter)=>set((state)=>({
                    chapters: {
                        ...state.chapters,
                        [projectId]: [
                            ...state.chapters[projectId] || [],
                            {
                                ...chapter,
                                id: generateId(),
                                createdAt: new Date(),
                                updatedAt: new Date()
                            }
                        ]
                    }
                })),
        updateChapter: (projectId, id, updates)=>set((state)=>({
                    chapters: {
                        ...state.chapters,
                        [projectId]: (state.chapters[projectId] || []).map((c)=>c.id === id ? {
                                ...c,
                                ...updates,
                                updatedAt: new Date()
                            } : c)
                    }
                })),
        deleteChapter: (projectId, id)=>set((state)=>({
                    chapters: {
                        ...state.chapters,
                        [projectId]: (state.chapters[projectId] || []).filter((c)=>c.id !== id)
                    }
                })),
        reorderChapters: (projectId, fromIndex, toIndex)=>set((state)=>{
                const chapters = [
                    ...state.chapters[projectId] || []
                ];
                const [removed] = chapters.splice(fromIndex, 1);
                chapters.splice(toIndex, 0, removed);
                // 重新设置order
                chapters.forEach((chapter, index)=>{
                    chapter.order = index + 1;
                });
                return {
                    chapters: {
                        ...state.chapters,
                        [projectId]: chapters
                    }
                };
            }),
        getChapters: (projectId)=>get().chapters[projectId] || [],
        promptTemplates: [],
        addPromptTemplate: (template)=>set((state)=>({
                    promptTemplates: [
                        ...state.promptTemplates,
                        {
                            ...template,
                            id: generateId()
                        }
                    ]
                })),
        updatePromptTemplate: (id, updates)=>set((state)=>({
                    promptTemplates: state.promptTemplates.map((t)=>t.id === id ? {
                            ...t,
                            ...updates
                        } : t)
                })),
        deletePromptTemplate: (id)=>set((state)=>({
                    promptTemplates: state.promptTemplates.filter((t)=>t.id !== id)
                })),
        getPromptTemplatesByCategory: (category)=>get().promptTemplates.filter((t)=>t.category === category),
        documentStructures: {},
        initializeDocumentStructure: (projectId)=>set((state)=>({
                    documentStructures: {
                        ...state.documentStructures,
                        [projectId]: {
                            projectId,
                            folders: [],
                            files: [],
                            lastBackup: new Date()
                        }
                    }
                })),
        updateDocumentStructure: (projectId, structure)=>set((state)=>({
                    documentStructures: {
                        ...state.documentStructures,
                        [projectId]: {
                            ...state.documentStructures[projectId],
                            ...structure
                        }
                    }
                })),
        executionContexts: {},
        startExecution: (projectId, workflowId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            projectId,
                            workflowId,
                            status: 'running',
                            progress: {
                                totalSteps: 0,
                                completedSteps: 0,
                                currentStep: '',
                                percentage: 0
                            },
                            queue: [],
                            results: {},
                            startTime: new Date()
                        }
                    }
                })),
        pauseExecution: (projectId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            status: 'paused'
                        }
                    }
                })),
        resumeExecution: (projectId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            status: 'running'
                        }
                    }
                })),
        stopExecution: (projectId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            status: 'idle',
                            endTime: new Date()
                        }
                    }
                })),
        updateExecutionProgress: (projectId, progress)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            ...progress
                        }
                    }
                }))
    }), {
    name: 'ai-novel-workflow-storage',
    partialize: (state)=>({
            projects: state.projects,
            workflows: state.workflows,
            characters: state.characters,
            worldBuilding: state.worldBuilding,
            outlines: state.outlines,
            plotLines: state.plotLines,
            bookTitles: state.bookTitles,
            chapters: state.chapters,
            promptTemplates: state.promptTemplates,
            documentStructures: state.documentStructures,
            ui: {
                theme: state.ui.theme,
                language: state.ui.language,
                sidebarCollapsed: state.ui.sidebarCollapsed
            }
        })
}), {
    name: 'ai-novel-workflow'
}));
const __TURBOPACK__default__export__ = useAppStore;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/workflowAI.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// AI工作流生成器
__turbopack_context__.s({
    "WORKFLOW_TEMPLATES": ()=>WORKFLOW_TEMPLATES,
    "WorkflowAI": ()=>WorkflowAI
});
// 预定义工作流模板
const WORKFLOW_TEMPLATES = [
    {
        id: 'quick-novel',
        name: '快速小说生成',
        description: '适合新手的简单工作流，快速生成短篇小说',
        nodes: [
            {
                type: 'input',
                label: '创作输入',
                position: {
                    x: 100,
                    y: 100
                }
            },
            {
                type: 'title-generator',
                label: '生成书名',
                position: {
                    x: 100,
                    y: 250
                }
            },
            {
                type: 'character-creator',
                label: '创建角色',
                position: {
                    x: 350,
                    y: 250
                }
            },
            {
                type: 'outline-generator',
                label: '生成大纲',
                position: {
                    x: 225,
                    y: 400
                }
            },
            {
                type: 'chapter-generator',
                label: '生成章节',
                position: {
                    x: 225,
                    y: 550
                }
            },
            {
                type: 'output',
                label: '完成输出',
                position: {
                    x: 225,
                    y: 700
                }
            }
        ],
        connections: [
            {
                source: 0,
                target: 1
            },
            {
                source: 0,
                target: 2
            },
            {
                source: 1,
                target: 3
            },
            {
                source: 2,
                target: 3
            },
            {
                source: 3,
                target: 4
            },
            {
                source: 4,
                target: 5
            }
        ],
        tags: [
            '简单',
            '快速',
            '新手'
        ],
        complexity: 'simple'
    },
    {
        id: 'professional-novel',
        name: '专业小说创作',
        description: '完整的专业级工作流，包含详细的世界观和角色设定',
        nodes: [
            {
                type: 'input',
                label: '创作参数',
                position: {
                    x: 100,
                    y: 50
                }
            },
            {
                type: 'title-generator',
                label: '书名生成',
                position: {
                    x: 50,
                    y: 200
                }
            },
            {
                type: 'detail-generator',
                label: '详情生成',
                position: {
                    x: 200,
                    y: 200
                }
            },
            {
                type: 'character-creator',
                label: '角色创建',
                position: {
                    x: 350,
                    y: 200
                }
            },
            {
                type: 'worldbuilding',
                label: '世界观构建',
                position: {
                    x: 500,
                    y: 200
                }
            },
            {
                type: 'plotline-planner',
                label: '主线规划',
                position: {
                    x: 150,
                    y: 350
                }
            },
            {
                type: 'outline-generator',
                label: '大纲生成',
                position: {
                    x: 350,
                    y: 350
                }
            },
            {
                type: 'detailed-outline',
                label: '详细大纲',
                position: {
                    x: 250,
                    y: 500
                }
            },
            {
                type: 'chapter-generator',
                label: '章节生成',
                position: {
                    x: 250,
                    y: 650
                }
            },
            {
                type: 'content-polisher',
                label: '内容润色',
                position: {
                    x: 100,
                    y: 800
                }
            },
            {
                type: 'consistency-checker',
                label: '一致性检查',
                position: {
                    x: 400,
                    y: 800
                }
            },
            {
                type: 'output',
                label: '最终输出',
                position: {
                    x: 250,
                    y: 950
                }
            }
        ],
        connections: [
            {
                source: 0,
                target: 1
            },
            {
                source: 0,
                target: 2
            },
            {
                source: 0,
                target: 3
            },
            {
                source: 0,
                target: 4
            },
            {
                source: 1,
                target: 5
            },
            {
                source: 2,
                target: 5
            },
            {
                source: 3,
                target: 5
            },
            {
                source: 4,
                target: 5
            },
            {
                source: 3,
                target: 6
            },
            {
                source: 4,
                target: 6
            },
            {
                source: 5,
                target: 6
            },
            {
                source: 6,
                target: 7
            },
            {
                source: 7,
                target: 8
            },
            {
                source: 8,
                target: 9
            },
            {
                source: 8,
                target: 10
            },
            {
                source: 9,
                target: 11
            },
            {
                source: 10,
                target: 11
            }
        ],
        tags: [
            '专业',
            '完整',
            '高质量'
        ],
        complexity: 'complex'
    },
    {
        id: 'fantasy-novel',
        name: '奇幻小说专用',
        description: '专为奇幻类小说设计的工作流，重点关注世界观和魔法体系',
        nodes: [
            {
                type: 'input',
                label: '奇幻设定',
                position: {
                    x: 100,
                    y: 100
                }
            },
            {
                type: 'worldbuilding',
                label: '魔法世界',
                position: {
                    x: 100,
                    y: 250
                }
            },
            {
                type: 'character-creator',
                label: '角色种族',
                position: {
                    x: 300,
                    y: 250
                }
            },
            {
                type: 'plotline-planner',
                label: '冒险主线',
                position: {
                    x: 200,
                    y: 400
                }
            },
            {
                type: 'outline-generator',
                label: '冒险大纲',
                position: {
                    x: 200,
                    y: 550
                }
            },
            {
                type: 'chapter-generator',
                label: '章节创作',
                position: {
                    x: 200,
                    y: 700
                }
            },
            {
                type: 'consistency-checker',
                label: '设定检查',
                position: {
                    x: 200,
                    y: 850
                }
            },
            {
                type: 'output',
                label: '奇幻小说',
                position: {
                    x: 200,
                    y: 1000
                }
            }
        ],
        connections: [
            {
                source: 0,
                target: 1
            },
            {
                source: 0,
                target: 2
            },
            {
                source: 1,
                target: 3
            },
            {
                source: 2,
                target: 3
            },
            {
                source: 3,
                target: 4
            },
            {
                source: 4,
                target: 5
            },
            {
                source: 5,
                target: 6
            },
            {
                source: 6,
                target: 7
            }
        ],
        tags: [
            '奇幻',
            '魔法',
            '冒险'
        ],
        complexity: 'medium'
    },
    {
        id: 'romance-novel',
        name: '言情小说工作流',
        description: '专注于情感描写和角色关系的言情小说创作流程',
        nodes: [
            {
                type: 'input',
                label: '言情设定',
                position: {
                    x: 100,
                    y: 100
                }
            },
            {
                type: 'character-creator',
                label: '主角设定',
                position: {
                    x: 50,
                    y: 250
                }
            },
            {
                type: 'character-creator',
                label: '配角设定',
                position: {
                    x: 200,
                    y: 250
                }
            },
            {
                type: 'plotline-planner',
                label: '情感主线',
                position: {
                    x: 125,
                    y: 400
                }
            },
            {
                type: 'outline-generator',
                label: '情节大纲',
                position: {
                    x: 125,
                    y: 550
                }
            },
            {
                type: 'chapter-generator',
                label: '章节创作',
                position: {
                    x: 125,
                    y: 700
                }
            },
            {
                type: 'content-polisher',
                label: '情感润色',
                position: {
                    x: 125,
                    y: 850
                }
            },
            {
                type: 'output',
                label: '言情小说',
                position: {
                    x: 125,
                    y: 1000
                }
            }
        ],
        connections: [
            {
                source: 0,
                target: 1
            },
            {
                source: 0,
                target: 2
            },
            {
                source: 1,
                target: 3
            },
            {
                source: 2,
                target: 3
            },
            {
                source: 3,
                target: 4
            },
            {
                source: 4,
                target: 5
            },
            {
                source: 5,
                target: 6
            },
            {
                source: 6,
                target: 7
            }
        ],
        tags: [
            '言情',
            '情感',
            '关系'
        ],
        complexity: 'medium'
    }
];
class WorkflowAI {
    // 分析用户需求并推荐工作流
    static analyzeRequirements(requirements) {
        const { genre, style, length, experience, features = [] } = requirements;
        let recommendations = [];
        WORKFLOW_TEMPLATES.forEach((template)=>{
            let score = 0;
            // 根据类型匹配
            if (genre) {
                if (genre.includes('奇幻') && template.tags.includes('奇幻')) score += 30;
                if (genre.includes('言情') && template.tags.includes('言情')) score += 30;
                if (genre.includes('现代') && template.tags.includes('简单')) score += 20;
            }
            // 根据经验水平匹配
            if (experience) {
                if (experience === '新手' && template.complexity === 'simple') score += 25;
                if (experience === '进阶' && template.complexity === 'medium') score += 25;
                if (experience === '专业' && template.complexity === 'complex') score += 25;
            }
            // 根据长度匹配
            if (length) {
                if (length === '短篇' && template.complexity === 'simple') score += 20;
                if (length === '中篇' && template.complexity === 'medium') score += 20;
                if (length === '长篇' && template.complexity === 'complex') score += 20;
            }
            // 根据特殊需求匹配
            features.forEach((feature)=>{
                if (template.tags.some((tag)=>tag.includes(feature))) {
                    score += 15;
                }
            });
            recommendations.push({
                template,
                score
            });
        });
        // 按分数排序并返回前3个
        return recommendations.sort((a, b)=>b.score - a.score).slice(0, 3).map((r)=>r.template);
    }
    // 生成自定义工作流
    static generateCustomWorkflow(requirements) {
        const { genre, style, length, features } = requirements;
        // 基础节点
        const nodes = [
            {
                type: 'input',
                label: '创作输入',
                position: {
                    x: 200,
                    y: 100
                }
            }
        ];
        const connections = [];
        let currentY = 250;
        let nodeIndex = 1;
        // 根据需求添加节点
        const addNode = function(type, label) {
            let x = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 200;
            nodes.push({
                type,
                label,
                position: {
                    x,
                    y: currentY
                }
            });
            return nodeIndex++;
        };
        // 书名生成（总是需要）
        const titleNode = addNode('title-generator', '书名生成', 100);
        connections.push({
            source: 0,
            target: titleNode
        });
        // 根据类型添加特定节点
        if (genre.includes('奇幻') || features.includes('世界观')) {
            const worldNode = addNode('worldbuilding', '世界观构建', 300);
            connections.push({
                source: 0,
                target: worldNode
            });
        }
        // 角色创建（总是需要）
        currentY += 150;
        const charNode = addNode('character-creator', '角色创建');
        connections.push({
            source: 0,
            target: charNode
        });
        // 主线规划
        currentY += 150;
        const plotNode = addNode('plotline-planner', '主线规划');
        connections.push({
            source: titleNode,
            target: plotNode
        });
        connections.push({
            source: charNode,
            target: plotNode
        });
        // 大纲生成
        currentY += 150;
        const outlineNode = addNode('outline-generator', '大纲生成');
        connections.push({
            source: plotNode,
            target: outlineNode
        });
        // 根据长度决定是否需要详细大纲
        if (length === '长篇') {
            currentY += 150;
            const detailOutlineNode = addNode('detailed-outline', '详细大纲');
            connections.push({
                source: outlineNode,
                target: detailOutlineNode
            });
        }
        // 章节生成
        currentY += 150;
        const chapterNode = addNode('chapter-generator', '章节生成');
        const prevNode = length === '长篇' ? nodeIndex - 2 : outlineNode;
        connections.push({
            source: prevNode,
            target: chapterNode
        });
        // 根据需求添加润色和检查
        if (features.includes('高质量') || style.includes('文艺')) {
            currentY += 150;
            const polishNode = addNode('content-polisher', '内容润色', 100);
            connections.push({
                source: chapterNode,
                target: polishNode
            });
            const checkNode = addNode('consistency-checker', '一致性检查', 300);
            connections.push({
                source: chapterNode,
                target: checkNode
            });
            // 最终输出
            currentY += 150;
            const outputNode = addNode('output', '最终输出');
            connections.push({
                source: polishNode,
                target: outputNode
            });
            connections.push({
                source: checkNode,
                target: outputNode
            });
        } else {
            // 简单输出
            currentY += 150;
            const outputNode = addNode('output', '最终输出');
            connections.push({
                source: chapterNode,
                target: outputNode
            });
        }
        return {
            id: 'custom-' + Date.now(),
            name: '自定义工作流',
            description: "为".concat(genre, "类型的").concat(length, "小说定制的工作流"),
            nodes,
            connections,
            tags: [
                '自定义',
                genre,
                length
            ],
            complexity: length === '短篇' ? 'simple' : length === '中篇' ? 'medium' : 'complex'
        };
    }
    // 优化现有工作流
    static optimizeWorkflow(currentNodes, requirements) {
        // 分析现有节点
        const nodeTypes = currentNodes.map((node)=>node.data.type);
        // 检查缺失的关键节点
        const missingNodes = [];
        if (!nodeTypes.includes('input')) missingNodes.push('input');
        if (!nodeTypes.includes('output')) missingNodes.push('output');
        if (!nodeTypes.includes('character-creator')) missingNodes.push('character-creator');
        if (!nodeTypes.includes('outline-generator')) missingNodes.push('outline-generator');
        // 生成优化建议
        const optimizedNodes = [
            ...currentNodes
        ];
        const connections = [];
        // 添加缺失的节点
        missingNodes.forEach((nodeType, index)=>{
            optimizedNodes.push({
                type: nodeType,
                label: this.getNodeLabel(nodeType),
                position: {
                    x: 200 + index * 150,
                    y: 100 + index * 150
                }
            });
        });
        return {
            id: 'optimized-' + Date.now(),
            name: '优化工作流',
            description: '基于现有工作流的优化版本',
            nodes: optimizedNodes,
            connections,
            tags: [
                '优化',
                '改进'
            ],
            complexity: 'medium'
        };
    }
    static getNodeLabel(nodeType) {
        const labels = {
            'input': '输入节点',
            'title-generator': '书名生成',
            'detail-generator': '详情生成',
            'character-creator': '角色创建',
            'worldbuilding': '世界观构建',
            'plotline-planner': '主线规划',
            'outline-generator': '大纲生成',
            'chapter-count-input': '章节数设定',
            'detailed-outline': '详细大纲',
            'chapter-generator': '章节生成',
            'content-polisher': '内容润色',
            'consistency-checker': '一致性检查',
            'condition': '条件分支',
            'loop': '循环执行',
            'output': '结果输出'
        };
        return labels[nodeType] || nodeType;
    }
}
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/aiService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// AI服务集成
__turbopack_context__.s({
    "AIService": ()=>AIService,
    "aiService": ()=>aiService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class AIService {
    // 设置AI配置
    setConfig(config) {
        this.config = config;
        // 保存到localStorage
        localStorage.setItem('ai-config', JSON.stringify(config));
    }
    // 获取AI配置
    getConfig() {
        if (this.config) return this.config;
        const saved = localStorage.getItem('ai-config');
        if (saved) {
            this.config = JSON.parse(saved);
            return this.config;
        }
        return null;
    }
    // 检查是否已配置
    isConfigured() {
        return this.getConfig() !== null;
    }
    // 通用AI请求方法
    async makeRequest(prompt, systemPrompt) {
        const config = this.getConfig();
        if (!config) {
            return {
                success: false,
                error: '请先配置AI API'
            };
        }
        try {
            let response;
            switch(config.provider){
                case 'openai':
                    response = await this.callOpenAI(prompt, systemPrompt, config);
                    break;
                case 'claude':
                    response = await this.callClaude(prompt, systemPrompt, config);
                    break;
                case 'gemini':
                    response = await this.callGemini(prompt, systemPrompt, config);
                    break;
                case 'custom':
                    response = await this.callCustomAPI(prompt, systemPrompt, config);
                    break;
                default:
                    return {
                        success: false,
                        error: '不支持的AI提供商'
                    };
            }
            return {
                success: true,
                data: response
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || '请求失败'
            };
        }
    }
    // OpenAI API调用
    async callOpenAI(prompt) {
        let systemPrompt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '', config = arguments.length > 2 ? arguments[2] : void 0;
        const messages = [];
        if (systemPrompt) {
            messages.push({
                role: 'system',
                content: systemPrompt
            });
        }
        messages.push({
            role: 'user',
            content: prompt
        });
        const response = await fetch(config.baseUrl || 'https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(config.apiKey)
            },
            body: JSON.stringify({
                model: config.model || 'gpt-3.5-turbo',
                messages,
                temperature: config.temperature || 0.7,
                max_tokens: config.maxTokens || 2000
            })
        });
        if (!response.ok) {
            throw new Error("OpenAI API错误: ".concat(response.status, " ").concat(response.statusText));
        }
        const data = await response.json();
        return data.choices[0].message.content;
    }
    // Claude API调用
    async callClaude(prompt) {
        let systemPrompt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '', config = arguments.length > 2 ? arguments[2] : void 0;
        const response = await fetch(config.baseUrl || 'https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': config.apiKey,
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: config.model || 'claude-3-sonnet-20240229',
                max_tokens: config.maxTokens || 2000,
                temperature: config.temperature || 0.7,
                system: systemPrompt,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ]
            })
        });
        if (!response.ok) {
            throw new Error("Claude API错误: ".concat(response.status, " ").concat(response.statusText));
        }
        const data = await response.json();
        return data.content[0].text;
    }
    // Gemini API调用
    async callGemini(prompt) {
        let systemPrompt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '', config = arguments.length > 2 ? arguments[2] : void 0;
        const fullPrompt = systemPrompt ? "".concat(systemPrompt, "\n\n").concat(prompt) : prompt;
        const response = await fetch("".concat(config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta/models', "/").concat(config.model || 'gemini-pro', ":generateContent?key=").concat(config.apiKey), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [
                    {
                        parts: [
                            {
                                text: fullPrompt
                            }
                        ]
                    }
                ],
                generationConfig: {
                    temperature: config.temperature || 0.7,
                    maxOutputTokens: config.maxTokens || 2000
                }
            })
        });
        if (!response.ok) {
            throw new Error("Gemini API错误: ".concat(response.status, " ").concat(response.statusText));
        }
        const data = await response.json();
        return data.candidates[0].content.parts[0].text;
    }
    // 自定义API调用
    async callCustomAPI(prompt) {
        let systemPrompt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '', config = arguments.length > 2 ? arguments[2] : void 0;
        const response = await fetch(config.baseUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(config.apiKey)
            },
            body: JSON.stringify({
                prompt,
                system_prompt: systemPrompt,
                model: config.model,
                temperature: config.temperature || 0.7,
                max_tokens: config.maxTokens || 2000
            })
        });
        if (!response.ok) {
            throw new Error("自定义API错误: ".concat(response.status, " ").concat(response.statusText));
        }
        const data = await response.json();
        return data.response || data.content || data.text;
    }
    // 分析工作流需求
    async analyzeWorkflowRequirements(requirements) {
        const systemPrompt = "你是一个专业的小说创作工作流设计专家。根据用户的需求，分析并推荐最适合的工作流类型。\n\n可选的工作流类型：\n1. quick-novel - 快速小说生成（适合新手，简单流程）\n2. professional-novel - 专业小说创作（完整流程，适合有经验的作者）\n3. fantasy-novel - 奇幻小说专用（重点关注世界观和魔法体系）\n4. romance-novel - 言情小说工作流（专注情感描写和角色关系）\n\n请返回JSON格式的推荐结果，包含：\n- recommended: 推荐的工作流ID\n- confidence: 推荐置信度(0-100)\n- reasons: 推荐理由数组\n- alternatives: 备选方案数组";
        const prompt = "用户需求：\n- 小说类型：".concat(requirements.genre, "\n- 写作风格：").concat(requirements.style, "\n- 作品长度：").concat(requirements.length, "\n- 创作经验：").concat(requirements.experience, "\n- 特殊需求：").concat(requirements.features.join(', '), "\n\n请分析并推荐最适合的工作流。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成自定义工作流
    async generateCustomWorkflow(requirements) {
        const systemPrompt = "你是一个AI小说创作工作流设计师。根据用户需求设计一个定制化的工作流。\n\n可用的节点类型：\n- input: 用户参数输入\n- title-generator: 书名生成\n- detail-generator: 详情生成\n- character-creator: 角色创建\n- worldbuilding: 世界观构建\n- plotline-planner: 主线规划\n- outline-generator: 大纲生成\n- chapter-count-input: 章节数设定\n- detailed-outline: 详细大纲\n- chapter-generator: 章节生成\n- content-polisher: 内容润色\n- consistency-checker: 一致性检查\n- condition: 条件分支\n- loop: 循环执行\n- output: 结果输出\n\n请返回JSON格式的工作流定义，包含：\n- name: 工作流名称\n- description: 工作流描述\n- nodes: 节点数组，每个节点包含 {type, label, position: {x, y}}\n- connections: 连接数组，每个连接包含 {source: 节点索引, target: 节点索引}\n- complexity: 复杂度 (simple/medium/complex)";
        const prompt = "请为以下需求设计一个定制化的小说创作工作流：\n- 小说类型：".concat(requirements.genre, "\n- 写作风格：").concat(requirements.style, "\n- 作品长度：").concat(requirements.length, "\n- 特殊需求：").concat(requirements.features.join(', '), "\n\n请确保工作流逻辑合理，节点连接有序，适合用户的具体需求。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 优化现有工作流
    async optimizeWorkflow(currentNodes, requirements) {
        const systemPrompt = "你是一个工作流优化专家。分析现有的工作流并提供优化建议。\n\n请返回JSON格式的优化建议，包含：\n- issues: 发现的问题数组\n- suggestions: 优化建议数组\n- optimized_workflow: 优化后的工作流定义（如果需要重构）\n- improvement_score: 改进评分(0-100)";
        const nodeTypes = currentNodes.map((node)=>{
            var _node_data;
            return ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.type) || 'unknown';
        });
        const prompt = "当前工作流包含以下节点：".concat(nodeTypes.join(', '), "\n\n用户需求：").concat(JSON.stringify(requirements), "\n\n请分析这个工作流的问题并提供优化建议。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成书名
    async generateTitles(params) {
        var _params_keywords;
        const systemPrompt = "你是一个专业的书名创作专家。根据小说类型、风格和关键词生成吸引人的书名。\n\n请返回JSON格式的结果，包含：\n- titles: 书名数组，每个包含 {title, score, analysis: {appeal, memorability, genre_match, uniqueness, marketability}, suggestions: 优化建议数组}";
        const prompt = "请为以下小说生成".concat(params.count || 5, "个书名：\n- 类型：").concat(params.genre, "\n- 风格：").concat(params.style, "\n- 关键词：").concat(((_params_keywords = params.keywords) === null || _params_keywords === void 0 ? void 0 : _params_keywords.join(', ')) || '无', "\n\n要求书名要有吸引力、易记忆、符合类型特征。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成角色
    async generateCharacter(params) {
        const systemPrompt = "你是一个角色设计专家。根据小说类型和角色定位创建详细的角色设定。\n\n请返回JSON格式的角色信息，包含：\n- name: 角色姓名\n- age: 年龄\n- gender: 性别\n- appearance: 外貌描述\n- personality: 性格特征\n- background: 背景故事\n- skills: 技能特长\n- relationships: 人际关系\n- goals: 目标动机\n- flaws: 性格缺陷";
        const prompt = "请创建一个".concat(params.genre, "小说中的").concat(params.role, "角色：\n").concat(params.background ? "背景要求：".concat(params.background) : '', "\n").concat(params.personality ? "性格要求：".concat(params.personality) : '', "\n\n请提供详细的角色设定。");
        return this.makeRequest(prompt, systemPrompt);
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "config", null);
    }
}
const aiService = new AIService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Home
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$MainLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/MainLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$workflow$2f$WorkflowEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/workflow/WorkflowEditor.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$project$2f$ProjectOverview$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/project/ProjectOverview.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$outline$2f$OutlineManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/outline/OutlineManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$character$2f$CharacterManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/character/CharacterManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$worldbuilding$2f$WorldBuildingManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/worldbuilding/WorldBuildingManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$plotline$2f$PlotLineManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/plotline/PlotLineManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$title$2f$TitleManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/title/TitleManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$document$2f$DocumentManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/document/DocumentManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$prompt$2f$PromptManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/prompt/PromptManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/index.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
function Home() {
    _s();
    const { ui } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])();
    const renderContent = ()=>{
        switch(ui.activeTab){
            case 'workflow':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$workflow$2f$WorkflowEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 22,
                    columnNumber: 16
                }, this);
            case 'projects':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$project$2f$ProjectOverview$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 24,
                    columnNumber: 16
                }, this);
            case 'outlines':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$outline$2f$OutlineManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 26,
                    columnNumber: 16
                }, this);
            case 'characters':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$character$2f$CharacterManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 28,
                    columnNumber: 16
                }, this);
            case 'worldbuilding':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$worldbuilding$2f$WorldBuildingManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 30,
                    columnNumber: 16
                }, this);
            case 'plotlines':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$plotline$2f$PlotLineManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 32,
                    columnNumber: 16
                }, this);
            case 'titles':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$title$2f$TitleManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 34,
                    columnNumber: 16
                }, this);
            case 'documents':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$document$2f$DocumentManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 36,
                    columnNumber: 16
                }, this);
            case 'prompts':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$prompt$2f$PromptManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 38,
                    columnNumber: 16
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$workflow$2f$WorkflowEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 40,
                    columnNumber: 16
                }, this);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$MainLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: renderContent()
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 45,
        columnNumber: 5
    }, this);
}
_s(Home, "yqECtPIhn+ICmQUwF5lWA7aHPuU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_d3b0afb8._.js.map