# AI小说生成工作流平台 - 部署指南

## 概述

本文档详细说明了如何在不同环境中部署AI小说生成工作流平台。

## 环境要求

### 基础要求
- Node.js 18.0 或更高版本
- npm 8.0 或更高版本
- 现代浏览器支持 (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)

### 推荐配置
- CPU: 2核心或更多
- 内存: 4GB RAM 或更多
- 存储: 10GB 可用空间
- 网络: 稳定的互联网连接

## 本地开发环境部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd ai-novel-workflow
```

### 2. 安装依赖
```bash
npm install
```

### 3. 启动开发服务器
```bash
npm run dev
```

### 4. 访问应用
打开浏览器访问: http://localhost:3000

## 生产环境部署

### 方式一: 静态部署 (推荐)

#### 1. 构建生产版本
```bash
npm run build
```

#### 2. 导出静态文件
```bash
npm run export
```

#### 3. 部署到静态托管服务

**Vercel 部署:**
```bash
# 安装 Vercel CLI
npm install -g vercel

# 部署
vercel --prod
```

**Netlify 部署:**
```bash
# 安装 Netlify CLI
npm install -g netlify-cli

# 构建并部署
npm run build
netlify deploy --prod --dir=out
```

**GitHub Pages 部署:**
```bash
# 添加部署脚本到 package.json
"scripts": {
  "deploy": "npm run build && npm run export && gh-pages -d out"
}

# 安装 gh-pages
npm install --save-dev gh-pages

# 部署
npm run deploy
```

### 方式二: 服务器部署

#### 1. 使用 PM2 (推荐)
```bash
# 安装 PM2
npm install -g pm2

# 构建项目
npm run build

# 启动应用
pm2 start npm --name "ai-novel-workflow" -- start

# 保存 PM2 配置
pm2 save
pm2 startup
```

#### 2. 使用 Docker
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

```bash
# 构建镜像
docker build -t ai-novel-workflow .

# 运行容器
docker run -p 3000:3000 ai-novel-workflow
```

#### 3. 使用 Nginx 反向代理
```nginx
# /etc/nginx/sites-available/ai-novel-workflow
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 云平台部署

### 1. Vercel (推荐)

**特点:**
- 零配置部署
- 自动 HTTPS
- 全球 CDN
- 自动预览部署

**部署步骤:**
1. 连接 GitHub 仓库
2. 导入项目到 Vercel
3. 自动构建和部署
4. 获得生产环境 URL

### 2. Netlify

**特点:**
- 静态站点托管
- 表单处理
- 函数支持
- 分支预览

**部署步骤:**
1. 连接 Git 仓库
2. 设置构建命令: `npm run build && npm run export`
3. 设置发布目录: `out`
4. 部署

### 3. AWS S3 + CloudFront

**特点:**
- 高可用性
- 全球分发
- 成本效益
- 可扩展性

**部署步骤:**
1. 创建 S3 存储桶
2. 启用静态网站托管
3. 上传构建文件
4. 配置 CloudFront 分发

### 4. Azure Static Web Apps

**特点:**
- 集成 CI/CD
- 自定义域名
- API 支持
- 认证集成

**部署步骤:**
1. 在 Azure 门户创建静态 Web 应用
2. 连接 GitHub 仓库
3. 配置构建设置
4. 自动部署

## 环境配置

### 1. 环境变量
```bash
# .env.local
NEXT_PUBLIC_APP_NAME=AI小说生成工作流平台
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_API_URL=https://api.example.com
```

### 2. Next.js 配置
```javascript
// next.config.js
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  assetPrefix: process.env.NODE_ENV === 'production' ? '/ai-novel-workflow' : '',
  basePath: process.env.NODE_ENV === 'production' ? '/ai-novel-workflow' : '',
}

module.exports = nextConfig
```

## 性能优化

### 1. 构建优化
```javascript
// next.config.js
const nextConfig = {
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  experimental: {
    optimizeCss: true,
  },
  swcMinify: true,
}
```

### 2. 缓存策略
```javascript
// next.config.js
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ]
  },
}
```

### 3. 代码分割
```javascript
// 动态导入
const DynamicComponent = dynamic(() => import('../components/HeavyComponent'), {
  loading: () => <p>Loading...</p>,
})
```

## 监控和维护

### 1. 错误监控
```bash
# 安装 Sentry
npm install @sentry/nextjs

# 配置 Sentry
# sentry.client.config.js
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.SENTRY_DSN,
})
```

### 2. 性能监控
```javascript
// 使用 Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

getCLS(console.log)
getFID(console.log)
getFCP(console.log)
getLCP(console.log)
getTTFB(console.log)
```

### 3. 日志记录
```javascript
// 使用 Winston
import winston from 'winston'

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
})
```

## 安全配置

### 1. HTTPS 配置
```javascript
// next.config.js
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains; preload'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
        ],
      },
    ]
  },
}
```

### 2. CSP 配置
```javascript
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
          },
        ],
      },
    ]
  },
}
```

## 备份和恢复

### 1. 数据备份
```bash
# 备份本地存储数据
# 用户可以通过导出功能备份项目数据
```

### 2. 配置备份
```bash
# 备份配置文件
cp .env.local .env.backup
cp next.config.js next.config.backup.js
```

## 故障排除

### 1. 常见问题

**构建失败:**
```bash
# 清理缓存
npm run clean
rm -rf .next
rm -rf node_modules
npm install
```

**内存不足:**
```bash
# 增加 Node.js 内存限制
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

**依赖冲突:**
```bash
# 检查依赖
npm audit
npm audit fix
```

### 2. 调试工具
```bash
# 启用调试模式
DEBUG=* npm run dev

# 分析包大小
npm install -g @next/bundle-analyzer
ANALYZE=true npm run build
```

## 更新和维护

### 1. 依赖更新
```bash
# 检查过时的包
npm outdated

# 更新依赖
npm update

# 更新 Next.js
npm install next@latest react@latest react-dom@latest
```

### 2. 安全更新
```bash
# 安全审计
npm audit

# 修复安全问题
npm audit fix
```

### 3. 版本管理
```bash
# 使用语义化版本
npm version patch  # 1.0.0 -> 1.0.1
npm version minor  # 1.0.0 -> 1.1.0
npm version major  # 1.0.0 -> 2.0.0
```

---

**注意**: 在生产环境部署前，请确保完成所有测试，并备份重要数据。
