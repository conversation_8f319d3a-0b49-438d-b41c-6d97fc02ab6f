'use client';

import React, { useState, useCallback } from 'react';
import {
  Modal,
  Button,
  Progress,
  Card,
  List,
  Typography,
  Space,
  Tag,
  Alert,
  Divider,
  Spin,
  message
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { createWorkflowExecutor } from '@/utils/workflowExecutor';
import { aiService } from '@/utils/aiService';

const { Title, Text, Paragraph } = Typography;

interface WorkflowRunnerProps {
  visible: boolean;
  onClose: () => void;
  nodes: any[];
  edges: any[];
  projectId: string;
}

interface NodeStatus {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  result?: any;
  error?: string;
  duration?: number;
}

const WorkflowRunner: React.FC<WorkflowRunnerProps> = ({
  visible,
  onClose,
  nodes,
  edges,
  projectId
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [nodeStatuses, setNodeStatuses] = useState<Record<string, NodeStatus>>({});
  const [currentNodeId, setCurrentNodeId] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [executionResults, setExecutionResults] = useState<any>({});
  const [executionLog, setExecutionLog] = useState<string[]>([]);

  // 初始化节点状态
  const initializeNodeStatuses = useCallback(() => {
    const statuses: Record<string, NodeStatus> = {};
    nodes.forEach(node => {
      statuses[node.id] = {
        id: node.id,
        status: 'pending'
      };
    });
    setNodeStatuses(statuses);
    setProgress(0);
    setExecutionResults({});
    setExecutionLog([]);
  }, [nodes]);

  // 获取节点执行顺序
  const getExecutionOrder = useCallback(() => {
    // 简单的拓扑排序实现
    const visited = new Set<string>();
    const order: string[] = [];
    
    const visit = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);
      
      // 找到所有依赖的节点
      const dependencies = edges
        .filter(edge => edge.target === nodeId)
        .map(edge => edge.source);
      
      dependencies.forEach(depId => visit(depId));
      order.push(nodeId);
    };

    nodes.forEach(node => visit(node.id));
    return order;
  }, [nodes, edges]);

  // 节点进度回调
  const onNodeProgress = useCallback((nodeId: string, status: string, result?: any) => {
    setNodeStatuses(prev => ({
      ...prev,
      [nodeId]: {
        ...prev[nodeId],
        status: status as any,
        result: status === 'completed' ? result : prev[nodeId].result,
        error: status === 'error' ? result : undefined
      }
    }));

    if (status === 'running') {
      setCurrentNodeId(nodeId);
      const node = nodes.find(n => n.id === nodeId);
      const logMessage = `开始执行: ${node?.data?.label || nodeId}`;
      setExecutionLog(prev => [...prev, logMessage]);
    } else if (status === 'completed') {
      const node = nodes.find(n => n.id === nodeId);
      const logMessage = `完成执行: ${node?.data?.label || nodeId}`;
      setExecutionLog(prev => [...prev, logMessage]);
      
      // 更新进度
      const completedCount = Object.values(nodeStatuses).filter(s => s.status === 'completed').length + 1;
      setProgress((completedCount / nodes.length) * 100);
    } else if (status === 'error') {
      const node = nodes.find(n => n.id === nodeId);
      const logMessage = `执行失败: ${node?.data?.label || nodeId} - ${result}`;
      setExecutionLog(prev => [...prev, logMessage]);
    }
  }, [nodes, nodeStatuses]);

  // 开始执行工作流
  const handleStartExecution = useCallback(async () => {
    // 检查AI配置
    if (!aiService.isConfigured()) {
      message.warning('请先配置AI API');
      return;
    }

    setIsRunning(true);
    setIsPaused(false);
    initializeNodeStatuses();

    const executionOrder = getExecutionOrder();
    const context = {
      projectId,
      workflowId: 'default',
      variables: {},
      results: {}
    };

    const executor = createWorkflowExecutor(context, onNodeProgress);

    try {
      for (const nodeId of executionOrder) {
        if (isPaused) {
          message.info('工作流执行已暂停');
          break;
        }

        const node = nodes.find(n => n.id === nodeId);
        if (!node) continue;

        const result = await executor.executeNode(node);
        
        if (!result.success) {
          message.error(`节点执行失败: ${node.data.label}`);
          break;
        }

        // 保存执行结果
        setExecutionResults(prev => ({
          ...prev,
          [nodeId]: result.data
        }));
      }

      if (!isPaused) {
        message.success('工作流执行完成！');
        setProgress(100);
      }

    } catch (error: any) {
      message.error(`工作流执行出错: ${error.message}`);
    } finally {
      setIsRunning(false);
      setCurrentNodeId(null);
    }
  }, [projectId, nodes, isPaused, initializeNodeStatuses, getExecutionOrder, onNodeProgress]);

  // 暂停执行
  const handlePauseExecution = useCallback(() => {
    setIsPaused(true);
    setIsRunning(false);
    message.info('工作流执行已暂停');
  }, []);

  // 停止执行
  const handleStopExecution = useCallback(() => {
    setIsRunning(false);
    setIsPaused(false);
    setCurrentNodeId(null);
    message.info('工作流执行已停止');
  }, []);

  // 获取节点状态图标
  const getNodeStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Spin size="small" />;
      case 'completed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  // 获取节点状态颜色
  const getNodeStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'processing';
      case 'completed':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Modal
      title={
        <div className="flex items-center space-x-2">
          <RobotOutlined className="text-blue-500" />
          <span>AI工作流执行器</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
      destroyOnClose
    >
      <div className="space-y-6">
        {/* 执行控制 */}
        <Card size="small">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleStartExecution}
                disabled={isRunning}
                loading={isRunning}
              >
                {isRunning ? '执行中...' : '开始执行'}
              </Button>
              
              <Button
                icon={<PauseCircleOutlined />}
                onClick={handlePauseExecution}
                disabled={!isRunning}
              >
                暂停
              </Button>
              
              <Button
                icon={<StopOutlined />}
                onClick={handleStopExecution}
                disabled={!isRunning && !isPaused}
              >
                停止
              </Button>
            </div>

            <div className="text-right">
              <Text type="secondary">
                节点总数: {nodes.length} | 
                已完成: {Object.values(nodeStatuses).filter(s => s.status === 'completed').length}
              </Text>
            </div>
          </div>

          {/* 进度条 */}
          <div className="mt-4">
            <Progress 
              percent={Math.round(progress)} 
              status={isRunning ? 'active' : progress === 100 ? 'success' : 'normal'}
              showInfo={true}
            />
          </div>
        </Card>

        {/* AI配置提醒 */}
        {!aiService.isConfigured() && (
          <Alert
            message="AI API未配置"
            description="请先在设置中配置AI API，否则无法执行工作流"
            type="warning"
            showIcon
            closable
          />
        )}

        {/* 节点执行状态 */}
        <Card title="节点执行状态" size="small">
          <List
            size="small"
            dataSource={nodes}
            renderItem={(node) => {
              const status = nodeStatuses[node.id];
              return (
                <List.Item>
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center space-x-3">
                      {getNodeStatusIcon(status?.status || 'pending')}
                      <span>{node.data.label}</span>
                      <Tag color={getNodeStatusColor(status?.status || 'pending')}>
                        {status?.status === 'running' ? '执行中' :
                         status?.status === 'completed' ? '已完成' :
                         status?.status === 'error' ? '失败' : '等待中'}
                      </Tag>
                    </div>
                    
                    {status?.error && (
                      <Text type="danger" className="text-sm">
                        {status.error}
                      </Text>
                    )}
                  </div>
                </List.Item>
              );
            }}
          />
        </Card>

        {/* 执行日志 */}
        {executionLog.length > 0 && (
          <Card title="执行日志" size="small">
            <div className="max-h-40 overflow-y-auto">
              {executionLog.map((log, index) => (
                <div key={index} className="text-sm text-gray-600 mb-1">
                  {log}
                </div>
              ))}
            </div>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default WorkflowRunner;
