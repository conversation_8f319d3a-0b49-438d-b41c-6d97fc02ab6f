function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import PoundCircleFilledSvg from "@ant-design/icons-svg/es/asn/PoundCircleFilled";
import AntdIcon from "../components/AntdIcon";
const PoundCircleFilled = (props, ref) => /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
  ref: ref,
  icon: PoundCircleFilledSvg
}));

/**![pound-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNDYgNjU4YzAgNC40LTMuNiA4LTggOEgzNzYuMmMtNC40IDAtOC0zLjYtOC04di0zOC41YzAtMy43IDIuNS02LjkgNi4xLTcuOCA0NC0xMC45IDcyLjgtNDkgNzIuOC05NC4yIDAtMTQuNy0yLjUtMjkuNC01LjktNDQuMkgzNzRjLTQuNCAwLTgtMy42LTgtOHYtMzBjMC00LjQgMy42LTggOC04aDUzLjdjLTcuOC0yNS4xLTE0LjYtNTAuNy0xNC42LTc3LjEgMC03NS44IDU4LjYtMTIwLjMgMTUxLjUtMTIwLjMgMjYuNSAwIDUxLjQgNS41IDcwLjMgMTIuNyAzLjEgMS4yIDUuMiA0LjIgNS4yIDcuNXYzOS41YTggOCAwIDAxLTEwLjYgNy42Yy0xNy45LTYuNC0zOS0xMC41LTYwLjQtMTAuNS01My4zIDAtODcuMyAyNi42LTg3LjMgNzAuMiAwIDI0LjcgNi4yIDQ3LjkgMTMuNCA3MC41aDExMmM0LjQgMCA4IDMuNiA4IDh2MzBjMCA0LjQtMy42IDgtOCA4aC05OC42YzMuMSAxMy4yIDUuMyAyNi45IDUuMyA0MSAwIDQwLjctMTYuNSA3My45LTQzLjkgOTEuMXY0LjdoMTgwYzQuNCAwIDggMy42IDggOFY3MjJ6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(PoundCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'PoundCircleFilled';
}
export default RefIcon;