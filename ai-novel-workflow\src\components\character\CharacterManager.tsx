'use client';

import React, { useState, useMemo } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Avatar,
  Tag,
  Tabs,
  List,
  Popconfirm,
  message,
  Row,
  Col,
  Divider,
  Badge,
  Tooltip
} from 'antd';
import {
  TeamOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  HeartOutlined,
  ThunderboltOutlined,
  CrownOutlined,
  StarOutlined,
  EyeOutlined,
  LinkOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/store';
import type { Character } from '@/types';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CharacterManager: React.FC = () => {
  const {
    currentProject,
    characters,
    addCharacter,
    updateCharacter,
    deleteCharacter,
    getCharacters
  } = useAppStore();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCharacter, setEditingCharacter] = useState<Character | null>(null);
  const [selected<PERSON>haracter, setSelectedCharacter] = useState<Character | null>(null);
  const [activeTab, setActiveTab] = useState('list');
  const [form] = Form.useForm();

  const projectCharacters = currentProject ? getCharacters(currentProject.id) : [];

  // 角色模板
  const characterTemplates = [
    {
      name: '主角模板',
      role: 'protagonist' as const,
      personality: ['勇敢', '善良', '坚韧'],
      description: '故事的主要角色，推动情节发展'
    },
    {
      name: '反派模板',
      role: 'antagonist' as const,
      personality: ['狡猾', '野心勃勃', '冷酷'],
      description: '与主角对立的角色'
    },
    {
      name: '配角模板',
      role: 'supporting' as const,
      personality: ['忠诚', '幽默', '可靠'],
      description: '支持主角的重要角色'
    }
  ];

  const handleCreateCharacter = (template?: any) => {
    setEditingCharacter(null);
    if (template) {
      form.setFieldsValue({
        role: template.role,
        personality: template.personality,
      });
    } else {
      form.resetFields();
    }
    setIsModalVisible(true);
  };

  const handleEditCharacter = (character: Character) => {
    setEditingCharacter(character);
    form.setFieldsValue({
      name: character.name,
      age: character.age,
      gender: character.gender,
      role: character.role,
      personality: character.personality,
      background: character.background,
      appearance: character.appearance,
      dialogueStyle: character.dialogueStyle,
      developmentArc: character.developmentArc,
    });
    setIsModalVisible(true);
  };

  const handleDeleteCharacter = (characterId: string) => {
    if (currentProject) {
      deleteCharacter(currentProject.id, characterId);
      message.success('角色已删除');
      if (selectedCharacter?.id === characterId) {
        setSelectedCharacter(null);
      }
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingCharacter) {
        // 更新角色
        if (currentProject) {
          updateCharacter(currentProject.id, editingCharacter.id, {
            ...values,
            personality: values.personality || [],
            relationships: editingCharacter.relationships || [],
            appearances: editingCharacter.appearances || [],
          });
          message.success('角色已更新');
        }
      } else {
        // 创建新角色
        if (currentProject) {
          addCharacter(currentProject.id, {
            ...values,
            personality: values.personality || [],
            relationships: [],
            appearances: [],
          });
          message.success('角色已创建');
        }
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setEditingCharacter(null);
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'protagonist': return <CrownOutlined className="text-yellow-500" />;
      case 'antagonist': return <ThunderboltOutlined className="text-red-500" />;
      case 'supporting': return <StarOutlined className="text-blue-500" />;
      default: return <UserOutlined className="text-gray-500" />;
    }
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'protagonist': return '主角';
      case 'antagonist': return '反派';
      case 'supporting': return '配角';
      case 'minor': return '次要角色';
      default: return '未知';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'protagonist': return 'gold';
      case 'antagonist': return 'red';
      case 'supporting': return 'blue';
      case 'minor': return 'default';
      default: return 'default';
    }
  };

  if (!currentProject) {
    return (
      <div className="p-8 text-center">
        <Title level={3}>请先选择或创建一个项目</Title>
        <Text type="secondary">
          您需要先在项目总览中创建或选择一个项目，然后才能管理角色。
        </Text>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Title level={2}>角色管理</Title>
          <Text type="secondary">管理小说角色设定和关系 - 项目: {currentProject.name}</Text>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => handleCreateCharacter()}
          >
            创建角色
          </Button>
        </Space>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'list',
            label: (
              <span>
                <TeamOutlined />
                角色列表 ({projectCharacters.length})
              </span>
            ),
            children: (
              <div>
                {/* 角色模板快速创建 */}
                <Card className="mb-6" title="快速创建">
                  <Row gutter={16}>
                    {characterTemplates.map((template, index) => (
                      <Col span={8} key={index}>
                        <Card
                          size="small"
                          className="cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => handleCreateCharacter(template)}
                        >
                          <div className="text-center">
                            {getRoleIcon(template.role)}
                            <div className="mt-2">
                              <Text strong>{template.name}</Text>
                              <div className="text-xs text-gray-500 mt-1">
                                {template.description}
                              </div>
                            </div>
                          </div>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                </Card>

                {/* 角色列表 */}
                {projectCharacters.length === 0 ? (
                  <Card className="text-center py-12">
                    <TeamOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                    <div className="mt-4">
                      <Text type="secondary">还没有创建任何角色</Text>
                      <br />
                      <Text type="secondary">点击上方按钮或使用模板快速创建角色</Text>
                    </div>
                  </Card>
                ) : (
                  <Row gutter={[16, 16]}>
                    {projectCharacters.map((character) => (
                      <Col span={8} key={character.id}>
                        <Card
                          className="hover:shadow-lg transition-shadow cursor-pointer"
                          onClick={() => setSelectedCharacter(character)}
                          actions={[
                            <Tooltip title="查看详情" key="view">
                              <EyeOutlined onClick={(e) => {
                                e.stopPropagation();
                                setSelectedCharacter(character);
                              }} />
                            </Tooltip>,
                            <Tooltip title="编辑" key="edit">
                              <EditOutlined onClick={(e) => {
                                e.stopPropagation();
                                handleEditCharacter(character);
                              }} />
                            </Tooltip>,
                            <Popconfirm
                              key="delete"
                              title="确定要删除这个角色吗？"
                              onConfirm={(e) => {
                                e?.stopPropagation();
                                handleDeleteCharacter(character.id);
                              }}
                              okText="确定"
                              cancelText="取消"
                            >
                              <DeleteOutlined onClick={(e) => e.stopPropagation()} />
                            </Popconfirm>
                          ]}
                        >
                          <div className="text-center">
                            <Avatar
                              size={64}
                              icon={<UserOutlined />}
                              src={character.avatar}
                              className="mb-3"
                            />
                            <div className="mb-2">
                              <Text strong className="text-lg">{character.name}</Text>
                              {character.age && (
                                <Text type="secondary" className="ml-2">({character.age}岁)</Text>
                              )}
                            </div>
                            <div className="mb-2">
                              <Tag color={getRoleColor(character.role)} icon={getRoleIcon(character.role)}>
                                {getRoleText(character.role)}
                              </Tag>
                              {character.gender && (
                                <Tag>{character.gender}</Tag>
                              )}
                            </div>
                            <Paragraph
                              ellipsis={{ rows: 2 }}
                              type="secondary"
                              className="text-sm"
                            >
                              {character.background || '暂无背景描述'}
                            </Paragraph>
                            {character.personality.length > 0 && (
                              <div className="mt-2">
                                {character.personality.slice(0, 3).map((trait, index) => (
                                  <Tag key={index} size="small" color="blue">
                                    {trait}
                                  </Tag>
                                ))}
                                {character.personality.length > 3 && (
                                  <Tag size="small">+{character.personality.length - 3}</Tag>
                                )}
                              </div>
                            )}
                          </div>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                )}
              </div>
            ),
          },
          {
            key: 'relationships',
            label: (
              <span>
                <LinkOutlined />
                关系图谱
              </span>
            ),
            children: (
              <Card className="text-center py-12">
                <LinkOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                <div className="mt-4">
                  <Text type="secondary">角色关系图谱功能开发中</Text>
                  <br />
                  <Text type="secondary">即将支持可视化角色关系网络</Text>
                </div>
              </Card>
            ),
          },
        ]}
      />

      {/* 角色详情侧边栏 */}
      {selectedCharacter && (
        <Modal
          title={`角色详情 - ${selectedCharacter.name}`}
          open={!!selectedCharacter}
          onCancel={() => setSelectedCharacter(null)}
          footer={[
            <Button key="edit" type="primary" onClick={() => {
              handleEditCharacter(selectedCharacter);
              setSelectedCharacter(null);
            }}>
              编辑角色
            </Button>,
            <Button key="close" onClick={() => setSelectedCharacter(null)}>
              关闭
            </Button>
          ]}
          width={800}
        >
          <Row gutter={24}>
            <Col span={8}>
              <div className="text-center">
                <Avatar
                  size={120}
                  icon={<UserOutlined />}
                  src={selectedCharacter.avatar}
                  className="mb-4"
                />
                <div className="mb-2">
                  <Title level={4}>{selectedCharacter.name}</Title>
                  {selectedCharacter.age && (
                    <Text type="secondary">年龄: {selectedCharacter.age}岁</Text>
                  )}
                </div>
                <div className="mb-4">
                  <Tag color={getRoleColor(selectedCharacter.role)} icon={getRoleIcon(selectedCharacter.role)}>
                    {getRoleText(selectedCharacter.role)}
                  </Tag>
                  {selectedCharacter.gender && (
                    <Tag>{selectedCharacter.gender}</Tag>
                  )}
                </div>
              </div>
            </Col>
            <Col span={16}>
              <div className="space-y-4">
                <div>
                  <Text strong>性格特征:</Text>
                  <div className="mt-2">
                    {selectedCharacter.personality.map((trait, index) => (
                      <Tag key={index} color="blue">{trait}</Tag>
                    ))}
                  </div>
                </div>

                {selectedCharacter.background && (
                  <div>
                    <Text strong>角色背景:</Text>
                    <Paragraph className="mt-2">{selectedCharacter.background}</Paragraph>
                  </div>
                )}

                {selectedCharacter.appearance && (
                  <div>
                    <Text strong>外貌描述:</Text>
                    <Paragraph className="mt-2">{selectedCharacter.appearance}</Paragraph>
                  </div>
                )}

                {selectedCharacter.dialogueStyle && (
                  <div>
                    <Text strong>对话风格:</Text>
                    <Paragraph className="mt-2">{selectedCharacter.dialogueStyle}</Paragraph>
                  </div>
                )}

                {selectedCharacter.developmentArc && (
                  <div>
                    <Text strong>发展轨迹:</Text>
                    <Paragraph className="mt-2">{selectedCharacter.developmentArc}</Paragraph>
                  </div>
                )}

                <div>
                  <Text strong>出场记录:</Text>
                  <div className="mt-2">
                    <Badge count={selectedCharacter.appearances.length} showZero>
                      <Text type="secondary">章节出场次数</Text>
                    </Badge>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Modal>
      )}

      {/* 创建/编辑角色模态框 */}
      <Modal
        title={editingCharacter ? '编辑角色' : '创建角色'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={800}
        okText={editingCharacter ? '更新' : '创建'}
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色姓名"
                rules={[{ required: true, message: '请输入角色姓名' }]}
              >
                <Input placeholder="请输入角色姓名" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="age" label="年龄">
                <Input type="number" placeholder="年龄" />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="gender" label="性别">
                <Select placeholder="请选择性别">
                  <Option value="男">男</Option>
                  <Option value="女">女</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="role"
            label="角色定位"
            rules={[{ required: true, message: '请选择角色定位' }]}
          >
            <Select placeholder="请选择角色定位">
              <Option value="protagonist">主角</Option>
              <Option value="antagonist">反派</Option>
              <Option value="supporting">配角</Option>
              <Option value="minor">次要角色</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="personality"
            label="性格特征"
            help="请输入角色的性格特征，用逗号分隔"
          >
            <Select
              mode="tags"
              placeholder="请输入性格特征，如：勇敢、善良、聪明"
              tokenSeparators={[',']}
            >
              <Option value="勇敢">勇敢</Option>
              <Option value="善良">善良</Option>
              <Option value="聪明">聪明</Option>
              <Option value="冷静">冷静</Option>
              <Option value="幽默">幽默</Option>
              <Option value="坚韧">坚韧</Option>
              <Option value="狡猾">狡猾</Option>
              <Option value="野心勃勃">野心勃勃</Option>
              <Option value="忠诚">忠诚</Option>
              <Option value="可靠">可靠</Option>
            </Select>
          </Form.Item>

          <Form.Item name="background" label="角色背景">
            <TextArea
              rows={3}
              placeholder="请描述角色的背景故事、成长经历等..."
            />
          </Form.Item>

          <Form.Item name="appearance" label="外貌描述">
            <TextArea
              rows={2}
              placeholder="请描述角色的外貌特征..."
            />
          </Form.Item>

          <Form.Item name="dialogueStyle" label="对话风格">
            <TextArea
              rows={2}
              placeholder="请描述角色的说话方式、语言特点..."
            />
          </Form.Item>

          <Form.Item name="developmentArc" label="发展轨迹">
            <TextArea
              rows={3}
              placeholder="请描述角色在故事中的成长和变化..."
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CharacterManager;
