'use client';

import React, { useCallback, useState, useMemo } from 'react';
import { Card, Button, Space, Typography, Divider, message, Modal, Form, Input, Select } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  SaveOutlined,
  PlusOutlined,
  SettingOutlined,
  DeleteOutlined,
  LinkOutlined,
  RobotOutlined
} from '@ant-design/icons';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  NodeTypes
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { useAppStore } from '@/store';
import CustomNode from './CustomNode';
import WorkflowAIAssistant from './WorkflowAIAssistant';
import { WorkflowTemplate } from '@/utils/workflowAI';

const { Title, Text } = Typography;

const WorkflowEditor: React.FC = () => {
  const {
    currentProject,
    currentWorkflow,
    addNode,
    updateNode,
    deleteNode,
    connectNodes,
    saveWorkflow,
    executionContexts,
    startExecution,
    pauseExecution,
    stopExecution
  } = useAppStore();

  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);
  const [isAIAssistantVisible, setIsAIAssistantVisible] = useState(false);
  const [editingNode, setEditingNode] = useState<any>(null);
  const [form] = Form.useForm();

  const executionContext = currentProject ? executionContexts[currentProject.id] : null;

  // 转换工作流数据为React Flow格式
  const initialNodes: Node[] = currentWorkflow.map(node => ({
    id: node.id,
    type: 'custom',
    position: node.position,
    data: {
      label: node.data.label,
      type: node.type,
      status: node.data.status,
      description: getNodeDescription(node.type),
      config: node.data.config,
      onEdit: () => handleEditNode(node),
      onDelete: () => handleDeleteNode(node.id),
    },
  }));

  const initialEdges: Edge[] = currentWorkflow.flatMap(node =>
    node.connections.map(conn => ({
      id: `${conn.sourceId}-${conn.targetId}`,
      source: conn.sourceId,
      target: conn.targetId,
      type: 'smoothstep',
      animated: node.data.status === 'running',
    }))
  );

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const reactFlowNodeTypes: NodeTypes = useMemo(() => ({
    custom: CustomNode,
  }), []);

  const getNodeDescription = (nodeType: string) => {
    const descriptions: Record<string, string> = {
      'input': '用户参数输入',
      'title-generator': 'AI生成书名候选',
      'detail-generator': '生成小说基础信息',
      'character-creator': 'AI生成角色设定',
      'worldbuilding': 'AI生成世界背景',
      'plotline-planner': 'AI生成主要故事线',
      'outline-generator': 'AI生成故事结构',
      'chapter-count-input': '用户指定章节总数',
      'detailed-outline': '生成详细情节要点',
      'chapter-generator': 'AI生成具体章节内容',
      'content-polisher': 'AI优化文本质量',
      'consistency-checker': '检查内容一致性',
      'condition': '条件分支',
      'loop': '循环执行',
      'output': '最终结果展示',
    };
    return descriptions[nodeType] || '未知节点类型';
  };

  const handleSaveWorkflow = useCallback(() => {
    if (currentProject) {
      saveWorkflow(currentProject.id);
      message.success('工作流已保存');
    }
  }, [currentProject, saveWorkflow]);

  const handleStartExecution = useCallback(() => {
    if (currentProject) {
      startExecution(currentProject.id, 'default');
      message.info('工作流开始执行');
    }
  }, [currentProject, startExecution]);

  const handlePauseExecution = useCallback(() => {
    if (currentProject) {
      pauseExecution(currentProject.id);
      message.info('工作流已暂停');
    }
  }, [currentProject, pauseExecution]);

  const handleStopExecution = useCallback(() => {
    if (currentProject) {
      stopExecution(currentProject.id);
      message.info('工作流已停止');
    }
  }, [currentProject, stopExecution]);

  const handleEditNode = useCallback((node: any) => {
    setEditingNode(node);
    form.setFieldsValue({
      label: node.data.label,
      ...node.data.config,
    });
    setIsConfigModalVisible(true);
  }, [form]);

  const handleDeleteNode = useCallback((nodeId: string) => {
    deleteNode(nodeId);
    setNodes(nodes => nodes.filter(n => n.id !== nodeId));
    setEdges(edges => edges.filter(e => e.source !== nodeId && e.target !== nodeId));
    message.success('节点已删除');
  }, [deleteNode, setNodes, setEdges]);

  const handleCreateSampleWorkflow = useCallback(() => {
    // 清空现有节点和连接
    setNodes([]);
    setEdges([]);

    // 创建示例节点
    const sampleNodes = [
      {
        id: 'input-1',
        type: 'custom',
        position: { x: 100, y: 100 },
        data: {
          label: '用户输入',
          type: 'input',
          status: 'idle' as const,
          description: '输入创作主题和风格',
          onEdit: () => {},
          onDelete: () => handleDeleteNode('input-1'),
        },
      },
      {
        id: 'title-gen-1',
        type: 'custom',
        position: { x: 100, y: 250 },
        data: {
          label: '书名生成',
          type: 'title-generator',
          status: 'idle' as const,
          description: 'AI生成书名候选',
          onEdit: () => {},
          onDelete: () => handleDeleteNode('title-gen-1'),
        },
      },
      {
        id: 'character-1',
        type: 'custom',
        position: { x: 350, y: 250 },
        data: {
          label: '角色创建',
          type: 'character-creator',
          status: 'idle' as const,
          description: 'AI生成角色设定',
          onEdit: () => {},
          onDelete: () => handleDeleteNode('character-1'),
        },
      },
      {
        id: 'outline-1',
        type: 'custom',
        position: { x: 225, y: 400 },
        data: {
          label: '大纲生成',
          type: 'outline-generator',
          status: 'idle' as const,
          description: 'AI生成故事结构',
          onEdit: () => {},
          onDelete: () => handleDeleteNode('outline-1'),
        },
      },
      {
        id: 'output-1',
        type: 'custom',
        position: { x: 225, y: 550 },
        data: {
          label: '结果输出',
          type: 'output',
          status: 'idle' as const,
          description: '最终结果展示',
          onEdit: () => {},
          onDelete: () => handleDeleteNode('output-1'),
        },
      },
    ];

    // 创建示例连接
    const sampleEdges: Edge[] = [
      {
        id: 'input-1-title-gen-1',
        source: 'input-1',
        target: 'title-gen-1',
        type: 'smoothstep',
        animated: false,
        style: { stroke: '#3b82f6', strokeWidth: 2 },
      },
      {
        id: 'input-1-character-1',
        source: 'input-1',
        target: 'character-1',
        type: 'smoothstep',
        animated: false,
        style: { stroke: '#3b82f6', strokeWidth: 2 },
      },
      {
        id: 'title-gen-1-outline-1',
        source: 'title-gen-1',
        target: 'outline-1',
        type: 'smoothstep',
        animated: false,
        style: { stroke: '#3b82f6', strokeWidth: 2 },
      },
      {
        id: 'character-1-outline-1',
        source: 'character-1',
        target: 'outline-1',
        type: 'smoothstep',
        animated: false,
        style: { stroke: '#3b82f6', strokeWidth: 2 },
      },
      {
        id: 'outline-1-output-1',
        source: 'outline-1',
        target: 'output-1',
        type: 'smoothstep',
        animated: false,
        style: { stroke: '#3b82f6', strokeWidth: 2 },
      },
    ];

    setNodes(sampleNodes);
    setEdges(sampleEdges);

    message.success('示例工作流已创建！您可以拖拽节点来连接它们');
  }, [setNodes, setEdges, handleDeleteNode]);

  // 处理AI工作流应用
  const handleApplyAIWorkflow = useCallback((template: WorkflowTemplate) => {
    // 清空现有节点和连接
    setNodes([]);
    setEdges([]);

    // 转换模板节点为React Flow格式
    const reactFlowNodes = template.nodes.map((node, index) => ({
      id: `node-${index}`,
      type: 'custom',
      position: node.position,
      data: {
        label: node.label,
        type: node.type,
        status: 'idle' as const,
        description: getNodeDescription(node.type),
        config: node.config || {},
        onEdit: () => {},
        onDelete: () => handleDeleteNode(`node-${index}`),
      },
    }));

    // 转换模板连接为React Flow格式
    const reactFlowEdges = template.connections.map((conn, index) => ({
      id: `edge-${index}`,
      source: `node-${conn.source}`,
      target: `node-${conn.target}`,
      type: 'smoothstep',
      animated: false,
      style: { stroke: '#3b82f6', strokeWidth: 2 },
    }));

    setNodes(reactFlowNodes);
    setEdges(reactFlowEdges);

    message.success(`AI工作流"${template.name}"已应用成功！`);
  }, [setNodes, setEdges, handleDeleteNode, getNodeDescription]);



  const handleAddNode = useCallback((nodeType: string) => {
    const nodeId = `node-${Date.now()}`;
    const position = { x: Math.random() * 400, y: Math.random() * 300 };

    // 同时添加到React Flow
    const reactFlowNode: Node = {
      id: nodeId,
      type: 'custom',
      position,
      data: {
        label: `新${nodeType}节点`,
        type: nodeType,
        status: 'idle' as const,
        description: getNodeDescription(nodeType),
        config: {},
        onEdit: () => handleEditNode({ id: nodeId, type: nodeType, data: { label: `新${nodeType}节点`, config: {}, status: 'idle' as const } }),
        onDelete: () => handleDeleteNode(nodeId),
      },
    };
    setNodes(nodes => [...nodes, reactFlowNode]);
    message.success('节点已添加');
  }, [setNodes, handleEditNode, handleDeleteNode]);

  const onConnect = useCallback((params: Connection) => {
    // 检查连接的有效性
    if (!params.source || !params.target) {
      message.error('连接失败：源节点或目标节点无效');
      return;
    }

    // 防止自连接
    if (params.source === params.target) {
      message.warning('不能连接到自身节点');
      return;
    }

    // 创建带样式的连接
    const newEdge = {
      ...params,
      type: 'smoothstep',
      animated: false,
      style: {
        stroke: '#3b82f6',
        strokeWidth: 2
      },
    };

    setEdges((eds) => addEdge(newEdge, eds));

    // 同时更新store中的连接信息
    if (currentProject && params.source && params.target) {
      connectNodes(params.source, params.target);
    }

    message.success('节点连接成功');
  }, [setEdges, connectNodes, currentProject]);

  const nodeTypeOptions = [
    { key: 'input', label: '输入节点', description: '用户参数输入' },
    { key: 'title-generator', label: '书名生成', description: 'AI生成书名候选' },
    { key: 'detail-generator', label: '详情生成', description: '生成小说基础信息' },
    { key: 'character-creator', label: '角色创建', description: 'AI生成角色设定' },
    { key: 'worldbuilding', label: '世界观构建', description: 'AI生成世界背景' },
    { key: 'plotline-planner', label: '主线规划', description: 'AI生成主要故事线' },
    { key: 'outline-generator', label: '大纲规划', description: 'AI生成故事结构' },
    { key: 'chapter-count-input', label: '章节数量', description: '用户指定章节总数' },
    { key: 'detailed-outline', label: '细纲生成', description: '生成详细情节要点' },
    { key: 'chapter-generator', label: '章节生成', description: 'AI生成具体章节内容' },
    { key: 'content-polisher', label: '内容润色', description: 'AI优化文本质量' },
    { key: 'consistency-checker', label: '一致性检查', description: '检查内容一致性' },
    { key: 'output', label: '输出节点', description: '最终结果展示' },
  ];

  if (!currentProject) {
    return (
      <div className="p-8 text-center">
        <Title level={3}>请先选择或创建一个项目</Title>
        <Text type="secondary">
          您需要先在项目总览中创建或选择一个项目，然后才能编辑工作流。
        </Text>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* 工具栏 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <Title level={4} className="mb-0">工作流编辑器</Title>
            <Text type="secondary">项目: {currentProject.name}</Text>
          </div>
          
          <Space>
            <Button
              icon={<SaveOutlined />}
              onClick={handleSaveWorkflow}
            >
              保存工作流
            </Button>

            <Button
              onClick={handleCreateSampleWorkflow}
            >
              创建示例工作流
            </Button>

            <Button
              type="primary"
              icon={<RobotOutlined />}
              onClick={() => setIsAIAssistantVisible(true)}
            >
              AI工作流助手
            </Button>
            
            {executionContext?.status === 'running' ? (
              <>
                <Button 
                  icon={<PauseCircleOutlined />} 
                  onClick={handlePauseExecution}
                >
                  暂停
                </Button>
                <Button 
                  icon={<StopOutlined />} 
                  danger
                  onClick={handleStopExecution}
                >
                  停止
                </Button>
              </>
            ) : (
              <Button 
                type="primary"
                icon={<PlayCircleOutlined />} 
                onClick={handleStartExecution}
                disabled={currentWorkflow.length === 0}
              >
                开始执行
              </Button>
            )}
          </Space>
        </div>

        {/* 执行状态 */}
        {executionContext && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between">
              <Text strong>执行状态: {
                executionContext.status === 'running' ? '运行中' :
                executionContext.status === 'paused' ? '已暂停' :
                executionContext.status === 'completed' ? '已完成' :
                executionContext.status === 'error' ? '错误' : '空闲'
              }</Text>
              <Text>进度: {executionContext.progress.percentage.toFixed(1)}%</Text>
            </div>
            {executionContext.progress.currentStep && (
              <Text type="secondary">当前步骤: {executionContext.progress.currentStep}</Text>
            )}
          </div>
        )}
      </div>

      <div className="flex-1 flex">
        {/* 节点面板 */}
        <div className="w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto">
          <Title level={5}>节点库</Title>
          <div className="space-y-2">
            {nodeTypeOptions.map((nodeType) => (
              <Card 
                key={nodeType.key}
                size="small" 
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => handleAddNode(nodeType.key)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <Text strong className="text-sm">{nodeType.label}</Text>
                    <div className="text-xs text-gray-500 mt-1">
                      {nodeType.description}
                    </div>
                  </div>
                  <PlusOutlined className="text-blue-500" />
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* 工作流画布 */}
        <div className="flex-1 bg-gray-50 relative">
          <div className="absolute inset-0">
            {nodes.length === 0 ? (
              <div className="w-full h-full flex items-center justify-center bg-white">
                <div className="text-center">
                  <Title level={4} type="secondary">工作流画布</Title>
                  <Text type="secondary">
                    从左侧节点库中选择节点开始构建您的AI小说生成工作流
                  </Text>
                </div>
              </div>
            ) : (
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                nodeTypes={reactFlowNodeTypes}
                fitView
                className="bg-gray-50"
              >
                <Background />
                <Controls />
                <MiniMap
                  nodeColor={(node) => {
                    switch (node.data?.status) {
                      case 'running': return '#3b82f6';
                      case 'completed': return '#10b981';
                      case 'error': return '#ef4444';
                      default: return '#6b7280';
                    }
                  }}
                />
              </ReactFlow>
            )}
          </div>
        </div>

      </div>

      {/* 节点配置模态框 */}
      <Modal
        title="节点配置"
        open={isConfigModalVisible}
        onOk={async () => {
          try {
            const values = await form.validateFields();
            if (editingNode) {
              // 更新节点配置
              setNodes(nodes =>
                nodes.map(node =>
                  node.id === editingNode.id
                    ? {
                        ...node,
                        data: {
                          ...node.data,
                          label: values.label,
                          config: { ...(node.data.config || {}), ...values }
                        }
                      }
                    : node
                )
              );
              message.success('节点配置已更新');
            }
            setIsConfigModalVisible(false);
            form.resetFields();
          } catch (error) {
            console.error('配置验证失败:', error);
          }
        }}
        onCancel={() => {
          setIsConfigModalVisible(false);
          form.resetFields();
        }}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="label"
            label="节点名称"
            rules={[{ required: true, message: '请输入节点名称' }]}
          >
            <Input placeholder="请输入节点名称" />
          </Form.Item>

          {editingNode?.type === 'input' && (
            <>
              <Form.Item name="theme" label="创作主题">
                <Input placeholder="请输入创作主题" />
              </Form.Item>
              <Form.Item name="style" label="写作风格">
                <Select placeholder="请选择写作风格">
                  <Select.Option value="轻松幽默">轻松幽默</Select.Option>
                  <Select.Option value="深沉严肃">深沉严肃</Select.Option>
                  <Select.Option value="浪漫温馨">浪漫温馨</Select.Option>
                </Select>
              </Form.Item>
            </>
          )}

          {editingNode?.type === 'chapter-count-input' && (
            <Form.Item
              name="chapterCount"
              label="章节数量"
              rules={[{ required: true, message: '请输入章节数量' }]}
            >
              <Input type="number" min={1} max={500} placeholder="请输入章节数量" />
            </Form.Item>
          )}

          {editingNode?.type === 'title-generator' && (
            <>
              <Form.Item name="count" label="生成数量">
                <Input type="number" min={1} max={20} placeholder="生成书名数量" />
              </Form.Item>
              <Form.Item name="genre" label="小说类型">
                <Select placeholder="请选择小说类型">
                  <Select.Option value="现代都市">现代都市</Select.Option>
                  <Select.Option value="古代言情">古代言情</Select.Option>
                  <Select.Option value="玄幻修仙">玄幻修仙</Select.Option>
                </Select>
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* AI工作流助手 */}
      <WorkflowAIAssistant
        visible={isAIAssistantVisible}
        onClose={() => setIsAIAssistantVisible(false)}
        onApplyWorkflow={handleApplyAIWorkflow}
        currentNodes={nodes}
      />
    </div>
  );
};

export default WorkflowEditor;
