{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { \n  Project, \n  WorkflowNode, \n  Character, \n  WorldBuilding, \n  Outline, \n  PlotLine, \n  BookTitle, \n  Chapter,\n  PromptTemplate,\n  DocumentStructure,\n  ExecutionContext,\n  UIState,\n  Notification\n} from '@/types';\n\n// 主应用状态接口\ninterface AppState {\n  // UI状态\n  ui: UIState;\n  setTheme: (theme: 'light' | 'dark') => void;\n  setLanguage: (language: 'zh-CN' | 'en-US') => void;\n  toggleSidebar: () => void;\n  setActiveTab: (tab: string) => void;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationRead: (id: string) => void;\n  clearNotifications: () => void;\n\n  // 项目管理\n  projects: Project[];\n  currentProject: Project | null;\n  createProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateProject: (id: string, updates: Partial<Project>) => void;\n  deleteProject: (id: string) => void;\n  setCurrentProject: (id: string) => void;\n\n  // 工作流管理\n  workflows: Record<string, WorkflowNode[]>;\n  currentWorkflow: WorkflowNode[];\n  addNode: (node: Omit<WorkflowNode, 'id'>) => void;\n  updateNode: (id: string, updates: Partial<WorkflowNode>) => void;\n  deleteNode: (id: string) => void;\n  connectNodes: (sourceId: string, targetId: string) => void;\n  disconnectNodes: (sourceId: string, targetId: string) => void;\n  loadWorkflow: (projectId: string) => void;\n  saveWorkflow: (projectId: string) => void;\n\n  // 角色管理\n  characters: Record<string, Character[]>;\n  addCharacter: (projectId: string, character: Omit<Character, 'id'>) => void;\n  updateCharacter: (projectId: string, id: string, updates: Partial<Character>) => void;\n  deleteCharacter: (projectId: string, id: string) => void;\n  getCharacters: (projectId: string) => Character[];\n\n  // 世界观管理\n  worldBuilding: Record<string, WorldBuilding[]>;\n  addWorldElement: (projectId: string, element: Omit<WorldBuilding, 'id'>) => void;\n  updateWorldElement: (projectId: string, id: string, updates: Partial<WorldBuilding>) => void;\n  deleteWorldElement: (projectId: string, id: string) => void;\n  getWorldElements: (projectId: string) => WorldBuilding[];\n\n  // 大纲管理\n  outlines: Record<string, Outline[]>;\n  addOutline: (projectId: string, outline: Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateOutline: (projectId: string, id: string, updates: Partial<Outline>) => void;\n  deleteOutline: (projectId: string, id: string) => void;\n  getOutlines: (projectId: string) => Outline[];\n\n  // 主线管理\n  plotLines: Record<string, PlotLine[]>;\n  addPlotLine: (projectId: string, plotLine: Omit<PlotLine, 'id'>) => void;\n  updatePlotLine: (projectId: string, id: string, updates: Partial<PlotLine>) => void;\n  deletePlotLine: (projectId: string, id: string) => void;\n  getPlotLines: (projectId: string) => PlotLine[];\n\n  // 书名管理\n  bookTitles: Record<string, BookTitle[]>;\n  addBookTitle: (projectId: string, title: Omit<BookTitle, 'id' | 'createdAt'>) => void;\n  updateBookTitle: (projectId: string, id: string, updates: Partial<BookTitle>) => void;\n  deleteBookTitle: (projectId: string, id: string) => void;\n  toggleTitleFavorite: (projectId: string, id: string) => void;\n  getBookTitles: (projectId: string) => BookTitle[];\n\n  // 章节管理\n  chapters: Record<string, Chapter[]>;\n  addChapter: (projectId: string, chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateChapter: (projectId: string, id: string, updates: Partial<Chapter>) => void;\n  deleteChapter: (projectId: string, id: string) => void;\n  reorderChapters: (projectId: string, fromIndex: number, toIndex: number) => void;\n  getChapters: (projectId: string) => Chapter[];\n\n  // 提示词管理\n  promptTemplates: PromptTemplate[];\n  addPromptTemplate: (template: Omit<PromptTemplate, 'id'>) => void;\n  updatePromptTemplate: (id: string, updates: Partial<PromptTemplate>) => void;\n  deletePromptTemplate: (id: string) => void;\n  getPromptTemplatesByCategory: (category: string) => PromptTemplate[];\n\n  // 文档管理\n  documentStructures: Record<string, DocumentStructure>;\n  initializeDocumentStructure: (projectId: string) => void;\n  updateDocumentStructure: (projectId: string, structure: Partial<DocumentStructure>) => void;\n\n  // 执行引擎\n  executionContexts: Record<string, ExecutionContext>;\n  startExecution: (projectId: string, workflowId: string) => void;\n  pauseExecution: (projectId: string) => void;\n  resumeExecution: (projectId: string) => void;\n  stopExecution: (projectId: string) => void;\n  updateExecutionProgress: (projectId: string, progress: Partial<ExecutionContext>) => void;\n}\n\n// 生成唯一ID的工具函数\nconst generateId = () => Math.random().toString(36).substr(2, 9);\n\n// 创建Zustand store\nexport const useAppStore = create<AppState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        // 初始UI状态\n        ui: {\n          theme: 'light',\n          language: 'zh-CN',\n          sidebarCollapsed: false,\n          activeTab: 'workflow',\n          selectedProject: undefined,\n          notifications: [],\n        },\n\n        // UI状态管理\n        setTheme: (theme) => set((state) => ({ \n          ui: { ...state.ui, theme } \n        })),\n        \n        setLanguage: (language) => set((state) => ({ \n          ui: { ...state.ui, language } \n        })),\n        \n        toggleSidebar: () => set((state) => ({ \n          ui: { ...state.ui, sidebarCollapsed: !state.ui.sidebarCollapsed } \n        })),\n        \n        setActiveTab: (activeTab) => set((state) => ({ \n          ui: { ...state.ui, activeTab } \n        })),\n        \n        addNotification: (notification) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: [\n              ...state.ui.notifications,\n              {\n                ...notification,\n                id: generateId(),\n                timestamp: new Date(),\n                read: false,\n              }\n            ]\n          }\n        })),\n        \n        markNotificationRead: (id) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: state.ui.notifications.map(n => \n              n.id === id ? { ...n, read: true } : n\n            )\n          }\n        })),\n        \n        clearNotifications: () => set((state) => ({\n          ui: { ...state.ui, notifications: [] }\n        })),\n\n        // 项目管理\n        projects: [],\n        currentProject: null,\n        \n        createProject: (project) => set((state) => {\n          const newProject: Project = {\n            ...project,\n            id: generateId(),\n            createdAt: new Date(),\n            updatedAt: new Date(),\n          };\n          return {\n            projects: [...state.projects, newProject],\n            currentProject: newProject,\n            ui: { ...state.ui, selectedProject: newProject.id }\n          };\n        }),\n        \n        updateProject: (id, updates) => set((state) => ({\n          projects: state.projects.map(p => \n            p.id === id ? { ...p, ...updates, updatedAt: new Date() } : p\n          ),\n          currentProject: state.currentProject?.id === id \n            ? { ...state.currentProject, ...updates, updatedAt: new Date() }\n            : state.currentProject\n        })),\n        \n        deleteProject: (id) => set((state) => ({\n          projects: state.projects.filter(p => p.id !== id),\n          currentProject: state.currentProject?.id === id ? null : state.currentProject,\n          ui: { \n            ...state.ui, \n            selectedProject: state.ui.selectedProject === id ? undefined : state.ui.selectedProject \n          }\n        })),\n        \n        setCurrentProject: (id) => set((state) => {\n          const project = state.projects.find(p => p.id === id);\n          return {\n            currentProject: project || null,\n            ui: { ...state.ui, selectedProject: id }\n          };\n        }),\n\n        // 工作流管理\n        workflows: {},\n        currentWorkflow: [],\n        \n        addNode: (node) => set((state) => ({\n          currentWorkflow: [...state.currentWorkflow, { ...node, id: generateId() }]\n        })),\n        \n        updateNode: (id, updates) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === id ? { ...n, ...updates } : n\n          )\n        })),\n        \n        deleteNode: (id) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.filter(n => n.id !== id)\n        })),\n        \n        connectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: [...n.connections, { sourceId, targetId }] }\n              : n\n          )\n        })),\n        \n        disconnectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: n.connections.filter(c => !(c.sourceId === sourceId && c.targetId === targetId)) }\n              : n\n          )\n        })),\n        \n        loadWorkflow: (projectId) => set((state) => ({\n          currentWorkflow: state.workflows[projectId] || []\n        })),\n        \n        saveWorkflow: (projectId) => set((state) => ({\n          workflows: { ...state.workflows, [projectId]: state.currentWorkflow }\n        })),\n\n        // 角色管理\n        characters: {},\n        \n        addCharacter: (projectId, character) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: [\n              ...(state.characters[projectId] || []),\n              { ...character, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateCharacter: (projectId, id, updates) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates } : c\n            )\n          }\n        })),\n        \n        deleteCharacter: (projectId, id) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        \n        getCharacters: (projectId) => get().characters[projectId] || [],\n\n        // 世界观管理\n        worldBuilding: {},\n        \n        addWorldElement: (projectId, element) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: [\n              ...(state.worldBuilding[projectId] || []),\n              { ...element, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateWorldElement: (projectId, id, updates) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).map(w => \n              w.id === id ? { ...w, ...updates } : w\n            )\n          }\n        })),\n        \n        deleteWorldElement: (projectId, id) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).filter(w => w.id !== id)\n          }\n        })),\n        \n        getWorldElements: (projectId) => get().worldBuilding[projectId] || [],\n\n        // 其他管理功能的占位符实现\n        outlines: {},\n        addOutline: (projectId, outline) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: [\n              ...(state.outlines[projectId] || []),\n              { \n                ...outline, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateOutline: (projectId, id, updates) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).map(o => \n              o.id === id ? { ...o, ...updates, updatedAt: new Date() } : o\n            )\n          }\n        })),\n        deleteOutline: (projectId, id) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).filter(o => o.id !== id)\n          }\n        })),\n        getOutlines: (projectId) => get().outlines[projectId] || [],\n\n        plotLines: {},\n        addPlotLine: (projectId, plotLine) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: [\n              ...(state.plotLines[projectId] || []),\n              { ...plotLine, id: generateId() }\n            ]\n          }\n        })),\n        updatePlotLine: (projectId, id, updates) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).map(p => \n              p.id === id ? { ...p, ...updates } : p\n            )\n          }\n        })),\n        deletePlotLine: (projectId, id) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).filter(p => p.id !== id)\n          }\n        })),\n        getPlotLines: (projectId) => get().plotLines[projectId] || [],\n\n        bookTitles: {},\n        addBookTitle: (projectId, title) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: [\n              ...(state.bookTitles[projectId] || []),\n              { ...title, id: generateId(), createdAt: new Date() }\n            ]\n          }\n        })),\n        updateBookTitle: (projectId, id, updates) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, ...updates } : t\n            )\n          }\n        })),\n        deleteBookTitle: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).filter(t => t.id !== id)\n          }\n        })),\n        toggleTitleFavorite: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, isFavorite: !t.isFavorite } : t\n            )\n          }\n        })),\n        getBookTitles: (projectId) => get().bookTitles[projectId] || [],\n\n        chapters: {},\n        addChapter: (projectId, chapter) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: [\n              ...(state.chapters[projectId] || []),\n              { \n                ...chapter, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateChapter: (projectId, id, updates) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates, updatedAt: new Date() } : c\n            )\n          }\n        })),\n        deleteChapter: (projectId, id) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        reorderChapters: (projectId, fromIndex, toIndex) => set((state) => {\n          const chapters = [...(state.chapters[projectId] || [])];\n          const [removed] = chapters.splice(fromIndex, 1);\n          chapters.splice(toIndex, 0, removed);\n          // 重新设置order\n          chapters.forEach((chapter, index) => {\n            chapter.order = index + 1;\n          });\n          return {\n            chapters: {\n              ...state.chapters,\n              [projectId]: chapters\n            }\n          };\n        }),\n        getChapters: (projectId) => get().chapters[projectId] || [],\n\n        promptTemplates: [],\n        addPromptTemplate: (template) => set((state) => ({\n          promptTemplates: [...state.promptTemplates, { ...template, id: generateId() }]\n        })),\n        updatePromptTemplate: (id, updates) => set((state) => ({\n          promptTemplates: state.promptTemplates.map(t => \n            t.id === id ? { ...t, ...updates } : t\n          )\n        })),\n        deletePromptTemplate: (id) => set((state) => ({\n          promptTemplates: state.promptTemplates.filter(t => t.id !== id)\n        })),\n        getPromptTemplatesByCategory: (category) => \n          get().promptTemplates.filter(t => t.category === category),\n\n        documentStructures: {},\n        initializeDocumentStructure: (projectId) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              projectId,\n              folders: [],\n              files: [],\n              lastBackup: new Date()\n            }\n          }\n        })),\n        updateDocumentStructure: (projectId, structure) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              ...state.documentStructures[projectId],\n              ...structure\n            }\n          }\n        })),\n\n        executionContexts: {},\n        startExecution: (projectId, workflowId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              projectId,\n              workflowId,\n              status: 'running',\n              progress: {\n                totalSteps: 0,\n                completedSteps: 0,\n                currentStep: '',\n                percentage: 0\n              },\n              queue: [],\n              results: {},\n              startTime: new Date()\n            }\n          }\n        })),\n        pauseExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'paused'\n            }\n          }\n        })),\n        resumeExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'running'\n            }\n          }\n        })),\n        stopExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'idle',\n              endTime: new Date()\n            }\n          }\n        })),\n        updateExecutionProgress: (projectId, progress) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              ...progress\n            }\n          }\n        })),\n      }),\n      {\n        name: 'ai-novel-workflow-storage',\n        partialize: (state) => ({\n          projects: state.projects,\n          workflows: state.workflows,\n          characters: state.characters,\n          worldBuilding: state.worldBuilding,\n          outlines: state.outlines,\n          plotLines: state.plotLines,\n          bookTitles: state.bookTitles,\n          chapters: state.chapters,\n          promptTemplates: state.promptTemplates,\n          documentStructures: state.documentStructures,\n          ui: {\n            theme: state.ui.theme,\n            language: state.ui.language,\n            sidebarCollapsed: state.ui.sidebarCollapsed,\n          }\n        }),\n      }\n    ),\n    { name: 'ai-novel-workflow' }\n  )\n);\n\nexport default useAppStore;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAiHA,cAAc;AACd,MAAM,aAAa,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAGvD,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,SAAS;QACT,IAAI;YACF,OAAO;YACP,UAAU;YACV,kBAAkB;YAClB,WAAW;YACX,iBAAiB;YACjB,eAAe,EAAE;QACnB;QAEA,SAAS;QACT,UAAU,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAM;gBAC3B,CAAC;QAED,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAS;gBAC9B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,kBAAkB,CAAC,MAAM,EAAE,CAAC,gBAAgB;oBAAC;gBAClE,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAU;gBAC/B,CAAC;QAED,iBAAiB,CAAC,eAAiB,IAAI,CAAC,QAAU,CAAC;oBACjD,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe;+BACV,MAAM,EAAE,CAAC,aAAa;4BACzB;gCACE,GAAG,YAAY;gCACf,IAAI;gCACJ,WAAW,IAAI;gCACf,MAAM;4BACR;yBACD;oBACH;gBACF,CAAC;QAED,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe,MAAM,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,MAAM;4BAAK,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACxC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,eAAe,EAAE;oBAAC;gBACvC,CAAC;QAED,OAAO;QACP,UAAU,EAAE;QACZ,gBAAgB;QAEhB,eAAe,CAAC,UAAY,IAAI,CAAC;gBAC/B,MAAM,aAAsB;oBAC1B,GAAG,OAAO;oBACV,IAAI;oBACJ,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBACA,OAAO;oBACL,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAW;oBACzC,gBAAgB;oBAChB,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB,WAAW,EAAE;oBAAC;gBACpD;YACF;QAEA,eAAe,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9C,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAC3B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI;wBAAO,IAAI;oBAE9D,gBAAgB,MAAM,cAAc,EAAE,OAAO,KACzC;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;wBAAE,WAAW,IAAI;oBAAO,IAC7D,MAAM,cAAc;gBAC1B,CAAC;QAED,eAAe,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACrC,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC9C,gBAAgB,MAAM,cAAc,EAAE,OAAO,KAAK,OAAO,MAAM,cAAc;oBAC7E,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,iBAAiB,MAAM,EAAE,CAAC,eAAe,KAAK,KAAK,YAAY,MAAM,EAAE,CAAC,eAAe;oBACzF;gBACF,CAAC;QAED,mBAAmB,CAAC,KAAO,IAAI,CAAC;gBAC9B,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAClD,OAAO;oBACL,gBAAgB,WAAW;oBAC3B,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB;oBAAG;gBACzC;YACF;QAEA,QAAQ;QACR,WAAW,CAAC;QACZ,iBAAiB,EAAE;QAEnB,SAAS,CAAC,OAAS,IAAI,CAAC,QAAU,CAAC;oBACjC,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,IAAI;4BAAE,IAAI;wBAAa;qBAAE;gBAC5E,CAAC;QAED,YAAY,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QAED,YAAY,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClC,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QAED,cAAc,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa;mCAAI,EAAE,WAAW;gCAAE;oCAAE;oCAAU;gCAAS;6BAAE;wBAAC,IAChE;gBAER,CAAC;QAED,iBAAiB,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACvD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,QAAQ,KAAK,YAAY,EAAE,QAAQ,KAAK,QAAQ;wBAAG,IACtG;gBAER,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;gBACnD,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,WAAW;wBAAE,GAAG,MAAM,SAAS;wBAAE,CAAC,UAAU,EAAE,MAAM,eAAe;oBAAC;gBACtE,CAAC;QAED,OAAO;QACP,YAAY,CAAC;QAEb,cAAc,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,SAAS;gCAAE,IAAI;4BAAa;yBAClC;oBACH;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QAED,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,QAAQ;QACR,eAAe,CAAC;QAEhB,iBAAiB,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBACvD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE;+BACP,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;4BACxC;gCAAE,GAAG,OAAO;gCAAE,IAAI;4BAAa;yBAChC;oBACH;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9D,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACtD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACrD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC3E;gBACF,CAAC;QAED,kBAAkB,CAAC,YAAc,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;QAErE,eAAe;QACf,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,WAAW,CAAC;QACZ,aAAa,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE;+BACP,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;4BACpC;gCAAE,GAAG,QAAQ;gCAAE,IAAI;4BAAa;yBACjC;oBACH;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC1D,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IAClD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACjD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACvE;gBACF,CAAC;QACD,cAAc,CAAC,YAAc,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;QAE7D,YAAY,CAAC;QACb,cAAc,CAAC,WAAW,QAAU,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,KAAK;gCAAE,IAAI;gCAAc,WAAW,IAAI;4BAAO;yBACrD;oBACH;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QACD,qBAAqB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,YAAY,CAAC,EAAE,UAAU;4BAAC,IAAI;oBAExD;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,WAAW,UAAY,IAAI,CAAC;gBACvD,MAAM,WAAW;uBAAK,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;iBAAE;gBACvD,MAAM,CAAC,QAAQ,GAAG,SAAS,MAAM,CAAC,WAAW;gBAC7C,SAAS,MAAM,CAAC,SAAS,GAAG;gBAC5B,YAAY;gBACZ,SAAS,OAAO,CAAC,CAAC,SAAS;oBACzB,QAAQ,KAAK,GAAG,QAAQ;gBAC1B;gBACA,OAAO;oBACL,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;oBACf;gBACF;YACF;QACA,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,iBAAiB,EAAE;QACnB,mBAAmB,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBAC/C,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,QAAQ;4BAAE,IAAI;wBAAa;qBAAE;gBAChF,CAAC;QACD,sBAAsB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACrD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QACD,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QACD,8BAA8B,CAAC,WAC7B,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QAEnD,oBAAoB,CAAC;QACrB,6BAA6B,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC1D,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX;4BACA,SAAS,EAAE;4BACX,OAAO,EAAE;4BACT,YAAY,IAAI;wBAClB;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACjE,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,kBAAkB,CAAC,UAAU;4BACtC,GAAG,SAAS;wBACd;oBACF;gBACF,CAAC;QAED,mBAAmB,CAAC;QACpB,gBAAgB,CAAC,WAAW,aAAe,IAAI,CAAC,QAAU,CAAC;oBACzD,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX;4BACA;4BACA,QAAQ;4BACR,UAAU;gCACR,YAAY;gCACZ,gBAAgB;gCAChB,aAAa;gCACb,YAAY;4BACd;4BACA,OAAO,EAAE;4BACT,SAAS,CAAC;4BACV,WAAW,IAAI;wBACjB;oBACF;gBACF,CAAC;QACD,gBAAgB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC7C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,iBAAiB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC9C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC5C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;4BACR,SAAS,IAAI;wBACf;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBAChE,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,GAAG,QAAQ;wBACb;oBACF;gBACF,CAAC;IACH,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,eAAe,MAAM,aAAa;YAClC,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,UAAU,MAAM,QAAQ;YACxB,iBAAiB,MAAM,eAAe;YACtC,oBAAoB,MAAM,kBAAkB;YAC5C,IAAI;gBACF,OAAO,MAAM,EAAE,CAAC,KAAK;gBACrB,UAAU,MAAM,EAAE,CAAC,QAAQ;gBAC3B,kBAAkB,MAAM,EAAE,CAAC,gBAAgB;YAC7C;QACF,CAAC;AACH,IAEF;IAAE,MAAM;AAAoB;uCAIjB", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Layout, Menu, Button, Dropdown, Avatar, Badge, Space, Typography } from 'antd';\nimport { \n  MenuFoldOutlined, \n  MenuUnfoldOutlined,\n  BellOutlined,\n  SettingOutlined,\n  UserOutlined,\n  ProjectOutlined,\n  FileTextOutlined,\n  TeamOutlined,\n  GlobalOutlined,\n  BookOutlined,\n  BranchesOutlined,\n  ThunderboltOutlined,\n  FolderOutlined,\n  SunOutlined,\n  MoonOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { MenuProps } from 'antd';\n\nconst { Header, Sider, Content } = Layout;\nconst { Text } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const {\n    ui,\n    toggleSidebar,\n    setTheme,\n    setActiveTab,\n    currentProject\n  } = useAppStore();\n\n  const [collapsed, setCollapsed] = useState(ui.sidebarCollapsed);\n\n  const handleToggleSidebar = () => {\n    setCollapsed(!collapsed);\n    toggleSidebar();\n  };\n\n  const handleThemeToggle = () => {\n    setTheme(ui.theme === 'light' ? 'dark' : 'light');\n  };\n\n  // 侧边栏菜单项\n  const menuItems: MenuProps['items'] = [\n    {\n      key: 'workflow',\n      icon: <ThunderboltOutlined />,\n      label: '工作流编辑器',\n    },\n    {\n      key: 'projects',\n      icon: <ProjectOutlined />,\n      label: '项目总览',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'content-management',\n      icon: <FileTextOutlined />,\n      label: '内容管理',\n      children: [\n        {\n          key: 'outlines',\n          icon: <FileTextOutlined />,\n          label: '大纲管理',\n        },\n        {\n          key: 'characters',\n          icon: <TeamOutlined />,\n          label: '角色管理',\n        },\n        {\n          key: 'worldbuilding',\n          icon: <GlobalOutlined />,\n          label: '世界观管理',\n        },\n        {\n          key: 'plotlines',\n          icon: <BranchesOutlined />,\n          label: '主线管理',\n        },\n        {\n          key: 'titles',\n          icon: <BookOutlined />,\n          label: '书名管理',\n        },\n      ],\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'documents',\n      icon: <FolderOutlined />,\n      label: '文档管理',\n    },\n    {\n      key: 'prompts',\n      icon: <FileTextOutlined />,\n      label: '提示词管理',\n    },\n  ];\n\n  // 用户菜单\n  const userMenuItems: MenuProps['items'] = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      label: '退出登录',\n    },\n  ];\n\n  // 通知菜单\n  const notificationMenuItems: MenuProps['items'] = (ui.notifications || []).slice(0, 5).map(notification => ({\n    key: notification.id,\n    label: (\n      <div className=\"max-w-xs\">\n        <div className=\"font-medium text-sm\">{notification.title}</div>\n        <div className=\"text-xs text-gray-500 mt-1\">{notification.message}</div>\n        <div className=\"text-xs text-gray-400 mt-1\">\n          {notification.timestamp.toLocaleTimeString()}\n        </div>\n      </div>\n    ),\n  }));\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    setActiveTab(key);\n  };\n\n  return (\n    <Layout className=\"min-h-screen\">\n      {/* 侧边栏 */}\n      <Sider \n        trigger={null} \n        collapsible \n        collapsed={collapsed}\n        width={240}\n        className=\"bg-white border-r border-gray-200\"\n        theme=\"light\"\n      >\n        {/* Logo区域 */}\n        <div className=\"h-16 flex items-center justify-center border-b border-gray-200\">\n          {collapsed ? (\n            <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n              <ThunderboltOutlined className=\"text-white text-lg\" />\n            </div>\n          ) : (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n                <ThunderboltOutlined className=\"text-white text-lg\" />\n              </div>\n              <Text strong className=\"text-lg\">AI小说工作流</Text>\n            </div>\n          )}\n        </div>\n\n        {/* 当前项目信息 */}\n        {!collapsed && currentProject && (\n          <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n            <Text type=\"secondary\" className=\"text-xs\">当前项目</Text>\n            <div className=\"mt-1\">\n              <Text strong className=\"text-sm\">{currentProject.name}</Text>\n            </div>\n            <div className=\"mt-1\">\n              <Text type=\"secondary\" className=\"text-xs\">\n                {currentProject.status === 'draft' && '草稿'}\n                {currentProject.status === 'in-progress' && '进行中'}\n                {currentProject.status === 'completed' && '已完成'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {/* 菜单 */}\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[ui.activeTab]}\n          items={menuItems}\n          onClick={handleMenuClick}\n          className=\"border-none\"\n        />\n      </Sider>\n\n      <Layout>\n        {/* 顶部导航栏 */}\n        <Header className=\"bg-white border-b border-gray-200 px-4 flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={handleToggleSidebar}\n              className=\"text-lg\"\n            />\n            \n            {/* 面包屑导航 */}\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n              <span>\n                {ui.activeTab === 'workflow' && '工作流编辑器'}\n                {ui.activeTab === 'projects' && '项目总览'}\n                {ui.activeTab === 'outlines' && '大纲管理'}\n                {ui.activeTab === 'characters' && '角色管理'}\n                {ui.activeTab === 'worldbuilding' && '世界观管理'}\n                {ui.activeTab === 'plotlines' && '主线管理'}\n                {ui.activeTab === 'titles' && '书名管理'}\n                {ui.activeTab === 'documents' && '文档管理'}\n                {ui.activeTab === 'prompts' && '提示词管理'}\n              </span>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* 主题切换 */}\n            <Button\n              type=\"text\"\n              icon={ui.theme === 'light' ? <MoonOutlined /> : <SunOutlined />}\n              onClick={handleThemeToggle}\n              className=\"text-lg\"\n            />\n\n            {/* 通知 */}\n            <Dropdown\n              menu={{ items: notificationMenuItems }}\n              trigger={['click']}\n              placement=\"bottomRight\"\n            >\n              <Button type=\"text\" className=\"text-lg\">\n                <Badge count={ui.notifications.filter(n => !n.read).length} size=\"small\">\n                  <BellOutlined />\n                </Badge>\n              </Button>\n            </Dropdown>\n\n            {/* 用户菜单 */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              trigger={['click']}\n              placement=\"bottomRight\"\n            >\n              <Space className=\"cursor-pointer\">\n                <Avatar icon={<UserOutlined />} />\n                <Text>用户</Text>\n              </Space>\n            </Dropdown>\n          </div>\n        </Header>\n\n        {/* 主内容区域 */}\n        <Content className=\"bg-gray-50 overflow-auto\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AArBA;;;;;;AAwBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAM3B,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE;IACzD,MAAM,EACJ,EAAE,EACF,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,cAAc,EACf,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,gBAAgB;IAE9D,MAAM,sBAAsB;QAC1B,aAAa,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,SAAS,GAAG,KAAK,KAAK,UAAU,SAAS;IAC3C;IAEA,SAAS;IACT,MAAM,YAAgC;QACpC;YACE,KAAK;YACL,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;QACT;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;oBACrB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;gBACT;aACD;QACH;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;KACD;IAED,OAAO;IACP,MAAM,gBAAoC;QACxC;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;QACT;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;QACT;KACD;IAED,OAAO;IACP,MAAM,wBAA4C,CAAC,GAAG,aAAa,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,eAAgB,CAAC;YAC1G,KAAK,aAAa,EAAE;YACpB,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAuB,aAAa,KAAK;;;;;;kCACxD,8OAAC;wBAAI,WAAU;kCAA8B,aAAa,OAAO;;;;;;kCACjE,8OAAC;wBAAI,WAAU;kCACZ,aAAa,SAAS,CAAC,kBAAkB;;;;;;;;;;;;QAIlD,CAAC;IAED,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,aAAa;IACf;IAEA,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,WAAU;;0BAEhB,8OAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,WAAU;gBACV,OAAM;;kCAGN,8OAAC;wBAAI,WAAU;kCACZ,0BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;;;qFAGjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAEjC,8OAAC;oCAAK,MAAM;oCAAC,WAAU;8CAAU;;;;;;;;;;;;;;;;;oBAMtC,CAAC,aAAa,gCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,MAAK;gCAAY,WAAU;0CAAU;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,MAAM;oCAAC,WAAU;8CAAW,eAAe,IAAI;;;;;;;;;;;0CAEvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,MAAK;oCAAY,WAAU;;wCAC9B,eAAe,MAAM,KAAK,WAAW;wCACrC,eAAe,MAAM,KAAK,iBAAiB;wCAC3C,eAAe,MAAM,KAAK,eAAe;;;;;;;;;;;;;;;;;;kCAOlD,8OAAC,8KAAA,CAAA,OAAI;wBACH,MAAK;wBACL,cAAc;4BAAC,GAAG,SAAS;yBAAC;wBAC5B,OAAO;wBACP,SAAS;wBACT,WAAU;;;;;;;;;;;;0BAId,8OAAC,kLAAA,CAAA,SAAM;;kCAEL,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,0BAAY,8OAAC,8NAAA,CAAA,qBAAkB;;;;mEAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;gDACE,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,gBAAgB;gDACjC,GAAG,SAAS,KAAK,mBAAmB;gDACpC,GAAG,SAAS,KAAK,eAAe;gDAChC,GAAG,SAAS,KAAK,YAAY;gDAC7B,GAAG,SAAS,KAAK,eAAe;gDAChC,GAAG,SAAS,KAAK,aAAa;;;;;;;;;;;;;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,GAAG,KAAK,KAAK,wBAAU,8OAAC,kNAAA,CAAA,eAAY;;;;mEAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAIZ,8OAAC,sLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAsB;wCACrC,SAAS;4CAAC;yCAAQ;wCAClB,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,WAAU;sDAC5B,cAAA,8OAAC,gLAAA,CAAA,QAAK;gDAAC,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;gDAAE,MAAK;0DAC/D,cAAA,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kDAMnB,8OAAC,sLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAc;wCAC7B,SAAS;4CAAC;yCAAQ;wCAClB,WAAU;kDAEV,cAAA,8OAAC,gMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,kLAAA,CAAA,SAAM;oDAAC,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;8DAC3B,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 1055, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/workflow/WorkflowEditor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState, useMemo } from 'react';\nimport { Card, Button, Space, Typography, Divider, message, Modal, Form, Input, Select } from 'antd';\nimport {\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  SaveOutlined,\n  PlusOutlined,\n  SettingOutlined,\n  DeleteOutlined,\n  LinkOutlined\n} from '@ant-design/icons';\nimport {\n  ReactFlow,\n  Background,\n  Controls,\n  MiniMap,\n  useNodesState,\n  useEdgesState,\n  addEdge,\n  Connection,\n  Edge,\n  Node,\n  NodeTypes\n} from '@xyflow/react';\nimport '@xyflow/react/dist/style.css';\nimport { useAppStore } from '@/store';\nimport CustomNode from './CustomNode';\n\nconst { Title, Text } = Typography;\n\nconst WorkflowEditor: React.FC = () => {\n  const { \n    currentProject, \n    currentWorkflow, \n    addNode, \n    updateNode, \n    deleteNode,\n    saveWorkflow,\n    executionContexts,\n    startExecution,\n    pauseExecution,\n    stopExecution\n  } = useAppStore();\n\n  const [selectedNode, setSelectedNode] = useState<string | null>(null);\n\n  const executionContext = currentProject ? executionContexts[currentProject.id] : null;\n\n  const handleSaveWorkflow = useCallback(() => {\n    if (currentProject) {\n      saveWorkflow(currentProject.id);\n      message.success('工作流已保存');\n    }\n  }, [currentProject, saveWorkflow]);\n\n  const handleStartExecution = useCallback(() => {\n    if (currentProject) {\n      startExecution(currentProject.id, 'default');\n      message.info('工作流开始执行');\n    }\n  }, [currentProject, startExecution]);\n\n  const handlePauseExecution = useCallback(() => {\n    if (currentProject) {\n      pauseExecution(currentProject.id);\n      message.info('工作流已暂停');\n    }\n  }, [currentProject, pauseExecution]);\n\n  const handleStopExecution = useCallback(() => {\n    if (currentProject) {\n      stopExecution(currentProject.id);\n      message.info('工作流已停止');\n    }\n  }, [currentProject, stopExecution]);\n\n  const handleAddNode = useCallback((nodeType: string) => {\n    const newNode = {\n      type: nodeType as any,\n      position: { x: Math.random() * 400, y: Math.random() * 300 },\n      data: {\n        label: `新${nodeType}节点`,\n        config: {},\n        status: 'idle' as const,\n      },\n      connections: [],\n    };\n    addNode(newNode);\n    message.success('节点已添加');\n  }, [addNode]);\n\n  const nodeTypes = [\n    { key: 'input', label: '输入节点', description: '用户参数输入' },\n    { key: 'title-generator', label: '书名生成', description: 'AI生成书名候选' },\n    { key: 'detail-generator', label: '详情生成', description: '生成小说基础信息' },\n    { key: 'character-creator', label: '角色创建', description: 'AI生成角色设定' },\n    { key: 'worldbuilding', label: '世界观构建', description: 'AI生成世界背景' },\n    { key: 'plotline-planner', label: '主线规划', description: 'AI生成主要故事线' },\n    { key: 'outline-generator', label: '大纲规划', description: 'AI生成故事结构' },\n    { key: 'chapter-count-input', label: '章节数量', description: '用户指定章节总数' },\n    { key: 'detailed-outline', label: '细纲生成', description: '生成详细情节要点' },\n    { key: 'chapter-generator', label: '章节生成', description: 'AI生成具体章节内容' },\n    { key: 'content-polisher', label: '内容润色', description: 'AI优化文本质量' },\n    { key: 'consistency-checker', label: '一致性检查', description: '检查内容一致性' },\n    { key: 'output', label: '输出节点', description: '最终结果展示' },\n  ];\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能编辑工作流。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* 工具栏 */}\n      <div className=\"bg-white border-b border-gray-200 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <Title level={4} className=\"mb-0\">工作流编辑器</Title>\n            <Text type=\"secondary\">项目: {currentProject.name}</Text>\n          </div>\n          \n          <Space>\n            <Button \n              icon={<SaveOutlined />} \n              onClick={handleSaveWorkflow}\n            >\n              保存工作流\n            </Button>\n            \n            {executionContext?.status === 'running' ? (\n              <>\n                <Button \n                  icon={<PauseCircleOutlined />} \n                  onClick={handlePauseExecution}\n                >\n                  暂停\n                </Button>\n                <Button \n                  icon={<StopOutlined />} \n                  danger\n                  onClick={handleStopExecution}\n                >\n                  停止\n                </Button>\n              </>\n            ) : (\n              <Button \n                type=\"primary\"\n                icon={<PlayCircleOutlined />} \n                onClick={handleStartExecution}\n                disabled={currentWorkflow.length === 0}\n              >\n                开始执行\n              </Button>\n            )}\n          </Space>\n        </div>\n\n        {/* 执行状态 */}\n        {executionContext && (\n          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <Text strong>执行状态: {\n                executionContext.status === 'running' ? '运行中' :\n                executionContext.status === 'paused' ? '已暂停' :\n                executionContext.status === 'completed' ? '已完成' :\n                executionContext.status === 'error' ? '错误' : '空闲'\n              }</Text>\n              <Text>进度: {executionContext.progress.percentage.toFixed(1)}%</Text>\n            </div>\n            {executionContext.progress.currentStep && (\n              <Text type=\"secondary\">当前步骤: {executionContext.progress.currentStep}</Text>\n            )}\n          </div>\n        )}\n      </div>\n\n      <div className=\"flex-1 flex\">\n        {/* 节点面板 */}\n        <div className=\"w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto\">\n          <Title level={5}>节点库</Title>\n          <div className=\"space-y-2\">\n            {nodeTypes.map((nodeType) => (\n              <Card \n                key={nodeType.key}\n                size=\"small\" \n                className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                onClick={() => handleAddNode(nodeType.key)}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <Text strong className=\"text-sm\">{nodeType.label}</Text>\n                    <div className=\"text-xs text-gray-500 mt-1\">\n                      {nodeType.description}\n                    </div>\n                  </div>\n                  <PlusOutlined className=\"text-blue-500\" />\n                </div>\n              </Card>\n            ))}\n          </div>\n        </div>\n\n        {/* 工作流画布 */}\n        <div className=\"flex-1 bg-gray-50 relative\">\n          <div className=\"absolute inset-0 p-4\">\n            <div className=\"w-full h-full bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center\">\n              {currentWorkflow.length === 0 ? (\n                <div className=\"text-center\">\n                  <Title level={4} type=\"secondary\">工作流画布</Title>\n                  <Text type=\"secondary\">\n                    从左侧节点库中选择节点开始构建您的AI小说生成工作流\n                  </Text>\n                </div>\n              ) : (\n                <div className=\"w-full h-full p-4\">\n                  <Title level={5}>当前工作流节点 ({currentWorkflow.length})</Title>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4\">\n                    {currentWorkflow.map((node) => (\n                      <Card \n                        key={node.id}\n                        size=\"small\"\n                        className={`cursor-pointer transition-all ${\n                          selectedNode === node.id ? 'ring-2 ring-blue-500' : ''\n                        }`}\n                        onClick={() => setSelectedNode(node.id)}\n                        actions={[\n                          <SettingOutlined key=\"setting\" />,\n                          <Button \n                            key=\"delete\" \n                            type=\"text\" \n                            danger \n                            size=\"small\"\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              deleteNode(node.id);\n                              if (selectedNode === node.id) {\n                                setSelectedNode(null);\n                              }\n                            }}\n                          >\n                            删除\n                          </Button>\n                        ]}\n                      >\n                        <div>\n                          <Text strong className=\"text-sm\">{node.data.label}</Text>\n                          <div className=\"mt-2\">\n                            <Text \n                              className={`text-xs px-2 py-1 rounded ${\n                                node.data.status === 'completed' ? 'bg-green-100 text-green-600' :\n                                node.data.status === 'running' ? 'bg-blue-100 text-blue-600' :\n                                node.data.status === 'error' ? 'bg-red-100 text-red-600' :\n                                'bg-gray-100 text-gray-600'\n                              }`}\n                            >\n                              {node.data.status === 'completed' ? '已完成' :\n                               node.data.status === 'running' ? '运行中' :\n                               node.data.status === 'error' ? '错误' : '等待'}\n                            </Text>\n                          </div>\n                        </div>\n                      </Card>\n                    ))}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* 属性面板 */}\n        {selectedNode && (\n          <div className=\"w-80 bg-white border-l border-gray-200 p-4\">\n            <Title level={5}>节点属性</Title>\n            <Divider />\n            {(() => {\n              const node = currentWorkflow.find(n => n.id === selectedNode);\n              if (!node) return null;\n              \n              return (\n                <div className=\"space-y-4\">\n                  <div>\n                    <Text strong>节点名称</Text>\n                    <div className=\"mt-1\">\n                      <Text>{node.data.label}</Text>\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <Text strong>节点类型</Text>\n                    <div className=\"mt-1\">\n                      <Text>{node.type}</Text>\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <Text strong>状态</Text>\n                    <div className=\"mt-1\">\n                      <Text>{node.data.status}</Text>\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <Text strong>连接数</Text>\n                    <div className=\"mt-1\">\n                      <Text>{node.connections.length}</Text>\n                    </div>\n                  </div>\n                  \n                  {node.data.error && (\n                    <div>\n                      <Text strong type=\"danger\">错误信息</Text>\n                      <div className=\"mt-1\">\n                        <Text type=\"danger\">{node.data.error}</Text>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              );\n            })()}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default WorkflowEditor;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAwBA;AA5BA;;;;;;;AA+BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,iBAA2B;IAC/B,MAAM,EACJ,cAAc,EACd,eAAe,EACf,OAAO,EACP,UAAU,EACV,UAAU,EACV,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,aAAa,EACd,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,mBAAmB,iBAAiB,iBAAiB,CAAC,eAAe,EAAE,CAAC,GAAG;IAEjF,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,gBAAgB;YAClB,aAAa,eAAe,EAAE;YAC9B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;IACF,GAAG;QAAC;QAAgB;KAAa;IAEjC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,gBAAgB;YAClB,eAAe,eAAe,EAAE,EAAE;YAClC,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf;IACF,GAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,gBAAgB;YAClB,eAAe,eAAe,EAAE;YAChC,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf;IACF,GAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,gBAAgB;YAClB,cAAc,eAAe,EAAE;YAC/B,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf;IACF,GAAG;QAAC;QAAgB;KAAc;IAElC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,UAAU;YACd,MAAM;YACN,UAAU;gBAAE,GAAG,KAAK,MAAM,KAAK;gBAAK,GAAG,KAAK,MAAM,KAAK;YAAI;YAC3D,MAAM;gBACJ,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC;gBACvB,QAAQ,CAAC;gBACT,QAAQ;YACV;YACA,aAAa,EAAE;QACjB;QACA,QAAQ;QACR,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB,GAAG;QAAC;KAAQ;IAEZ,MAAM,YAAY;QAChB;YAAE,KAAK;YAAS,OAAO;YAAQ,aAAa;QAAS;QACrD;YAAE,KAAK;YAAmB,OAAO;YAAQ,aAAa;QAAW;QACjE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAW;QACnE;YAAE,KAAK;YAAiB,OAAO;YAAS,aAAa;QAAW;QAChE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAY;QACnE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAW;QACnE;YAAE,KAAK;YAAuB,OAAO;YAAQ,aAAa;QAAW;QACrE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAa;QACrE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAuB,OAAO;YAAS,aAAa;QAAU;QACrE;YAAE,KAAK;YAAU,OAAO;YAAQ,aAAa;QAAS;KACvD;IAED,IAAI,CAAC,gBAAgB;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,8OAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAO;;;;;;kDAClC,8OAAC;wCAAK,MAAK;;4CAAY;4CAAK,eAAe,IAAI;;;;;;;;;;;;;0CAGjD,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS;kDACV;;;;;;oCAIA,kBAAkB,WAAW,0BAC5B;;0DACE,8OAAC,kMAAA,CAAA,SAAM;gDACL,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gDAC1B,SAAS;0DACV;;;;;;0DAGD,8OAAC,kMAAA,CAAA,SAAM;gDACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gDACnB,MAAM;gDACN,SAAS;0DACV;;;;;;;qEAKH,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,8NAAA,CAAA,qBAAkB;;;;;wCACzB,SAAS;wCACT,UAAU,gBAAgB,MAAM,KAAK;kDACtC;;;;;;;;;;;;;;;;;;oBAQN,kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,MAAM;;4CAAC;4CACX,iBAAiB,MAAM,KAAK,YAAY,QACxC,iBAAiB,MAAM,KAAK,WAAW,QACvC,iBAAiB,MAAM,KAAK,cAAc,QAC1C,iBAAiB,MAAM,KAAK,UAAU,OAAO;;;;;;;kDAE/C,8OAAC;;4CAAK;4CAAK,iBAAiB,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;4BAE5D,iBAAiB,QAAQ,CAAC,WAAW,kBACpC,8OAAC;gCAAK,MAAK;;oCAAY;oCAAO,iBAAiB,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,8KAAA,CAAA,OAAI;wCAEH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc,SAAS,GAAG;kDAEzC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,MAAM;4DAAC,WAAU;sEAAW,SAAS,KAAK;;;;;;sEAChD,8OAAC;4DAAI,WAAU;sEACZ,SAAS,WAAW;;;;;;;;;;;;8DAGzB,8OAAC,kNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;uCAZrB,SAAS,GAAG;;;;;;;;;;;;;;;;kCAoBzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,OAAO;4CAAG,MAAK;sDAAY;;;;;;sDAClC,8OAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;6FAKzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,OAAO;;gDAAG;gDAAU,gBAAgB,MAAM;gDAAC;;;;;;;sDAClD,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,8KAAA,CAAA,OAAI;oDAEH,MAAK;oDACL,WAAW,CAAC,8BAA8B,EACxC,iBAAiB,KAAK,EAAE,GAAG,yBAAyB,IACpD;oDACF,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,SAAS;sEACP,8OAAC,wNAAA,CAAA,kBAAe,MAAK;;;;;sEACrB,8OAAC,kMAAA,CAAA,SAAM;4DAEL,MAAK;4DACL,MAAM;4DACN,MAAK;4DACL,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,WAAW,KAAK,EAAE;gEAClB,IAAI,iBAAiB,KAAK,EAAE,EAAE;oEAC5B,gBAAgB;gEAClB;4DACF;sEACD;2DAXK;;;;;qDAcP;8DAED,cAAA,8OAAC;;0EACC,8OAAC;gEAAK,MAAM;gEAAC,WAAU;0EAAW,KAAK,IAAI,CAAC,KAAK;;;;;;0EACjD,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAW,CAAC,0BAA0B,EACpC,KAAK,IAAI,CAAC,MAAM,KAAK,cAAc,gCACnC,KAAK,IAAI,CAAC,MAAM,KAAK,YAAY,8BACjC,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU,4BAC/B,6BACA;8EAED,KAAK,IAAI,CAAC,MAAM,KAAK,cAAc,QACnC,KAAK,IAAI,CAAC,MAAM,KAAK,YAAY,QACjC,KAAK,IAAI,CAAC,MAAM,KAAK,UAAU,OAAO;;;;;;;;;;;;;;;;;mDAtCxC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAoD3B,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,8OAAC,oLAAA,CAAA,UAAO;;;;;4BACP,CAAC;gCACA,MAAM,OAAO,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gCAChD,IAAI,CAAC,MAAM,OAAO;gCAElB,qBACE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,MAAM;8DAAC;;;;;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAM,KAAK,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;sDAI1B,8OAAC;;8DACC,8OAAC;oDAAK,MAAM;8DAAC;;;;;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;sDAIpB,8OAAC;;8DACC,8OAAC;oDAAK,MAAM;8DAAC;;;;;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAM,KAAK,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;;sDAI3B,8OAAC;;8DACC,8OAAC;oDAAK,MAAM;8DAAC;;;;;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;kEAAM,KAAK,WAAW,CAAC,MAAM;;;;;;;;;;;;;;;;;wCAIjC,KAAK,IAAI,CAAC,KAAK,kBACd,8OAAC;;8DACC,8OAAC;oDAAK,MAAM;oDAAC,MAAK;8DAAS;;;;;;8DAC3B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,MAAK;kEAAU,KAAK,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;4BAMhD,CAAC;;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}, {"offset": {"line": 1821, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/project/ProjectOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Card, \n  Button, \n  Space, \n  Typography, \n  Modal, \n  Form, \n  Input, \n  Select, \n  InputNumber,\n  message,\n  Empty,\n  Tag,\n  Popconfirm\n} from 'antd';\nimport { \n  PlusOutlined, \n  EditOutlined, \n  DeleteOutlined,\n  PlayCircleOutlined,\n  FileTextOutlined,\n  CalendarOutlined,\n  SettingOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { Project } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\n\nconst ProjectOverview: React.FC = () => {\n  const { \n    projects, \n    currentProject,\n    createProject, \n    updateProject, \n    deleteProject, \n    setCurrentProject,\n    setActiveTab\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState<Project | null>(null);\n  const [form] = Form.useForm();\n\n  const handleCreateProject = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEditProject = (project: Project) => {\n    setEditingProject(project);\n    form.setFieldsValue({\n      name: project.name,\n      description: project.description,\n      genre: project.settings.genre,\n      style: project.settings.style,\n      targetWordCount: project.settings.targetWordCount,\n      chapterCount: project.settings.chapterCount,\n      language: project.settings.language,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteProject = (projectId: string) => {\n    deleteProject(projectId);\n    message.success('项目已删除');\n  };\n\n  const handleSelectProject = (project: Project) => {\n    setCurrentProject(project.id);\n    message.success(`已选择项目: ${project.name}`);\n  };\n\n  const handleStartWorkflow = (project: Project) => {\n    setCurrentProject(project.id);\n    setActiveTab('workflow');\n    message.info(`正在打开项目 ${project.name} 的工作流编辑器`);\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingProject) {\n        // 更新项目\n        updateProject(editingProject.id, {\n          name: values.name,\n          description: values.description,\n          settings: {\n            genre: values.genre,\n            style: values.style,\n            targetWordCount: values.targetWordCount,\n            chapterCount: values.chapterCount,\n            language: values.language,\n          }\n        });\n        message.success('项目已更新');\n      } else {\n        // 创建新项目\n        createProject({\n          name: values.name,\n          description: values.description,\n          status: 'draft',\n          settings: {\n            genre: values.genre,\n            style: values.style,\n            targetWordCount: values.targetWordCount,\n            chapterCount: values.chapterCount,\n            language: values.language,\n          }\n        });\n        message.success('项目已创建');\n      }\n      \n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n    setEditingProject(null);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'draft': return 'default';\n      case 'in-progress': return 'processing';\n      case 'completed': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'draft': return '草稿';\n      case 'in-progress': return '进行中';\n      case 'completed': return '已完成';\n      default: return '未知';\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>项目总览</Title>\n          <Text type=\"secondary\">管理您的AI小说创作项目</Text>\n        </div>\n        <Button \n          type=\"primary\" \n          icon={<PlusOutlined />}\n          onClick={handleCreateProject}\n        >\n          创建新项目\n        </Button>\n      </div>\n\n      {projects.length === 0 ? (\n        <Card className=\"text-center py-12\">\n          <Empty\n            description={\n              <div>\n                <Text type=\"secondary\">还没有任何项目</Text>\n                <br />\n                <Text type=\"secondary\">点击上方按钮创建您的第一个AI小说项目</Text>\n              </div>\n            }\n          />\n        </Card>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {projects.map((project) => (\n            <Card\n              key={project.id}\n              className={`hover:shadow-lg transition-shadow ${\n                currentProject?.id === project.id ? 'ring-2 ring-blue-500' : ''\n              }`}\n              actions={[\n                <Button \n                  key=\"select\"\n                  type=\"text\" \n                  icon={<SettingOutlined />}\n                  onClick={() => handleSelectProject(project)}\n                >\n                  选择\n                </Button>,\n                <Button \n                  key=\"workflow\"\n                  type=\"text\" \n                  icon={<PlayCircleOutlined />}\n                  onClick={() => handleStartWorkflow(project)}\n                >\n                  工作流\n                </Button>,\n                <Button \n                  key=\"edit\"\n                  type=\"text\" \n                  icon={<EditOutlined />}\n                  onClick={() => handleEditProject(project)}\n                >\n                  编辑\n                </Button>,\n                <Popconfirm\n                  key=\"delete\"\n                  title=\"确定要删除这个项目吗？\"\n                  description=\"删除后将无法恢复，请谨慎操作。\"\n                  onConfirm={() => handleDeleteProject(project.id)}\n                  okText=\"确定\"\n                  cancelText=\"取消\"\n                >\n                  <Button \n                    type=\"text\" \n                    danger\n                    icon={<DeleteOutlined />}\n                  >\n                    删除\n                  </Button>\n                </Popconfirm>\n              ]}\n            >\n              <div className=\"mb-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <Title level={4} className=\"mb-0\">{project.name}</Title>\n                  <Tag color={getStatusColor(project.status)}>\n                    {getStatusText(project.status)}\n                  </Tag>\n                </div>\n                \n                <Paragraph \n                  type=\"secondary\" \n                  ellipsis={{ rows: 2 }}\n                  className=\"mb-3\"\n                >\n                  {project.description}\n                </Paragraph>\n\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">类型:</Text>\n                    <Text>{project.settings.genre}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">风格:</Text>\n                    <Text>{project.settings.style}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">目标字数:</Text>\n                    <Text>{project.settings.targetWordCount.toLocaleString()}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">章节数:</Text>\n                    <Text>{project.settings.chapterCount}</Text>\n                  </div>\n                </div>\n\n                <div className=\"mt-4 pt-3 border-t border-gray-100\">\n                  <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                    <span className=\"flex items-center\">\n                      <CalendarOutlined className=\"mr-1\" />\n                      创建: {new Date(project.createdAt).toLocaleDateString()}\n                    </span>\n                    <span className=\"flex items-center\">\n                      <FileTextOutlined className=\"mr-1\" />\n                      更新: {new Date(project.updatedAt).toLocaleDateString()}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </Card>\n          ))}\n        </div>\n      )}\n\n      {/* 创建/编辑项目模态框 */}\n      <Modal\n        title={editingProject ? '编辑项目' : '创建新项目'}\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={600}\n        okText={editingProject ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            genre: '现代都市',\n            style: '轻松幽默',\n            targetWordCount: 100000,\n            chapterCount: 50,\n            language: 'zh-CN',\n          }}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"项目名称\"\n            rules={[{ required: true, message: '请输入项目名称' }]}\n          >\n            <Input placeholder=\"请输入项目名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"项目描述\"\n            rules={[{ required: true, message: '请输入项目描述' }]}\n          >\n            <Input.TextArea \n              rows={3} \n              placeholder=\"请简要描述您的小说项目...\" \n            />\n          </Form.Item>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"genre\"\n              label=\"小说类型\"\n              rules={[{ required: true, message: '请选择小说类型' }]}\n            >\n              <Select placeholder=\"请选择类型\">\n                <Option value=\"现代都市\">现代都市</Option>\n                <Option value=\"古代言情\">古代言情</Option>\n                <Option value=\"玄幻修仙\">玄幻修仙</Option>\n                <Option value=\"科幻未来\">科幻未来</Option>\n                <Option value=\"悬疑推理\">悬疑推理</Option>\n                <Option value=\"历史军事\">历史军事</Option>\n                <Option value=\"青春校园\">青春校园</Option>\n                <Option value=\"商战职场\">商战职场</Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item\n              name=\"style\"\n              label=\"写作风格\"\n              rules={[{ required: true, message: '请选择写作风格' }]}\n            >\n              <Select placeholder=\"请选择风格\">\n                <Option value=\"轻松幽默\">轻松幽默</Option>\n                <Option value=\"深沉严肃\">深沉严肃</Option>\n                <Option value=\"浪漫温馨\">浪漫温馨</Option>\n                <Option value=\"紧张刺激\">紧张刺激</Option>\n                <Option value=\"文艺清新\">文艺清新</Option>\n                <Option value=\"热血激昂\">热血激昂</Option>\n              </Select>\n            </Form.Item>\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"targetWordCount\"\n              label=\"目标字数\"\n              rules={[{ required: true, message: '请输入目标字数' }]}\n            >\n              <InputNumber\n                min={10000}\n                max={1000000}\n                step={10000}\n                placeholder=\"100000\"\n                className=\"w-full\"\n                formatter={(value) => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n                parser={(value) => value!.replace(/\\$\\s?|(,*)/g, '')}\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"chapterCount\"\n              label=\"预计章节数\"\n              rules={[{ required: true, message: '请输入章节数' }]}\n            >\n              <InputNumber\n                min={1}\n                max={500}\n                placeholder=\"50\"\n                className=\"w-full\"\n              />\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            name=\"language\"\n            label=\"语言\"\n            rules={[{ required: true, message: '请选择语言' }]}\n          >\n            <Select placeholder=\"请选择语言\">\n              <Option value=\"zh-CN\">简体中文</Option>\n              <Option value=\"en-US\">English</Option>\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectOverview;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AA3BA;;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AAEzB,MAAM,kBAA4B;IAChC,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,aAAa,EACb,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,YAAY,EACb,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,KAAK,cAAc,CAAC;YAClB,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,OAAO,QAAQ,QAAQ,CAAC,KAAK;YAC7B,OAAO,QAAQ,QAAQ,CAAC,KAAK;YAC7B,iBAAiB,QAAQ,QAAQ,CAAC,eAAe;YACjD,cAAc,QAAQ,QAAQ,CAAC,YAAY;YAC3C,UAAU,QAAQ,QAAQ,CAAC,QAAQ;QACrC;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,QAAQ,EAAE;QAC5B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,IAAI,EAAE;IAC1C;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,QAAQ,EAAE;QAC5B,aAAa;QACb,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,QAAQ,CAAC;IAC/C;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,gBAAgB;gBAClB,OAAO;gBACP,cAAc,eAAe,EAAE,EAAE;oBAC/B,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,UAAU;wBACR,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,iBAAiB,OAAO,eAAe;wBACvC,cAAc,OAAO,YAAY;wBACjC,UAAU,OAAO,QAAQ;oBAC3B;gBACF;gBACA,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,QAAQ;gBACR,cAAc;oBACZ,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,QAAQ;oBACR,UAAU;wBACR,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,iBAAiB,OAAO,eAAe;wBACvC,cAAc,OAAO,YAAY;wBACjC,UAAU,OAAO,QAAQ;oBAC3B;gBACF;gBACA,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,kBAAkB;YAClB,KAAK,WAAW;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;kCAEzB,8OAAC,kMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wBACnB,SAAS;kCACV;;;;;;;;;;;;YAKF,SAAS,MAAM,KAAK,kBACnB,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,2BACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;yEAM/B,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,8KAAA,CAAA,OAAI;wBAEH,WAAW,CAAC,kCAAkC,EAC5C,gBAAgB,OAAO,QAAQ,EAAE,GAAG,yBAAyB,IAC7D;wBACF,SAAS;0CACP,8OAAC,kMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;gCACtB,SAAS,IAAM,oBAAoB;0CACpC;+BAJK;;;;;0CAON,8OAAC,kMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,8OAAC,8NAAA,CAAA,qBAAkB;;;;;gCACzB,SAAS,IAAM,oBAAoB;0CACpC;+BAJK;;;;;0CAON,8OAAC,kMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,kBAAkB;0CAClC;+BAJK;;;;;0CAON,8OAAC,0LAAA,CAAA,aAAU;gCAET,OAAM;gCACN,aAAY;gCACZ,WAAW,IAAM,oBAAoB,QAAQ,EAAE;gCAC/C,QAAO;gCACP,YAAW;0CAEX,cAAA,8OAAC,kMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,MAAM;oCACN,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;8CACtB;;;;;;+BAXG;;;;;yBAeP;kCAED,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAQ,QAAQ,IAAI;;;;;;sDAC/C,8OAAC,4KAAA,CAAA,MAAG;4CAAC,OAAO,eAAe,QAAQ,MAAM;sDACtC,cAAc,QAAQ,MAAM;;;;;;;;;;;;8CAIjC,8OAAC;oCACC,MAAK;oCACL,UAAU;wCAAE,MAAM;oCAAE;oCACpB,WAAU;8CAET,QAAQ,WAAW;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,8OAAC;8DAAM,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,8OAAC;8DAAM,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,8OAAC;8DAAM,QAAQ,QAAQ,CAAC,eAAe,CAAC,cAAc;;;;;;;;;;;;sDAExD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,8OAAC;8DAAM,QAAQ,QAAQ,CAAC,YAAY;;;;;;;;;;;;;;;;;;8CAIxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,0NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAAS;oDAChC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;0DAErD,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,0NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAAS;oDAChC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;uBA1FtD,QAAQ,EAAE;;;;;;;;;;0BAqGvB,8OAAC,gLAAA,CAAA,QAAK;gBACJ,OAAO,iBAAiB,SAAS;gBACjC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ,iBAAiB,OAAO;gBAChC,YAAW;0BAEX,cAAA,8OAAC,8KAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;oBACP,eAAe;wBACb,OAAO;wBACP,OAAO;wBACP,iBAAiB;wBACjB,cAAc;wBACd,UAAU;oBACZ;;sCAEA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;gCACb,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;8CAIzB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;sCAK3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,8OAAC,gMAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,aAAY;wCACZ,WAAU;wCACV,WAAW,CAAC,QAAU,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB;wCAClE,QAAQ,CAAC,QAAU,MAAO,OAAO,CAAC,eAAe;;;;;;;;;;;8CAIrD,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;8CAE9C,cAAA,8OAAC,gMAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAKhB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,8OAAC,kLAAA,CAAA,SAAM;gCAAC,aAAY;;kDAClB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 2687, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/outline/OutlineManager.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, Empty } from 'antd';\nimport { FileTextOutlined } from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst OutlineManager: React.FC = () => {\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <Title level={2}>大纲管理</Title>\n        <Text type=\"secondary\">管理故事大纲和章节结构</Text>\n      </div>\n\n      <Card className=\"text-center py-12\">\n        <Empty\n          image={<FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}\n          description={\n            <div>\n              <Text type=\"secondary\">大纲管理功能正在开发中</Text>\n              <br />\n              <Text type=\"secondary\">即将支持树形大纲结构、章节管理、版本控制等功能</Text>\n            </div>\n          }\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default OutlineManager;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAMA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,iBAA2B;IAC/B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAGzB,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,qBAAO,8OAAC,0NAAA,CAAA,mBAAgB;wBAAC,OAAO;4BAAE,UAAU;4BAAI,OAAO;wBAAU;;;;;;oBACjE,2BACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;uCAEe", "debugId": null}}, {"offset": {"line": 2793, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/character/CharacterManager.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, Empty } from 'antd';\nimport { TeamOutlined } from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst CharacterManager: React.FC = () => {\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <Title level={2}>角色管理</Title>\n        <Text type=\"secondary\">管理小说角色设定和关系</Text>\n      </div>\n\n      <Card className=\"text-center py-12\">\n        <Empty\n          image={<TeamOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}\n          description={\n            <div>\n              <Text type=\"secondary\">角色管理功能正在开发中</Text>\n              <br />\n              <Text type=\"secondary\">即将支持角色卡片、关系图谱、角色模板等功能</Text>\n            </div>\n          }\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default CharacterManager;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAMA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,mBAA6B;IACjC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAGzB,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,qBAAO,8OAAC,kNAAA,CAAA,eAAY;wBAAC,OAAO;4BAAE,UAAU;4BAAI,OAAO;wBAAU;;;;;;oBAC7D,2BACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;uCAEe", "debugId": null}}, {"offset": {"line": 2899, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/worldbuilding/WorldBuildingManager.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, Empty } from 'antd';\nimport { GlobalOutlined } from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst WorldBuildingManager: React.FC = () => {\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <Title level={2}>世界观管理</Title>\n        <Text type=\"secondary\">管理故事世界观设定和背景</Text>\n      </div>\n\n      <Card className=\"text-center py-12\">\n        <Empty\n          image={<GlobalOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}\n          description={\n            <div>\n              <Text type=\"secondary\">世界观管理功能正在开发中</Text>\n              <br />\n              <Text type=\"secondary\">即将支持世界设定、分类管理、关联图谱等功能</Text>\n            </div>\n          }\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default WorldBuildingManager;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAMA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,uBAAiC;IACrC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAGzB,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,qBAAO,8OAAC,sNAAA,CAAA,iBAAc;wBAAC,OAAO;4BAAE,UAAU;4BAAI,OAAO;wBAAU;;;;;;oBAC/D,2BACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;uCAEe", "debugId": null}}, {"offset": {"line": 3005, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/plotline/PlotLineManager.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, Empty } from 'antd';\nimport { BranchesOutlined } from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst PlotLineManager: React.FC = () => {\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <Title level={2}>主线管理</Title>\n        <Text type=\"secondary\">管理故事主线和支线情节</Text>\n      </div>\n\n      <Card className=\"text-center py-12\">\n        <Empty\n          image={<BranchesOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}\n          description={\n            <div>\n              <Text type=\"secondary\">主线管理功能正在开发中</Text>\n              <br />\n              <Text type=\"secondary\">即将支持故事线时间轴、支线管理、情节冲突标记等功能</Text>\n            </div>\n          }\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default PlotLineManager;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAMA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,kBAA4B;IAChC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAGzB,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,qBAAO,8OAAC,0NAAA,CAAA,mBAAgB;wBAAC,OAAO;4BAAE,UAAU;4BAAI,OAAO;wBAAU;;;;;;oBACjE,2BACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;uCAEe", "debugId": null}}, {"offset": {"line": 3111, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/title/TitleManager.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, Empty } from 'antd';\nimport { BookOutlined } from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst TitleManager: React.FC = () => {\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <Title level={2}>书名管理</Title>\n        <Text type=\"secondary\">管理书名生成和选择</Text>\n      </div>\n\n      <Card className=\"text-center py-12\">\n        <Empty\n          image={<BookOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}\n          description={\n            <div>\n              <Text type=\"secondary\">书名管理功能正在开发中</Text>\n              <br />\n              <Text type=\"secondary\">即将支持书名生成器、评分分析、收藏管理等功能</Text>\n            </div>\n          }\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default TitleManager;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAMA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,eAAyB;IAC7B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAGzB,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,qBAAO,8OAAC,kNAAA,CAAA,eAAY;wBAAC,OAAO;4BAAE,UAAU;4BAAI,OAAO;wBAAU;;;;;;oBAC7D,2BACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;uCAEe", "debugId": null}}, {"offset": {"line": 3217, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/document/DocumentManager.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, Empty } from 'antd';\nimport { FolderOutlined } from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst DocumentManager: React.FC = () => {\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <Title level={2}>文档管理</Title>\n        <Text type=\"secondary\">管理项目文档和文件</Text>\n      </div>\n\n      <Card className=\"text-center py-12\">\n        <Empty\n          image={<FolderOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}\n          description={\n            <div>\n              <Text type=\"secondary\">文档管理功能正在开发中</Text>\n              <br />\n              <Text type=\"secondary\">即将支持统一文档结构、版本控制、搜索和导出等功能</Text>\n            </div>\n          }\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default DocumentManager;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAMA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,kBAA4B;IAChC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAGzB,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,qBAAO,8OAAC,sNAAA,CAAA,iBAAc;wBAAC,OAAO;4BAAE,UAAU;4BAAI,OAAO;wBAAU;;;;;;oBAC/D,2BACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;uCAEe", "debugId": null}}, {"offset": {"line": 3323, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/prompt/PromptManager.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, Typography, Empty } from 'antd';\nimport { FileTextOutlined } from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst PromptManager: React.FC = () => {\n  return (\n    <div className=\"p-6\">\n      <div className=\"mb-6\">\n        <Title level={2}>提示词管理</Title>\n        <Text type=\"secondary\">管理AI提示词模板和配置</Text>\n      </div>\n\n      <Card className=\"text-center py-12\">\n        <Empty\n          image={<FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}\n          description={\n            <div>\n              <Text type=\"secondary\">提示词管理功能正在开发中</Text>\n              <br />\n              <Text type=\"secondary\">即将支持提示词模板、版本控制、A/B测试等功能</Text>\n            </div>\n          }\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default PromptManager;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAMA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,gBAA0B;IAC9B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,8OAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;0BAGzB,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,qBAAO,8OAAC,0NAAA,CAAA,mBAAgB;wBAAC,OAAO;4BAAE,UAAU;4BAAI,OAAO;wBAAU;;;;;;oBACjE,2BACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;uCAEe", "debugId": null}}, {"offset": {"line": 3429, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport WorkflowEditor from '@/components/workflow/WorkflowEditor';\nimport ProjectOverview from '@/components/project/ProjectOverview';\nimport OutlineManager from '@/components/outline/OutlineManager';\nimport CharacterManager from '@/components/character/CharacterManager';\nimport WorldBuildingManager from '@/components/worldbuilding/WorldBuildingManager';\nimport PlotLineManager from '@/components/plotline/PlotLineManager';\nimport TitleManager from '@/components/title/TitleManager';\nimport DocumentManager from '@/components/document/DocumentManager';\nimport PromptManager from '@/components/prompt/PromptManager';\nimport { useAppStore } from '@/store';\n\nexport default function Home() {\n  const { ui } = useAppStore();\n\n  const renderContent = () => {\n    switch (ui.activeTab) {\n      case 'workflow':\n        return <WorkflowEditor />;\n      case 'projects':\n        return <ProjectOverview />;\n      case 'outlines':\n        return <OutlineManager />;\n      case 'characters':\n        return <CharacterManager />;\n      case 'worldbuilding':\n        return <WorldBuildingManager />;\n      case 'plotlines':\n        return <PlotLineManager />;\n      case 'titles':\n        return <TitleManager />;\n      case 'documents':\n        return <DocumentManager />;\n      case 'prompts':\n        return <PromptManager />;\n      default:\n        return <WorkflowEditor />;\n    }\n  };\n\n  return (\n    <MainLayout>\n      {renderContent()}\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEzB,MAAM,gBAAgB;QACpB,OAAQ,GAAG,SAAS;YAClB,KAAK;gBACH,qBAAO,8OAAC,gJAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,gJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,+IAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,mJAAA,CAAA,UAAgB;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2JAAA,CAAA,UAAoB;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,iJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,2IAAA,CAAA,UAAY;;;;;YACtB,KAAK;gBACH,qBAAO,8OAAC,iJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,6IAAA,CAAA,UAAa;;;;;YACvB;gBACE,qBAAO,8OAAC,gJAAA,CAAA,UAAc;;;;;QAC1B;IACF;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACR;;;;;;AAGP", "debugId": null}}]}