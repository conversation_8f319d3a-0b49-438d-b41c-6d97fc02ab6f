{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { \n  Project, \n  WorkflowNode, \n  Character, \n  WorldBuilding, \n  Outline, \n  PlotLine, \n  BookTitle, \n  Chapter,\n  PromptTemplate,\n  DocumentStructure,\n  ExecutionContext,\n  UIState,\n  Notification\n} from '@/types';\n\n// 主应用状态接口\ninterface AppState {\n  // UI状态\n  ui: UIState;\n  setTheme: (theme: 'light' | 'dark') => void;\n  setLanguage: (language: 'zh-CN' | 'en-US') => void;\n  toggleSidebar: () => void;\n  setActiveTab: (tab: string) => void;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationRead: (id: string) => void;\n  clearNotifications: () => void;\n\n  // 项目管理\n  projects: Project[];\n  currentProject: Project | null;\n  createProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateProject: (id: string, updates: Partial<Project>) => void;\n  deleteProject: (id: string) => void;\n  setCurrentProject: (id: string) => void;\n\n  // 工作流管理\n  workflows: Record<string, WorkflowNode[]>;\n  currentWorkflow: WorkflowNode[];\n  addNode: (node: Omit<WorkflowNode, 'id'>) => void;\n  updateNode: (id: string, updates: Partial<WorkflowNode>) => void;\n  deleteNode: (id: string) => void;\n  connectNodes: (sourceId: string, targetId: string) => void;\n  disconnectNodes: (sourceId: string, targetId: string) => void;\n  loadWorkflow: (projectId: string) => void;\n  saveWorkflow: (projectId: string) => void;\n\n  // 角色管理\n  characters: Record<string, Character[]>;\n  addCharacter: (projectId: string, character: Omit<Character, 'id'>) => void;\n  updateCharacter: (projectId: string, id: string, updates: Partial<Character>) => void;\n  deleteCharacter: (projectId: string, id: string) => void;\n  getCharacters: (projectId: string) => Character[];\n\n  // 世界观管理\n  worldBuilding: Record<string, WorldBuilding[]>;\n  addWorldElement: (projectId: string, element: Omit<WorldBuilding, 'id'>) => void;\n  updateWorldElement: (projectId: string, id: string, updates: Partial<WorldBuilding>) => void;\n  deleteWorldElement: (projectId: string, id: string) => void;\n  getWorldElements: (projectId: string) => WorldBuilding[];\n\n  // 大纲管理\n  outlines: Record<string, Outline[]>;\n  addOutline: (projectId: string, outline: Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateOutline: (projectId: string, id: string, updates: Partial<Outline>) => void;\n  deleteOutline: (projectId: string, id: string) => void;\n  getOutlines: (projectId: string) => Outline[];\n\n  // 主线管理\n  plotLines: Record<string, PlotLine[]>;\n  addPlotLine: (projectId: string, plotLine: Omit<PlotLine, 'id'>) => void;\n  updatePlotLine: (projectId: string, id: string, updates: Partial<PlotLine>) => void;\n  deletePlotLine: (projectId: string, id: string) => void;\n  getPlotLines: (projectId: string) => PlotLine[];\n\n  // 书名管理\n  bookTitles: Record<string, BookTitle[]>;\n  addBookTitle: (projectId: string, title: Omit<BookTitle, 'id' | 'createdAt'>) => void;\n  updateBookTitle: (projectId: string, id: string, updates: Partial<BookTitle>) => void;\n  deleteBookTitle: (projectId: string, id: string) => void;\n  toggleTitleFavorite: (projectId: string, id: string) => void;\n  getBookTitles: (projectId: string) => BookTitle[];\n\n  // 章节管理\n  chapters: Record<string, Chapter[]>;\n  addChapter: (projectId: string, chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateChapter: (projectId: string, id: string, updates: Partial<Chapter>) => void;\n  deleteChapter: (projectId: string, id: string) => void;\n  reorderChapters: (projectId: string, fromIndex: number, toIndex: number) => void;\n  getChapters: (projectId: string) => Chapter[];\n\n  // 提示词管理\n  promptTemplates: PromptTemplate[];\n  addPromptTemplate: (template: Omit<PromptTemplate, 'id'>) => void;\n  updatePromptTemplate: (id: string, updates: Partial<PromptTemplate>) => void;\n  deletePromptTemplate: (id: string) => void;\n  getPromptTemplatesByCategory: (category: string) => PromptTemplate[];\n\n  // 文档管理\n  documentStructures: Record<string, DocumentStructure>;\n  initializeDocumentStructure: (projectId: string) => void;\n  updateDocumentStructure: (projectId: string, structure: Partial<DocumentStructure>) => void;\n\n  // 执行引擎\n  executionContexts: Record<string, ExecutionContext>;\n  startExecution: (projectId: string, workflowId: string) => void;\n  pauseExecution: (projectId: string) => void;\n  resumeExecution: (projectId: string) => void;\n  stopExecution: (projectId: string) => void;\n  updateExecutionProgress: (projectId: string, progress: Partial<ExecutionContext>) => void;\n}\n\n// 生成唯一ID的工具函数\nconst generateId = () => Math.random().toString(36).substr(2, 9);\n\n// 创建Zustand store\nexport const useAppStore = create<AppState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        // 初始UI状态\n        ui: {\n          theme: 'light',\n          language: 'zh-CN',\n          sidebarCollapsed: false,\n          activeTab: 'workflow',\n          selectedProject: undefined,\n          notifications: [],\n        },\n\n        // UI状态管理\n        setTheme: (theme) => set((state) => ({ \n          ui: { ...state.ui, theme } \n        })),\n        \n        setLanguage: (language) => set((state) => ({ \n          ui: { ...state.ui, language } \n        })),\n        \n        toggleSidebar: () => set((state) => ({ \n          ui: { ...state.ui, sidebarCollapsed: !state.ui.sidebarCollapsed } \n        })),\n        \n        setActiveTab: (activeTab) => set((state) => ({ \n          ui: { ...state.ui, activeTab } \n        })),\n        \n        addNotification: (notification) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: [\n              ...state.ui.notifications,\n              {\n                ...notification,\n                id: generateId(),\n                timestamp: new Date(),\n                read: false,\n              }\n            ]\n          }\n        })),\n        \n        markNotificationRead: (id) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: state.ui.notifications.map(n => \n              n.id === id ? { ...n, read: true } : n\n            )\n          }\n        })),\n        \n        clearNotifications: () => set((state) => ({\n          ui: { ...state.ui, notifications: [] }\n        })),\n\n        // 项目管理\n        projects: [],\n        currentProject: null,\n        \n        createProject: (project) => set((state) => {\n          const newProject: Project = {\n            ...project,\n            id: generateId(),\n            createdAt: new Date(),\n            updatedAt: new Date(),\n          };\n          return {\n            projects: [...state.projects, newProject],\n            currentProject: newProject,\n            ui: { ...state.ui, selectedProject: newProject.id }\n          };\n        }),\n        \n        updateProject: (id, updates) => set((state) => ({\n          projects: state.projects.map(p => \n            p.id === id ? { ...p, ...updates, updatedAt: new Date() } : p\n          ),\n          currentProject: state.currentProject?.id === id \n            ? { ...state.currentProject, ...updates, updatedAt: new Date() }\n            : state.currentProject\n        })),\n        \n        deleteProject: (id) => set((state) => ({\n          projects: state.projects.filter(p => p.id !== id),\n          currentProject: state.currentProject?.id === id ? null : state.currentProject,\n          ui: { \n            ...state.ui, \n            selectedProject: state.ui.selectedProject === id ? undefined : state.ui.selectedProject \n          }\n        })),\n        \n        setCurrentProject: (id) => set((state) => {\n          const project = state.projects.find(p => p.id === id);\n          return {\n            currentProject: project || null,\n            ui: { ...state.ui, selectedProject: id }\n          };\n        }),\n\n        // 工作流管理\n        workflows: {},\n        currentWorkflow: [],\n        \n        addNode: (node) => set((state) => ({\n          currentWorkflow: [...state.currentWorkflow, { ...node, id: generateId() }]\n        })),\n        \n        updateNode: (id, updates) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === id ? { ...n, ...updates } : n\n          )\n        })),\n        \n        deleteNode: (id) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.filter(n => n.id !== id)\n        })),\n        \n        connectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: [...n.connections, { sourceId, targetId }] }\n              : n\n          )\n        })),\n        \n        disconnectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: n.connections.filter(c => !(c.sourceId === sourceId && c.targetId === targetId)) }\n              : n\n          )\n        })),\n        \n        loadWorkflow: (projectId) => set((state) => ({\n          currentWorkflow: state.workflows[projectId] || []\n        })),\n        \n        saveWorkflow: (projectId) => set((state) => ({\n          workflows: { ...state.workflows, [projectId]: state.currentWorkflow }\n        })),\n\n        // 角色管理\n        characters: {},\n        \n        addCharacter: (projectId, character) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: [\n              ...(state.characters[projectId] || []),\n              { ...character, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateCharacter: (projectId, id, updates) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates } : c\n            )\n          }\n        })),\n        \n        deleteCharacter: (projectId, id) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        \n        getCharacters: (projectId) => get().characters[projectId] || [],\n\n        // 世界观管理\n        worldBuilding: {},\n        \n        addWorldElement: (projectId, element) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: [\n              ...(state.worldBuilding[projectId] || []),\n              { ...element, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateWorldElement: (projectId, id, updates) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).map(w => \n              w.id === id ? { ...w, ...updates } : w\n            )\n          }\n        })),\n        \n        deleteWorldElement: (projectId, id) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).filter(w => w.id !== id)\n          }\n        })),\n        \n        getWorldElements: (projectId) => get().worldBuilding[projectId] || [],\n\n        // 其他管理功能的占位符实现\n        outlines: {},\n        addOutline: (projectId, outline) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: [\n              ...(state.outlines[projectId] || []),\n              { \n                ...outline, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateOutline: (projectId, id, updates) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).map(o => \n              o.id === id ? { ...o, ...updates, updatedAt: new Date() } : o\n            )\n          }\n        })),\n        deleteOutline: (projectId, id) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).filter(o => o.id !== id)\n          }\n        })),\n        getOutlines: (projectId) => get().outlines[projectId] || [],\n\n        plotLines: {},\n        addPlotLine: (projectId, plotLine) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: [\n              ...(state.plotLines[projectId] || []),\n              { ...plotLine, id: generateId() }\n            ]\n          }\n        })),\n        updatePlotLine: (projectId, id, updates) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).map(p => \n              p.id === id ? { ...p, ...updates } : p\n            )\n          }\n        })),\n        deletePlotLine: (projectId, id) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).filter(p => p.id !== id)\n          }\n        })),\n        getPlotLines: (projectId) => get().plotLines[projectId] || [],\n\n        bookTitles: {},\n        addBookTitle: (projectId, title) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: [\n              ...(state.bookTitles[projectId] || []),\n              { ...title, id: generateId(), createdAt: new Date() }\n            ]\n          }\n        })),\n        updateBookTitle: (projectId, id, updates) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, ...updates } : t\n            )\n          }\n        })),\n        deleteBookTitle: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).filter(t => t.id !== id)\n          }\n        })),\n        toggleTitleFavorite: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, isFavorite: !t.isFavorite } : t\n            )\n          }\n        })),\n        getBookTitles: (projectId) => get().bookTitles[projectId] || [],\n\n        chapters: {},\n        addChapter: (projectId, chapter) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: [\n              ...(state.chapters[projectId] || []),\n              { \n                ...chapter, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateChapter: (projectId, id, updates) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates, updatedAt: new Date() } : c\n            )\n          }\n        })),\n        deleteChapter: (projectId, id) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        reorderChapters: (projectId, fromIndex, toIndex) => set((state) => {\n          const chapters = [...(state.chapters[projectId] || [])];\n          const [removed] = chapters.splice(fromIndex, 1);\n          chapters.splice(toIndex, 0, removed);\n          // 重新设置order\n          chapters.forEach((chapter, index) => {\n            chapter.order = index + 1;\n          });\n          return {\n            chapters: {\n              ...state.chapters,\n              [projectId]: chapters\n            }\n          };\n        }),\n        getChapters: (projectId) => get().chapters[projectId] || [],\n\n        promptTemplates: [],\n        addPromptTemplate: (template) => set((state) => ({\n          promptTemplates: [...state.promptTemplates, { ...template, id: generateId() }]\n        })),\n        updatePromptTemplate: (id, updates) => set((state) => ({\n          promptTemplates: state.promptTemplates.map(t => \n            t.id === id ? { ...t, ...updates } : t\n          )\n        })),\n        deletePromptTemplate: (id) => set((state) => ({\n          promptTemplates: state.promptTemplates.filter(t => t.id !== id)\n        })),\n        getPromptTemplatesByCategory: (category) => \n          get().promptTemplates.filter(t => t.category === category),\n\n        documentStructures: {},\n        initializeDocumentStructure: (projectId) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              projectId,\n              folders: [],\n              files: [],\n              lastBackup: new Date()\n            }\n          }\n        })),\n        updateDocumentStructure: (projectId, structure) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              ...state.documentStructures[projectId],\n              ...structure\n            }\n          }\n        })),\n\n        executionContexts: {},\n        startExecution: (projectId, workflowId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              projectId,\n              workflowId,\n              status: 'running',\n              progress: {\n                totalSteps: 0,\n                completedSteps: 0,\n                currentStep: '',\n                percentage: 0\n              },\n              queue: [],\n              results: {},\n              startTime: new Date()\n            }\n          }\n        })),\n        pauseExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'paused'\n            }\n          }\n        })),\n        resumeExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'running'\n            }\n          }\n        })),\n        stopExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'idle',\n              endTime: new Date()\n            }\n          }\n        })),\n        updateExecutionProgress: (projectId, progress) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              ...progress\n            }\n          }\n        })),\n      }),\n      {\n        name: 'ai-novel-workflow-storage',\n        partialize: (state) => ({\n          projects: state.projects,\n          workflows: state.workflows,\n          characters: state.characters,\n          worldBuilding: state.worldBuilding,\n          outlines: state.outlines,\n          plotLines: state.plotLines,\n          bookTitles: state.bookTitles,\n          chapters: state.chapters,\n          promptTemplates: state.promptTemplates,\n          documentStructures: state.documentStructures,\n          ui: {\n            theme: state.ui.theme,\n            language: state.ui.language,\n            sidebarCollapsed: state.ui.sidebarCollapsed,\n          }\n        }),\n      }\n    ),\n    { name: 'ai-novel-workflow' }\n  )\n);\n\nexport default useAppStore;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAiHA,cAAc;AACd,MAAM,aAAa,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAGvD,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,SAAS;QACT,IAAI;YACF,OAAO;YACP,UAAU;YACV,kBAAkB;YAClB,WAAW;YACX,iBAAiB;YACjB,eAAe,EAAE;QACnB;QAEA,SAAS;QACT,UAAU,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAM;gBAC3B,CAAC;QAED,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAS;gBAC9B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,kBAAkB,CAAC,MAAM,EAAE,CAAC,gBAAgB;oBAAC;gBAClE,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAU;gBAC/B,CAAC;QAED,iBAAiB,CAAC,eAAiB,IAAI,CAAC,QAAU,CAAC;oBACjD,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe;+BACV,MAAM,EAAE,CAAC,aAAa;4BACzB;gCACE,GAAG,YAAY;gCACf,IAAI;gCACJ,WAAW,IAAI;gCACf,MAAM;4BACR;yBACD;oBACH;gBACF,CAAC;QAED,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe,MAAM,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,MAAM;4BAAK,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACxC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,eAAe,EAAE;oBAAC;gBACvC,CAAC;QAED,OAAO;QACP,UAAU,EAAE;QACZ,gBAAgB;QAEhB,eAAe,CAAC,UAAY,IAAI,CAAC;gBAC/B,MAAM,aAAsB;oBAC1B,GAAG,OAAO;oBACV,IAAI;oBACJ,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBACA,OAAO;oBACL,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAW;oBACzC,gBAAgB;oBAChB,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB,WAAW,EAAE;oBAAC;gBACpD;YACF;QAEA,eAAe,CAAC,IAAI,UAAY,IAAI,CAAC;oBAInB;uBAJ8B;oBAC9C,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAC3B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI;wBAAO,IAAI;oBAE9D,gBAAgB,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,EAAE,MAAK,KACzC;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;wBAAE,WAAW,IAAI;oBAAO,IAC7D,MAAM,cAAc;gBAC1B;;QAEA,eAAe,CAAC,KAAO,IAAI,CAAC;oBAEV;uBAFqB;oBACrC,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC9C,gBAAgB,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,EAAE,MAAK,KAAK,OAAO,MAAM,cAAc;oBAC7E,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,iBAAiB,MAAM,EAAE,CAAC,eAAe,KAAK,KAAK,YAAY,MAAM,EAAE,CAAC,eAAe;oBACzF;gBACF;;QAEA,mBAAmB,CAAC,KAAO,IAAI,CAAC;gBAC9B,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAClD,OAAO;oBACL,gBAAgB,WAAW;oBAC3B,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB;oBAAG;gBACzC;YACF;QAEA,QAAQ;QACR,WAAW,CAAC;QACZ,iBAAiB,EAAE;QAEnB,SAAS,CAAC,OAAS,IAAI,CAAC,QAAU,CAAC;oBACjC,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,IAAI;4BAAE,IAAI;wBAAa;qBAAE;gBAC5E,CAAC;QAED,YAAY,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QAED,YAAY,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClC,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QAED,cAAc,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa;mCAAI,EAAE,WAAW;gCAAE;oCAAE;oCAAU;gCAAS;6BAAE;wBAAC,IAChE;gBAER,CAAC;QAED,iBAAiB,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACvD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,QAAQ,KAAK,YAAY,EAAE,QAAQ,KAAK,QAAQ;wBAAG,IACtG;gBAER,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;gBACnD,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,WAAW;wBAAE,GAAG,MAAM,SAAS;wBAAE,CAAC,UAAU,EAAE,MAAM,eAAe;oBAAC;gBACtE,CAAC;QAED,OAAO;QACP,YAAY,CAAC;QAEb,cAAc,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,SAAS;gCAAE,IAAI;4BAAa;yBAClC;oBACH;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QAED,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,QAAQ;QACR,eAAe,CAAC;QAEhB,iBAAiB,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBACvD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE;+BACP,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;4BACxC;gCAAE,GAAG,OAAO;gCAAE,IAAI;4BAAa;yBAChC;oBACH;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9D,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACtD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACrD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC3E;gBACF,CAAC;QAED,kBAAkB,CAAC,YAAc,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;QAErE,eAAe;QACf,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,WAAW,CAAC;QACZ,aAAa,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE;+BACP,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;4BACpC;gCAAE,GAAG,QAAQ;gCAAE,IAAI;4BAAa;yBACjC;oBACH;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC1D,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IAClD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACjD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACvE;gBACF,CAAC;QACD,cAAc,CAAC,YAAc,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;QAE7D,YAAY,CAAC;QACb,cAAc,CAAC,WAAW,QAAU,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,KAAK;gCAAE,IAAI;gCAAc,WAAW,IAAI;4BAAO;yBACrD;oBACH;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QACD,qBAAqB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,YAAY,CAAC,EAAE,UAAU;4BAAC,IAAI;oBAExD;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,WAAW,UAAY,IAAI,CAAC;gBACvD,MAAM,WAAW;uBAAK,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;iBAAE;gBACvD,MAAM,CAAC,QAAQ,GAAG,SAAS,MAAM,CAAC,WAAW;gBAC7C,SAAS,MAAM,CAAC,SAAS,GAAG;gBAC5B,YAAY;gBACZ,SAAS,OAAO,CAAC,CAAC,SAAS;oBACzB,QAAQ,KAAK,GAAG,QAAQ;gBAC1B;gBACA,OAAO;oBACL,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;oBACf;gBACF;YACF;QACA,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,iBAAiB,EAAE;QACnB,mBAAmB,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBAC/C,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,QAAQ;4BAAE,IAAI;wBAAa;qBAAE;gBAChF,CAAC;QACD,sBAAsB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACrD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QACD,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QACD,8BAA8B,CAAC,WAC7B,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QAEnD,oBAAoB,CAAC;QACrB,6BAA6B,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC1D,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX;4BACA,SAAS,EAAE;4BACX,OAAO,EAAE;4BACT,YAAY,IAAI;wBAClB;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACjE,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,kBAAkB,CAAC,UAAU;4BACtC,GAAG,SAAS;wBACd;oBACF;gBACF,CAAC;QAED,mBAAmB,CAAC;QACpB,gBAAgB,CAAC,WAAW,aAAe,IAAI,CAAC,QAAU,CAAC;oBACzD,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX;4BACA;4BACA,QAAQ;4BACR,UAAU;gCACR,YAAY;gCACZ,gBAAgB;gCAChB,aAAa;gCACb,YAAY;4BACd;4BACA,OAAO,EAAE;4BACT,SAAS,CAAC;4BACV,WAAW,IAAI;wBACjB;oBACF;gBACF,CAAC;QACD,gBAAgB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC7C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,iBAAiB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC9C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC5C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;4BACR,SAAS,IAAI;wBACf;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBAChE,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,GAAG,QAAQ;wBACb;oBACF;gBACF,CAAC;IACH,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,eAAe,MAAM,aAAa;YAClC,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,UAAU,MAAM,QAAQ;YACxB,iBAAiB,MAAM,eAAe;YACtC,oBAAoB,MAAM,kBAAkB;YAC5C,IAAI;gBACF,OAAO,MAAM,EAAE,CAAC,KAAK;gBACrB,UAAU,MAAM,EAAE,CAAC,QAAQ;gBAC3B,kBAAkB,MAAM,EAAE,CAAC,gBAAgB;YAC7C;QACF,CAAC;AACH,IAEF;IAAE,MAAM;AAAoB;uCAIjB", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/utils/aiService.ts"], "sourcesContent": ["// AI服务集成\ninterface AIConfig {\n  provider: 'openai' | 'claude' | 'gemini' | 'custom';\n  apiKey: string;\n  baseUrl?: string;\n  model: string;\n  temperature?: number;\n  maxTokens?: number;\n}\n\ninterface AIResponse {\n  success: boolean;\n  data?: any;\n  error?: string;\n}\n\n// AI服务类\nexport class AIService {\n  private config: AIConfig | null = null;\n\n  // 设置AI配置\n  setConfig(config: AIConfig) {\n    this.config = config;\n    // 保存到localStorage (仅在客户端)\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('ai-config', JSON.stringify(config));\n    }\n  }\n\n  // 获取AI配置\n  getConfig(): AIConfig | null {\n    if (this.config) return this.config;\n\n    // 仅在客户端访问localStorage\n    if (typeof window !== 'undefined') {\n      const saved = localStorage.getItem('ai-config');\n      if (saved) {\n        this.config = JSON.parse(saved);\n        return this.config;\n      }\n    }\n    return null;\n  }\n\n  // 检查是否已配置\n  isConfigured(): boolean {\n    return this.getConfig() !== null;\n  }\n\n  // 通用AI请求方法\n  private async makeRequest(prompt: string, systemPrompt?: string): Promise<AIResponse> {\n    const config = this.getConfig();\n    if (!config) {\n      return { success: false, error: '请先配置AI API' };\n    }\n\n    try {\n      let response;\n      \n      switch (config.provider) {\n        case 'openai':\n          response = await this.callOpenAI(prompt, systemPrompt, config);\n          break;\n        case 'claude':\n          response = await this.callClaude(prompt, systemPrompt, config);\n          break;\n        case 'gemini':\n          response = await this.callGemini(prompt, systemPrompt, config);\n          break;\n        case 'custom':\n          response = await this.callCustomAPI(prompt, systemPrompt, config);\n          break;\n        default:\n          return { success: false, error: '不支持的AI提供商' };\n      }\n\n      return { success: true, data: response };\n    } catch (error: any) {\n      return { success: false, error: error.message || '请求失败' };\n    }\n  }\n\n  // OpenAI API调用\n  private async callOpenAI(prompt: string, systemPrompt: string = '', config: AIConfig) {\n    const messages = [];\n    if (systemPrompt) {\n      messages.push({ role: 'system', content: systemPrompt });\n    }\n    messages.push({ role: 'user', content: prompt });\n\n    const response = await fetch(config.baseUrl || 'https://api.openai.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${config.apiKey}`,\n      },\n      body: JSON.stringify({\n        model: config.model || 'gpt-3.5-turbo',\n        messages,\n        temperature: config.temperature || 0.7,\n        max_tokens: config.maxTokens || 2000,\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`OpenAI API错误: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.choices[0].message.content;\n  }\n\n  // Claude API调用\n  private async callClaude(prompt: string, systemPrompt: string = '', config: AIConfig) {\n    const response = await fetch(config.baseUrl || 'https://api.anthropic.com/v1/messages', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-api-key': config.apiKey,\n        'anthropic-version': '2023-06-01',\n      },\n      body: JSON.stringify({\n        model: config.model || 'claude-3-sonnet-20240229',\n        max_tokens: config.maxTokens || 2000,\n        temperature: config.temperature || 0.7,\n        system: systemPrompt,\n        messages: [{ role: 'user', content: prompt }],\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`Claude API错误: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.content[0].text;\n  }\n\n  // Gemini API调用\n  private async callGemini(prompt: string, systemPrompt: string = '', config: AIConfig) {\n    const fullPrompt = systemPrompt ? `${systemPrompt}\\n\\n${prompt}` : prompt;\n    \n    const response = await fetch(\n      `${config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta/models'}/${config.model || 'gemini-pro'}:generateContent?key=${config.apiKey}`,\n      {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contents: [{ parts: [{ text: fullPrompt }] }],\n          generationConfig: {\n            temperature: config.temperature || 0.7,\n            maxOutputTokens: config.maxTokens || 2000,\n          },\n        }),\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`Gemini API错误: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.candidates[0].content.parts[0].text;\n  }\n\n  // 自定义API调用\n  private async callCustomAPI(prompt: string, systemPrompt: string = '', config: AIConfig) {\n    const response = await fetch(config.baseUrl!, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${config.apiKey}`,\n      },\n      body: JSON.stringify({\n        prompt,\n        system_prompt: systemPrompt,\n        model: config.model,\n        temperature: config.temperature || 0.7,\n        max_tokens: config.maxTokens || 2000,\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`自定义API错误: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.response || data.content || data.text;\n  }\n\n  // 分析工作流需求\n  async analyzeWorkflowRequirements(requirements: {\n    genre: string;\n    style: string;\n    length: string;\n    experience: string;\n    features: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个专业的小说创作工作流设计专家。根据用户的需求，分析并推荐最适合的工作流类型。\n\n可选的工作流类型：\n1. quick-novel - 快速小说生成（适合新手，简单流程）\n2. professional-novel - 专业小说创作（完整流程，适合有经验的作者）\n3. fantasy-novel - 奇幻小说专用（重点关注世界观和魔法体系）\n4. romance-novel - 言情小说工作流（专注情感描写和角色关系）\n\n请返回JSON格式的推荐结果，包含：\n- recommended: 推荐的工作流ID\n- confidence: 推荐置信度(0-100)\n- reasons: 推荐理由数组\n- alternatives: 备选方案数组`;\n\n    const prompt = `用户需求：\n- 小说类型：${requirements.genre}\n- 写作风格：${requirements.style}\n- 作品长度：${requirements.length}\n- 创作经验：${requirements.experience}\n- 特殊需求：${requirements.features.join(', ')}\n\n请分析并推荐最适合的工作流。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成自定义工作流\n  async generateCustomWorkflow(requirements: {\n    genre: string;\n    style: string;\n    length: string;\n    features: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个AI小说创作工作流设计师。根据用户需求设计一个定制化的工作流。\n\n可用的节点类型：\n- input: 用户参数输入\n- title-generator: 书名生成\n- detail-generator: 详情生成\n- character-creator: 角色创建\n- worldbuilding: 世界观构建\n- plotline-planner: 主线规划\n- outline-generator: 大纲生成\n- chapter-count-input: 章节数设定\n- detailed-outline: 详细大纲\n- chapter-generator: 章节生成\n- content-polisher: 内容润色\n- consistency-checker: 一致性检查\n- condition: 条件分支\n- loop: 循环执行\n- output: 结果输出\n\n请返回JSON格式的工作流定义，包含：\n- name: 工作流名称\n- description: 工作流描述\n- nodes: 节点数组，每个节点包含 {type, label, position: {x, y}}\n- connections: 连接数组，每个连接包含 {source: 节点索引, target: 节点索引}\n- complexity: 复杂度 (simple/medium/complex)`;\n\n    const prompt = `请为以下需求设计一个定制化的小说创作工作流：\n- 小说类型：${requirements.genre}\n- 写作风格：${requirements.style}\n- 作品长度：${requirements.length}\n- 特殊需求：${requirements.features.join(', ')}\n\n请确保工作流逻辑合理，节点连接有序，适合用户的具体需求。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 优化现有工作流\n  async optimizeWorkflow(currentNodes: any[], requirements: any): Promise<AIResponse> {\n    const systemPrompt = `你是一个工作流优化专家。分析现有的工作流并提供优化建议。\n\n请返回JSON格式的优化建议，包含：\n- issues: 发现的问题数组\n- suggestions: 优化建议数组\n- optimized_workflow: 优化后的工作流定义（如果需要重构）\n- improvement_score: 改进评分(0-100)`;\n\n    const nodeTypes = currentNodes.map(node => node.data?.type || 'unknown');\n    const prompt = `当前工作流包含以下节点：${nodeTypes.join(', ')}\n\n用户需求：${JSON.stringify(requirements)}\n\n请分析这个工作流的问题并提供优化建议。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成书名\n  async generateTitles(params: {\n    genre: string;\n    style: string;\n    keywords?: string[];\n    count?: number;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个专业的书名创作专家。根据小说类型、风格和关键词生成吸引人的书名。\n\n请返回JSON格式的结果，包含：\n- titles: 书名数组，每个包含 {title, score, analysis: {appeal, memorability, genre_match, uniqueness, marketability}, suggestions: 优化建议数组}`;\n\n    const prompt = `请为以下小说生成${params.count || 5}个书名：\n- 类型：${params.genre}\n- 风格：${params.style}\n- 关键词：${params.keywords?.join(', ') || '无'}\n\n要求书名要有吸引力、易记忆、符合类型特征。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成角色\n  async generateCharacter(params: {\n    genre: string;\n    role: string;\n    background?: string;\n    personality?: string;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个角色设计专家。根据小说类型和角色定位创建详细的角色设定。\n\n请返回JSON格式的角色信息，包含：\n- name: 角色姓名\n- age: 年龄\n- gender: 性别\n- appearance: 外貌描述\n- personality: 性格特征\n- background: 背景故事\n- skills: 技能特长\n- relationships: 人际关系\n- goals: 目标动机\n- flaws: 性格缺陷`;\n\n    const prompt = `请创建一个${params.genre}小说中的${params.role}角色：\n${params.background ? `背景要求：${params.background}` : ''}\n${params.personality ? `性格要求：${params.personality}` : ''}\n\n请提供详细的角色设定。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成世界观\n  async generateWorldbuilding(params: {\n    genre: string;\n    style: string;\n    scope?: string;\n    elements?: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个世界观构建专家。根据小说类型和风格创建详细的世界设定。\n\n请返回JSON格式的世界观信息，包含：\n- name: 世界名称\n- overview: 世界概述\n- geography: 地理环境\n- history: 历史背景\n- culture: 文化特色\n- politics: 政治体系\n- economy: 经济体系\n- technology: 科技水平\n- magic_system: 魔法/超能力体系（如适用）\n- races: 种族设定\n- languages: 语言系统\n- religions: 宗教信仰\n- conflicts: 主要冲突`;\n\n    const prompt = `请为${params.genre}类型的小说创建世界观设定：\n- 风格：${params.style}\n- 范围：${params.scope || '中等规模'}\n- 特殊元素：${params.elements?.join(', ') || '无'}\n\n请提供详细的世界观设定。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成主线规划\n  async generatePlotline(params: {\n    genre: string;\n    style: string;\n    characters: any[];\n    worldbuilding?: any;\n    themes?: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个故事情节规划专家。根据角色和世界观创建引人入胜的主线情节。\n\n请返回JSON格式的主线规划，包含：\n- title: 主线标题\n- premise: 故事前提\n- inciting_incident: 引发事件\n- plot_points: 关键情节点数组，每个包含 {act, description, characters_involved, conflicts, outcomes}\n- climax: 高潮设定\n- resolution: 结局安排\n- themes: 主题表达\n- character_arcs: 角色成长弧线\n- subplots: 支线情节建议`;\n\n    const charactersInfo = params.characters.map(char => `${char.name}(${char.role})`).join(', ');\n    const prompt = `请为${params.genre}小说规划主线情节：\n- 风格：${params.style}\n- 主要角色：${charactersInfo}\n- 世界背景：${params.worldbuilding?.name || '待定'}\n- 主题方向：${params.themes?.join(', ') || '待定'}\n\n请创建完整的故事主线规划。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成大纲\n  async generateOutline(params: {\n    genre: string;\n    plotline: any;\n    characters: any[];\n    targetLength: string;\n    chapterCount?: number;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个小说大纲创作专家。根据主线情节创建详细的章节大纲。\n\n请返回JSON格式的大纲信息，包含：\n- title: 大纲标题\n- structure: 结构类型（三幕式、英雄之旅等）\n- chapters: 章节数组，每个包含 {number, title, summary, key_events, character_focus, word_count_target, notes}\n- pacing: 节奏安排\n- tension_curve: 张力曲线描述\n- themes_distribution: 主题分布`;\n\n    const prompt = `请为${params.genre}小说创建详细大纲：\n- 目标长度：${params.targetLength}\n- 预计章节数：${params.chapterCount || '待定'}\n- 主线情节：${params.plotline.title || '待定'}\n- 主要角色：${params.characters.map(c => c.name).join(', ')}\n\n请创建完整的章节大纲。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成详细大纲\n  async generateDetailedOutline(params: {\n    outline: any;\n    characters: any[];\n    worldbuilding?: any;\n    selectedChapters?: number[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个详细大纲创作专家。将基础大纲扩展为详细的情节要点。\n\n请返回JSON格式的详细大纲，包含：\n- chapters: 详细章节数组，每个包含 {\n    number, title, detailed_summary, scenes: [{\n      scene_number, location, characters, objective, conflict, outcome, mood, pov\n    }], character_development, plot_advancement, foreshadowing, themes\n  }`;\n\n    const chapterInfo = params.selectedChapters ?\n      `重点章节：${params.selectedChapters.join(', ')}` :\n      '所有章节';\n\n    const prompt = `请为以下大纲创建详细的情节要点：\n- 基础大纲：${params.outline.title}\n- 角色信息：${params.characters.map(c => c.name).join(', ')}\n- 世界设定：${params.worldbuilding?.name || '待定'}\n- 处理范围：${chapterInfo}\n\n请提供详细的场景和情节要点。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成章节内容\n  async generateChapter(params: {\n    chapterNumber: number;\n    chapterOutline: any;\n    characters: any[];\n    worldbuilding?: any;\n    previousChapters?: string[];\n    style: string;\n    targetWordCount?: number;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个专业的小说创作者。根据章节大纲创作具体的章节内容。\n\n请返回JSON格式的章节内容，包含：\n- title: 章节标题\n- content: 章节正文内容\n- word_count: 字数统计\n- scenes: 场景分解\n- character_moments: 角色重要时刻\n- plot_advancement: 情节推进要点\n- foreshadowing: 伏笔设置\n- themes_explored: 探讨的主题`;\n\n    const previousContext = params.previousChapters?.length ?\n      `前文概要：${params.previousChapters.slice(-2).join('\\n')}` :\n      '这是开篇章节';\n\n    const prompt = `请创作第${params.chapterNumber}章的具体内容：\n- 章节大纲：${JSON.stringify(params.chapterOutline)}\n- 主要角色：${params.characters.map(c => c.name).join(', ')}\n- 写作风格：${params.style}\n- 目标字数：${params.targetWordCount || 2000}字\n- ${previousContext}\n\n请创作完整的章节内容。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 内容润色\n  async polishContent(params: {\n    content: string;\n    style: string;\n    focusAreas?: string[];\n    targetAudience?: string;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个专业的文本润色专家。优化文本的表达、节奏和文学性。\n\n请返回JSON格式的润色结果，包含：\n- polished_content: 润色后的内容\n- improvements: 改进说明数组\n- style_analysis: 风格分析\n- readability_score: 可读性评分\n- suggestions: 进一步优化建议`;\n\n    const focusInfo = params.focusAreas?.length ?\n      `重点关注：${params.focusAreas.join(', ')}` :\n      '全面优化';\n\n    const prompt = `请润色以下文本内容：\n- 目标风格：${params.style}\n- 目标读者：${params.targetAudience || '一般读者'}\n- ${focusInfo}\n\n原文内容：\n${params.content}\n\n请提供润色后的版本和改进说明。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 一致性检查\n  async checkConsistency(params: {\n    content: string[];\n    characters: any[];\n    worldbuilding?: any;\n    checkTypes?: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个内容一致性检查专家。检查文本中的逻辑、角色、设定等一致性问题。\n\n请返回JSON格式的检查结果，包含：\n- consistency_score: 一致性评分(0-100)\n- issues: 问题数组，每个包含 {type, severity, description, location, suggestion}\n- character_consistency: 角色一致性分析\n- plot_consistency: 情节一致性分析\n- world_consistency: 世界观一致性分析\n- timeline_issues: 时间线问题\n- recommendations: 修改建议`;\n\n    const checkInfo = params.checkTypes?.length ?\n      `检查类型：${params.checkTypes.join(', ')}` :\n      '全面检查';\n\n    const prompt = `请检查以下内容的一致性：\n- 角色设定：${params.characters.map(c => c.name).join(', ')}\n- 世界设定：${params.worldbuilding?.name || '待定'}\n- ${checkInfo}\n\n内容文本：\n${params.content.join('\\n\\n---\\n\\n')}\n\n请提供详细的一致性分析报告。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成小说详情\n  async generateNovelDetails(params: {\n    title: string;\n    genre: string;\n    style: string;\n    outline?: any;\n    characters?: any[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个小说包装专家。根据小说内容生成吸引人的详情信息。\n\n请返回JSON格式的详情信息，包含：\n- synopsis: 内容简介\n- tagline: 宣传语\n- keywords: 关键词数组\n- target_audience: 目标读者群体\n- selling_points: 卖点分析\n- genre_tags: 类型标签\n- mood_tags: 氛围标签\n- content_warnings: 内容提醒\n- marketing_description: 营销描述`;\n\n    const prompt = `请为小说《${params.title}》生成详情信息：\n- 类型：${params.genre}\n- 风格：${params.style}\n- 大纲概要：${params.outline?.title || '待定'}\n- 主要角色：${params.characters?.map(c => c.name).join(', ') || '待定'}\n\n请生成完整的小说详情包装。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n}\n\n// 导出单例实例\nexport const aiService = new AIService();\n"], "names": [], "mappings": "AAAA,SAAS;;;;;;;AAiBF,MAAM;IAGX,SAAS;IACT,UAAU,MAAgB,EAAE;QAC1B,IAAI,CAAC,MAAM,GAAG;QACd,0BAA0B;QAC1B,wCAAmC;YACjC,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACnD;IACF;IAEA,SAAS;IACT,YAA6B;QAC3B,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,MAAM;QAEnC,sBAAsB;QACtB,wCAAmC;YACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC;gBACzB,OAAO,IAAI,CAAC,MAAM;YACpB;QACF;QACA,OAAO;IACT;IAEA,UAAU;IACV,eAAwB;QACtB,OAAO,IAAI,CAAC,SAAS,OAAO;IAC9B;IAEA,WAAW;IACX,MAAc,YAAY,MAAc,EAAE,YAAqB,EAAuB;QACpF,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,IAAI,CAAC,QAAQ;YACX,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;QAEA,IAAI;YACF,IAAI;YAEJ,OAAQ,OAAO,QAAQ;gBACrB,KAAK;oBACH,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,cAAc;oBACvD;gBACF,KAAK;oBACH,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,cAAc;oBACvD;gBACF,KAAK;oBACH,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,cAAc;oBACvD;gBACF,KAAK;oBACH,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,cAAc;oBAC1D;gBACF;oBACE,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAY;YAChD;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO,IAAI;YAAO;QAC1D;IACF;IAEA,eAAe;IACf,MAAc,WAAW,MAAc,EAA+C;YAA7C,eAAA,iEAAuB,IAAI;QAClE,MAAM,WAAW,EAAE;QACnB,IAAI,cAAc;YAChB,SAAS,IAAI,CAAC;gBAAE,MAAM;gBAAU,SAAS;YAAa;QACxD;QACA,SAAS,IAAI,CAAC;YAAE,MAAM;YAAQ,SAAS;QAAO;QAE9C,MAAM,WAAW,MAAM,MAAM,OAAO,OAAO,IAAI,8CAA8C;YAC3F,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,AAAC,UAAuB,OAAd,OAAO,MAAM;YAC1C;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK,IAAI;gBACvB;gBACA,aAAa,OAAO,WAAW,IAAI;gBACnC,YAAY,OAAO,SAAS,IAAI;YAClC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,iBAAmC,OAAnB,SAAS,MAAM,EAAC,KAAuB,OAApB,SAAS,UAAU;QACzE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;IACxC;IAEA,eAAe;IACf,MAAc,WAAW,MAAc,EAA+C;YAA7C,eAAA,iEAAuB,IAAI;QAClE,MAAM,WAAW,MAAM,MAAM,OAAO,OAAO,IAAI,yCAAyC;YACtF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,aAAa,OAAO,MAAM;gBAC1B,qBAAqB;YACvB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK,IAAI;gBACvB,YAAY,OAAO,SAAS,IAAI;gBAChC,aAAa,OAAO,WAAW,IAAI;gBACnC,QAAQ;gBACR,UAAU;oBAAC;wBAAE,MAAM;wBAAQ,SAAS;oBAAO;iBAAE;YAC/C;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,iBAAmC,OAAnB,SAAS,MAAM,EAAC,KAAuB,OAApB,SAAS,UAAU;QACzE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI;IAC7B;IAEA,eAAe;IACf,MAAc,WAAW,MAAc,EAA+C;YAA7C,eAAA,iEAAuB,IAAI;QAClE,MAAM,aAAa,eAAe,AAAC,GAAqB,OAAnB,cAAa,QAAa,OAAP,UAAW;QAEnE,MAAM,WAAW,MAAM,MACrB,AAAC,GAAiF,OAA/E,OAAO,OAAO,IAAI,2DAA0D,KAAuD,OAApD,OAAO,KAAK,IAAI,cAAa,yBAAqC,OAAd,OAAO,MAAM,GACnJ;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,UAAU;oBAAC;wBAAE,OAAO;4BAAC;gCAAE,MAAM;4BAAW;yBAAE;oBAAC;iBAAE;gBAC7C,kBAAkB;oBAChB,aAAa,OAAO,WAAW,IAAI;oBACnC,iBAAiB,OAAO,SAAS,IAAI;gBACvC;YACF;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,iBAAmC,OAAnB,SAAS,MAAM,EAAC,KAAuB,OAApB,SAAS,UAAU;QACzE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;IACjD;IAEA,WAAW;IACX,MAAc,cAAc,MAAc,EAA+C;YAA7C,eAAA,iEAAuB,IAAI;QACrE,MAAM,WAAW,MAAM,MAAM,OAAO,OAAO,EAAG;YAC5C,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,AAAC,UAAuB,OAAd,OAAO,MAAM;YAC1C;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA,eAAe;gBACf,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,WAAW,IAAI;gBACnC,YAAY,OAAO,SAAS,IAAI;YAClC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,AAAC,aAA+B,OAAnB,SAAS,MAAM,EAAC,KAAuB,OAApB,SAAS,UAAU;QACrE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI;IACnD;IAEA,UAAU;IACV,MAAM,4BAA4B,YAMjC,EAAuB;QACtB,MAAM,eAAgB;QActB,MAAM,SAAS,AAAC,iBAEX,OADA,aAAa,KAAK,EAAC,aAEnB,OADA,aAAa,KAAK,EAAC,aAEnB,OADA,aAAa,MAAM,EAAC,aAEpB,OADA,aAAa,UAAU,EAAC,aACS,OAAjC,aAAa,QAAQ,CAAC,IAAI,CAAC,OAAM;QAItC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,WAAW;IACX,MAAM,uBAAuB,YAK5B,EAAuB;QACtB,MAAM,eAAgB;QA0BtB,MAAM,SAAS,AAAC,kCAEX,OADA,aAAa,KAAK,EAAC,aAEnB,OADA,aAAa,KAAK,EAAC,aAEnB,OADA,aAAa,MAAM,EAAC,aACa,OAAjC,aAAa,QAAQ,CAAC,IAAI,CAAC,OAAM;QAItC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,UAAU;IACV,MAAM,iBAAiB,YAAmB,EAAE,YAAiB,EAAuB;QAClF,MAAM,eAAgB;QAQtB,MAAM,YAAY,aAAa,GAAG,CAAC,CAAA;gBAAQ;mBAAA,EAAA,aAAA,KAAK,IAAI,cAAT,iCAAA,WAAW,IAAI,KAAI;;QAC9D,MAAM,SAAS,AAAC,eAEb,OAF2B,UAAU,IAAI,CAAC,OAAM,aAEnB,OAA7B,KAAK,SAAS,CAAC,eAAc;QAIhC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,OAAO;IACP,MAAM,eAAe,MAKpB,EAAuB;YASlB;QARJ,MAAM,eAAgB;QAKtB,MAAM,SAAS,AAAC,WACb,OADuB,OAAO,KAAK,IAAI,GAAE,eAEzC,OADA,OAAO,KAAK,EAAC,WAEZ,OADD,OAAO,KAAK,EAAC,YACuB,OAAnC,EAAA,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,IAAI,CAAC,UAAS,KAAI;QAIvC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,OAAO;IACP,MAAM,kBAAkB,MAKvB,EAAuB;QACtB,MAAM,eAAgB;QActB,MAAM,SAAS,AAAC,QAA0B,OAAnB,OAAO,KAAK,EAAC,QACtC,OAD4C,OAAO,IAAI,EAAC,SAExD,OADA,OAAO,UAAU,GAAG,AAAC,QAAyB,OAAlB,OAAO,UAAU,IAAK,IAAG,MACE,OAAvD,OAAO,WAAW,GAAG,AAAC,QAA0B,OAAnB,OAAO,WAAW,IAAK,IAAG;QAIrD,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,QAAQ;IACR,MAAM,sBAAsB,MAK3B,EAAuB;YAqBjB;QApBL,MAAM,eAAgB;QAiBtB,MAAM,SAAS,AAAC,KACb,OADiB,OAAO,KAAK,EAAC,wBAE9B,OADA,OAAO,KAAK,EAAC,WAEX,OADF,OAAO,KAAK,IAAI,QAAO,aACc,OAAnC,EAAA,mBAAA,OAAO,QAAQ,cAAf,uCAAA,iBAAiB,IAAI,CAAC,UAAS,KAAI;QAIxC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,SAAS;IACT,MAAM,iBAAiB,MAMtB,EAAuB;YAkBjB,uBACA;QAlBL,MAAM,eAAgB;QAatB,MAAM,iBAAiB,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,OAAQ,AAAC,GAAe,OAAb,KAAK,IAAI,EAAC,KAAa,OAAV,KAAK,IAAI,EAAC,MAAI,IAAI,CAAC;QACxF,MAAM,SAAS,AAAC,KACb,OADiB,OAAO,KAAK,EAAC,oBAE5B,OADF,OAAO,KAAK,EAAC,aAEX,OADA,gBAAe,aAEf,OADA,EAAA,wBAAA,OAAO,aAAa,cAApB,4CAAA,sBAAsB,IAAI,KAAI,MAAK,aACD,OAAlC,EAAA,iBAAA,OAAO,MAAM,cAAb,qCAAA,eAAe,IAAI,CAAC,UAAS,MAAK;QAIvC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,OAAO;IACP,MAAM,gBAAgB,MAMrB,EAAuB;QACtB,MAAM,eAAgB;QAUtB,MAAM,SAAS,AAAC,KACX,OADe,OAAO,KAAK,EAAC,sBAE3B,OADD,OAAO,YAAY,EAAC,cAEpB,OADC,OAAO,YAAY,IAAI,MAAK,aAE7B,OADA,OAAO,QAAQ,CAAC,KAAK,IAAI,MAAK,aACgB,OAA9C,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAM;QAInD,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,SAAS;IACT,MAAM,wBAAwB,MAK7B,EAAuB;YAiBjB;QAhBL,MAAM,eAAgB;QAStB,MAAM,cAAc,OAAO,gBAAgB,GACzC,AAAC,QAA0C,OAAnC,OAAO,gBAAgB,CAAC,IAAI,CAAC,SACrC;QAEF,MAAM,SAAS,AAAC,4BAEX,OADA,OAAO,OAAO,CAAC,KAAK,EAAC,aAErB,OADA,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAM,aAE9C,OADA,EAAA,wBAAA,OAAO,aAAa,cAApB,4CAAA,sBAAsB,IAAI,KAAI,MAAK,aACvB,OAAZ,aAAY;QAIjB,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,SAAS;IACT,MAAM,gBAAgB,MAQrB,EAAuB;YAaE;QAZxB,MAAM,eAAgB;QAYtB,MAAM,kBAAkB,EAAA,2BAAA,OAAO,gBAAgB,cAAvB,+CAAA,yBAAyB,MAAM,IACrD,AAAC,QAAoD,OAA7C,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,SAC/C;QAEF,MAAM,SAAS,AAAC,OACX,OADiB,OAAO,aAAa,EAAC,oBAEtC,OADA,KAAK,SAAS,CAAC,OAAO,cAAc,GAAE,aAEtC,OADA,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAM,aAE9C,OADA,OAAO,KAAK,EAAC,aAElB,OADK,OAAO,eAAe,IAAI,MAAK,SACpB,OAAhB,iBAAgB;QAIhB,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,OAAO;IACP,MAAM,cAAc,MAKnB,EAAuB;YAUJ;QATlB,MAAM,eAAgB;QAStB,MAAM,YAAY,EAAA,qBAAA,OAAO,UAAU,cAAjB,yCAAA,mBAAmB,MAAM,IACzC,AAAC,QAAoC,OAA7B,OAAO,UAAU,CAAC,IAAI,CAAC,SAC/B;QAEF,MAAM,SAAS,AAAC,sBAEX,OADA,OAAO,KAAK,EAAC,aAElB,OADK,OAAO,cAAc,IAAI,QAAO,QAIvC,OAHE,WAAU,eAGG,OAAf,OAAO,OAAO,EAAC;QAIb,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,QAAQ;IACR,MAAM,iBAAiB,MAKtB,EAAuB;YAYJ,oBAMb;QAjBL,MAAM,eAAgB;QAWtB,MAAM,YAAY,EAAA,qBAAA,OAAO,UAAU,cAAjB,yCAAA,mBAAmB,MAAM,IACzC,AAAC,QAAoC,OAA7B,OAAO,UAAU,CAAC,IAAI,CAAC,SAC/B;QAEF,MAAM,SAAS,AAAC,wBAEX,OADA,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAM,aAEnD,OADK,EAAA,wBAAA,OAAO,aAAa,cAApB,4CAAA,sBAAsB,IAAI,KAAI,MAAK,QAI1C,OAHE,WAAU,eAGuB,OAAnC,OAAO,OAAO,CAAC,IAAI,CAAC,gBAAe;QAIjC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,SAAS;IACT,MAAM,qBAAqB,MAM1B,EAAuB;YAiBjB,iBACA;QAjBL,MAAM,eAAgB;QAatB,MAAM,SAAS,AAAC,QACb,OADoB,OAAO,KAAK,EAAC,mBAEjC,OADA,OAAO,KAAK,EAAC,WAEX,OADF,OAAO,KAAK,EAAC,aAEX,OADA,EAAA,kBAAA,OAAO,OAAO,cAAd,sCAAA,gBAAgB,KAAK,KAAI,MAAK,aACyB,OAAvD,EAAA,qBAAA,OAAO,UAAU,cAAjB,yCAAA,mBAAmB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,UAAS,MAAK;QAI5D,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;;QA3kBA,+KAAQ,UAA0B;;AA4kBpC;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/utils/workflowAI.ts"], "sourcesContent": ["// AI工作流生成器\nimport { WorkflowNode } from '@/types';\n\n// 工作流模板定义\ninterface WorkflowTemplate {\n  id: string;\n  name: string;\n  description: string;\n  nodes: Array<{\n    type: string;\n    label: string;\n    position: { x: number; y: number };\n    config?: Record<string, any>;\n  }>;\n  connections: Array<{\n    source: number;\n    target: number;\n  }>;\n  tags: string[];\n  complexity: 'simple' | 'medium' | 'complex';\n}\n\n// 预定义工作流模板\nconst WORKFLOW_TEMPLATES: WorkflowTemplate[] = [\n  {\n    id: 'quick-novel',\n    name: '快速小说生成',\n    description: '适合新手的简单工作流，快速生成短篇小说',\n    nodes: [\n      { type: 'input', label: '创作输入', position: { x: 100, y: 100 } },\n      { type: 'title-generator', label: '生成书名', position: { x: 100, y: 250 } },\n      { type: 'character-creator', label: '创建角色', position: { x: 350, y: 250 } },\n      { type: 'outline-generator', label: '生成大纲', position: { x: 225, y: 400 } },\n      { type: 'chapter-generator', label: '生成章节', position: { x: 225, y: 550 } },\n      { type: 'output', label: '完成输出', position: { x: 225, y: 700 } }\n    ],\n    connections: [\n      { source: 0, target: 1 },\n      { source: 0, target: 2 },\n      { source: 1, target: 3 },\n      { source: 2, target: 3 },\n      { source: 3, target: 4 },\n      { source: 4, target: 5 }\n    ],\n    tags: ['简单', '快速', '新手'],\n    complexity: 'simple'\n  },\n  {\n    id: 'professional-novel',\n    name: '专业小说创作',\n    description: '完整的专业级工作流，包含详细的世界观和角色设定',\n    nodes: [\n      { type: 'input', label: '创作参数', position: { x: 100, y: 50 } },\n      { type: 'title-generator', label: '书名生成', position: { x: 50, y: 200 } },\n      { type: 'detail-generator', label: '详情生成', position: { x: 200, y: 200 } },\n      { type: 'character-creator', label: '角色创建', position: { x: 350, y: 200 } },\n      { type: 'worldbuilding', label: '世界观构建', position: { x: 500, y: 200 } },\n      { type: 'plotline-planner', label: '主线规划', position: { x: 150, y: 350 } },\n      { type: 'outline-generator', label: '大纲生成', position: { x: 350, y: 350 } },\n      { type: 'detailed-outline', label: '详细大纲', position: { x: 250, y: 500 } },\n      { type: 'chapter-generator', label: '章节生成', position: { x: 250, y: 650 } },\n      { type: 'content-polisher', label: '内容润色', position: { x: 100, y: 800 } },\n      { type: 'consistency-checker', label: '一致性检查', position: { x: 400, y: 800 } },\n      { type: 'output', label: '最终输出', position: { x: 250, y: 950 } }\n    ],\n    connections: [\n      { source: 0, target: 1 }, { source: 0, target: 2 }, { source: 0, target: 3 }, { source: 0, target: 4 },\n      { source: 1, target: 5 }, { source: 2, target: 5 }, { source: 3, target: 5 }, { source: 4, target: 5 },\n      { source: 3, target: 6 }, { source: 4, target: 6 }, { source: 5, target: 6 },\n      { source: 6, target: 7 }, { source: 7, target: 8 }, { source: 8, target: 9 }, { source: 8, target: 10 },\n      { source: 9, target: 11 }, { source: 10, target: 11 }\n    ],\n    tags: ['专业', '完整', '高质量'],\n    complexity: 'complex'\n  },\n  {\n    id: 'fantasy-novel',\n    name: '奇幻小说专用',\n    description: '专为奇幻类小说设计的工作流，重点关注世界观和魔法体系',\n    nodes: [\n      { type: 'input', label: '奇幻设定', position: { x: 100, y: 100 } },\n      { type: 'worldbuilding', label: '魔法世界', position: { x: 100, y: 250 } },\n      { type: 'character-creator', label: '角色种族', position: { x: 300, y: 250 } },\n      { type: 'plotline-planner', label: '冒险主线', position: { x: 200, y: 400 } },\n      { type: 'outline-generator', label: '冒险大纲', position: { x: 200, y: 550 } },\n      { type: 'chapter-generator', label: '章节创作', position: { x: 200, y: 700 } },\n      { type: 'consistency-checker', label: '设定检查', position: { x: 200, y: 850 } },\n      { type: 'output', label: '奇幻小说', position: { x: 200, y: 1000 } }\n    ],\n    connections: [\n      { source: 0, target: 1 }, { source: 0, target: 2 },\n      { source: 1, target: 3 }, { source: 2, target: 3 },\n      { source: 3, target: 4 }, { source: 4, target: 5 },\n      { source: 5, target: 6 }, { source: 6, target: 7 }\n    ],\n    tags: ['奇幻', '魔法', '冒险'],\n    complexity: 'medium'\n  },\n  {\n    id: 'romance-novel',\n    name: '言情小说工作流',\n    description: '专注于情感描写和角色关系的言情小说创作流程',\n    nodes: [\n      { type: 'input', label: '言情设定', position: { x: 100, y: 100 } },\n      { type: 'character-creator', label: '主角设定', position: { x: 50, y: 250 } },\n      { type: 'character-creator', label: '配角设定', position: { x: 200, y: 250 } },\n      { type: 'plotline-planner', label: '情感主线', position: { x: 125, y: 400 } },\n      { type: 'outline-generator', label: '情节大纲', position: { x: 125, y: 550 } },\n      { type: 'chapter-generator', label: '章节创作', position: { x: 125, y: 700 } },\n      { type: 'content-polisher', label: '情感润色', position: { x: 125, y: 850 } },\n      { type: 'output', label: '言情小说', position: { x: 125, y: 1000 } }\n    ],\n    connections: [\n      { source: 0, target: 1 }, { source: 0, target: 2 },\n      { source: 1, target: 3 }, { source: 2, target: 3 },\n      { source: 3, target: 4 }, { source: 4, target: 5 },\n      { source: 5, target: 6 }, { source: 6, target: 7 }\n    ],\n    tags: ['言情', '情感', '关系'],\n    complexity: 'medium'\n  }\n];\n\n// AI工作流分析器\nexport class WorkflowAI {\n  // 分析用户需求并推荐工作流\n  static analyzeRequirements(requirements: {\n    genre?: string;\n    style?: string;\n    length?: string;\n    experience?: string;\n    features?: string[];\n  }): WorkflowTemplate[] {\n    const { genre, style, length, experience, features = [] } = requirements;\n    \n    let recommendations: Array<{ template: WorkflowTemplate; score: number }> = [];\n    \n    WORKFLOW_TEMPLATES.forEach(template => {\n      let score = 0;\n      \n      // 根据类型匹配\n      if (genre) {\n        if (genre.includes('奇幻') && template.tags.includes('奇幻')) score += 30;\n        if (genre.includes('言情') && template.tags.includes('言情')) score += 30;\n        if (genre.includes('现代') && template.tags.includes('简单')) score += 20;\n      }\n      \n      // 根据经验水平匹配\n      if (experience) {\n        if (experience === '新手' && template.complexity === 'simple') score += 25;\n        if (experience === '进阶' && template.complexity === 'medium') score += 25;\n        if (experience === '专业' && template.complexity === 'complex') score += 25;\n      }\n      \n      // 根据长度匹配\n      if (length) {\n        if (length === '短篇' && template.complexity === 'simple') score += 20;\n        if (length === '中篇' && template.complexity === 'medium') score += 20;\n        if (length === '长篇' && template.complexity === 'complex') score += 20;\n      }\n      \n      // 根据特殊需求匹配\n      features.forEach(feature => {\n        if (template.tags.some(tag => tag.includes(feature))) {\n          score += 15;\n        }\n      });\n      \n      recommendations.push({ template, score });\n    });\n    \n    // 按分数排序并返回前3个\n    return recommendations\n      .sort((a, b) => b.score - a.score)\n      .slice(0, 3)\n      .map(r => r.template);\n  }\n  \n  // 生成自定义工作流\n  static generateCustomWorkflow(requirements: {\n    genre: string;\n    style: string;\n    length: string;\n    features: string[];\n  }): WorkflowTemplate {\n    const { genre, style, length, features } = requirements;\n    \n    // 基础节点\n    const nodes = [\n      { type: 'input', label: '创作输入', position: { x: 200, y: 100 } }\n    ];\n    \n    const connections: Array<{ source: number; target: number }> = [];\n    let currentY = 250;\n    let nodeIndex = 1;\n    \n    // 根据需求添加节点\n    const addNode = (type: string, label: string, x: number = 200) => {\n      nodes.push({ type, label, position: { x, y: currentY } });\n      return nodeIndex++;\n    };\n    \n    // 书名生成（总是需要）\n    const titleNode = addNode('title-generator', '书名生成', 100);\n    connections.push({ source: 0, target: titleNode });\n    \n    // 根据类型添加特定节点\n    if (genre.includes('奇幻') || features.includes('世界观')) {\n      const worldNode = addNode('worldbuilding', '世界观构建', 300);\n      connections.push({ source: 0, target: worldNode });\n    }\n    \n    // 角色创建（总是需要）\n    currentY += 150;\n    const charNode = addNode('character-creator', '角色创建');\n    connections.push({ source: 0, target: charNode });\n    \n    // 主线规划\n    currentY += 150;\n    const plotNode = addNode('plotline-planner', '主线规划');\n    connections.push({ source: titleNode, target: plotNode });\n    connections.push({ source: charNode, target: plotNode });\n    \n    // 大纲生成\n    currentY += 150;\n    const outlineNode = addNode('outline-generator', '大纲生成');\n    connections.push({ source: plotNode, target: outlineNode });\n    \n    // 根据长度决定是否需要详细大纲\n    if (length === '长篇') {\n      currentY += 150;\n      const detailOutlineNode = addNode('detailed-outline', '详细大纲');\n      connections.push({ source: outlineNode, target: detailOutlineNode });\n    }\n    \n    // 章节生成\n    currentY += 150;\n    const chapterNode = addNode('chapter-generator', '章节生成');\n    const prevNode = length === '长篇' ? nodeIndex - 2 : outlineNode;\n    connections.push({ source: prevNode, target: chapterNode });\n    \n    // 根据需求添加润色和检查\n    if (features.includes('高质量') || style.includes('文艺')) {\n      currentY += 150;\n      const polishNode = addNode('content-polisher', '内容润色', 100);\n      connections.push({ source: chapterNode, target: polishNode });\n      \n      const checkNode = addNode('consistency-checker', '一致性检查', 300);\n      connections.push({ source: chapterNode, target: checkNode });\n      \n      // 最终输出\n      currentY += 150;\n      const outputNode = addNode('output', '最终输出');\n      connections.push({ source: polishNode, target: outputNode });\n      connections.push({ source: checkNode, target: outputNode });\n    } else {\n      // 简单输出\n      currentY += 150;\n      const outputNode = addNode('output', '最终输出');\n      connections.push({ source: chapterNode, target: outputNode });\n    }\n    \n    return {\n      id: 'custom-' + Date.now(),\n      name: '自定义工作流',\n      description: `为${genre}类型的${length}小说定制的工作流`,\n      nodes,\n      connections,\n      tags: ['自定义', genre, length],\n      complexity: length === '短篇' ? 'simple' : length === '中篇' ? 'medium' : 'complex'\n    };\n  }\n  \n  // 优化现有工作流\n  static optimizeWorkflow(currentNodes: any[], requirements: any): WorkflowTemplate {\n    // 分析现有节点\n    const nodeTypes = currentNodes.map(node => node.data.type);\n    \n    // 检查缺失的关键节点\n    const missingNodes = [];\n    if (!nodeTypes.includes('input')) missingNodes.push('input');\n    if (!nodeTypes.includes('output')) missingNodes.push('output');\n    if (!nodeTypes.includes('character-creator')) missingNodes.push('character-creator');\n    if (!nodeTypes.includes('outline-generator')) missingNodes.push('outline-generator');\n    \n    // 生成优化建议\n    const optimizedNodes = [...currentNodes];\n    const connections: Array<{ source: number; target: number }> = [];\n    \n    // 添加缺失的节点\n    missingNodes.forEach((nodeType, index) => {\n      optimizedNodes.push({\n        type: nodeType,\n        label: this.getNodeLabel(nodeType),\n        position: { x: 200 + index * 150, y: 100 + index * 150 }\n      });\n    });\n    \n    return {\n      id: 'optimized-' + Date.now(),\n      name: '优化工作流',\n      description: '基于现有工作流的优化版本',\n      nodes: optimizedNodes,\n      connections,\n      tags: ['优化', '改进'],\n      complexity: 'medium'\n    };\n  }\n  \n  private static getNodeLabel(nodeType: string): string {\n    const labels: Record<string, string> = {\n      'input': '输入节点',\n      'title-generator': '书名生成',\n      'detail-generator': '详情生成',\n      'character-creator': '角色创建',\n      'worldbuilding': '世界观构建',\n      'plotline-planner': '主线规划',\n      'outline-generator': '大纲生成',\n      'chapter-count-input': '章节数设定',\n      'detailed-outline': '详细大纲',\n      'chapter-generator': '章节生成',\n      'content-polisher': '内容润色',\n      'consistency-checker': '一致性检查',\n      'condition': '条件分支',\n      'loop': '循环执行',\n      'output': '结果输出'\n    };\n    return labels[nodeType] || nodeType;\n  }\n}\n\nexport { WORKFLOW_TEMPLATES };\nexport type { WorkflowTemplate };\n"], "names": [], "mappings": "AAAA,WAAW;;;;;AAsBX,WAAW;AACX,MAAM,qBAAyC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC7D;gBAAE,MAAM;gBAAmB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACvE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAU,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;SAC/D;QACD,aAAa;YACX;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;SACxB;QACD,MAAM;YAAC;YAAM;YAAM;SAAK;QACxB,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAG;YAAE;YAC5D;gBAAE,MAAM;gBAAmB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAI,GAAG;gBAAI;YAAE;YACtE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAiB,OAAO;gBAAS,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACtE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAuB,OAAO;gBAAS,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC5E;gBAAE,MAAM;gBAAU,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;SAC/D;QACD,aAAa;YACX;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACrG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACrG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAC3E;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAG;YACtG;gBAAE,QAAQ;gBAAG,QAAQ;YAAG;YAAG;gBAAE,QAAQ;gBAAI,QAAQ;YAAG;SACrD;QACD,MAAM;YAAC;YAAM;YAAM;SAAM;QACzB,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC7D;gBAAE,MAAM;gBAAiB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACrE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAuB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC3E;gBAAE,MAAM;gBAAU,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAK;YAAE;SAChE;QACD,aAAa;YACX;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;SAClD;QACD,MAAM;YAAC;YAAM;YAAM;SAAK;QACxB,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC7D;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAI,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAU,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAK;YAAE;SAChE;QACD,aAAa;YACX;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;SAClD;QACD,MAAM;YAAC;YAAM;YAAM;SAAK;QACxB,YAAY;IACd;CACD;AAGM,MAAM;IACX,eAAe;IACf,OAAO,oBAAoB,YAM1B,EAAsB;QACrB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,GAAG;QAE5D,IAAI,kBAAwE,EAAE;QAE9E,mBAAmB,OAAO,CAAC,CAAA;YACzB,IAAI,QAAQ;YAEZ,SAAS;YACT,IAAI,OAAO;gBACT,IAAI,MAAM,QAAQ,CAAC,SAAS,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,SAAS;gBACnE,IAAI,MAAM,QAAQ,CAAC,SAAS,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,SAAS;gBACnE,IAAI,MAAM,QAAQ,CAAC,SAAS,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,SAAS;YACrE;YAEA,WAAW;YACX,IAAI,YAAY;gBACd,IAAI,eAAe,QAAQ,SAAS,UAAU,KAAK,UAAU,SAAS;gBACtE,IAAI,eAAe,QAAQ,SAAS,UAAU,KAAK,UAAU,SAAS;gBACtE,IAAI,eAAe,QAAQ,SAAS,UAAU,KAAK,WAAW,SAAS;YACzE;YAEA,SAAS;YACT,IAAI,QAAQ;gBACV,IAAI,WAAW,QAAQ,SAAS,UAAU,KAAK,UAAU,SAAS;gBAClE,IAAI,WAAW,QAAQ,SAAS,UAAU,KAAK,UAAU,SAAS;gBAClE,IAAI,WAAW,QAAQ,SAAS,UAAU,KAAK,WAAW,SAAS;YACrE;YAEA,WAAW;YACX,SAAS,OAAO,CAAC,CAAA;gBACf,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC,WAAW;oBACpD,SAAS;gBACX;YACF;YAEA,gBAAgB,IAAI,CAAC;gBAAE;gBAAU;YAAM;QACzC;QAEA,cAAc;QACd,OAAO,gBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IACxB;IAEA,WAAW;IACX,OAAO,uBAAuB,YAK7B,EAAoB;QACnB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG;QAE3C,OAAO;QACP,MAAM,QAAQ;YACZ;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;SAC9D;QAED,MAAM,cAAyD,EAAE;QACjE,IAAI,WAAW;QACf,IAAI,YAAY;QAEhB,WAAW;QACX,MAAM,UAAU,SAAC,MAAc;gBAAe,qEAAY;YACxD,MAAM,IAAI,CAAC;gBAAE;gBAAM;gBAAO,UAAU;oBAAE;oBAAG,GAAG;gBAAS;YAAE;YACvD,OAAO;QACT;QAEA,aAAa;QACb,MAAM,YAAY,QAAQ,mBAAmB,QAAQ;QACrD,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAG,QAAQ;QAAU;QAEhD,aAAa;QACb,IAAI,MAAM,QAAQ,CAAC,SAAS,SAAS,QAAQ,CAAC,QAAQ;YACpD,MAAM,YAAY,QAAQ,iBAAiB,SAAS;YACpD,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAG,QAAQ;YAAU;QAClD;QAEA,aAAa;QACb,YAAY;QACZ,MAAM,WAAW,QAAQ,qBAAqB;QAC9C,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAG,QAAQ;QAAS;QAE/C,OAAO;QACP,YAAY;QACZ,MAAM,WAAW,QAAQ,oBAAoB;QAC7C,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAW,QAAQ;QAAS;QACvD,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAU,QAAQ;QAAS;QAEtD,OAAO;QACP,YAAY;QACZ,MAAM,cAAc,QAAQ,qBAAqB;QACjD,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAU,QAAQ;QAAY;QAEzD,iBAAiB;QACjB,IAAI,WAAW,MAAM;YACnB,YAAY;YACZ,MAAM,oBAAoB,QAAQ,oBAAoB;YACtD,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAa,QAAQ;YAAkB;QACpE;QAEA,OAAO;QACP,YAAY;QACZ,MAAM,cAAc,QAAQ,qBAAqB;QACjD,MAAM,WAAW,WAAW,OAAO,YAAY,IAAI;QACnD,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAU,QAAQ;QAAY;QAEzD,cAAc;QACd,IAAI,SAAS,QAAQ,CAAC,UAAU,MAAM,QAAQ,CAAC,OAAO;YACpD,YAAY;YACZ,MAAM,aAAa,QAAQ,oBAAoB,QAAQ;YACvD,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAa,QAAQ;YAAW;YAE3D,MAAM,YAAY,QAAQ,uBAAuB,SAAS;YAC1D,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAa,QAAQ;YAAU;YAE1D,OAAO;YACP,YAAY;YACZ,MAAM,aAAa,QAAQ,UAAU;YACrC,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAY,QAAQ;YAAW;YAC1D,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAW,QAAQ;YAAW;QAC3D,OAAO;YACL,OAAO;YACP,YAAY;YACZ,MAAM,aAAa,QAAQ,UAAU;YACrC,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAa,QAAQ;YAAW;QAC7D;QAEA,OAAO;YACL,IAAI,YAAY,KAAK,GAAG;YACxB,MAAM;YACN,aAAa,AAAC,IAAc,OAAX,OAAM,OAAY,OAAP,QAAO;YACnC;YACA;YACA,MAAM;gBAAC;gBAAO;gBAAO;aAAO;YAC5B,YAAY,WAAW,OAAO,WAAW,WAAW,OAAO,WAAW;QACxE;IACF;IAEA,UAAU;IACV,OAAO,iBAAiB,YAAmB,EAAE,YAAiB,EAAoB;QAChF,SAAS;QACT,MAAM,YAAY,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,IAAI;QAEzD,YAAY;QACZ,MAAM,eAAe,EAAE;QACvB,IAAI,CAAC,UAAU,QAAQ,CAAC,UAAU,aAAa,IAAI,CAAC;QACpD,IAAI,CAAC,UAAU,QAAQ,CAAC,WAAW,aAAa,IAAI,CAAC;QACrD,IAAI,CAAC,UAAU,QAAQ,CAAC,sBAAsB,aAAa,IAAI,CAAC;QAChE,IAAI,CAAC,UAAU,QAAQ,CAAC,sBAAsB,aAAa,IAAI,CAAC;QAEhE,SAAS;QACT,MAAM,iBAAiB;eAAI;SAAa;QACxC,MAAM,cAAyD,EAAE;QAEjE,UAAU;QACV,aAAa,OAAO,CAAC,CAAC,UAAU;YAC9B,eAAe,IAAI,CAAC;gBAClB,MAAM;gBACN,OAAO,IAAI,CAAC,YAAY,CAAC;gBACzB,UAAU;oBAAE,GAAG,MAAM,QAAQ;oBAAK,GAAG,MAAM,QAAQ;gBAAI;YACzD;QACF;QAEA,OAAO;YACL,IAAI,eAAe,KAAK,GAAG;YAC3B,MAAM;YACN,aAAa;YACb,OAAO;YACP;YACA,MAAM;gBAAC;gBAAM;aAAK;YAClB,YAAY;QACd;IACF;IAEA,OAAe,aAAa,QAAgB,EAAU;QACpD,MAAM,SAAiC;YACrC,SAAS;YACT,mBAAmB;YACnB,oBAAoB;YACpB,qBAAqB;YACrB,iBAAiB;YACjB,oBAAoB;YACpB,qBAAqB;YACrB,uBAAuB;YACvB,oBAAoB;YACpB,qBAAqB;YACrB,oBAAoB;YACpB,uBAAuB;YACvB,aAAa;YACb,QAAQ;YACR,UAAU;QACZ;QACA,OAAO,MAAM,CAAC,SAAS,IAAI;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 1564, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport WorkflowEditor from '@/components/workflow/WorkflowEditor';\nimport ProjectOverview from '@/components/project/ProjectOverview';\nimport OutlineManager from '@/components/outline/OutlineManager';\nimport CharacterManager from '@/components/character/CharacterManager';\nimport WorldBuildingManager from '@/components/worldbuilding/WorldBuildingManager';\nimport PlotLineManager from '@/components/plotline/PlotLineManager';\nimport TitleManager from '@/components/title/TitleManager';\nimport DocumentManager from '@/components/document/DocumentManager';\nimport PromptManager from '@/components/prompt/PromptManager';\nimport { useAppStore } from '@/store';\n\nexport default function Home() {\n  const { ui } = useAppStore();\n\n  const renderContent = () => {\n    switch (ui.activeTab) {\n      case 'workflow':\n        return <WorkflowEditor />;\n      case 'projects':\n        return <ProjectOverview />;\n      case 'outlines':\n        return <OutlineManager />;\n      case 'characters':\n        return <CharacterManager />;\n      case 'worldbuilding':\n        return <WorldBuildingManager />;\n      case 'plotlines':\n        return <PlotLineManager />;\n      case 'titles':\n        return <TitleManager />;\n      case 'documents':\n        return <DocumentManager />;\n      case 'prompts':\n        return <PromptManager />;\n      default:\n        return <WorkflowEditor />;\n    }\n  };\n\n  return (\n    <MainLayout>\n      {renderContent()}\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;AAee,SAAS;;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEzB,MAAM,gBAAgB;QACpB,OAAQ,GAAG,SAAS;YAClB,KAAK;gBACH,qBAAO,6LAAC,mJAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,mJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,kJAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,sJAAA,CAAA,UAAgB;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,8JAAA,CAAA,UAAoB;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,oJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,UAAY;;;;;YACtB,KAAK;gBACH,qBAAO,6LAAC,oJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,gJAAA,CAAA,UAAa;;;;;YACvB;gBACE,qBAAO,6LAAC,mJAAA,CAAA,UAAc;;;;;QAC1B;IACF;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;kBACR;;;;;;AAGP;GAjCwB;;QACP,wHAAA,CAAA,cAAW;;;KADJ", "debugId": null}}]}