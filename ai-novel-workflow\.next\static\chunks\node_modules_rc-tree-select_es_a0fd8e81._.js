(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/rc-tree-select/es/hooks/useCache.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = function(values) {
    var cacheRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"]({
        valueLabels: new Map()
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "useMemo": function() {
            var valueLabels = cacheRef.current.valueLabels;
            var valueLabelsCache = new Map();
            var filledValues = values.map({
                "useMemo.filledValues": function(item) {
                    var value = item.value, label = item.label;
                    var mergedLabel = label !== null && label !== void 0 ? label : valueLabels.get(value);
                    // Save in cache
                    valueLabelsCache.set(value, mergedLabel);
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, item), {}, {
                        label: mergedLabel
                    });
                }
            }["useMemo.filledValues"]);
            cacheRef.current.valueLabels = valueLabelsCache;
            return [
                filledValues
            ];
        }
    }["useMemo"], [
        values
    ]);
};
}),
"[project]/node_modules/rc-tree-select/es/hooks/useCheckedKeys.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$conductUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/utils/conductUtil.js [app-client] (ecmascript)");
;
;
;
var useCheckedKeys = function useCheckedKeys(rawLabeledValues, rawHalfCheckedValues, treeConduction, keyEntities) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "useCheckedKeys.useMemo": function() {
            var extractValues = function extractValues(values) {
                return values.map({
                    "useCheckedKeys.useMemo.extractValues": function(_ref) {
                        var value = _ref.value;
                        return value;
                    }
                }["useCheckedKeys.useMemo.extractValues"]);
            };
            var checkedKeys = extractValues(rawLabeledValues);
            var halfCheckedKeys = extractValues(rawHalfCheckedValues);
            var missingValues = checkedKeys.filter({
                "useCheckedKeys.useMemo.missingValues": function(key) {
                    return !keyEntities[key];
                }
            }["useCheckedKeys.useMemo.missingValues"]);
            var finalCheckedKeys = checkedKeys;
            var finalHalfCheckedKeys = halfCheckedKeys;
            if (treeConduction) {
                var conductResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$conductUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["conductCheck"])(checkedKeys, true, keyEntities);
                finalCheckedKeys = conductResult.checkedKeys;
                finalHalfCheckedKeys = conductResult.halfCheckedKeys;
            }
            return [
                Array.from(new Set([].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(missingValues), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(finalCheckedKeys)))),
                finalHalfCheckedKeys
            ];
        }
    }["useCheckedKeys.useMemo"], [
        rawLabeledValues,
        rawHalfCheckedValues,
        treeConduction,
        keyEntities
    ]);
};
const __TURBOPACK__default__export__ = useCheckedKeys;
}),
"[project]/node_modules/rc-tree-select/es/utils/valueUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "fillFieldNames": ()=>fillFieldNames,
    "getAllKeys": ()=>getAllKeys,
    "isCheckDisabled": ()=>isCheckDisabled,
    "isNil": ()=>isNil,
    "toArray": ()=>toArray
});
var toArray = function toArray(value) {
    return Array.isArray(value) ? value : value !== undefined ? [
        value
    ] : [];
};
var fillFieldNames = function fillFieldNames(fieldNames) {
    var _ref = fieldNames || {}, label = _ref.label, value = _ref.value, children = _ref.children;
    return {
        _title: label ? [
            label
        ] : [
            'title',
            'label'
        ],
        value: value || 'value',
        key: value || 'value',
        children: children || 'children'
    };
};
var isCheckDisabled = function isCheckDisabled(node) {
    return !node || node.disabled || node.disableCheckbox || node.checkable === false;
};
var getAllKeys = function getAllKeys(treeData, fieldNames) {
    var keys = [];
    var dig = function dig(list) {
        list.forEach(function(item) {
            var children = item[fieldNames.children];
            if (children) {
                keys.push(item[fieldNames.value]);
                dig(children);
            }
        });
    };
    dig(treeData);
    return keys;
};
var isNil = function isNil(val) {
    return val === null || val === undefined;
};
}),
"[project]/node_modules/rc-tree-select/es/hooks/useDataEntities.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$treeUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/utils/treeUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/valueUtil.js [app-client] (ecmascript)");
;
;
;
;
;
const __TURBOPACK__default__export__ = function(treeData, fieldNames) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "useMemo": function() {
            var collection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$treeUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertDataToEntities"])(treeData, {
                fieldNames: fieldNames,
                initWrapper: function initWrapper(wrapper) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, wrapper), {}, {
                        valueEntities: new Map()
                    });
                },
                processEntity: function processEntity(entity, wrapper) {
                    var val = entity.node[fieldNames.value];
                    // Check if exist same value
                    if ("TURBOPACK compile-time truthy", 1) {
                        var key = entity.node.key;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(val), 'TreeNode `value` is invalidate: undefined');
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!wrapper.valueEntities.has(val), "Same `value` exist in the tree: ".concat(val));
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!key || String(key) === String(val), "`key` or `value` with TreeNode must be the same or you can remove one of them. key: ".concat(key, ", value: ").concat(val, "."));
                    }
                    wrapper.valueEntities.set(val, entity);
                }
            });
            return collection;
        }
    }["useMemo"], [
        treeData,
        fieldNames
    ]);
};
}),
"[project]/node_modules/rc-tree-select/es/TreeNode.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/* istanbul ignore file */ /** This is a placeholder, not real render in dom */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var TreeNode = function TreeNode() {
    return null;
};
const __TURBOPACK__default__export__ = TreeNode;
}),
"[project]/node_modules/rc-tree-select/es/utils/legacyUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "convertChildrenToData": ()=>convertChildrenToData,
    "fillAdditionalInfo": ()=>fillAdditionalInfo,
    "fillLegacyProps": ()=>fillLegacyProps
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Children$2f$toArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Children/toArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/TreeNode.js [app-client] (ecmascript)");
;
;
var _excluded = [
    "children",
    "value"
];
;
;
;
;
function convertChildrenToData(nodes) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Children$2f$toArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(nodes).map(function(node) {
        if (!/*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"](node) || !node.type) {
            return null;
        }
        var _ref = node, key = _ref.key, _ref$props = _ref.props, children = _ref$props.children, value = _ref$props.value, restProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref$props, _excluded);
        var data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            key: key,
            value: value
        }, restProps);
        var childData = convertChildrenToData(children);
        if (childData.length) {
            data.children = childData;
        }
        return data;
    }).filter(function(data) {
        return data;
    });
}
function fillLegacyProps(dataNode) {
    if (!dataNode) {
        return dataNode;
    }
    var cloneNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, dataNode);
    if (!('props' in cloneNode)) {
        Object.defineProperty(cloneNode, 'props', {
            get: function get() {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, 'New `rc-tree-select` not support return node instance as argument anymore. Please consider to remove `props` access.');
                return cloneNode;
            }
        });
    }
    return cloneNode;
}
function fillAdditionalInfo(extra, triggerValue, checkedValues, treeData, showPosition, fieldNames) {
    var triggerNode = null;
    var nodeList = null;
    function generateMap() {
        function dig(list) {
            var level = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '0';
            var parentIncluded = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
            return list.map(function(option, index) {
                var pos = "".concat(level, "-").concat(index);
                var value = option[fieldNames.value];
                var included = checkedValues.includes(value);
                var children = dig(option[fieldNames.children] || [], pos, included);
                var node = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], option, children.map(function(child) {
                    return child.node;
                }));
                // Link with trigger node
                if (triggerValue === value) {
                    triggerNode = node;
                }
                if (included) {
                    var checkedNode = {
                        pos: pos,
                        node: node,
                        children: children
                    };
                    if (!parentIncluded) {
                        nodeList.push(checkedNode);
                    }
                    return checkedNode;
                }
                return null;
            }).filter(function(node) {
                return node;
            });
        }
        if (!nodeList) {
            nodeList = [];
            dig(treeData);
            // Sort to keep the checked node length
            nodeList.sort(function(_ref2, _ref3) {
                var val1 = _ref2.node.props.value;
                var val2 = _ref3.node.props.value;
                var index1 = checkedValues.indexOf(val1);
                var index2 = checkedValues.indexOf(val2);
                return index1 - index2;
            });
        }
    }
    Object.defineProperty(extra, 'triggerNode', {
        get: function get() {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '`triggerNode` is deprecated. Please consider decoupling data with node.');
            generateMap();
            return triggerNode;
        }
    });
    Object.defineProperty(extra, 'allCheckedNodes', {
        get: function get() {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '`allCheckedNodes` is deprecated. Please consider decoupling data with node.');
            generateMap();
            if (showPosition) {
                return nodeList;
            }
            return nodeList.map(function(_ref4) {
                var node = _ref4.node;
                return node;
            });
        }
    });
}
}),
"[project]/node_modules/rc-tree-select/es/hooks/useFilterTreeData.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/legacyUtil.js [app-client] (ecmascript)");
;
;
;
;
var useFilterTreeData = function useFilterTreeData(treeData, searchValue, options) {
    var fieldNames = options.fieldNames, treeNodeFilterProp = options.treeNodeFilterProp, filterTreeNode = options.filterTreeNode;
    var fieldChildren = fieldNames.children;
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "useFilterTreeData.useMemo": function() {
            if (!searchValue || filterTreeNode === false) {
                return treeData;
            }
            var filterOptionFunc = typeof filterTreeNode === 'function' ? filterTreeNode : ({
                "useFilterTreeData.useMemo": function(_, dataNode) {
                    return String(dataNode[treeNodeFilterProp]).toUpperCase().includes(searchValue.toUpperCase());
                }
            })["useFilterTreeData.useMemo"];
            var filterTreeNodes = function filterTreeNodes(nodes) {
                var keepAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
                return nodes.reduce({
                    "useFilterTreeData.useMemo.filterTreeNodes": function(filtered, node) {
                        var children = node[fieldChildren];
                        var isMatch = keepAll || filterOptionFunc(searchValue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fillLegacyProps"])(node));
                        var filteredChildren = filterTreeNodes(children || [], isMatch);
                        if (isMatch || filteredChildren.length) {
                            filtered.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, node), {}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                                isLeaf: undefined
                            }, fieldChildren, filteredChildren)));
                        }
                        return filtered;
                    }
                }["useFilterTreeData.useMemo.filterTreeNodes"], []);
            };
            return filterTreeNodes(treeData);
        }
    }["useFilterTreeData.useMemo"], [
        treeData,
        searchValue,
        fieldChildren,
        treeNodeFilterProp,
        filterTreeNode
    ]);
};
const __TURBOPACK__default__export__ = useFilterTreeData;
}),
"[project]/node_modules/rc-tree-select/es/hooks/useRefFunc.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>useRefFunc
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function useRefFunc(callback) {
    var funcRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"]();
    funcRef.current = callback;
    var cacheFn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "useRefFunc.useCallback[cacheFn]": function() {
            return funcRef.current.apply(funcRef, arguments);
        }
    }["useRefFunc.useCallback[cacheFn]"], []);
    return cacheFn;
}
}),
"[project]/node_modules/rc-tree-select/es/hooks/useTreeData.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>useTreeData
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/legacyUtil.js [app-client] (ecmascript)");
;
;
;
;
function buildTreeStructure(nodes, config) {
    var id = config.id, pId = config.pId, rootPId = config.rootPId;
    var nodeMap = new Map();
    var rootNodes = [];
    nodes.forEach(function(node) {
        var nodeKey = node[id];
        var clonedNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, node), {}, {
            key: node.key || nodeKey
        });
        nodeMap.set(nodeKey, clonedNode);
    });
    nodeMap.forEach(function(node) {
        var parentKey = node[pId];
        var parent = nodeMap.get(parentKey);
        if (parent) {
            parent.children = parent.children || [];
            parent.children.push(node);
        } else if (parentKey === rootPId || rootPId === null) {
            rootNodes.push(node);
        }
    });
    return rootNodes;
}
function useTreeData(treeData, children, simpleMode) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "useTreeData.useMemo": function() {
            if (treeData) {
                if (simpleMode) {
                    var config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                        id: 'id',
                        pId: 'pId',
                        rootPId: null
                    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(simpleMode) === 'object' ? simpleMode : {});
                    return buildTreeStructure(treeData, config);
                }
                return treeData;
            }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertChildrenToData"])(children);
        }
    }["useTreeData.useMemo"], [
        children,
        simpleMode,
        treeData
    ]);
}
}),
"[project]/node_modules/rc-tree-select/es/LegacyContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var LegacySelectContext = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"](null);
const __TURBOPACK__default__export__ = LegacySelectContext;
}),
"[project]/node_modules/rc-tree-select/es/TreeSelectContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var TreeSelectContext = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"](null);
const __TURBOPACK__default__export__ = TreeSelectContext;
}),
"[project]/node_modules/rc-tree-select/es/OptionList.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createForOfIteratorHelper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-select/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$hooks$2f$useBaseProps$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useBaseProps$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-select/es/hooks/useBaseProps.js [app-client] (ecmascript) <export default as useBaseProps>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$contextTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/contextTypes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$KeyCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/KeyCode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMemo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useMemo.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$LegacyContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/LegacyContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeSelectContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/TreeSelectContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/valueUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-client] (ecmascript) <export default as useEvent>");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
var HIDDEN_STYLE = {
    width: 0,
    height: 0,
    display: 'flex',
    overflow: 'hidden',
    opacity: 0,
    border: 0,
    padding: 0,
    margin: 0
};
var OptionList = function OptionList(_, ref) {
    var _useBaseProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$hooks$2f$useBaseProps$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useBaseProps$3e$__["useBaseProps"])(), prefixCls = _useBaseProps.prefixCls, multiple = _useBaseProps.multiple, searchValue = _useBaseProps.searchValue, toggleOpen = _useBaseProps.toggleOpen, open = _useBaseProps.open, notFoundContent = _useBaseProps.notFoundContent;
    var _React$useContext = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeSelectContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]), virtual = _React$useContext.virtual, listHeight = _React$useContext.listHeight, listItemHeight = _React$useContext.listItemHeight, listItemScrollOffset = _React$useContext.listItemScrollOffset, treeData = _React$useContext.treeData, fieldNames = _React$useContext.fieldNames, onSelect = _React$useContext.onSelect, dropdownMatchSelectWidth = _React$useContext.dropdownMatchSelectWidth, treeExpandAction = _React$useContext.treeExpandAction, treeTitleRender = _React$useContext.treeTitleRender, onPopupScroll = _React$useContext.onPopupScroll, leftMaxCount = _React$useContext.leftMaxCount, leafCountOnly = _React$useContext.leafCountOnly, valueEntities = _React$useContext.valueEntities;
    var _React$useContext2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$LegacyContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]), checkable = _React$useContext2.checkable, checkedKeys = _React$useContext2.checkedKeys, halfCheckedKeys = _React$useContext2.halfCheckedKeys, treeExpandedKeys = _React$useContext2.treeExpandedKeys, treeDefaultExpandAll = _React$useContext2.treeDefaultExpandAll, treeDefaultExpandedKeys = _React$useContext2.treeDefaultExpandedKeys, onTreeExpand = _React$useContext2.onTreeExpand, treeIcon = _React$useContext2.treeIcon, showTreeIcon = _React$useContext2.showTreeIcon, switcherIcon = _React$useContext2.switcherIcon, treeLine = _React$useContext2.treeLine, treeNodeFilterProp = _React$useContext2.treeNodeFilterProp, loadData = _React$useContext2.loadData, treeLoadedKeys = _React$useContext2.treeLoadedKeys, treeMotion = _React$useContext2.treeMotion, onTreeLoad = _React$useContext2.onTreeLoad, keyEntities = _React$useContext2.keyEntities;
    var treeRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"]();
    var memoTreeData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMemo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "OptionList.useMemo[memoTreeData]": function() {
            return treeData;
        }
    }["OptionList.useMemo[memoTreeData]"], // eslint-disable-next-line react-hooks/exhaustive-deps
    [
        open,
        treeData
    ], {
        "OptionList.useMemo[memoTreeData]": function(prev, next) {
            return next[0] && prev[1] !== next[1];
        }
    }["OptionList.useMemo[memoTreeData]"]);
    // ========================== Values ==========================
    var mergedCheckedKeys = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "OptionList.useMemo[mergedCheckedKeys]": function() {
            if (!checkable) {
                return null;
            }
            return {
                checked: checkedKeys,
                halfChecked: halfCheckedKeys
            };
        }
    }["OptionList.useMemo[mergedCheckedKeys]"], [
        checkable,
        checkedKeys,
        halfCheckedKeys
    ]);
    // ========================== Scroll ==========================
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "OptionList.useEffect": function() {
            // Single mode should scroll to current key
            if (open && !multiple && checkedKeys.length) {
                var _treeRef$current;
                (_treeRef$current = treeRef.current) === null || _treeRef$current === void 0 || _treeRef$current.scrollTo({
                    key: checkedKeys[0]
                });
            }
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["OptionList.useEffect"], [
        open
    ]);
    // ========================== Events ==========================
    var onListMouseDown = function onListMouseDown(event) {
        event.preventDefault();
    };
    var onInternalSelect = function onInternalSelect(__, info) {
        var node = info.node;
        if (checkable && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isCheckDisabled"])(node)) {
            return;
        }
        onSelect(node.key, {
            selected: !checkedKeys.includes(node.key)
        });
        if (!multiple) {
            toggleOpen(false);
        }
    };
    // =========================== Keys ===========================
    var _React$useState = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](treeDefaultExpandedKeys), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), expandedKeys = _React$useState2[0], setExpandedKeys = _React$useState2[1];
    var _React$useState3 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](null), _React$useState4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState3, 2), searchExpandedKeys = _React$useState4[0], setSearchExpandedKeys = _React$useState4[1];
    var mergedExpandedKeys = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "OptionList.useMemo[mergedExpandedKeys]": function() {
            if (treeExpandedKeys) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(treeExpandedKeys);
            }
            return searchValue ? searchExpandedKeys : expandedKeys;
        }
    }["OptionList.useMemo[mergedExpandedKeys]"], [
        expandedKeys,
        searchExpandedKeys,
        treeExpandedKeys,
        searchValue
    ]);
    var onInternalExpand = function onInternalExpand(keys) {
        setExpandedKeys(keys);
        setSearchExpandedKeys(keys);
        if (onTreeExpand) {
            onTreeExpand(keys);
        }
    };
    // ========================== Search ==========================
    var lowerSearchValue = String(searchValue).toLowerCase();
    var filterTreeNode = function filterTreeNode(treeNode) {
        if (!lowerSearchValue) {
            return false;
        }
        return String(treeNode[treeNodeFilterProp]).toLowerCase().includes(lowerSearchValue);
    };
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "OptionList.useEffect": function() {
            if (searchValue) {
                setSearchExpandedKeys((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAllKeys"])(treeData, fieldNames));
            }
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["OptionList.useEffect"], [
        searchValue
    ]);
    // ========================= Disabled =========================
    // Cache disabled states in React state to ensure re-render when cache updates
    var _React$useState5 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"]({
        "OptionList.useState[_React$useState5]": function() {
            return new Map();
        }
    }["OptionList.useState[_React$useState5]"]), _React$useState6 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState5, 2), disabledCache = _React$useState6[0], setDisabledCache = _React$useState6[1];
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "OptionList.useEffect": function() {
            if (leftMaxCount) {
                setDisabledCache(new Map());
            }
        }
    }["OptionList.useEffect"], [
        leftMaxCount
    ]);
    function getDisabledWithCache(node) {
        var value = node[fieldNames.value];
        if (!disabledCache.has(value)) {
            var entity = valueEntities.get(value);
            var isLeaf = (entity.children || []).length === 0;
            if (!isLeaf) {
                var checkableChildren = entity.children.filter(function(childTreeNode) {
                    return !childTreeNode.node.disabled && !childTreeNode.node.disableCheckbox && !checkedKeys.includes(childTreeNode.node[fieldNames.value]);
                });
                var checkableChildrenCount = checkableChildren.length;
                disabledCache.set(value, checkableChildrenCount > leftMaxCount);
            } else {
                disabledCache.set(value, false);
            }
        }
        return disabledCache.get(value);
    }
    var nodeDisabled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useEvent$3e$__["useEvent"])({
        "OptionList.useEvent[nodeDisabled]": function(node) {
            var nodeValue = node[fieldNames.value];
            if (checkedKeys.includes(nodeValue)) {
                return false;
            }
            if (leftMaxCount === null) {
                return false;
            }
            if (leftMaxCount <= 0) {
                return true;
            }
            // This is a low performance calculation
            if (leafCountOnly && leftMaxCount) {
                return getDisabledWithCache(node);
            }
            return false;
        }
    }["OptionList.useEvent[nodeDisabled]"]);
    // ========================== Get First Selectable Node ==========================
    var getFirstMatchingNode = function getFirstMatchingNode(nodes) {
        var _iterator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createForOfIteratorHelper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(nodes), _step;
        try {
            for(_iterator.s(); !(_step = _iterator.n()).done;){
                var node = _step.value;
                if (node.disabled || node.selectable === false) {
                    continue;
                }
                if (searchValue) {
                    if (filterTreeNode(node)) {
                        return node;
                    }
                } else {
                    return node;
                }
                if (node[fieldNames.children]) {
                    var matchInChildren = getFirstMatchingNode(node[fieldNames.children]);
                    if (matchInChildren) {
                        return matchInChildren;
                    }
                }
            }
        } catch (err) {
            _iterator.e(err);
        } finally{
            _iterator.f();
        }
        return null;
    };
    // ========================== Active ==========================
    var _React$useState7 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"](null), _React$useState8 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useState7, 2), activeKey = _React$useState8[0], setActiveKey = _React$useState8[1];
    var activeEntity = keyEntities[activeKey];
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"]({
        "OptionList.useEffect": function() {
            if (!open) {
                return;
            }
            var nextActiveKey = null;
            var getFirstNode = function getFirstNode() {
                var firstNode = getFirstMatchingNode(memoTreeData);
                return firstNode ? firstNode[fieldNames.value] : null;
            };
            // single mode active first checked node
            if (!multiple && checkedKeys.length && !searchValue) {
                nextActiveKey = checkedKeys[0];
            } else {
                nextActiveKey = getFirstNode();
            }
            setActiveKey(nextActiveKey);
        }
    }["OptionList.useEffect"], [
        open,
        searchValue
    ]);
    // ========================= Keyboard =========================
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"](ref, {
        "OptionList.useImperativeHandle": function() {
            var _treeRef$current2;
            return {
                scrollTo: (_treeRef$current2 = treeRef.current) === null || _treeRef$current2 === void 0 ? void 0 : _treeRef$current2.scrollTo,
                onKeyDown: function onKeyDown(event) {
                    var _treeRef$current3;
                    var which = event.which;
                    switch(which){
                        // >>> Arrow keys
                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$KeyCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].UP:
                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$KeyCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].DOWN:
                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$KeyCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].LEFT:
                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$KeyCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].RIGHT:
                            (_treeRef$current3 = treeRef.current) === null || _treeRef$current3 === void 0 || _treeRef$current3.onKeyDown(event);
                            break;
                        // >>> Select item
                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$KeyCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ENTER:
                            {
                                if (activeEntity) {
                                    var isNodeDisabled = nodeDisabled(activeEntity.node);
                                    var _ref = (activeEntity === null || activeEntity === void 0 ? void 0 : activeEntity.node) || {}, selectable = _ref.selectable, value = _ref.value, disabled = _ref.disabled;
                                    if (selectable !== false && !disabled && !isNodeDisabled) {
                                        onInternalSelect(null, {
                                            node: {
                                                key: activeKey
                                            },
                                            selected: !checkedKeys.includes(value)
                                        });
                                    }
                                }
                                break;
                            }
                        // >>> Close
                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$KeyCode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].ESC:
                            {
                                toggleOpen(false);
                            }
                    }
                },
                onKeyUp: function onKeyUp() {}
            };
        }
    }["OptionList.useImperativeHandle"]);
    var hasLoadDataFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMemo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "OptionList.useMemo[hasLoadDataFn]": function() {
            return searchValue ? false : true;
        }
    }["OptionList.useMemo[hasLoadDataFn]"], [
        searchValue,
        treeExpandedKeys || expandedKeys
    ], {
        "OptionList.useMemo[hasLoadDataFn]": function(_ref2, _ref3) {
            var _ref4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref2, 1), preSearchValue = _ref4[0];
            var _ref5 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_ref3, 2), nextSearchValue = _ref5[0], nextExcludeSearchExpandedKeys = _ref5[1];
            return preSearchValue !== nextSearchValue && !!(nextSearchValue || nextExcludeSearchExpandedKeys);
        }
    }["OptionList.useMemo[hasLoadDataFn]"]);
    var syncLoadData = hasLoadDataFn ? loadData : null;
    // ========================== Render ==========================
    if (memoTreeData.length === 0) {
        return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("div", {
            role: "listbox",
            className: "".concat(prefixCls, "-empty"),
            onMouseDown: onListMouseDown
        }, notFoundContent);
    }
    var treeProps = {
        fieldNames: fieldNames
    };
    if (treeLoadedKeys) {
        treeProps.loadedKeys = treeLoadedKeys;
    }
    if (mergedExpandedKeys) {
        treeProps.expandedKeys = mergedExpandedKeys;
    }
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("div", {
        onMouseDown: onListMouseDown
    }, activeEntity && open && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("span", {
        style: HIDDEN_STYLE,
        "aria-live": "assertive"
    }, activeEntity.node.value), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$contextTypes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UnstableContext"].Provider, {
        value: {
            nodeDisabled: nodeDisabled
        }
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        ref: treeRef,
        focusable: false,
        prefixCls: "".concat(prefixCls, "-tree"),
        treeData: memoTreeData,
        height: listHeight,
        itemHeight: listItemHeight,
        itemScrollOffset: listItemScrollOffset,
        virtual: virtual !== false && dropdownMatchSelectWidth !== false,
        multiple: multiple,
        icon: treeIcon,
        showIcon: showTreeIcon,
        switcherIcon: switcherIcon,
        showLine: treeLine,
        loadData: syncLoadData,
        motion: treeMotion,
        activeKey: activeKey,
        checkable: checkable,
        checkStrictly: true,
        checkedKeys: mergedCheckedKeys,
        selectedKeys: !checkable ? checkedKeys : [],
        defaultExpandAll: treeDefaultExpandAll,
        titleRender: treeTitleRender
    }, treeProps, {
        // Proxy event out
        onActiveChange: setActiveKey,
        onSelect: onInternalSelect,
        onCheck: onInternalSelect,
        onExpand: onInternalExpand,
        onLoad: onTreeLoad,
        filterTreeNode: filterTreeNode,
        expandAction: treeExpandAction,
        onScroll: onPopupScroll
    }))));
};
var RefOptionList = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"](OptionList);
if ("TURBOPACK compile-time truthy", 1) {
    RefOptionList.displayName = 'OptionList';
}
const __TURBOPACK__default__export__ = RefOptionList;
}),
"[project]/node_modules/rc-tree-select/es/utils/strategyUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "SHOW_ALL": ()=>SHOW_ALL,
    "SHOW_CHILD": ()=>SHOW_CHILD,
    "SHOW_PARENT": ()=>SHOW_PARENT,
    "formatStrategyValues": ()=>formatStrategyValues
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/valueUtil.js [app-client] (ecmascript)");
;
var SHOW_ALL = 'SHOW_ALL';
var SHOW_PARENT = 'SHOW_PARENT';
var SHOW_CHILD = 'SHOW_CHILD';
function formatStrategyValues(values, strategy, keyEntities, fieldNames) {
    var valueSet = new Set(values);
    if (strategy === SHOW_CHILD) {
        return values.filter(function(key) {
            var entity = keyEntities[key];
            return !entity || !entity.children || !entity.children.some(function(_ref) {
                var node = _ref.node;
                return valueSet.has(node[fieldNames.value]);
            }) || !entity.children.every(function(_ref2) {
                var node = _ref2.node;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isCheckDisabled"])(node) || valueSet.has(node[fieldNames.value]);
            });
        });
    }
    if (strategy === SHOW_PARENT) {
        return values.filter(function(key) {
            var entity = keyEntities[key];
            var parent = entity ? entity.parent : null;
            return !parent || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isCheckDisabled"])(parent.node) || !valueSet.has(parent.key);
        });
    }
    return values;
}
}),
"[project]/node_modules/rc-tree-select/es/utils/warningPropsUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/valueUtil.js [app-client] (ecmascript)");
;
;
;
function warningProps(props) {
    var searchPlaceholder = props.searchPlaceholder, treeCheckStrictly = props.treeCheckStrictly, treeCheckable = props.treeCheckable, labelInValue = props.labelInValue, value = props.value, multiple = props.multiple, showCheckedStrategy = props.showCheckedStrategy, maxCount = props.maxCount;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!searchPlaceholder, '`searchPlaceholder` has been removed.');
    if (treeCheckStrictly && labelInValue === false) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '`treeCheckStrictly` will force set `labelInValue` to `true`.');
    }
    if (labelInValue || treeCheckStrictly) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toArray"])(value).every(function(val) {
            return val && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(val) === 'object' && 'value' in val;
        }), 'Invalid prop `value` supplied to `TreeSelect`. You should use { label: string, value: string | number } or [{ label: string, value: string | number }] instead.');
    }
    if (treeCheckStrictly || multiple || treeCheckable) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!value || Array.isArray(value), '`value` should be an array when `TreeSelect` is checkable or multiple.');
    } else {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(!Array.isArray(value), '`value` should not be array when `TreeSelect` is single mode.');
    }
    if (maxCount && (showCheckedStrategy === 'SHOW_ALL' && !treeCheckStrictly || showCheckedStrategy === 'SHOW_PARENT')) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, '`maxCount` not work with `showCheckedStrategy=SHOW_ALL` (when `treeCheckStrictly=false`) or `showCheckedStrategy=SHOW_PARENT`.');
    }
}
const __TURBOPACK__default__export__ = warningProps;
}),
"[project]/node_modules/rc-tree-select/es/TreeSelect.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-select/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$BaseSelect$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BaseSelect$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-select/es/BaseSelect/index.js [app-client] (ecmascript) <export default as BaseSelect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$hooks$2f$useId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-select/es/hooks/useId.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$conductUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/utils/conductUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useMergedState.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useCache$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/hooks/useCache.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useCheckedKeys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/hooks/useCheckedKeys.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useDataEntities$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/hooks/useDataEntities.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useFilterTreeData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/hooks/useFilterTreeData.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useRefFunc$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/hooks/useRefFunc.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useTreeData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/hooks/useTreeData.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$LegacyContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/LegacyContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$OptionList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/OptionList.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/TreeNode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeSelectContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/TreeSelectContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/legacyUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/strategyUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/valueUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$warningPropsUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/warningPropsUtil.js [app-client] (ecmascript)");
;
;
;
;
;
;
var _excluded = [
    "id",
    "prefixCls",
    "value",
    "defaultValue",
    "onChange",
    "onSelect",
    "onDeselect",
    "searchValue",
    "inputValue",
    "onSearch",
    "autoClearSearchValue",
    "filterTreeNode",
    "treeNodeFilterProp",
    "showCheckedStrategy",
    "treeNodeLabelProp",
    "multiple",
    "treeCheckable",
    "treeCheckStrictly",
    "labelInValue",
    "maxCount",
    "fieldNames",
    "treeDataSimpleMode",
    "treeData",
    "children",
    "loadData",
    "treeLoadedKeys",
    "onTreeLoad",
    "treeDefaultExpandAll",
    "treeExpandedKeys",
    "treeDefaultExpandedKeys",
    "onTreeExpand",
    "treeExpandAction",
    "virtual",
    "listHeight",
    "listItemHeight",
    "listItemScrollOffset",
    "onDropdownVisibleChange",
    "dropdownMatchSelectWidth",
    "treeLine",
    "treeIcon",
    "showTreeIcon",
    "switcherIcon",
    "treeMotion",
    "treeTitleRender",
    "onPopupScroll"
];
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function isRawValue(value) {
    return !value || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(value) !== 'object';
}
var TreeSelect = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"](function(props, ref) {
    var id = props.id, _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? 'rc-tree-select' : _props$prefixCls, value = props.value, defaultValue = props.defaultValue, onChange = props.onChange, onSelect = props.onSelect, onDeselect = props.onDeselect, searchValue = props.searchValue, inputValue = props.inputValue, onSearch = props.onSearch, _props$autoClearSearc = props.autoClearSearchValue, autoClearSearchValue = _props$autoClearSearc === void 0 ? true : _props$autoClearSearc, filterTreeNode = props.filterTreeNode, _props$treeNodeFilter = props.treeNodeFilterProp, treeNodeFilterProp = _props$treeNodeFilter === void 0 ? 'value' : _props$treeNodeFilter, showCheckedStrategy = props.showCheckedStrategy, treeNodeLabelProp = props.treeNodeLabelProp, multiple = props.multiple, treeCheckable = props.treeCheckable, treeCheckStrictly = props.treeCheckStrictly, labelInValue = props.labelInValue, maxCount = props.maxCount, fieldNames = props.fieldNames, treeDataSimpleMode = props.treeDataSimpleMode, treeData = props.treeData, children = props.children, loadData = props.loadData, treeLoadedKeys = props.treeLoadedKeys, onTreeLoad = props.onTreeLoad, treeDefaultExpandAll = props.treeDefaultExpandAll, treeExpandedKeys = props.treeExpandedKeys, treeDefaultExpandedKeys = props.treeDefaultExpandedKeys, onTreeExpand = props.onTreeExpand, treeExpandAction = props.treeExpandAction, virtual = props.virtual, _props$listHeight = props.listHeight, listHeight = _props$listHeight === void 0 ? 200 : _props$listHeight, _props$listItemHeight = props.listItemHeight, listItemHeight = _props$listItemHeight === void 0 ? 20 : _props$listItemHeight, _props$listItemScroll = props.listItemScrollOffset, listItemScrollOffset = _props$listItemScroll === void 0 ? 0 : _props$listItemScroll, onDropdownVisibleChange = props.onDropdownVisibleChange, _props$dropdownMatchS = props.dropdownMatchSelectWidth, dropdownMatchSelectWidth = _props$dropdownMatchS === void 0 ? true : _props$dropdownMatchS, treeLine = props.treeLine, treeIcon = props.treeIcon, showTreeIcon = props.showTreeIcon, switcherIcon = props.switcherIcon, treeMotion = props.treeMotion, treeTitleRender = props.treeTitleRender, onPopupScroll = props.onPopupScroll, restProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, _excluded);
    var mergedId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$hooks$2f$useId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(id);
    var treeConduction = treeCheckable && !treeCheckStrictly;
    var mergedCheckable = treeCheckable || treeCheckStrictly;
    var mergedLabelInValue = treeCheckStrictly || labelInValue;
    var mergedMultiple = mergedCheckable || multiple;
    var _useMergedState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(defaultValue, {
        value: value
    }), _useMergedState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useMergedState, 2), internalValue = _useMergedState2[0], setInternalValue = _useMergedState2[1];
    // `multiple` && `!treeCheckable` should be show all
    var mergedShowCheckedStrategy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "TreeSelect.useMemo[mergedShowCheckedStrategy]": function() {
            if (!treeCheckable) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SHOW_ALL"];
            }
            return showCheckedStrategy || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SHOW_CHILD"];
        }
    }["TreeSelect.useMemo[mergedShowCheckedStrategy]"], [
        showCheckedStrategy,
        treeCheckable
    ]);
    // ========================== Warning ===========================
    if ("TURBOPACK compile-time truthy", 1) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$warningPropsUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    // ========================= FieldNames =========================
    var mergedFieldNames = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "TreeSelect.useMemo[mergedFieldNames]": function() {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fillFieldNames"])(fieldNames);
        }
    }["TreeSelect.useMemo[mergedFieldNames]"], /* eslint-disable react-hooks/exhaustive-deps */ [
        JSON.stringify(fieldNames)
    ]);
    // =========================== Search ===========================
    var _useMergedState3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])('', {
        value: searchValue !== undefined ? searchValue : inputValue,
        postState: function postState(search) {
            return search || '';
        }
    }), _useMergedState4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useMergedState3, 2), mergedSearchValue = _useMergedState4[0], setSearchValue = _useMergedState4[1];
    var onInternalSearch = function onInternalSearch(searchText) {
        setSearchValue(searchText);
        onSearch === null || onSearch === void 0 || onSearch(searchText);
    };
    // ============================ Data ============================
    // `useTreeData` only do convert of `children` or `simpleMode`.
    // Else will return origin `treeData` for perf consideration.
    // Do not do anything to loop the data.
    var mergedTreeData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useTreeData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(treeData, children, treeDataSimpleMode);
    var _useDataEntities = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useDataEntities$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(mergedTreeData, mergedFieldNames), keyEntities = _useDataEntities.keyEntities, valueEntities = _useDataEntities.valueEntities;
    /** Get `missingRawValues` which not exist in the tree yet */ var splitRawValues = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "TreeSelect.useCallback[splitRawValues]": function(newRawValues) {
            var missingRawValues = [];
            var existRawValues = [];
            // Keep missing value in the cache
            newRawValues.forEach({
                "TreeSelect.useCallback[splitRawValues]": function(val) {
                    if (valueEntities.has(val)) {
                        existRawValues.push(val);
                    } else {
                        missingRawValues.push(val);
                    }
                }
            }["TreeSelect.useCallback[splitRawValues]"]);
            return {
                missingRawValues: missingRawValues,
                existRawValues: existRawValues
            };
        }
    }["TreeSelect.useCallback[splitRawValues]"], [
        valueEntities
    ]);
    // Filtered Tree
    var filteredTreeData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useFilterTreeData$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(mergedTreeData, mergedSearchValue, {
        fieldNames: mergedFieldNames,
        treeNodeFilterProp: treeNodeFilterProp,
        filterTreeNode: filterTreeNode
    });
    // =========================== Label ============================
    var getLabel = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "TreeSelect.useCallback[getLabel]": function(item) {
            if (item) {
                if (treeNodeLabelProp) {
                    return item[treeNodeLabelProp];
                }
                // Loop from fieldNames
                var titleList = mergedFieldNames._title;
                for(var i = 0; i < titleList.length; i += 1){
                    var title = item[titleList[i]];
                    if (title !== undefined) {
                        return title;
                    }
                }
            }
        }
    }["TreeSelect.useCallback[getLabel]"], [
        mergedFieldNames,
        treeNodeLabelProp
    ]);
    // ========================= Wrap Value =========================
    var toLabeledValues = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "TreeSelect.useCallback[toLabeledValues]": function(draftValues) {
            var values = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toArray"])(draftValues);
            return values.map({
                "TreeSelect.useCallback[toLabeledValues]": function(val) {
                    if (isRawValue(val)) {
                        return {
                            value: val
                        };
                    }
                    return val;
                }
            }["TreeSelect.useCallback[toLabeledValues]"]);
        }
    }["TreeSelect.useCallback[toLabeledValues]"], []);
    var convert2LabelValues = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "TreeSelect.useCallback[convert2LabelValues]": function(draftValues) {
            var values = toLabeledValues(draftValues);
            return values.map({
                "TreeSelect.useCallback[convert2LabelValues]": function(item) {
                    var rawLabel = item.label;
                    var rawValue = item.value, rawHalfChecked = item.halfChecked;
                    var rawDisabled;
                    var entity = valueEntities.get(rawValue);
                    // Fill missing label & status
                    if (entity) {
                        var _rawLabel;
                        rawLabel = treeTitleRender ? treeTitleRender(entity.node) : (_rawLabel = rawLabel) !== null && _rawLabel !== void 0 ? _rawLabel : getLabel(entity.node);
                        rawDisabled = entity.node.disabled;
                    } else if (rawLabel === undefined) {
                        // We try to find in current `labelInValue` value
                        var labelInValueItem = toLabeledValues(internalValue).find({
                            "TreeSelect.useCallback[convert2LabelValues].labelInValueItem": function(labeledItem) {
                                return labeledItem.value === rawValue;
                            }
                        }["TreeSelect.useCallback[convert2LabelValues].labelInValueItem"]);
                        rawLabel = labelInValueItem.label;
                    }
                    return {
                        label: rawLabel,
                        value: rawValue,
                        halfChecked: rawHalfChecked,
                        disabled: rawDisabled
                    };
                }
            }["TreeSelect.useCallback[convert2LabelValues]"]);
        }
    }["TreeSelect.useCallback[convert2LabelValues]"], [
        valueEntities,
        getLabel,
        toLabeledValues,
        internalValue
    ]);
    // =========================== Values ===========================
    var rawMixedLabeledValues = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "TreeSelect.useMemo[rawMixedLabeledValues]": function() {
            return toLabeledValues(internalValue === null ? [] : internalValue);
        }
    }["TreeSelect.useMemo[rawMixedLabeledValues]"], [
        toLabeledValues,
        internalValue
    ]);
    // Split value into full check and half check
    var _React$useMemo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "TreeSelect.useMemo[_React$useMemo]": function() {
            var fullCheckValues = [];
            var halfCheckValues = [];
            rawMixedLabeledValues.forEach({
                "TreeSelect.useMemo[_React$useMemo]": function(item) {
                    if (item.halfChecked) {
                        halfCheckValues.push(item);
                    } else {
                        fullCheckValues.push(item);
                    }
                }
            }["TreeSelect.useMemo[_React$useMemo]"]);
            return [
                fullCheckValues,
                halfCheckValues
            ];
        }
    }["TreeSelect.useMemo[_React$useMemo]"], [
        rawMixedLabeledValues
    ]), _React$useMemo2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_React$useMemo, 2), rawLabeledValues = _React$useMemo2[0], rawHalfLabeledValues = _React$useMemo2[1];
    // const [mergedValues] = useCache(rawLabeledValues);
    var rawValues = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "TreeSelect.useMemo[rawValues]": function() {
            return rawLabeledValues.map({
                "TreeSelect.useMemo[rawValues]": function(item) {
                    return item.value;
                }
            }["TreeSelect.useMemo[rawValues]"]);
        }
    }["TreeSelect.useMemo[rawValues]"], [
        rawLabeledValues
    ]);
    // Convert value to key. Will fill missed keys for conduct check.
    var _useCheckedKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useCheckedKeys$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(rawLabeledValues, rawHalfLabeledValues, treeConduction, keyEntities), _useCheckedKeys2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useCheckedKeys, 2), rawCheckedValues = _useCheckedKeys2[0], rawHalfCheckedValues = _useCheckedKeys2[1];
    // Convert rawCheckedKeys to check strategy related values
    var displayValues = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "TreeSelect.useMemo[displayValues]": function() {
            // Collect keys which need to show
            var displayKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatStrategyValues"])(rawCheckedValues, mergedShowCheckedStrategy, keyEntities, mergedFieldNames);
            // Convert to value and filled with label
            var values = displayKeys.map({
                "TreeSelect.useMemo[displayValues].values": function(key) {
                    var _keyEntities$key$node, _keyEntities$key;
                    return (_keyEntities$key$node = (_keyEntities$key = keyEntities[key]) === null || _keyEntities$key === void 0 || (_keyEntities$key = _keyEntities$key.node) === null || _keyEntities$key === void 0 ? void 0 : _keyEntities$key[mergedFieldNames.value]) !== null && _keyEntities$key$node !== void 0 ? _keyEntities$key$node : key;
                }
            }["TreeSelect.useMemo[displayValues].values"]);
            // Back fill with origin label
            var labeledValues = values.map({
                "TreeSelect.useMemo[displayValues].labeledValues": function(val) {
                    var targetItem = rawLabeledValues.find({
                        "TreeSelect.useMemo[displayValues].labeledValues.targetItem": function(item) {
                            return item.value === val;
                        }
                    }["TreeSelect.useMemo[displayValues].labeledValues.targetItem"]);
                    var label = labelInValue ? targetItem === null || targetItem === void 0 ? void 0 : targetItem.label : treeTitleRender === null || treeTitleRender === void 0 ? void 0 : treeTitleRender(targetItem);
                    return {
                        value: val,
                        label: label
                    };
                }
            }["TreeSelect.useMemo[displayValues].labeledValues"]);
            var rawDisplayValues = convert2LabelValues(labeledValues);
            var firstVal = rawDisplayValues[0];
            if (!mergedMultiple && firstVal && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(firstVal.value) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$valueUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(firstVal.label)) {
                return [];
            }
            return rawDisplayValues.map({
                "TreeSelect.useMemo[displayValues]": function(item) {
                    var _item$label;
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, item), {}, {
                        label: (_item$label = item.label) !== null && _item$label !== void 0 ? _item$label : item.value
                    });
                }
            }["TreeSelect.useMemo[displayValues]"]);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        }
    }["TreeSelect.useMemo[displayValues]"], [
        mergedFieldNames,
        mergedMultiple,
        rawCheckedValues,
        rawLabeledValues,
        convert2LabelValues,
        mergedShowCheckedStrategy,
        keyEntities
    ]);
    var _useCache = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useCache$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(displayValues), _useCache2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(_useCache, 1), cachedDisplayValues = _useCache2[0];
    // ========================== MaxCount ==========================
    var mergedMaxCount = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "TreeSelect.useMemo[mergedMaxCount]": function() {
            if (mergedMultiple && (mergedShowCheckedStrategy === 'SHOW_CHILD' || treeCheckStrictly || !treeCheckable)) {
                return maxCount;
            }
            return null;
        }
    }["TreeSelect.useMemo[mergedMaxCount]"], [
        maxCount,
        mergedMultiple,
        treeCheckStrictly,
        mergedShowCheckedStrategy,
        treeCheckable
    ]);
    // =========================== Change ===========================
    var triggerChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useRefFunc$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "TreeSelect.useRefFunc[triggerChange]": function(newRawValues, extra, source) {
            var formattedKeyList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatStrategyValues"])(newRawValues, mergedShowCheckedStrategy, keyEntities, mergedFieldNames);
            // Not allow pass with `maxCount`
            if (mergedMaxCount && formattedKeyList.length > mergedMaxCount) {
                return;
            }
            var labeledValues = convert2LabelValues(newRawValues);
            setInternalValue(labeledValues);
            // Clean up if needed
            if (autoClearSearchValue) {
                setSearchValue('');
            }
            // Generate rest parameters is costly, so only do it when necessary
            if (onChange) {
                var eventValues = newRawValues;
                if (treeConduction) {
                    eventValues = formattedKeyList.map({
                        "TreeSelect.useRefFunc[triggerChange]": function(key) {
                            var entity = valueEntities.get(key);
                            return entity ? entity.node[mergedFieldNames.value] : key;
                        }
                    }["TreeSelect.useRefFunc[triggerChange]"]);
                }
                var _ref = extra || {
                    triggerValue: undefined,
                    selected: undefined
                }, triggerValue = _ref.triggerValue, selected = _ref.selected;
                var returnRawValues = eventValues;
                // We need fill half check back
                if (treeCheckStrictly) {
                    var halfValues = rawHalfLabeledValues.filter({
                        "TreeSelect.useRefFunc[triggerChange].halfValues": function(item) {
                            return !eventValues.includes(item.value);
                        }
                    }["TreeSelect.useRefFunc[triggerChange].halfValues"]);
                    returnRawValues = [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(returnRawValues), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(halfValues));
                }
                var returnLabeledValues = convert2LabelValues(returnRawValues);
                var additionalInfo = {
                    // [Legacy] Always return as array contains label & value
                    preValue: rawLabeledValues,
                    triggerValue: triggerValue
                };
                // [Legacy] Fill legacy data if user query.
                // This is expansive that we only fill when user query
                // https://github.com/react-component/tree-select/blob/fe33eb7c27830c9ac70cd1fdb1ebbe7bc679c16a/src/Select.jsx
                var showPosition = true;
                if (treeCheckStrictly || source === 'selection' && !selected) {
                    showPosition = false;
                }
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fillAdditionalInfo"])(additionalInfo, triggerValue, newRawValues, mergedTreeData, showPosition, mergedFieldNames);
                if (mergedCheckable) {
                    additionalInfo.checked = selected;
                } else {
                    additionalInfo.selected = selected;
                }
                var returnValues = mergedLabelInValue ? returnLabeledValues : returnLabeledValues.map({
                    "TreeSelect.useRefFunc[triggerChange]": function(item) {
                        return item.value;
                    }
                }["TreeSelect.useRefFunc[triggerChange]"]);
                onChange(mergedMultiple ? returnValues : returnValues[0], mergedLabelInValue ? null : returnLabeledValues.map({
                    "TreeSelect.useRefFunc[triggerChange]": function(item) {
                        return item.label;
                    }
                }["TreeSelect.useRefFunc[triggerChange]"]), additionalInfo);
            }
        }
    }["TreeSelect.useRefFunc[triggerChange]"]);
    // ========================== Options ===========================
    /** Trigger by option list */ var onOptionSelect = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "TreeSelect.useCallback[onOptionSelect]": function(selectedKey, _ref2) {
            var _node$mergedFieldName;
            var selected = _ref2.selected, source = _ref2.source;
            var entity = keyEntities[selectedKey];
            var node = entity === null || entity === void 0 ? void 0 : entity.node;
            var selectedValue = (_node$mergedFieldName = node === null || node === void 0 ? void 0 : node[mergedFieldNames.value]) !== null && _node$mergedFieldName !== void 0 ? _node$mergedFieldName : selectedKey;
            // Never be falsy but keep it safe
            if (!mergedMultiple) {
                // Single mode always set value
                triggerChange([
                    selectedValue
                ], {
                    selected: true,
                    triggerValue: selectedValue
                }, 'option');
            } else {
                var newRawValues = selected ? [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(rawValues), [
                    selectedValue
                ]) : rawCheckedValues.filter({
                    "TreeSelect.useCallback[onOptionSelect]": function(v) {
                        return v !== selectedValue;
                    }
                }["TreeSelect.useCallback[onOptionSelect]"]);
                // Add keys if tree conduction
                if (treeConduction) {
                    // Should keep missing values
                    var _splitRawValues = splitRawValues(newRawValues), missingRawValues = _splitRawValues.missingRawValues, existRawValues = _splitRawValues.existRawValues;
                    var keyList = existRawValues.map({
                        "TreeSelect.useCallback[onOptionSelect].keyList": function(val) {
                            return valueEntities.get(val).key;
                        }
                    }["TreeSelect.useCallback[onOptionSelect].keyList"]);
                    // Conduction by selected or not
                    var checkedKeys;
                    if (selected) {
                        var _conductCheck = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$conductUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["conductCheck"])(keyList, true, keyEntities);
                        checkedKeys = _conductCheck.checkedKeys;
                    } else {
                        var _conductCheck2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$conductUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["conductCheck"])(keyList, {
                            checked: false,
                            halfCheckedKeys: rawHalfCheckedValues
                        }, keyEntities);
                        checkedKeys = _conductCheck2.checkedKeys;
                    }
                    // Fill back of keys
                    newRawValues = [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(missingRawValues), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(checkedKeys.map({
                        "TreeSelect.useCallback[onOptionSelect]": function(key) {
                            return keyEntities[key].node[mergedFieldNames.value];
                        }
                    }["TreeSelect.useCallback[onOptionSelect]"])));
                }
                triggerChange(newRawValues, {
                    selected: selected,
                    triggerValue: selectedValue
                }, source || 'option');
            }
            // Trigger select event
            if (selected || !mergedMultiple) {
                onSelect === null || onSelect === void 0 || onSelect(selectedValue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fillLegacyProps"])(node));
            } else {
                onDeselect === null || onDeselect === void 0 || onDeselect(selectedValue, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$legacyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fillLegacyProps"])(node));
            }
        }
    }["TreeSelect.useCallback[onOptionSelect]"], [
        splitRawValues,
        valueEntities,
        keyEntities,
        mergedFieldNames,
        mergedMultiple,
        rawValues,
        triggerChange,
        treeConduction,
        onSelect,
        onDeselect,
        rawCheckedValues,
        rawHalfCheckedValues,
        maxCount
    ]);
    // ========================== Dropdown ==========================
    var onInternalDropdownVisibleChange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"]({
        "TreeSelect.useCallback[onInternalDropdownVisibleChange]": function(open) {
            if (onDropdownVisibleChange) {
                var legacyParam = {};
                Object.defineProperty(legacyParam, 'documentClickClose', {
                    get: function get() {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, 'Second param of `onDropdownVisibleChange` has been removed.');
                        return false;
                    }
                });
                onDropdownVisibleChange(open, legacyParam);
            }
        }
    }["TreeSelect.useCallback[onInternalDropdownVisibleChange]"], [
        onDropdownVisibleChange
    ]);
    // ====================== Display Change ========================
    var onDisplayValuesChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$hooks$2f$useRefFunc$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "TreeSelect.useRefFunc[onDisplayValuesChange]": function(newValues, info) {
            var newRawValues = newValues.map({
                "TreeSelect.useRefFunc[onDisplayValuesChange].newRawValues": function(item) {
                    return item.value;
                }
            }["TreeSelect.useRefFunc[onDisplayValuesChange].newRawValues"]);
            if (info.type === 'clear') {
                triggerChange(newRawValues, {}, 'selection');
                return;
            }
            // TreeSelect only have multiple mode which means display change only has remove
            if (info.values.length) {
                onOptionSelect(info.values[0].value, {
                    selected: false,
                    source: 'selection'
                });
            }
        }
    }["TreeSelect.useRefFunc[onDisplayValuesChange]"]);
    // ========================== Context ===========================
    var treeSelectContext = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "TreeSelect.useMemo[treeSelectContext]": function() {
            return {
                virtual: virtual,
                dropdownMatchSelectWidth: dropdownMatchSelectWidth,
                listHeight: listHeight,
                listItemHeight: listItemHeight,
                listItemScrollOffset: listItemScrollOffset,
                treeData: filteredTreeData,
                fieldNames: mergedFieldNames,
                onSelect: onOptionSelect,
                treeExpandAction: treeExpandAction,
                treeTitleRender: treeTitleRender,
                onPopupScroll: onPopupScroll,
                leftMaxCount: maxCount === undefined ? null : maxCount - cachedDisplayValues.length,
                leafCountOnly: mergedShowCheckedStrategy === 'SHOW_CHILD' && !treeCheckStrictly && !!treeCheckable,
                valueEntities: valueEntities
            };
        }
    }["TreeSelect.useMemo[treeSelectContext]"], [
        virtual,
        dropdownMatchSelectWidth,
        listHeight,
        listItemHeight,
        listItemScrollOffset,
        filteredTreeData,
        mergedFieldNames,
        onOptionSelect,
        treeExpandAction,
        treeTitleRender,
        onPopupScroll,
        maxCount,
        cachedDisplayValues.length,
        mergedShowCheckedStrategy,
        treeCheckStrictly,
        treeCheckable,
        valueEntities
    ]);
    // ======================= Legacy Context =======================
    var legacyContext = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"]({
        "TreeSelect.useMemo[legacyContext]": function() {
            return {
                checkable: mergedCheckable,
                loadData: loadData,
                treeLoadedKeys: treeLoadedKeys,
                onTreeLoad: onTreeLoad,
                checkedKeys: rawCheckedValues,
                halfCheckedKeys: rawHalfCheckedValues,
                treeDefaultExpandAll: treeDefaultExpandAll,
                treeExpandedKeys: treeExpandedKeys,
                treeDefaultExpandedKeys: treeDefaultExpandedKeys,
                onTreeExpand: onTreeExpand,
                treeIcon: treeIcon,
                treeMotion: treeMotion,
                showTreeIcon: showTreeIcon,
                switcherIcon: switcherIcon,
                treeLine: treeLine,
                treeNodeFilterProp: treeNodeFilterProp,
                keyEntities: keyEntities
            };
        }
    }["TreeSelect.useMemo[legacyContext]"], [
        mergedCheckable,
        loadData,
        treeLoadedKeys,
        onTreeLoad,
        rawCheckedValues,
        rawHalfCheckedValues,
        treeDefaultExpandAll,
        treeExpandedKeys,
        treeDefaultExpandedKeys,
        onTreeExpand,
        treeIcon,
        treeMotion,
        showTreeIcon,
        switcherIcon,
        treeLine,
        treeNodeFilterProp,
        keyEntities
    ]);
    // =========================== Render ===========================
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeSelectContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
        value: treeSelectContext
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$LegacyContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
        value: legacyContext
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$BaseSelect$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BaseSelect$3e$__["BaseSelect"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        ref: ref
    }, restProps, {
        // >>> MISC
        id: mergedId,
        prefixCls: prefixCls,
        mode: mergedMultiple ? 'multiple' : undefined,
        displayValues: cachedDisplayValues,
        onDisplayValuesChange: onDisplayValuesChange,
        searchValue: mergedSearchValue,
        onSearch: onInternalSearch,
        OptionList: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$OptionList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        emptyOptions: !mergedTreeData.length,
        onDropdownVisibleChange: onInternalDropdownVisibleChange,
        dropdownMatchSelectWidth: dropdownMatchSelectWidth
    }))));
});
// Assign name for Debug
if ("TURBOPACK compile-time truthy", 1) {
    TreeSelect.displayName = 'TreeSelect';
}
var GenericTreeSelect = TreeSelect;
GenericTreeSelect.TreeNode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
GenericTreeSelect.SHOW_ALL = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SHOW_ALL"];
GenericTreeSelect.SHOW_PARENT = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SHOW_PARENT"];
GenericTreeSelect.SHOW_CHILD = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SHOW_CHILD"];
const __TURBOPACK__default__export__ = GenericTreeSelect;
}),
"[project]/node_modules/rc-tree-select/es/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeSelect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/TreeSelect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/TreeNode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/strategyUtil.js [app-client] (ecmascript)");
;
;
;
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeSelect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}),
"[project]/node_modules/rc-tree-select/es/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeSelect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/TreeSelect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/TreeNode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$utils$2f$strategyUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/utils/strategyUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/rc-tree-select/es/TreeNode.js [app-client] (ecmascript) <export default as TreeNode>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TreeNode": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2d$select$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree-select/es/TreeNode.js [app-client] (ecmascript)");
}),
}]);

//# sourceMappingURL=node_modules_rc-tree-select_es_a0fd8e81._.js.map