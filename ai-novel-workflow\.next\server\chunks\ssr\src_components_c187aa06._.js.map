{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Layout, Menu, Button, Dropdown, Avatar, Badge, Space, Typography } from 'antd';\nimport { \n  MenuFoldOutlined, \n  MenuUnfoldOutlined,\n  BellOutlined,\n  SettingOutlined,\n  UserOutlined,\n  ProjectOutlined,\n  FileTextOutlined,\n  TeamOutlined,\n  GlobalOutlined,\n  BookOutlined,\n  BranchesOutlined,\n  ThunderboltOutlined,\n  FolderOutlined,\n  SunOutlined,\n  MoonOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { MenuProps } from 'antd';\n\nconst { Header, Sider, Content } = Layout;\nconst { Text } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const {\n    ui,\n    toggleSidebar,\n    setTheme,\n    setActiveTab,\n    currentProject\n  } = useAppStore();\n\n  const [collapsed, setCollapsed] = useState(ui.sidebarCollapsed);\n\n  const handleToggleSidebar = () => {\n    setCollapsed(!collapsed);\n    toggleSidebar();\n  };\n\n  const handleThemeToggle = () => {\n    setTheme(ui.theme === 'light' ? 'dark' : 'light');\n  };\n\n  // 侧边栏菜单项\n  const menuItems: MenuProps['items'] = [\n    {\n      key: 'workflow',\n      icon: <ThunderboltOutlined />,\n      label: '工作流编辑器',\n    },\n    {\n      key: 'projects',\n      icon: <ProjectOutlined />,\n      label: '项目总览',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'content-management',\n      icon: <FileTextOutlined />,\n      label: '内容管理',\n      children: [\n        {\n          key: 'outlines',\n          icon: <FileTextOutlined />,\n          label: '大纲管理',\n        },\n        {\n          key: 'characters',\n          icon: <TeamOutlined />,\n          label: '角色管理',\n        },\n        {\n          key: 'worldbuilding',\n          icon: <GlobalOutlined />,\n          label: '世界观管理',\n        },\n        {\n          key: 'plotlines',\n          icon: <BranchesOutlined />,\n          label: '主线管理',\n        },\n        {\n          key: 'titles',\n          icon: <BookOutlined />,\n          label: '书名管理',\n        },\n      ],\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'documents',\n      icon: <FolderOutlined />,\n      label: '文档管理',\n    },\n    {\n      key: 'prompts',\n      icon: <FileTextOutlined />,\n      label: '提示词管理',\n    },\n  ];\n\n  // 用户菜单\n  const userMenuItems: MenuProps['items'] = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      label: '退出登录',\n    },\n  ];\n\n  // 通知菜单\n  const notifications = ui?.notifications || [];\n  const notificationMenuItems: MenuProps['items'] = notifications.slice(0, 5).map(notification => ({\n    key: notification.id,\n    label: (\n      <div className=\"max-w-xs\">\n        <div className=\"font-medium text-sm\">{notification.title}</div>\n        <div className=\"text-xs text-gray-500 mt-1\">{notification.message}</div>\n        <div className=\"text-xs text-gray-400 mt-1\">\n          {notification.timestamp.toLocaleTimeString()}\n        </div>\n      </div>\n    ),\n  }));\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    setActiveTab(key);\n  };\n\n  return (\n    <Layout className=\"min-h-screen\">\n      {/* 侧边栏 */}\n      <Sider \n        trigger={null} \n        collapsible \n        collapsed={collapsed}\n        width={240}\n        className=\"bg-white border-r border-gray-200\"\n        theme=\"light\"\n      >\n        {/* Logo区域 */}\n        <div className=\"h-16 flex items-center justify-center border-b border-gray-200\">\n          {collapsed ? (\n            <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n              <ThunderboltOutlined className=\"text-white text-lg\" />\n            </div>\n          ) : (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n                <ThunderboltOutlined className=\"text-white text-lg\" />\n              </div>\n              <Text strong className=\"text-lg\">AI小说工作流</Text>\n            </div>\n          )}\n        </div>\n\n        {/* 当前项目信息 */}\n        {!collapsed && currentProject && (\n          <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n            <Text type=\"secondary\" className=\"text-xs\">当前项目</Text>\n            <div className=\"mt-1\">\n              <Text strong className=\"text-sm\">{currentProject.name}</Text>\n            </div>\n            <div className=\"mt-1\">\n              <Text type=\"secondary\" className=\"text-xs\">\n                {currentProject.status === 'draft' && '草稿'}\n                {currentProject.status === 'in-progress' && '进行中'}\n                {currentProject.status === 'completed' && '已完成'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {/* 菜单 */}\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[ui.activeTab]}\n          items={menuItems}\n          onClick={handleMenuClick}\n          className=\"border-none\"\n        />\n      </Sider>\n\n      <Layout>\n        {/* 顶部导航栏 */}\n        <Header className=\"bg-white border-b border-gray-200 px-4 flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={handleToggleSidebar}\n              className=\"text-lg\"\n            />\n            \n            {/* 面包屑导航 */}\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n              <span>\n                {ui.activeTab === 'workflow' && '工作流编辑器'}\n                {ui.activeTab === 'projects' && '项目总览'}\n                {ui.activeTab === 'outlines' && '大纲管理'}\n                {ui.activeTab === 'characters' && '角色管理'}\n                {ui.activeTab === 'worldbuilding' && '世界观管理'}\n                {ui.activeTab === 'plotlines' && '主线管理'}\n                {ui.activeTab === 'titles' && '书名管理'}\n                {ui.activeTab === 'documents' && '文档管理'}\n                {ui.activeTab === 'prompts' && '提示词管理'}\n              </span>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* 主题切换 */}\n            <Button\n              type=\"text\"\n              icon={ui.theme === 'light' ? <MoonOutlined /> : <SunOutlined />}\n              onClick={handleThemeToggle}\n              className=\"text-lg\"\n            />\n\n            {/* 通知 */}\n            <Dropdown\n              menu={{ items: notificationMenuItems }}\n              trigger={['click']}\n              placement=\"bottomRight\"\n            >\n              <Button type=\"text\" className=\"text-lg\">\n                <Badge count={notifications.filter(n => !n.read).length} size=\"small\">\n                  <BellOutlined />\n                </Badge>\n              </Button>\n            </Dropdown>\n\n            {/* 用户菜单 */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              trigger={['click']}\n              placement=\"bottomRight\"\n            >\n              <Space className=\"cursor-pointer\">\n                <Avatar icon={<UserOutlined />} />\n                <Text>用户</Text>\n              </Space>\n            </Dropdown>\n          </div>\n        </Header>\n\n        {/* 主内容区域 */}\n        <Content className=\"bg-gray-50 overflow-auto\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AArBA;;;;;;AAwBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAM3B,MAAM,aAAwC,CAAC,EAAE,QAAQ,EAAE;IACzD,MAAM,EACJ,EAAE,EACF,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,cAAc,EACf,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,gBAAgB;IAE9D,MAAM,sBAAsB;QAC1B,aAAa,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,SAAS,GAAG,KAAK,KAAK,UAAU,SAAS;IAC3C;IAEA,SAAS;IACT,MAAM,YAAgC;QACpC;YACE,KAAK;YACL,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;QACT;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;oBACrB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;gBACT;aACD;QACH;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;KACD;IAED,OAAO;IACP,MAAM,gBAAoC;QACxC;YACE,KAAK;YACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;QACT;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;QACT;KACD;IAED,OAAO;IACP,MAAM,gBAAgB,IAAI,iBAAiB,EAAE;IAC7C,MAAM,wBAA4C,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,eAAgB,CAAC;YAC/F,KAAK,aAAa,EAAE;YACpB,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAuB,aAAa,KAAK;;;;;;kCACxD,8OAAC;wBAAI,WAAU;kCAA8B,aAAa,OAAO;;;;;;kCACjE,8OAAC;wBAAI,WAAU;kCACZ,aAAa,SAAS,CAAC,kBAAkB;;;;;;;;;;;;QAIlD,CAAC;IAED,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,aAAa;IACf;IAEA,qBACE,8OAAC,kLAAA,CAAA,SAAM;QAAC,WAAU;;0BAEhB,8OAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,WAAU;gBACV,OAAM;;kCAGN,8OAAC;wBAAI,WAAU;kCACZ,0BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;;;qFAGjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAEjC,8OAAC;oCAAK,MAAM;oCAAC,WAAU;8CAAU;;;;;;;;;;;;;;;;;oBAMtC,CAAC,aAAa,gCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,MAAK;gCAAY,WAAU;0CAAU;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,MAAM;oCAAC,WAAU;8CAAW,eAAe,IAAI;;;;;;;;;;;0CAEvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,MAAK;oCAAY,WAAU;;wCAC9B,eAAe,MAAM,KAAK,WAAW;wCACrC,eAAe,MAAM,KAAK,iBAAiB;wCAC3C,eAAe,MAAM,KAAK,eAAe;;;;;;;;;;;;;;;;;;kCAOlD,8OAAC,8KAAA,CAAA,OAAI;wBACH,MAAK;wBACL,cAAc;4BAAC,GAAG,SAAS;yBAAC;wBAC5B,OAAO;wBACP,SAAS;wBACT,WAAU;;;;;;;;;;;;0BAId,8OAAC,kLAAA,CAAA,SAAM;;kCAEL,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,0BAAY,8OAAC,8NAAA,CAAA,qBAAkB;;;;mEAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;gDACE,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,gBAAgB;gDACjC,GAAG,SAAS,KAAK,mBAAmB;gDACpC,GAAG,SAAS,KAAK,eAAe;gDAChC,GAAG,SAAS,KAAK,YAAY;gDAC7B,GAAG,SAAS,KAAK,eAAe;gDAChC,GAAG,SAAS,KAAK,aAAa;;;;;;;;;;;;;;;;;;0CAKrC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,GAAG,KAAK,KAAK,wBAAU,8OAAC,kNAAA,CAAA,eAAY;;;;mEAAM,8OAAC,gNAAA,CAAA,cAAW;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAIZ,8OAAC,sLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAsB;wCACrC,SAAS;4CAAC;yCAAQ;wCAClB,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,WAAU;sDAC5B,cAAA,8OAAC,gLAAA,CAAA,QAAK;gDAAC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;gDAAE,MAAK;0DAC5D,cAAA,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kDAMnB,8OAAC,sLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAc;wCAC7B,SAAS;4CAAC;yCAAQ;wCAClB,WAAU;kDAEV,cAAA,8OAAC,gMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,kLAAA,CAAA,SAAM;oDAAC,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;8DAC3B,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/workflow/CustomNode.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Handle, Position, NodeProps } from '@xyflow/react';\nimport { Card, Typography, Tag, Button } from 'antd';\nimport { \n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  SettingOutlined,\n  DeleteOutlined\n} from '@ant-design/icons';\n\nconst { Text } = Typography;\n\ninterface CustomNodeData {\n  label: string;\n  type: string;\n  status: 'idle' | 'running' | 'completed' | 'error';\n  description?: string;\n  config?: Record<string, any>;\n  onEdit?: () => void;\n  onDelete?: () => void;\n}\n\nconst CustomNode: React.FC<NodeProps<CustomNodeData>> = ({ data, selected }) => {\n  const getStatusIcon = () => {\n    switch (data.status) {\n      case 'running':\n        return <PauseCircleOutlined className=\"text-blue-500\" />;\n      case 'completed':\n        return <CheckCircleOutlined className=\"text-green-500\" />;\n      case 'error':\n        return <ExclamationCircleOutlined className=\"text-red-500\" />;\n      default:\n        return <PlayCircleOutlined className=\"text-gray-400\" />;\n    }\n  };\n\n  const getStatusColor = () => {\n    switch (data.status) {\n      case 'running':\n        return 'processing';\n      case 'completed':\n        return 'success';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const getNodeIcon = () => {\n    switch (data.type) {\n      case 'input':\n        return '📝';\n      case 'title-generator':\n        return '📚';\n      case 'detail-generator':\n        return '📄';\n      case 'character-creator':\n        return '👥';\n      case 'worldbuilding':\n        return '🌍';\n      case 'plotline-planner':\n        return '📈';\n      case 'outline-generator':\n        return '📋';\n      case 'chapter-count-input':\n        return '🔢';\n      case 'detailed-outline':\n        return '📝';\n      case 'chapter-generator':\n        return '📖';\n      case 'content-polisher':\n        return '✨';\n      case 'consistency-checker':\n        return '✅';\n      case 'condition':\n        return '🔀';\n      case 'loop':\n        return '🔄';\n      case 'output':\n        return '📤';\n      default:\n        return '⚙️';\n    }\n  };\n\n  return (\n    <div className={`custom-node ${selected ? 'selected' : ''}`}>\n      <Handle\n        type=\"target\"\n        position={Position.Top}\n        className=\"w-3 h-3 !bg-gray-400 border-2 border-white\"\n      />\n      \n      <Card\n        size=\"small\"\n        className={`min-w-48 shadow-md transition-all duration-200 ${\n          selected ? 'ring-2 ring-blue-500 shadow-lg' : ''\n        } ${data.status === 'running' ? 'animate-pulse' : ''}`}\n        bodyStyle={{ padding: '12px' }}\n      >\n        <div className=\"flex items-start justify-between mb-2\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg\">{getNodeIcon()}</span>\n            <div>\n              <Text strong className=\"text-sm block\">\n                {data.label}\n              </Text>\n              {data.description && (\n                <Text type=\"secondary\" className=\"text-xs\">\n                  {data.description}\n                </Text>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            {getStatusIcon()}\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <Tag color={getStatusColor()} size=\"small\">\n            {data.status === 'idle' && '等待'}\n            {data.status === 'running' && '运行中'}\n            {data.status === 'completed' && '已完成'}\n            {data.status === 'error' && '错误'}\n          </Tag>\n          \n          <div className=\"flex space-x-1\">\n            <Button\n              type=\"text\"\n              size=\"small\"\n              icon={<SettingOutlined />}\n              onClick={data.onEdit}\n              className=\"text-xs\"\n            />\n            <Button\n              type=\"text\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={data.onDelete}\n              className=\"text-xs\"\n            />\n          </div>\n        </div>\n\n        {/* 配置信息 */}\n        {data.config && Object.keys(data.config).length > 0 && (\n          <div className=\"mt-2 pt-2 border-t border-gray-100\">\n            <Text type=\"secondary\" className=\"text-xs\">\n              已配置 {Object.keys(data.config).length} 个参数\n            </Text>\n          </div>\n        )}\n      </Card>\n\n      <Handle\n        type=\"source\"\n        position={Position.Bottom}\n        className=\"w-3 h-3 !bg-gray-400 border-2 border-white\"\n      />\n\n      <style jsx>{`\n        .custom-node.selected {\n          z-index: 1000;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default CustomNode;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAcA,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAY3B,MAAM,aAAkD,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;IACzE,MAAM,gBAAgB;QACpB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBAAO,8OAAC,gOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACxC,KAAK;gBACH,qBAAO,8OAAC,gOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACxC,KAAK;gBACH,qBAAO,8OAAC,4OAAA,CAAA,4BAAyB;oBAAC,WAAU;;;;;;YAC9C;gBACE,qBAAO,8OAAC,8NAAA,CAAA,qBAAkB;oBAAC,WAAU;;;;;;QACzC;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc;QAClB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;kDAAe,CAAC,YAAY,EAAE,WAAW,aAAa,IAAI;;0BACzD,8OAAC,yKAAA,CAAA,SAAM;gBACL,MAAK;gBACL,UAAU,0JAAA,CAAA,WAAQ,CAAC,GAAG;gBACtB,WAAU;;;;;;0BAGZ,8OAAC,8KAAA,CAAA,OAAI;gBACH,MAAK;gBACL,WAAW,CAAC,+CAA+C,EACzD,WAAW,mCAAmC,GAC/C,CAAC,EAAE,KAAK,MAAM,KAAK,YAAY,kBAAkB,IAAI;gBACtD,WAAW;oBAAE,SAAS;gBAAO;;kCAE7B,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAe;kDAAW;;;;;;kDAC3B,8OAAC;;;0DACC,8OAAC;gDAAK,MAAM;gDAAC,WAAU;0DACpB,KAAK,KAAK;;;;;;4CAEZ,KAAK,WAAW,kBACf,8OAAC;gDAAK,MAAK;gDAAY,WAAU;0DAC9B,KAAK,WAAW;;;;;;;;;;;;;;;;;;0CAKzB,8OAAC;0EAAc;0CACZ;;;;;;;;;;;;kCAIL,8OAAC;kEAAc;;0CACb,8OAAC,4KAAA,CAAA,MAAG;gCAAC,OAAO;gCAAkB,MAAK;;oCAChC,KAAK,MAAM,KAAK,UAAU;oCAC1B,KAAK,MAAM,KAAK,aAAa;oCAC7B,KAAK,MAAM,KAAK,eAAe;oCAC/B,KAAK,MAAM,KAAK,WAAW;;;;;;;0CAG9B,8OAAC;0EAAc;;kDACb,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAK;wCACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;wCACtB,SAAS,KAAK,MAAM;wCACpB,WAAU;;;;;;kDAEZ,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAK;wCACL,MAAM;wCACN,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,KAAK,QAAQ;wCACtB,WAAU;;;;;;;;;;;;;;;;;;oBAMf,KAAK,MAAM,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE,MAAM,GAAG,mBAChD,8OAAC;kEAAc;kCACb,cAAA,8OAAC;4BAAK,MAAK;4BAAY,WAAU;;gCAAU;gCACpC,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC,yKAAA,CAAA,SAAM;gBACL,MAAK;gBACL,UAAU,0JAAA,CAAA,WAAQ,CAAC,MAAM;gBACzB,WAAU;;;;;;;;;;;;;;;;AAUlB;uCAEe", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/workflow/WorkflowEditor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState, useMemo } from 'react';\nimport { Card, Button, Space, Typography, Divider, message, Modal, Form, Input, Select } from 'antd';\nimport {\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  SaveOutlined,\n  PlusOutlined,\n  SettingOutlined,\n  DeleteOutlined,\n  LinkOutlined,\n  RobotOutlined\n} from '@ant-design/icons';\nimport {\n  ReactFlow,\n  Background,\n  Controls,\n  MiniMap,\n  useNodesState,\n  useEdgesState,\n  addEdge,\n  Connection,\n  Edge,\n  Node,\n  NodeTypes\n} from '@xyflow/react';\nimport '@xyflow/react/dist/style.css';\nimport { useAppStore } from '@/store';\nimport CustomNode from './CustomNode';\nimport WorkflowAIAssistant from './WorkflowAIAssistant';\nimport { WorkflowTemplate } from '@/utils/workflowAI';\n\nconst { Title, Text } = Typography;\n\nconst WorkflowEditor: React.FC = () => {\n  const {\n    currentProject,\n    currentWorkflow,\n    addNode,\n    updateNode,\n    deleteNode,\n    connectNodes,\n    saveWorkflow,\n    executionContexts,\n    startExecution,\n    pauseExecution,\n    stopExecution\n  } = useAppStore();\n\n  const [selectedNode, setSelectedNode] = useState<string | null>(null);\n  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);\n  const [isAIAssistantVisible, setIsAIAssistantVisible] = useState(false);\n  const [editingNode, setEditingNode] = useState<any>(null);\n  const [form] = Form.useForm();\n\n  const executionContext = currentProject ? executionContexts[currentProject.id] : null;\n\n  // 转换工作流数据为React Flow格式\n  const initialNodes: Node[] = currentWorkflow.map(node => ({\n    id: node.id,\n    type: 'custom',\n    position: node.position,\n    data: {\n      label: node.data.label,\n      type: node.type,\n      status: node.data.status,\n      description: getNodeDescription(node.type),\n      config: node.data.config,\n      onEdit: () => handleEditNode(node),\n      onDelete: () => handleDeleteNode(node.id),\n    },\n  }));\n\n  const initialEdges: Edge[] = currentWorkflow.flatMap(node =>\n    node.connections.map(conn => ({\n      id: `${conn.sourceId}-${conn.targetId}`,\n      source: conn.sourceId,\n      target: conn.targetId,\n      type: 'smoothstep',\n      animated: node.data.status === 'running',\n    }))\n  );\n\n  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);\n  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n\n  const reactFlowNodeTypes: NodeTypes = useMemo(() => ({\n    custom: CustomNode,\n  }), []);\n\n  const getNodeDescription = (nodeType: string) => {\n    const descriptions: Record<string, string> = {\n      'input': '用户参数输入',\n      'title-generator': 'AI生成书名候选',\n      'detail-generator': '生成小说基础信息',\n      'character-creator': 'AI生成角色设定',\n      'worldbuilding': 'AI生成世界背景',\n      'plotline-planner': 'AI生成主要故事线',\n      'outline-generator': 'AI生成故事结构',\n      'chapter-count-input': '用户指定章节总数',\n      'detailed-outline': '生成详细情节要点',\n      'chapter-generator': 'AI生成具体章节内容',\n      'content-polisher': 'AI优化文本质量',\n      'consistency-checker': '检查内容一致性',\n      'condition': '条件分支',\n      'loop': '循环执行',\n      'output': '最终结果展示',\n    };\n    return descriptions[nodeType] || '未知节点类型';\n  };\n\n  const handleSaveWorkflow = useCallback(() => {\n    if (currentProject) {\n      saveWorkflow(currentProject.id);\n      message.success('工作流已保存');\n    }\n  }, [currentProject, saveWorkflow]);\n\n  const handleStartExecution = useCallback(() => {\n    if (currentProject) {\n      startExecution(currentProject.id, 'default');\n      message.info('工作流开始执行');\n    }\n  }, [currentProject, startExecution]);\n\n  const handlePauseExecution = useCallback(() => {\n    if (currentProject) {\n      pauseExecution(currentProject.id);\n      message.info('工作流已暂停');\n    }\n  }, [currentProject, pauseExecution]);\n\n  const handleStopExecution = useCallback(() => {\n    if (currentProject) {\n      stopExecution(currentProject.id);\n      message.info('工作流已停止');\n    }\n  }, [currentProject, stopExecution]);\n\n  const handleEditNode = useCallback((node: any) => {\n    setEditingNode(node);\n    form.setFieldsValue({\n      label: node.data.label,\n      ...node.data.config,\n    });\n    setIsConfigModalVisible(true);\n  }, [form]);\n\n  const handleDeleteNode = useCallback((nodeId: string) => {\n    deleteNode(nodeId);\n    setNodes(nodes => nodes.filter(n => n.id !== nodeId));\n    setEdges(edges => edges.filter(e => e.source !== nodeId && e.target !== nodeId));\n    message.success('节点已删除');\n  }, [deleteNode, setNodes, setEdges]);\n\n  const handleCreateSampleWorkflow = useCallback(() => {\n    // 清空现有节点和连接\n    setNodes([]);\n    setEdges([]);\n\n    // 创建示例节点\n    const sampleNodes = [\n      {\n        id: 'input-1',\n        type: 'custom',\n        position: { x: 100, y: 100 },\n        data: {\n          label: '用户输入',\n          type: 'input',\n          status: 'idle' as const,\n          description: '输入创作主题和风格',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('input-1'),\n        },\n      },\n      {\n        id: 'title-gen-1',\n        type: 'custom',\n        position: { x: 100, y: 250 },\n        data: {\n          label: '书名生成',\n          type: 'title-generator',\n          status: 'idle' as const,\n          description: 'AI生成书名候选',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('title-gen-1'),\n        },\n      },\n      {\n        id: 'character-1',\n        type: 'custom',\n        position: { x: 350, y: 250 },\n        data: {\n          label: '角色创建',\n          type: 'character-creator',\n          status: 'idle' as const,\n          description: 'AI生成角色设定',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('character-1'),\n        },\n      },\n      {\n        id: 'outline-1',\n        type: 'custom',\n        position: { x: 225, y: 400 },\n        data: {\n          label: '大纲生成',\n          type: 'outline-generator',\n          status: 'idle' as const,\n          description: 'AI生成故事结构',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('outline-1'),\n        },\n      },\n      {\n        id: 'output-1',\n        type: 'custom',\n        position: { x: 225, y: 550 },\n        data: {\n          label: '结果输出',\n          type: 'output',\n          status: 'idle' as const,\n          description: '最终结果展示',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('output-1'),\n        },\n      },\n    ];\n\n    // 创建示例连接\n    const sampleEdges: Edge[] = [\n      {\n        id: 'input-1-title-gen-1',\n        source: 'input-1',\n        target: 'title-gen-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'input-1-character-1',\n        source: 'input-1',\n        target: 'character-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'title-gen-1-outline-1',\n        source: 'title-gen-1',\n        target: 'outline-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'character-1-outline-1',\n        source: 'character-1',\n        target: 'outline-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'outline-1-output-1',\n        source: 'outline-1',\n        target: 'output-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n    ];\n\n    setNodes(sampleNodes);\n    setEdges(sampleEdges);\n\n    message.success('示例工作流已创建！您可以拖拽节点来连接它们');\n  }, [setNodes, setEdges, handleDeleteNode]);\n\n  // 处理AI工作流应用\n  const handleApplyAIWorkflow = useCallback((template: WorkflowTemplate) => {\n    // 清空现有节点和连接\n    setNodes([]);\n    setEdges([]);\n\n    // 转换模板节点为React Flow格式\n    const reactFlowNodes = template.nodes.map((node, index) => ({\n      id: `node-${index}`,\n      type: 'custom',\n      position: node.position,\n      data: {\n        label: node.label,\n        type: node.type,\n        status: 'idle' as const,\n        description: getNodeDescription(node.type),\n        config: node.config || {},\n        onEdit: () => {},\n        onDelete: () => handleDeleteNode(`node-${index}`),\n      },\n    }));\n\n    // 转换模板连接为React Flow格式\n    const reactFlowEdges = template.connections.map((conn, index) => ({\n      id: `edge-${index}`,\n      source: `node-${conn.source}`,\n      target: `node-${conn.target}`,\n      type: 'smoothstep',\n      animated: false,\n      style: { stroke: '#3b82f6', strokeWidth: 2 },\n    }));\n\n    setNodes(reactFlowNodes);\n    setEdges(reactFlowEdges);\n\n    message.success(`AI工作流\"${template.name}\"已应用成功！`);\n  }, [setNodes, setEdges, handleDeleteNode, getNodeDescription]);\n\n\n\n  const handleAddNode = useCallback((nodeType: string) => {\n    const nodeId = `node-${Date.now()}`;\n    const position = { x: Math.random() * 400, y: Math.random() * 300 };\n\n    // 同时添加到React Flow\n    const reactFlowNode: Node = {\n      id: nodeId,\n      type: 'custom',\n      position,\n      data: {\n        label: `新${nodeType}节点`,\n        type: nodeType,\n        status: 'idle' as const,\n        description: getNodeDescription(nodeType),\n        config: {},\n        onEdit: () => handleEditNode({ id: nodeId, type: nodeType, data: { label: `新${nodeType}节点`, config: {}, status: 'idle' as const } }),\n        onDelete: () => handleDeleteNode(nodeId),\n      },\n    };\n    setNodes(nodes => [...nodes, reactFlowNode]);\n    message.success('节点已添加');\n  }, [setNodes, handleEditNode, handleDeleteNode]);\n\n  const onConnect = useCallback((params: Connection) => {\n    // 检查连接的有效性\n    if (!params.source || !params.target) {\n      message.error('连接失败：源节点或目标节点无效');\n      return;\n    }\n\n    // 防止自连接\n    if (params.source === params.target) {\n      message.warning('不能连接到自身节点');\n      return;\n    }\n\n    // 创建带样式的连接\n    const newEdge = {\n      ...params,\n      type: 'smoothstep',\n      animated: false,\n      style: {\n        stroke: '#3b82f6',\n        strokeWidth: 2\n      },\n    };\n\n    setEdges((eds) => addEdge(newEdge, eds));\n\n    // 同时更新store中的连接信息\n    if (currentProject && params.source && params.target) {\n      connectNodes(params.source, params.target);\n    }\n\n    message.success('节点连接成功');\n  }, [setEdges, connectNodes, currentProject]);\n\n  const nodeTypeOptions = [\n    { key: 'input', label: '输入节点', description: '用户参数输入' },\n    { key: 'title-generator', label: '书名生成', description: 'AI生成书名候选' },\n    { key: 'detail-generator', label: '详情生成', description: '生成小说基础信息' },\n    { key: 'character-creator', label: '角色创建', description: 'AI生成角色设定' },\n    { key: 'worldbuilding', label: '世界观构建', description: 'AI生成世界背景' },\n    { key: 'plotline-planner', label: '主线规划', description: 'AI生成主要故事线' },\n    { key: 'outline-generator', label: '大纲规划', description: 'AI生成故事结构' },\n    { key: 'chapter-count-input', label: '章节数量', description: '用户指定章节总数' },\n    { key: 'detailed-outline', label: '细纲生成', description: '生成详细情节要点' },\n    { key: 'chapter-generator', label: '章节生成', description: 'AI生成具体章节内容' },\n    { key: 'content-polisher', label: '内容润色', description: 'AI优化文本质量' },\n    { key: 'consistency-checker', label: '一致性检查', description: '检查内容一致性' },\n    { key: 'output', label: '输出节点', description: '最终结果展示' },\n  ];\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能编辑工作流。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* 工具栏 */}\n      <div className=\"bg-white border-b border-gray-200 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <Title level={4} className=\"mb-0\">工作流编辑器</Title>\n            <Text type=\"secondary\">项目: {currentProject.name}</Text>\n          </div>\n          \n          <Space>\n            <Button\n              icon={<SaveOutlined />}\n              onClick={handleSaveWorkflow}\n            >\n              保存工作流\n            </Button>\n\n            <Button\n              onClick={handleCreateSampleWorkflow}\n            >\n              创建示例工作流\n            </Button>\n\n            <Button\n              type=\"primary\"\n              icon={<RobotOutlined />}\n              onClick={() => setIsAIAssistantVisible(true)}\n            >\n              AI工作流助手\n            </Button>\n            \n            {executionContext?.status === 'running' ? (\n              <>\n                <Button \n                  icon={<PauseCircleOutlined />} \n                  onClick={handlePauseExecution}\n                >\n                  暂停\n                </Button>\n                <Button \n                  icon={<StopOutlined />} \n                  danger\n                  onClick={handleStopExecution}\n                >\n                  停止\n                </Button>\n              </>\n            ) : (\n              <Button \n                type=\"primary\"\n                icon={<PlayCircleOutlined />} \n                onClick={handleStartExecution}\n                disabled={currentWorkflow.length === 0}\n              >\n                开始执行\n              </Button>\n            )}\n          </Space>\n        </div>\n\n        {/* 执行状态 */}\n        {executionContext && (\n          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <Text strong>执行状态: {\n                executionContext.status === 'running' ? '运行中' :\n                executionContext.status === 'paused' ? '已暂停' :\n                executionContext.status === 'completed' ? '已完成' :\n                executionContext.status === 'error' ? '错误' : '空闲'\n              }</Text>\n              <Text>进度: {executionContext.progress.percentage.toFixed(1)}%</Text>\n            </div>\n            {executionContext.progress.currentStep && (\n              <Text type=\"secondary\">当前步骤: {executionContext.progress.currentStep}</Text>\n            )}\n          </div>\n        )}\n      </div>\n\n      <div className=\"flex-1 flex\">\n        {/* 节点面板 */}\n        <div className=\"w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto\">\n          <Title level={5}>节点库</Title>\n          <div className=\"space-y-2\">\n            {nodeTypeOptions.map((nodeType) => (\n              <Card \n                key={nodeType.key}\n                size=\"small\" \n                className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                onClick={() => handleAddNode(nodeType.key)}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <Text strong className=\"text-sm\">{nodeType.label}</Text>\n                    <div className=\"text-xs text-gray-500 mt-1\">\n                      {nodeType.description}\n                    </div>\n                  </div>\n                  <PlusOutlined className=\"text-blue-500\" />\n                </div>\n              </Card>\n            ))}\n          </div>\n        </div>\n\n        {/* 工作流画布 */}\n        <div className=\"flex-1 bg-gray-50 relative\">\n          <div className=\"absolute inset-0\">\n            {nodes.length === 0 ? (\n              <div className=\"w-full h-full flex items-center justify-center bg-white\">\n                <div className=\"text-center\">\n                  <Title level={4} type=\"secondary\">工作流画布</Title>\n                  <Text type=\"secondary\">\n                    从左侧节点库中选择节点开始构建您的AI小说生成工作流\n                  </Text>\n                </div>\n              </div>\n            ) : (\n              <ReactFlow\n                nodes={nodes}\n                edges={edges}\n                onNodesChange={onNodesChange}\n                onEdgesChange={onEdgesChange}\n                onConnect={onConnect}\n                nodeTypes={reactFlowNodeTypes}\n                fitView\n                className=\"bg-gray-50\"\n              >\n                <Background />\n                <Controls />\n                <MiniMap\n                  nodeColor={(node) => {\n                    switch (node.data?.status) {\n                      case 'running': return '#3b82f6';\n                      case 'completed': return '#10b981';\n                      case 'error': return '#ef4444';\n                      default: return '#6b7280';\n                    }\n                  }}\n                />\n              </ReactFlow>\n            )}\n          </div>\n        </div>\n\n      </div>\n\n      {/* 节点配置模态框 */}\n      <Modal\n        title=\"节点配置\"\n        open={isConfigModalVisible}\n        onOk={async () => {\n          try {\n            const values = await form.validateFields();\n            if (editingNode) {\n              // 更新节点配置\n              setNodes(nodes =>\n                nodes.map(node =>\n                  node.id === editingNode.id\n                    ? {\n                        ...node,\n                        data: {\n                          ...node.data,\n                          label: values.label,\n                          config: { ...(node.data.config || {}), ...values }\n                        }\n                      }\n                    : node\n                )\n              );\n              message.success('节点配置已更新');\n            }\n            setIsConfigModalVisible(false);\n            form.resetFields();\n          } catch (error) {\n            console.error('配置验证失败:', error);\n          }\n        }}\n        onCancel={() => {\n          setIsConfigModalVisible(false);\n          form.resetFields();\n        }}\n        width={600}\n      >\n        <Form form={form} layout=\"vertical\">\n          <Form.Item\n            name=\"label\"\n            label=\"节点名称\"\n            rules={[{ required: true, message: '请输入节点名称' }]}\n          >\n            <Input placeholder=\"请输入节点名称\" />\n          </Form.Item>\n\n          {editingNode?.type === 'input' && (\n            <>\n              <Form.Item name=\"theme\" label=\"创作主题\">\n                <Input placeholder=\"请输入创作主题\" />\n              </Form.Item>\n              <Form.Item name=\"style\" label=\"写作风格\">\n                <Select placeholder=\"请选择写作风格\">\n                  <Select.Option value=\"轻松幽默\">轻松幽默</Select.Option>\n                  <Select.Option value=\"深沉严肃\">深沉严肃</Select.Option>\n                  <Select.Option value=\"浪漫温馨\">浪漫温馨</Select.Option>\n                </Select>\n              </Form.Item>\n            </>\n          )}\n\n          {editingNode?.type === 'chapter-count-input' && (\n            <Form.Item\n              name=\"chapterCount\"\n              label=\"章节数量\"\n              rules={[{ required: true, message: '请输入章节数量' }]}\n            >\n              <Input type=\"number\" min={1} max={500} placeholder=\"请输入章节数量\" />\n            </Form.Item>\n          )}\n\n          {editingNode?.type === 'title-generator' && (\n            <>\n              <Form.Item name=\"count\" label=\"生成数量\">\n                <Input type=\"number\" min={1} max={20} placeholder=\"生成书名数量\" />\n              </Form.Item>\n              <Form.Item name=\"genre\" label=\"小说类型\">\n                <Select placeholder=\"请选择小说类型\">\n                  <Select.Option value=\"现代都市\">现代都市</Select.Option>\n                  <Select.Option value=\"古代言情\">古代言情</Select.Option>\n                  <Select.Option value=\"玄幻修仙\">玄幻修仙</Select.Option>\n                </Select>\n              </Form.Item>\n            </>\n          )}\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default WorkflowEditor;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAcA;AACA;AA9BA;;;;;;;;;AAkCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,iBAA2B;IAC/B,MAAM,EACJ,cAAc,EACd,eAAe,EACf,OAAO,EACP,UAAU,EACV,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,aAAa,EACd,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,mBAAmB,iBAAiB,iBAAiB,CAAC,eAAe,EAAE,CAAC,GAAG;IAEjF,uBAAuB;IACvB,MAAM,eAAuB,gBAAgB,GAAG,CAAC,CAAA,OAAQ,CAAC;YACxD,IAAI,KAAK,EAAE;YACX,MAAM;YACN,UAAU,KAAK,QAAQ;YACvB,MAAM;gBACJ,OAAO,KAAK,IAAI,CAAC,KAAK;gBACtB,MAAM,KAAK,IAAI;gBACf,QAAQ,KAAK,IAAI,CAAC,MAAM;gBACxB,aAAa,mBAAmB,KAAK,IAAI;gBACzC,QAAQ,KAAK,IAAI,CAAC,MAAM;gBACxB,QAAQ,IAAM,eAAe;gBAC7B,UAAU,IAAM,iBAAiB,KAAK,EAAE;YAC1C;QACF,CAAC;IAED,MAAM,eAAuB,gBAAgB,OAAO,CAAC,CAAA,OACnD,KAAK,WAAW,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC5B,IAAI,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;gBACvC,QAAQ,KAAK,QAAQ;gBACrB,QAAQ,KAAK,QAAQ;gBACrB,MAAM;gBACN,UAAU,KAAK,IAAI,CAAC,MAAM,KAAK;YACjC,CAAC;IAGH,MAAM,CAAC,OAAO,UAAU,cAAc,GAAG,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,UAAU,cAAc,GAAG,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE;IAEvD,MAAM,qBAAgC,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACnD,QAAQ,4IAAA,CAAA,UAAU;QACpB,CAAC,GAAG,EAAE;IAEN,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAuC;YAC3C,SAAS;YACT,mBAAmB;YACnB,oBAAoB;YACpB,qBAAqB;YACrB,iBAAiB;YACjB,oBAAoB;YACpB,qBAAqB;YACrB,uBAAuB;YACvB,oBAAoB;YACpB,qBAAqB;YACrB,oBAAoB;YACpB,uBAAuB;YACvB,aAAa;YACb,QAAQ;YACR,UAAU;QACZ;QACA,OAAO,YAAY,CAAC,SAAS,IAAI;IACnC;IAEA,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,gBAAgB;YAClB,aAAa,eAAe,EAAE;YAC9B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;IACF,GAAG;QAAC;QAAgB;KAAa;IAEjC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,gBAAgB;YAClB,eAAe,eAAe,EAAE,EAAE;YAClC,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf;IACF,GAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,gBAAgB;YAClB,eAAe,eAAe,EAAE;YAChC,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf;IACF,GAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,gBAAgB;YAClB,cAAc,eAAe,EAAE;YAC/B,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf;IACF,GAAG;QAAC;QAAgB;KAAc;IAElC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,eAAe;QACf,KAAK,cAAc,CAAC;YAClB,OAAO,KAAK,IAAI,CAAC,KAAK;YACtB,GAAG,KAAK,IAAI,CAAC,MAAM;QACrB;QACA,wBAAwB;IAC1B,GAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,WAAW;QACX,SAAS,CAAA,QAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,SAAS,CAAA,QAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK;QACxE,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB,GAAG;QAAC;QAAY;QAAU;KAAS;IAEnC,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7C,YAAY;QACZ,SAAS,EAAE;QACX,SAAS,EAAE;QAEX,SAAS;QACT,MAAM,cAAc;YAClB;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;SACD;QAED,SAAS;QACT,MAAM,cAAsB;YAC1B;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;SACD;QAED,SAAS;QACT,SAAS;QAET,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB,GAAG;QAAC;QAAU;QAAU;KAAiB;IAEzC,YAAY;IACZ,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,YAAY;QACZ,SAAS,EAAE;QACX,SAAS,EAAE;QAEX,sBAAsB;QACtB,MAAM,iBAAiB,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC1D,IAAI,CAAC,KAAK,EAAE,OAAO;gBACnB,MAAM;gBACN,UAAU,KAAK,QAAQ;gBACvB,MAAM;oBACJ,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ;oBACR,aAAa,mBAAmB,KAAK,IAAI;oBACzC,QAAQ,KAAK,MAAM,IAAI,CAAC;oBACxB,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB,CAAC,KAAK,EAAE,OAAO;gBAClD;YACF,CAAC;QAED,sBAAsB;QACtB,MAAM,iBAAiB,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAChE,IAAI,CAAC,KAAK,EAAE,OAAO;gBACnB,QAAQ,CAAC,KAAK,EAAE,KAAK,MAAM,EAAE;gBAC7B,QAAQ,CAAC,KAAK,EAAE,KAAK,MAAM,EAAE;gBAC7B,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C,CAAC;QAED,SAAS;QACT,SAAS;QAET,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,OAAO,CAAC;IACjD,GAAG;QAAC;QAAU;QAAU;QAAkB;KAAmB;IAI7D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;QACnC,MAAM,WAAW;YAAE,GAAG,KAAK,MAAM,KAAK;YAAK,GAAG,KAAK,MAAM,KAAK;QAAI;QAElE,kBAAkB;QAClB,MAAM,gBAAsB;YAC1B,IAAI;YACJ,MAAM;YACN;YACA,MAAM;gBACJ,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC;gBACvB,MAAM;gBACN,QAAQ;gBACR,aAAa,mBAAmB;gBAChC,QAAQ,CAAC;gBACT,QAAQ,IAAM,eAAe;wBAAE,IAAI;wBAAQ,MAAM;wBAAU,MAAM;4BAAE,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC;4BAAE,QAAQ,CAAC;4BAAG,QAAQ;wBAAgB;oBAAE;gBAClI,UAAU,IAAM,iBAAiB;YACnC;QACF;QACA,SAAS,CAAA,QAAS;mBAAI;gBAAO;aAAc;QAC3C,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB,GAAG;QAAC;QAAU;QAAgB;KAAiB;IAE/C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,WAAW;QACX,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,EAAE;YACpC,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd;QACF;QAEA,QAAQ;QACR,IAAI,OAAO,MAAM,KAAK,OAAO,MAAM,EAAE;YACnC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAW;QACX,MAAM,UAAU;YACd,GAAG,MAAM;YACT,MAAM;YACN,UAAU;YACV,OAAO;gBACL,QAAQ;gBACR,aAAa;YACf;QACF;QAEA,SAAS,CAAC,MAAQ,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAEnC,kBAAkB;QAClB,IAAI,kBAAkB,OAAO,MAAM,IAAI,OAAO,MAAM,EAAE;YACpD,aAAa,OAAO,MAAM,EAAE,OAAO,MAAM;QAC3C;QAEA,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB,GAAG;QAAC;QAAU;QAAc;KAAe;IAE3C,MAAM,kBAAkB;QACtB;YAAE,KAAK;YAAS,OAAO;YAAQ,aAAa;QAAS;QACrD;YAAE,KAAK;YAAmB,OAAO;YAAQ,aAAa;QAAW;QACjE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAW;QACnE;YAAE,KAAK;YAAiB,OAAO;YAAS,aAAa;QAAW;QAChE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAY;QACnE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAW;QACnE;YAAE,KAAK;YAAuB,OAAO;YAAQ,aAAa;QAAW;QACrE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAa;QACrE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAuB,OAAO;YAAS,aAAa;QAAU;QACrE;YAAE,KAAK;YAAU,OAAO;YAAQ,aAAa;QAAS;KACvD;IAED,IAAI,CAAC,gBAAgB;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,8OAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAO;;;;;;kDAClC,8OAAC;wCAAK,MAAK;;4CAAY;4CAAK,eAAe,IAAI;;;;;;;;;;;;;0CAGjD,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS;kDACV;;;;;;kDAID,8OAAC,kMAAA,CAAA,SAAM;wCACL,SAAS;kDACV;;;;;;kDAID,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;wCACpB,SAAS,IAAM,wBAAwB;kDACxC;;;;;;oCAIA,kBAAkB,WAAW,0BAC5B;;0DACE,8OAAC,kMAAA,CAAA,SAAM;gDACL,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gDAC1B,SAAS;0DACV;;;;;;0DAGD,8OAAC,kMAAA,CAAA,SAAM;gDACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gDACnB,MAAM;gDACN,SAAS;0DACV;;;;;;;qEAKH,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,8NAAA,CAAA,qBAAkB;;;;;wCACzB,SAAS;wCACT,UAAU,gBAAgB,MAAM,KAAK;kDACtC;;;;;;;;;;;;;;;;;;oBAQN,kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,MAAM;;4CAAC;4CACX,iBAAiB,MAAM,KAAK,YAAY,QACxC,iBAAiB,MAAM,KAAK,WAAW,QACvC,iBAAiB,MAAM,KAAK,cAAc,QAC1C,iBAAiB,MAAM,KAAK,UAAU,OAAO;;;;;;;kDAE/C,8OAAC;;4CAAK;4CAAK,iBAAiB,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;4BAE5D,iBAAiB,QAAQ,CAAC,WAAW,kBACpC,8OAAC;gCAAK,MAAK;;oCAAY;oCAAO,iBAAiB,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC,8KAAA,CAAA,OAAI;wCAEH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc,SAAS,GAAG;kDAEzC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,MAAM;4DAAC,WAAU;sEAAW,SAAS,KAAK;;;;;;sEAChD,8OAAC;4DAAI,WAAU;sEACZ,SAAS,WAAW;;;;;;;;;;;;8DAGzB,8OAAC,kNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;uCAZrB,SAAS,GAAG;;;;;;;;;;;;;;;;kCAoBzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,KAAK,kBAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,OAAO;4CAAG,MAAK;sDAAY;;;;;;sDAClC,8OAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;yFAM3B,8OAAC,yKAAA,CAAA,YAAS;gCACR,OAAO;gCACP,OAAO;gCACP,eAAe;gCACf,eAAe;gCACf,WAAW;gCACX,WAAW;gCACX,OAAO;gCACP,WAAU;;kDAEV,8OAAC,yKAAA,CAAA,aAAU;;;;;kDACX,8OAAC,yKAAA,CAAA,WAAQ;;;;;kDACT,8OAAC,yKAAA,CAAA,UAAO;wCACN,WAAW,CAAC;4CACV,OAAQ,KAAK,IAAI,EAAE;gDACjB,KAAK;oDAAW,OAAO;gDACvB,KAAK;oDAAa,OAAO;gDACzB,KAAK;oDAAS,OAAO;gDACrB;oDAAS,OAAO;4CAClB;wCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUZ,8OAAC,gLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,MAAM;oBACJ,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBACxC,IAAI,aAAa;4BACf,SAAS;4BACT,SAAS,CAAA,QACP,MAAM,GAAG,CAAC,CAAA,OACR,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;wCACE,GAAG,IAAI;wCACP,MAAM;4CACJ,GAAG,KAAK,IAAI;4CACZ,OAAO,OAAO,KAAK;4CACnB,QAAQ;gDAAE,GAAI,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;gDAAG,GAAG,MAAM;4CAAC;wCACnD;oCACF,IACA;4BAGR,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;wBAClB;wBACA,wBAAwB;wBACxB,KAAK,WAAW;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B;gBACF;gBACA,UAAU;oBACR,wBAAwB;oBACxB,KAAK,WAAW;gBAClB;gBACA,OAAO;0BAEP,cAAA,8OAAC,8KAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;wBAGpB,aAAa,SAAS,yBACrB;;8CACE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;;;;;;8CAErB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;wBAMnC,aAAa,SAAS,uCACrB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCAAC,MAAK;gCAAS,KAAK;gCAAG,KAAK;gCAAK,aAAY;;;;;;;;;;;wBAItD,aAAa,SAAS,mCACrB;;8CACE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCAAC,MAAK;wCAAS,KAAK;wCAAG,KAAK;wCAAI,aAAY;;;;;;;;;;;8CAEpD,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;uCAEe", "debugId": null}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/project/ProjectOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Card, \n  Button, \n  Space, \n  Typography, \n  Modal, \n  Form, \n  Input, \n  Select, \n  InputNumber,\n  message,\n  Empty,\n  Tag,\n  Popconfirm\n} from 'antd';\nimport { \n  PlusOutlined, \n  EditOutlined, \n  DeleteOutlined,\n  PlayCircleOutlined,\n  FileTextOutlined,\n  CalendarOutlined,\n  SettingOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { Project } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\n\nconst ProjectOverview: React.FC = () => {\n  const { \n    projects, \n    currentProject,\n    createProject, \n    updateProject, \n    deleteProject, \n    setCurrentProject,\n    setActiveTab\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState<Project | null>(null);\n  const [form] = Form.useForm();\n\n  const handleCreateProject = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEditProject = (project: Project) => {\n    setEditingProject(project);\n    form.setFieldsValue({\n      name: project.name,\n      description: project.description,\n      genre: project.settings.genre,\n      style: project.settings.style,\n      targetWordCount: project.settings.targetWordCount,\n      chapterCount: project.settings.chapterCount,\n      language: project.settings.language,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteProject = (projectId: string) => {\n    deleteProject(projectId);\n    message.success('项目已删除');\n  };\n\n  const handleSelectProject = (project: Project) => {\n    setCurrentProject(project.id);\n    message.success(`已选择项目: ${project.name}`);\n  };\n\n  const handleStartWorkflow = (project: Project) => {\n    setCurrentProject(project.id);\n    setActiveTab('workflow');\n    message.info(`正在打开项目 ${project.name} 的工作流编辑器`);\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingProject) {\n        // 更新项目\n        updateProject(editingProject.id, {\n          name: values.name,\n          description: values.description,\n          settings: {\n            genre: values.genre,\n            style: values.style,\n            targetWordCount: values.targetWordCount,\n            chapterCount: values.chapterCount,\n            language: values.language,\n          }\n        });\n        message.success('项目已更新');\n      } else {\n        // 创建新项目\n        createProject({\n          name: values.name,\n          description: values.description,\n          status: 'draft',\n          settings: {\n            genre: values.genre,\n            style: values.style,\n            targetWordCount: values.targetWordCount,\n            chapterCount: values.chapterCount,\n            language: values.language,\n          }\n        });\n        message.success('项目已创建');\n      }\n      \n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n    setEditingProject(null);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'draft': return 'default';\n      case 'in-progress': return 'processing';\n      case 'completed': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'draft': return '草稿';\n      case 'in-progress': return '进行中';\n      case 'completed': return '已完成';\n      default: return '未知';\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>项目总览</Title>\n          <Text type=\"secondary\">管理您的AI小说创作项目</Text>\n        </div>\n        <Button \n          type=\"primary\" \n          icon={<PlusOutlined />}\n          onClick={handleCreateProject}\n        >\n          创建新项目\n        </Button>\n      </div>\n\n      {projects.length === 0 ? (\n        <Card className=\"text-center py-12\">\n          <Empty\n            description={\n              <div>\n                <Text type=\"secondary\">还没有任何项目</Text>\n                <br />\n                <Text type=\"secondary\">点击上方按钮创建您的第一个AI小说项目</Text>\n              </div>\n            }\n          />\n        </Card>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {projects.map((project) => (\n            <Card\n              key={project.id}\n              className={`hover:shadow-lg transition-shadow ${\n                currentProject?.id === project.id ? 'ring-2 ring-blue-500' : ''\n              }`}\n              actions={[\n                <Button \n                  key=\"select\"\n                  type=\"text\" \n                  icon={<SettingOutlined />}\n                  onClick={() => handleSelectProject(project)}\n                >\n                  选择\n                </Button>,\n                <Button \n                  key=\"workflow\"\n                  type=\"text\" \n                  icon={<PlayCircleOutlined />}\n                  onClick={() => handleStartWorkflow(project)}\n                >\n                  工作流\n                </Button>,\n                <Button \n                  key=\"edit\"\n                  type=\"text\" \n                  icon={<EditOutlined />}\n                  onClick={() => handleEditProject(project)}\n                >\n                  编辑\n                </Button>,\n                <Popconfirm\n                  key=\"delete\"\n                  title=\"确定要删除这个项目吗？\"\n                  description=\"删除后将无法恢复，请谨慎操作。\"\n                  onConfirm={() => handleDeleteProject(project.id)}\n                  okText=\"确定\"\n                  cancelText=\"取消\"\n                >\n                  <Button \n                    type=\"text\" \n                    danger\n                    icon={<DeleteOutlined />}\n                  >\n                    删除\n                  </Button>\n                </Popconfirm>\n              ]}\n            >\n              <div className=\"mb-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <Title level={4} className=\"mb-0\">{project.name}</Title>\n                  <Tag color={getStatusColor(project.status)}>\n                    {getStatusText(project.status)}\n                  </Tag>\n                </div>\n                \n                <Paragraph \n                  type=\"secondary\" \n                  ellipsis={{ rows: 2 }}\n                  className=\"mb-3\"\n                >\n                  {project.description}\n                </Paragraph>\n\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">类型:</Text>\n                    <Text>{project.settings.genre}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">风格:</Text>\n                    <Text>{project.settings.style}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">目标字数:</Text>\n                    <Text>{project.settings.targetWordCount.toLocaleString()}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">章节数:</Text>\n                    <Text>{project.settings.chapterCount}</Text>\n                  </div>\n                </div>\n\n                <div className=\"mt-4 pt-3 border-t border-gray-100\">\n                  <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                    <span className=\"flex items-center\">\n                      <CalendarOutlined className=\"mr-1\" />\n                      创建: {new Date(project.createdAt).toLocaleDateString()}\n                    </span>\n                    <span className=\"flex items-center\">\n                      <FileTextOutlined className=\"mr-1\" />\n                      更新: {new Date(project.updatedAt).toLocaleDateString()}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </Card>\n          ))}\n        </div>\n      )}\n\n      {/* 创建/编辑项目模态框 */}\n      <Modal\n        title={editingProject ? '编辑项目' : '创建新项目'}\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={600}\n        okText={editingProject ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            genre: '现代都市',\n            style: '轻松幽默',\n            targetWordCount: 100000,\n            chapterCount: 50,\n            language: 'zh-CN',\n          }}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"项目名称\"\n            rules={[{ required: true, message: '请输入项目名称' }]}\n          >\n            <Input placeholder=\"请输入项目名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"项目描述\"\n            rules={[{ required: true, message: '请输入项目描述' }]}\n          >\n            <Input.TextArea \n              rows={3} \n              placeholder=\"请简要描述您的小说项目...\" \n            />\n          </Form.Item>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"genre\"\n              label=\"小说类型\"\n              rules={[{ required: true, message: '请选择小说类型' }]}\n            >\n              <Select placeholder=\"请选择类型\">\n                <Option value=\"现代都市\">现代都市</Option>\n                <Option value=\"古代言情\">古代言情</Option>\n                <Option value=\"玄幻修仙\">玄幻修仙</Option>\n                <Option value=\"科幻未来\">科幻未来</Option>\n                <Option value=\"悬疑推理\">悬疑推理</Option>\n                <Option value=\"历史军事\">历史军事</Option>\n                <Option value=\"青春校园\">青春校园</Option>\n                <Option value=\"商战职场\">商战职场</Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item\n              name=\"style\"\n              label=\"写作风格\"\n              rules={[{ required: true, message: '请选择写作风格' }]}\n            >\n              <Select placeholder=\"请选择风格\">\n                <Option value=\"轻松幽默\">轻松幽默</Option>\n                <Option value=\"深沉严肃\">深沉严肃</Option>\n                <Option value=\"浪漫温馨\">浪漫温馨</Option>\n                <Option value=\"紧张刺激\">紧张刺激</Option>\n                <Option value=\"文艺清新\">文艺清新</Option>\n                <Option value=\"热血激昂\">热血激昂</Option>\n              </Select>\n            </Form.Item>\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"targetWordCount\"\n              label=\"目标字数\"\n              rules={[{ required: true, message: '请输入目标字数' }]}\n            >\n              <InputNumber\n                min={10000}\n                max={1000000}\n                step={10000}\n                placeholder=\"100000\"\n                className=\"w-full\"\n                formatter={(value) => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n                parser={(value) => value!.replace(/\\$\\s?|(,*)/g, '')}\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"chapterCount\"\n              label=\"预计章节数\"\n              rules={[{ required: true, message: '请输入章节数' }]}\n            >\n              <InputNumber\n                min={1}\n                max={500}\n                placeholder=\"50\"\n                className=\"w-full\"\n              />\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            name=\"language\"\n            label=\"语言\"\n            rules={[{ required: true, message: '请选择语言' }]}\n          >\n            <Select placeholder=\"请选择语言\">\n              <Option value=\"zh-CN\">简体中文</Option>\n              <Option value=\"en-US\">English</Option>\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectOverview;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AA3BA;;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AAEzB,MAAM,kBAA4B;IAChC,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,aAAa,EACb,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,YAAY,EACb,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,KAAK,cAAc,CAAC;YAClB,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,OAAO,QAAQ,QAAQ,CAAC,KAAK;YAC7B,OAAO,QAAQ,QAAQ,CAAC,KAAK;YAC7B,iBAAiB,QAAQ,QAAQ,CAAC,eAAe;YACjD,cAAc,QAAQ,QAAQ,CAAC,YAAY;YAC3C,UAAU,QAAQ,QAAQ,CAAC,QAAQ;QACrC;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,QAAQ,EAAE;QAC5B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,QAAQ,IAAI,EAAE;IAC1C;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,QAAQ,EAAE;QAC5B,aAAa;QACb,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,QAAQ,CAAC;IAC/C;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,gBAAgB;gBAClB,OAAO;gBACP,cAAc,eAAe,EAAE,EAAE;oBAC/B,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,UAAU;wBACR,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,iBAAiB,OAAO,eAAe;wBACvC,cAAc,OAAO,YAAY;wBACjC,UAAU,OAAO,QAAQ;oBAC3B;gBACF;gBACA,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,QAAQ;gBACR,cAAc;oBACZ,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,QAAQ;oBACR,UAAU;wBACR,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,iBAAiB,OAAO,eAAe;wBACvC,cAAc,OAAO,YAAY;wBACjC,UAAU,OAAO,QAAQ;oBAC3B;gBACF;gBACA,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,kBAAkB;YAClB,KAAK,WAAW;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;kCAEzB,8OAAC,kMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wBACnB,SAAS;kCACV;;;;;;;;;;;;YAKF,SAAS,MAAM,KAAK,kBACnB,8OAAC,8KAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gLAAA,CAAA,QAAK;oBACJ,2BACE,8OAAC;;0CACC,8OAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,8OAAC;;;;;0CACD,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;yEAM/B,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,8KAAA,CAAA,OAAI;wBAEH,WAAW,CAAC,kCAAkC,EAC5C,gBAAgB,OAAO,QAAQ,EAAE,GAAG,yBAAyB,IAC7D;wBACF,SAAS;0CACP,8OAAC,kMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;gCACtB,SAAS,IAAM,oBAAoB;0CACpC;+BAJK;;;;;0CAON,8OAAC,kMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,8OAAC,8NAAA,CAAA,qBAAkB;;;;;gCACzB,SAAS,IAAM,oBAAoB;0CACpC;+BAJK;;;;;0CAON,8OAAC,kMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,kBAAkB;0CAClC;+BAJK;;;;;0CAON,8OAAC,0LAAA,CAAA,aAAU;gCAET,OAAM;gCACN,aAAY;gCACZ,WAAW,IAAM,oBAAoB,QAAQ,EAAE;gCAC/C,QAAO;gCACP,YAAW;0CAEX,cAAA,8OAAC,kMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,MAAM;oCACN,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;8CACtB;;;;;;+BAXG;;;;;yBAeP;kCAED,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAQ,QAAQ,IAAI;;;;;;sDAC/C,8OAAC,4KAAA,CAAA,MAAG;4CAAC,OAAO,eAAe,QAAQ,MAAM;sDACtC,cAAc,QAAQ,MAAM;;;;;;;;;;;;8CAIjC,8OAAC;oCACC,MAAK;oCACL,UAAU;wCAAE,MAAM;oCAAE;oCACpB,WAAU;8CAET,QAAQ,WAAW;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,8OAAC;8DAAM,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,8OAAC;8DAAM,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,8OAAC;8DAAM,QAAQ,QAAQ,CAAC,eAAe,CAAC,cAAc;;;;;;;;;;;;sDAExD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,8OAAC;8DAAM,QAAQ,QAAQ,CAAC,YAAY;;;;;;;;;;;;;;;;;;8CAIxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,0NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAAS;oDAChC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;0DAErD,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,0NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAAS;oDAChC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;uBA1FtD,QAAQ,EAAE;;;;;;;;;;0BAqGvB,8OAAC,gLAAA,CAAA,QAAK;gBACJ,OAAO,iBAAiB,SAAS;gBACjC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ,iBAAiB,OAAO;gBAChC,YAAW;0BAEX,cAAA,8OAAC,8KAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;oBACP,eAAe;wBACb,OAAO;wBACP,OAAO;wBACP,iBAAiB;wBACjB,cAAc;wBACd,UAAU;oBACZ;;sCAEA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK,CAAC,QAAQ;gCACb,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;8CAIzB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;sCAK3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,8OAAC,gMAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,aAAY;wCACZ,WAAU;wCACV,WAAW,CAAC,QAAU,GAAG,OAAO,CAAC,OAAO,CAAC,yBAAyB;wCAClE,QAAQ,CAAC,QAAU,MAAO,OAAO,CAAC,eAAe;;;;;;;;;;;8CAIrD,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;8CAE9C,cAAA,8OAAC,gMAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAKhB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,8OAAC,kLAAA,CAAA,SAAM;gCAAC,aAAY;;kDAClB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 2808, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/worldbuilding/WorldBuildingManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Tabs,\n  List,\n  Tag,\n  Popconfirm,\n  message,\n  Row,\n  Col,\n  Divider,\n  Tree,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  GlobalOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EnvironmentOutlined,\n  BookOutlined,\n  SettingOutlined,\n  ThunderboltOutlined,\n  CrownOutlined,\n  HomeOutlined,\n  TeamOutlined,\n  DollarOutlined,\n  HistoryOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { WorldBuilding, WorldCategory } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst WorldBuildingManager: React.FC = () => {\n  const {\n    currentProject,\n    worldBuilding,\n    addWorldElement,\n    updateWorldElement,\n    deleteWorldElement,\n    getWorldElements\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingElement, setEditingElement] = useState<WorldBuilding | null>(null);\n  const [selectedCategory, setSelectedCategory] = useState<WorldCategory | 'all'>('all');\n  const [activeTab, setActiveTab] = useState('elements');\n  const [form] = Form.useForm();\n\n  const projectWorldElements = currentProject ? getWorldElements(currentProject.id) : [];\n\n  // 世界观分类配置\n  const worldCategories = [\n    {\n      key: 'setting' as WorldCategory,\n      label: '环境设定',\n      icon: <EnvironmentOutlined />,\n      color: 'green',\n      description: '地理环境、气候、地形等自然环境设定'\n    },\n    {\n      key: 'magic-system' as WorldCategory,\n      label: '魔法体系',\n      icon: <ThunderboltOutlined />,\n      color: 'purple',\n      description: '魔法规则、能力体系、超自然力量设定'\n    },\n    {\n      key: 'technology' as WorldCategory,\n      label: '科技水平',\n      icon: <SettingOutlined />,\n      color: 'blue',\n      description: '科技发展程度、工具、武器、交通等'\n    },\n    {\n      key: 'culture' as WorldCategory,\n      label: '文化背景',\n      icon: <BookOutlined />,\n      color: 'orange',\n      description: '宗教信仰、传统习俗、艺术文化等'\n    },\n    {\n      key: 'geography' as WorldCategory,\n      label: '地理结构',\n      icon: <GlobalOutlined />,\n      color: 'cyan',\n      description: '大陆分布、城市布局、重要地标等'\n    },\n    {\n      key: 'history' as WorldCategory,\n      label: '历史背景',\n      icon: <HistoryOutlined />,\n      color: 'gold',\n      description: '重大历史事件、时代变迁、传说故事等'\n    },\n    {\n      key: 'society' as WorldCategory,\n      label: '社会制度',\n      icon: <CrownOutlined />,\n      color: 'red',\n      description: '政治体制、社会阶层、法律制度等'\n    },\n    {\n      key: 'economy' as WorldCategory,\n      label: '经济体系',\n      icon: <DollarOutlined />,\n      color: 'lime',\n      description: '货币制度、贸易体系、经济结构等'\n    }\n  ];\n\n  const filteredElements = selectedCategory === 'all'\n    ? projectWorldElements\n    : projectWorldElements.filter(element => element.category === selectedCategory);\n\n  const handleCreateElement = (category?: WorldCategory) => {\n    setEditingElement(null);\n    if (category) {\n      form.setFieldsValue({ category });\n    } else {\n      form.resetFields();\n    }\n    setIsModalVisible(true);\n  };\n\n  const handleEditElement = (element: WorldBuilding) => {\n    setEditingElement(element);\n    form.setFieldsValue({\n      name: element.name,\n      category: element.category,\n      description: element.description,\n      details: element.details,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteElement = (elementId: string) => {\n    if (currentProject) {\n      deleteWorldElement(currentProject.id, elementId);\n      message.success('世界观元素已删除');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (editingElement) {\n        // 更新元素\n        if (currentProject) {\n          updateWorldElement(currentProject.id, editingElement.id, {\n            ...values,\n            relationships: editingElement.relationships || [],\n            consistency: editingElement.consistency || [],\n          });\n          message.success('世界观元素已更新');\n        }\n      } else {\n        // 创建新元素\n        if (currentProject) {\n          addWorldElement(currentProject.id, {\n            ...values,\n            relationships: [],\n            consistency: [],\n          });\n          message.success('世界观元素已创建');\n        }\n      }\n\n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n    setEditingElement(null);\n  };\n\n  const getCategoryConfig = (category: WorldCategory) => {\n    return worldCategories.find(cat => cat.key === category) || worldCategories[0];\n  };\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理世界观。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>世界观管理</Title>\n          <Text type=\"secondary\">管理故事世界观设定和背景 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => handleCreateElement()}\n          >\n            添加设定\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'elements',\n            label: (\n              <span>\n                <GlobalOutlined />\n                世界观元素 ({projectWorldElements.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {/* 分类过滤器 */}\n                <Card className=\"mb-6\" title=\"分类浏览\">\n                  <Row gutter={[16, 16]}>\n                    <Col span={3}>\n                      <Card\n                        size=\"small\"\n                        className={`cursor-pointer transition-all ${\n                          selectedCategory === 'all' ? 'ring-2 ring-blue-500' : ''\n                        }`}\n                        onClick={() => setSelectedCategory('all')}\n                      >\n                        <div className=\"text-center\">\n                          <GlobalOutlined className=\"text-2xl text-gray-500\" />\n                          <div className=\"mt-2\">\n                            <Text strong>全部</Text>\n                            <div className=\"text-xs text-gray-500\">\n                              {projectWorldElements.length} 个元素\n                            </div>\n                          </div>\n                        </div>\n                      </Card>\n                    </Col>\n                    {worldCategories.map((category) => {\n                      const count = projectWorldElements.filter(e => e.category === category.key).length;\n                      return (\n                        <Col span={3} key={category.key}>\n                          <Card\n                            size=\"small\"\n                            className={`cursor-pointer transition-all ${\n                              selectedCategory === category.key ? 'ring-2 ring-blue-500' : ''\n                            }`}\n                            onClick={() => setSelectedCategory(category.key)}\n                          >\n                            <div className=\"text-center\">\n                              <div className={`text-2xl text-${category.color}-500`}>\n                                {category.icon}\n                              </div>\n                              <div className=\"mt-2\">\n                                <Text strong>{category.label}</Text>\n                                <div className=\"text-xs text-gray-500\">\n                                  {count} 个元素\n                                </div>\n                              </div>\n                            </div>\n                          </Card>\n                        </Col>\n                      );\n                    })}\n                  </Row>\n                </Card>\n\n                {/* 快速创建模板 */}\n                <Card className=\"mb-6\" title=\"快速创建\">\n                  <Row gutter={16}>\n                    {worldCategories.slice(0, 4).map((category) => (\n                      <Col span={6} key={category.key}>\n                        <Card\n                          size=\"small\"\n                          className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                          onClick={() => handleCreateElement(category.key)}\n                        >\n                          <div className=\"text-center\">\n                            <div className={`text-xl text-${category.color}-500`}>\n                              {category.icon}\n                            </div>\n                            <div className=\"mt-2\">\n                              <Text strong>{category.label}</Text>\n                              <div className=\"text-xs text-gray-500 mt-1\">\n                                {category.description}\n                              </div>\n                            </div>\n                          </div>\n                        </Card>\n                      </Col>\n                    ))}\n                  </Row>\n                </Card>\n\n                {/* 世界观元素列表 */}\n                {filteredElements.length === 0 ? (\n                  <Card className=\"text-center py-12\">\n                    <GlobalOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                    <div className=\"mt-4\">\n                      <Text type=\"secondary\">\n                        {selectedCategory === 'all' ? '还没有创建任何世界观元素' : `还没有创建${getCategoryConfig(selectedCategory as WorldCategory).label}相关的元素`}\n                      </Text>\n                      <br />\n                      <Text type=\"secondary\">点击上方按钮或使用模板快速创建</Text>\n                    </div>\n                  </Card>\n                ) : (\n                  <List\n                    grid={{ gutter: 16, column: 2 }}\n                    dataSource={filteredElements}\n                    renderItem={(element) => {\n                      const categoryConfig = getCategoryConfig(element.category);\n                      return (\n                        <List.Item>\n                          <Card\n                            className=\"hover:shadow-lg transition-shadow\"\n                            actions={[\n                              <Tooltip title=\"编辑\" key=\"edit\">\n                                <EditOutlined onClick={() => handleEditElement(element)} />\n                              </Tooltip>,\n                              <Popconfirm\n                                key=\"delete\"\n                                title=\"确定要删除这个世界观元素吗？\"\n                                onConfirm={() => handleDeleteElement(element.id)}\n                                okText=\"确定\"\n                                cancelText=\"取消\"\n                              >\n                                <DeleteOutlined />\n                              </Popconfirm>\n                            ]}\n                          >\n                            <div className=\"mb-3\">\n                              <div className=\"flex items-center justify-between mb-2\">\n                                <Title level={5} className=\"mb-0\">{element.name}</Title>\n                                <Tag\n                                  color={categoryConfig.color}\n                                  icon={categoryConfig.icon}\n                                >\n                                  {categoryConfig.label}\n                                </Tag>\n                              </div>\n                              <Paragraph\n                                ellipsis={{ rows: 3 }}\n                                type=\"secondary\"\n                              >\n                                {element.description}\n                              </Paragraph>\n                            </div>\n\n                            {element.details && Object.keys(element.details).length > 0 && (\n                              <div className=\"mt-3 pt-3 border-t border-gray-100\">\n                                <Text type=\"secondary\" className=\"text-sm\">\n                                  包含 {Object.keys(element.details).length} 个详细设定\n                                </Text>\n                              </div>\n                            )}\n\n                            {element.relationships.length > 0 && (\n                              <div className=\"mt-2\">\n                                <Badge count={element.relationships.length} showZero={false}>\n                                  <Text type=\"secondary\" className=\"text-sm\">关联元素</Text>\n                                </Badge>\n                              </div>\n                            )}\n                          </Card>\n                        </List.Item>\n                      );\n                    }}\n                  />\n                )}\n              </div>\n            ),\n          },\n          {\n            key: 'consistency',\n            label: (\n              <span>\n                <CheckCircleOutlined />\n                一致性检查\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <CheckCircleOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">一致性检查功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持世界观元素间的逻辑一致性验证</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n\n      {/* 创建/编辑世界观元素模态框 */}\n      <Modal\n        title={editingElement ? '编辑世界观元素' : '创建世界观元素'}\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={800}\n        okText={editingElement ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"name\"\n                label=\"元素名称\"\n                rules={[{ required: true, message: '请输入元素名称' }]}\n              >\n                <Input placeholder=\"请输入世界观元素名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"category\"\n                label=\"分类\"\n                rules={[{ required: true, message: '请选择分类' }]}\n              >\n                <Select placeholder=\"请选择分类\">\n                  {worldCategories.map((category) => (\n                    <Option key={category.key} value={category.key}>\n                      <Space>\n                        {category.icon}\n                        {category.label}\n                      </Space>\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"基本描述\"\n            rules={[{ required: true, message: '请输入基本描述' }]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请描述这个世界观元素的基本信息...\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"details\" label=\"详细设定\">\n            <TextArea\n              rows={6}\n              placeholder=\"请输入更详细的设定信息，如规则、特点、影响等...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default WorldBuildingManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAzCA;;;;;;AA4CA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,gLAAA,CAAA,QAAK;AAE1B,MAAM,uBAAiC;IACrC,MAAM,EACJ,cAAc,EACd,aAAa,EACb,eAAe,EACf,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EACjB,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,uBAAuB,iBAAiB,iBAAiB,eAAe,EAAE,IAAI,EAAE;IAEtF,UAAU;IACV,MAAM,kBAAkB;QACtB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;YACpB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,mBAAmB,qBAAqB,QAC1C,uBACA,qBAAqB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEhE,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,IAAI,UAAU;YACZ,KAAK,cAAc,CAAC;gBAAE;YAAS;QACjC,OAAO;YACL,KAAK,WAAW;QAClB;QACA,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,KAAK,cAAc,CAAC;YAClB,MAAM,QAAQ,IAAI;YAClB,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,SAAS,QAAQ,OAAO;QAC1B;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,gBAAgB;YAClB,mBAAmB,eAAe,EAAE,EAAE;YACtC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,gBAAgB;gBAClB,OAAO;gBACP,IAAI,gBAAgB;oBAClB,mBAAmB,eAAe,EAAE,EAAE,eAAe,EAAE,EAAE;wBACvD,GAAG,MAAM;wBACT,eAAe,eAAe,aAAa,IAAI,EAAE;wBACjD,aAAa,eAAe,WAAW,IAAI,EAAE;oBAC/C;oBACA,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF,OAAO;gBACL,QAAQ;gBACR,IAAI,gBAAgB;oBAClB,gBAAgB,eAAe,EAAE,EAAE;wBACjC,GAAG,MAAM;wBACT,eAAe,EAAE;wBACjB,aAAa,EAAE;oBACjB;oBACA,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF;YAEA,kBAAkB;YAClB,KAAK,WAAW;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,aAAa,eAAe,CAAC,EAAE;IAChF;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,8OAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,8OAAC;gCAAK,MAAK;;oCAAY;oCAAoB,eAAe,IAAI;;;;;;;;;;;;;kCAEhE,8OAAC,gMAAA,CAAA,QAAK;kCACJ,cAAA,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM;sCAChB;;;;;;;;;;;;;;;;;0BAML,8OAAC,8KAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,8OAAC;;8CACC,8OAAC,sNAAA,CAAA,iBAAc;;;;;gCAAG;gCACV,qBAAqB,MAAM;gCAAC;;;;;;;wBAGxC,wBACE,8OAAC;;8CAEC,8OAAC,8KAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,OAAM;8CAC3B,cAAA,8OAAC,4KAAA,CAAA,MAAG;wCAAC,QAAQ;4CAAC;4CAAI;yCAAG;;0DACnB,8OAAC,4KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAW,CAAC,8BAA8B,EACxC,qBAAqB,QAAQ,yBAAyB,IACtD;oDACF,SAAS,IAAM,oBAAoB;8DAEnC,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,sNAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;0EAC1B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,MAAM;kFAAC;;;;;;kFACb,8OAAC;wEAAI,WAAU;;4EACZ,qBAAqB,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAMtC,gBAAgB,GAAG,CAAC,CAAC;gDACpB,MAAM,QAAQ,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,GAAG,EAAE,MAAM;gDAClF,qBACE,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAW,CAAC,8BAA8B,EACxC,qBAAqB,SAAS,GAAG,GAAG,yBAAyB,IAC7D;wDACF,SAAS,IAAM,oBAAoB,SAAS,GAAG;kEAE/C,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAW,CAAC,cAAc,EAAE,SAAS,KAAK,CAAC,IAAI,CAAC;8EAClD,SAAS,IAAI;;;;;;8EAEhB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,MAAM;sFAAE,SAAS,KAAK;;;;;;sFAC5B,8OAAC;4EAAI,WAAU;;gFACZ;gFAAM;;;;;;;;;;;;;;;;;;;;;;;;mDAfE,SAAS,GAAG;;;;;4CAsBnC;;;;;;;;;;;;8CAKJ,8OAAC,8KAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,OAAM;8CAC3B,cAAA,8OAAC,4KAAA,CAAA,MAAG;wCAAC,QAAQ;kDACV,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAChC,8OAAC,4KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,oBAAoB,SAAS,GAAG;8DAE/C,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,aAAa,EAAE,SAAS,KAAK,CAAC,IAAI,CAAC;0EACjD,SAAS,IAAI;;;;;;0EAEhB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,MAAM;kFAAE,SAAS,KAAK;;;;;;kFAC5B,8OAAC;wEAAI,WAAU;kFACZ,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;+CAbZ,SAAS,GAAG;;;;;;;;;;;;;;;gCAwBpC,iBAAiB,MAAM,KAAK,kBAC3B,8OAAC,8KAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,sNAAA,CAAA,iBAAc;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACxD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,MAAK;8DACR,qBAAqB,QAAQ,iBAAiB,CAAC,KAAK,EAAE,kBAAkB,kBAAmC,KAAK,CAAC,KAAK,CAAC;;;;;;8DAE1H,8OAAC;;;;;8DACD,8OAAC;oDAAK,MAAK;8DAAY;;;;;;;;;;;;;;;;;2DAI3B,8OAAC,8KAAA,CAAA,OAAI;oCACH,MAAM;wCAAE,QAAQ;wCAAI,QAAQ;oCAAE;oCAC9B,YAAY;oCACZ,YAAY,CAAC;wCACX,MAAM,iBAAiB,kBAAkB,QAAQ,QAAQ;wCACzD,qBACE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;sDACR,cAAA,8OAAC,8KAAA,CAAA,OAAI;gDACH,WAAU;gDACV,SAAS;kEACP,8OAAC,oLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,8OAAC,kNAAA,CAAA,eAAY;4DAAC,SAAS,IAAM,kBAAkB;;;;;;uDADzB;;;;;kEAGxB,8OAAC,0LAAA,CAAA,aAAU;wDAET,OAAM;wDACN,WAAW,IAAM,oBAAoB,QAAQ,EAAE;wDAC/C,QAAO;wDACP,YAAW;kEAEX,cAAA,8OAAC,sNAAA,CAAA,iBAAc;;;;;uDANX;;;;;iDAQP;;kEAED,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,OAAO;wEAAG,WAAU;kFAAQ,QAAQ,IAAI;;;;;;kFAC/C,8OAAC,4KAAA,CAAA,MAAG;wEACF,OAAO,eAAe,KAAK;wEAC3B,MAAM,eAAe,IAAI;kFAExB,eAAe,KAAK;;;;;;;;;;;;0EAGzB,8OAAC;gEACC,UAAU;oEAAE,MAAM;gEAAE;gEACpB,MAAK;0EAEJ,QAAQ,WAAW;;;;;;;;;;;;oDAIvB,QAAQ,OAAO,IAAI,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE,MAAM,GAAG,mBACxD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,MAAK;4DAAY,WAAU;;gEAAU;gEACrC,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE,MAAM;gEAAC;;;;;;;;;;;;oDAK7C,QAAQ,aAAa,CAAC,MAAM,GAAG,mBAC9B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gLAAA,CAAA,QAAK;4DAAC,OAAO,QAAQ,aAAa,CAAC,MAAM;4DAAE,UAAU;sEACpD,cAAA,8OAAC;gEAAK,MAAK;gEAAY,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAOzD;;;;;;;;;;;;oBAKV;oBACA;wBACE,KAAK;wBACL,qBACE,8OAAC;;8CACC,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gCAAG;;;;;;;wBAI3B,wBACE,8OAAC,8KAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gOAAA,CAAA,sBAAmB;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CAC7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,8OAAC;;;;;sDACD,8OAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;0BAIH,8OAAC,gLAAA,CAAA,QAAK;gBACJ,OAAO,iBAAiB,YAAY;gBACpC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ,iBAAiB,OAAO;gBAChC,YAAW;0BAEX,cAAA,8OAAC,8KAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;yCAAE;kDAE7C,cAAA,8OAAC,kLAAA,CAAA,SAAM;4CAAC,aAAY;sDACjB,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC;oDAA0B,OAAO,SAAS,GAAG;8DAC5C,cAAA,8OAAC,gMAAA,CAAA,QAAK;;4DACH,SAAS,IAAI;4DACb,SAAS,KAAK;;;;;;;mDAHN,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYnC,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,8OAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAU,OAAM;sCAC9B,cAAA,8OAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;uCAEe", "debugId": null}}, {"offset": {"line": 3777, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/document/DocumentManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Tree,\n  Tabs,\n  List,\n  Tag,\n  message,\n  Row,\n  Col,\n  Progress,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  FolderOutlined,\n  FileOutlined,\n  DownloadOutlined,\n  UploadOutlined,\n  SearchOutlined,\n  HistoryOutlined,\n  FileTextOutlined,\n  FilePdfOutlined,\n  FileWordOutlined,\n  FileExcelOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\n\nconst { Title, Text } = Typography;\n\nconst DocumentManager: React.FC = () => {\n  const { currentProject } = useAppStore();\n  const [activeTab, setActiveTab] = useState('structure');\n\n  // 模拟文档结构\n  const documentStructure = [\n    {\n      title: 'workflow-configs',\n      key: 'workflow-configs',\n      icon: <FolderOutlined />,\n      children: [\n        { title: 'main-workflow.json', key: 'main-workflow', icon: <FileOutlined /> },\n        { title: 'backup-workflow.json', key: 'backup-workflow', icon: <FileOutlined /> },\n      ]\n    },\n    {\n      title: 'prompts',\n      key: 'prompts',\n      icon: <FolderOutlined />,\n      children: [\n        { title: 'character-prompts.md', key: 'character-prompts', icon: <FileTextOutlined /> },\n        { title: 'plot-prompts.md', key: 'plot-prompts', icon: <FileTextOutlined /> },\n      ]\n    },\n    {\n      title: 'generated-content',\n      key: 'generated-content',\n      icon: <FolderOutlined />,\n      children: [\n        {\n          title: 'outlines',\n          key: 'outlines',\n          icon: <FolderOutlined />,\n          children: [\n            { title: 'main-outline.md', key: 'main-outline', icon: <FileTextOutlined /> },\n            { title: 'detailed-outline.md', key: 'detailed-outline', icon: <FileTextOutlined /> },\n          ]\n        },\n        {\n          title: 'characters',\n          key: 'characters',\n          icon: <FolderOutlined />,\n          children: [\n            { title: 'protagonist.json', key: 'protagonist', icon: <FileOutlined /> },\n            { title: 'supporting-characters.json', key: 'supporting', icon: <FileOutlined /> },\n          ]\n        },\n        {\n          title: 'chapters',\n          key: 'chapters',\n          icon: <FolderOutlined />,\n          children: [\n            { title: 'chapter-01.md', key: 'chapter-01', icon: <FileTextOutlined /> },\n            { title: 'chapter-02.md', key: 'chapter-02', icon: <FileTextOutlined /> },\n            { title: 'chapter-03.md', key: 'chapter-03', icon: <FileTextOutlined /> },\n          ]\n        }\n      ]\n    },\n    {\n      title: 'exports',\n      key: 'exports',\n      icon: <FolderOutlined />,\n      children: [\n        { title: 'novel-draft.docx', key: 'novel-docx', icon: <FileWordOutlined /> },\n        { title: 'novel-draft.pdf', key: 'novel-pdf', icon: <FilePdfOutlined /> },\n      ]\n    }\n  ];\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理文档。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>文档管理</Title>\n          <Text type=\"secondary\">管理项目文档和文件 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button icon={<UploadOutlined />}>\n            导入文档\n          </Button>\n          <Button type=\"primary\" icon={<DownloadOutlined />}>\n            导出项目\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'structure',\n            label: (\n              <span>\n                <FolderOutlined />\n                文档结构\n              </span>\n            ),\n            children: (\n              <Row gutter={16}>\n                <Col span={8}>\n                  <Card title=\"项目文档树\">\n                    <Tree\n                      showIcon\n                      defaultExpandAll\n                      treeData={documentStructure}\n                      onSelect={(keys, info) => {\n                        if (keys.length > 0) {\n                          message.info(`选中文件: ${info.node.title}`);\n                        }\n                      }}\n                    />\n                  </Card>\n                </Col>\n                <Col span={16}>\n                  <Card title=\"文档预览\">\n                    <div className=\"text-center py-12\">\n                      <FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                      <div className=\"mt-4\">\n                        <Text type=\"secondary\">选择左侧文档查看内容</Text>\n                        <br />\n                        <Text type=\"secondary\">支持Markdown、JSON、文本文件预览</Text>\n                      </div>\n                    </div>\n                  </Card>\n                </Col>\n              </Row>\n            ),\n          },\n          {\n            key: 'versions',\n            label: (\n              <span>\n                <HistoryOutlined />\n                版本历史\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <HistoryOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">版本控制功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持文档版本管理和历史对比</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n    </div>\n  );\n};\n\nexport default DocumentManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AA/BA;;;;;;AAiCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,kBAA4B;IAChC,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,MAAM,oBAAoB;QACxB;YACE,OAAO;YACP,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAsB,KAAK;oBAAiB,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBAAI;gBAC5E;oBAAE,OAAO;oBAAwB,KAAK;oBAAmB,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gBAAI;aACjF;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAwB,KAAK;oBAAqB,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBAAI;gBACtF;oBAAE,OAAO;oBAAmB,KAAK;oBAAgB,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBAAI;aAC7E;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBACE,OAAO;oBACP,KAAK;oBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;oBACrB,UAAU;wBACR;4BAAE,OAAO;4BAAmB,KAAK;4BAAgB,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wBAAI;wBAC5E;4BAAE,OAAO;4BAAuB,KAAK;4BAAoB,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wBAAI;qBACrF;gBACH;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;oBACrB,UAAU;wBACR;4BAAE,OAAO;4BAAoB,KAAK;4BAAe,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wBAAI;wBACxE;4BAAE,OAAO;4BAA8B,KAAK;4BAAc,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wBAAI;qBAClF;gBACH;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;oBACrB,UAAU;wBACR;4BAAE,OAAO;4BAAiB,KAAK;4BAAc,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wBAAI;wBACxE;4BAAE,OAAO;4BAAiB,KAAK;4BAAc,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wBAAI;wBACxE;4BAAE,OAAO;4BAAiB,KAAK;4BAAc,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;wBAAI;qBACzE;gBACH;aACD;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAoB,KAAK;oBAAc,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gBAAI;gBAC3E;oBAAE,OAAO;oBAAmB,KAAK;oBAAa,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;gBAAI;aACzE;QACH;KACD;IAED,IAAI,CAAC,gBAAgB;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,8OAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,8OAAC;gCAAK,MAAK;;oCAAY;oCAAiB,eAAe,IAAI;;;;;;;;;;;;;kCAE7D,8OAAC,gMAAA,CAAA,QAAK;;0CACJ,8OAAC,kMAAA,CAAA,SAAM;gCAAC,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;0CAAK;;;;;;0CAGlC,8OAAC,kMAAA,CAAA,SAAM;gCAAC,MAAK;gCAAU,oBAAM,8OAAC,0NAAA,CAAA,mBAAgB;;;;;0CAAK;;;;;;;;;;;;;;;;;;0BAMvD,8OAAC,8KAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,8OAAC;;8CACC,8OAAC,sNAAA,CAAA,iBAAc;;;;;gCAAG;;;;;;;wBAItB,wBACE,8OAAC,4KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;wCAAC,OAAM;kDACV,cAAA,8OAAC,8KAAA,CAAA,OAAI;4CACH,QAAQ;4CACR,gBAAgB;4CAChB,UAAU;4CACV,UAAU,CAAC,MAAM;gDACf,IAAI,KAAK,MAAM,GAAG,GAAG;oDACnB,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,KAAK,EAAE;gDACzC;4CACF;;;;;;;;;;;;;;;;8CAIN,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;wCAAC,OAAM;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0NAAA,CAAA,mBAAgB;oDAAC,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAU;;;;;;8DAC1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,MAAK;sEAAY;;;;;;sEACvB,8OAAC;;;;;sEACD,8OAAC;4DAAK,MAAK;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOrC;oBACA;wBACE,KAAK;wBACL,qBACE,8OAAC;;8CACC,8OAAC,wNAAA,CAAA,kBAAe;;;;;gCAAG;;;;;;;wBAIvB,wBACE,8OAAC,8KAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,wNAAA,CAAA,kBAAe;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,8OAAC;;;;;sDACD,8OAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 4311, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/prompt/PromptManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Tabs,\n  List,\n  Tag,\n  Rate,\n  message,\n  Row,\n  Col,\n  Divider,\n  Badge,\n  Progress\n} from 'antd';\nimport {\n  FileTextOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  CopyOutlined,\n  StarOutlined,\n  ExperimentOutlined,\n  Bar<PERSON>hartOutlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { PromptTemplate } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst PromptManager: React.FC = () => {\n  const {\n    promptTemplates,\n    addPromptTemplate,\n    updatePromptTemplate,\n    deletePromptTemplate,\n    getPromptTemplatesByCategory\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);\n  const [activeTab, setActiveTab] = useState('templates');\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [form] = Form.useForm();\n\n  // 提示词分类\n  const categories = [\n    { key: 'character', label: '角色生成', color: 'blue' },\n    { key: 'plot', label: '情节规划', color: 'green' },\n    { key: 'dialogue', label: '对话生成', color: 'orange' },\n    { key: 'description', label: '场景描述', color: 'purple' },\n    { key: 'title', label: '书名生成', color: 'red' },\n    { key: 'outline', label: '大纲规划', color: 'cyan' },\n    { key: 'worldbuilding', label: '世界观构建', color: 'gold' },\n    { key: 'polish', label: '内容润色', color: 'lime' },\n  ];\n\n  // 模拟提示词模板数据\n  const sampleTemplates: PromptTemplate[] = [\n    {\n      id: '1',\n      name: '主角角色生成',\n      category: 'character',\n      template: '请为一部{genre}小说创建一个{age}岁的{gender}主角。角色应该具有{personality}的性格特征，背景设定为{background}。请详细描述角色的外貌、性格、能力和成长经历。',\n      variables: [\n        { name: 'genre', type: 'string', description: '小说类型', defaultValue: '现代都市', required: true },\n        { name: 'age', type: 'number', description: '角色年龄', defaultValue: 25, required: true },\n        { name: 'gender', type: 'string', description: '角色性别', defaultValue: '男', required: true },\n        { name: 'personality', type: 'string', description: '性格特征', defaultValue: '勇敢、善良', required: true },\n        { name: 'background', type: 'string', description: '背景设定', defaultValue: '普通家庭', required: false },\n      ],\n      version: 1,\n      isActive: true,\n      performance: {\n        usage: 156,\n        rating: 4.5,\n        feedback: ['生成的角色很有特色', '背景设定合理', '可以增加更多细节'],\n      }\n    },\n    {\n      id: '2',\n      name: '情节冲突生成',\n      category: 'plot',\n      template: '为{genre}类型的小说设计一个{conflict_type}冲突。冲突应该涉及{characters}，发生在{setting}环境中。请描述冲突的起因、发展过程和可能的解决方案。',\n      variables: [\n        { name: 'genre', type: 'string', description: '小说类型', required: true },\n        { name: 'conflict_type', type: 'string', description: '冲突类型', required: true },\n        { name: 'characters', type: 'array', description: '涉及角色', required: true },\n        { name: 'setting', type: 'string', description: '场景设定', required: true },\n      ],\n      version: 2,\n      isActive: true,\n      performance: {\n        usage: 89,\n        rating: 4.2,\n        feedback: ['冲突设计有趣', '逻辑性强'],\n      }\n    }\n  ];\n\n  const filteredTemplates = selectedCategory === 'all'\n    ? sampleTemplates\n    : sampleTemplates.filter(template => template.category === selectedCategory);\n\n  const handleCreateTemplate = () => {\n    setEditingTemplate(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEditTemplate = (template: PromptTemplate) => {\n    setEditingTemplate(template);\n    form.setFieldsValue({\n      name: template.name,\n      category: template.category,\n      template: template.template,\n    });\n    setIsModalVisible(true);\n  };\n\n  const getCategoryConfig = (category: string) => {\n    return categories.find(cat => cat.key === category) || categories[0];\n  };\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>提示词管理</Title>\n          <Text type=\"secondary\">管理AI提示词模板和配置</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={handleCreateTemplate}\n          >\n            创建模板\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'templates',\n            label: (\n              <span>\n                <FileTextOutlined />\n                提示词模板 ({filteredTemplates.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {/* 分类过滤器 */}\n                <Card className=\"mb-6\">\n                  <Row gutter={16}>\n                    <Col span={3}>\n                      <Card\n                        size=\"small\"\n                        className={`cursor-pointer transition-all ${\n                          selectedCategory === 'all' ? 'ring-2 ring-blue-500' : ''\n                        }`}\n                        onClick={() => setSelectedCategory('all')}\n                      >\n                        <div className=\"text-center\">\n                          <FileTextOutlined className=\"text-2xl text-gray-500\" />\n                          <div className=\"mt-2\">\n                            <Text strong>全部</Text>\n                            <div className=\"text-xs text-gray-500\">\n                              {sampleTemplates.length} 个模板\n                            </div>\n                          </div>\n                        </div>\n                      </Card>\n                    </Col>\n                    {categories.map((category) => {\n                      const count = sampleTemplates.filter(t => t.category === category.key).length;\n                      return (\n                        <Col span={3} key={category.key}>\n                          <Card\n                            size=\"small\"\n                            className={`cursor-pointer transition-all ${\n                              selectedCategory === category.key ? 'ring-2 ring-blue-500' : ''\n                            }`}\n                            onClick={() => setSelectedCategory(category.key)}\n                          >\n                            <div className=\"text-center\">\n                              <FileTextOutlined className={`text-2xl text-${category.color}-500`} />\n                              <div className=\"mt-2\">\n                                <Text strong>{category.label}</Text>\n                                <div className=\"text-xs text-gray-500\">\n                                  {count} 个模板\n                                </div>\n                              </div>\n                            </div>\n                          </Card>\n                        </Col>\n                      );\n                    })}\n                  </Row>\n                </Card>\n\n                {/* 模板列表 */}\n                <List\n                  grid={{ gutter: 16, column: 1 }}\n                  dataSource={filteredTemplates}\n                  renderItem={(template) => {\n                    const categoryConfig = getCategoryConfig(template.category);\n                    return (\n                      <List.Item>\n                        <Card\n                          className=\"hover:shadow-lg transition-shadow\"\n                          actions={[\n                            <Button\n                              key=\"copy\"\n                              type=\"text\"\n                              icon={<CopyOutlined />}\n                              onClick={() => {\n                                navigator.clipboard.writeText(template.template);\n                                message.success('模板已复制到剪贴板');\n                              }}\n                            >\n                              复制\n                            </Button>,\n                            <Button\n                              key=\"edit\"\n                              type=\"text\"\n                              icon={<EditOutlined />}\n                              onClick={() => handleEditTemplate(template)}\n                            >\n                              编辑\n                            </Button>,\n                            <Button\n                              key=\"delete\"\n                              type=\"text\"\n                              danger\n                              icon={<DeleteOutlined />}\n                              onClick={() => {\n                                message.info('删除功能开发中');\n                              }}\n                            >\n                              删除\n                            </Button>\n                          ]}\n                        >\n                          <Row gutter={24}>\n                            <Col span={16}>\n                              <div className=\"mb-3\">\n                                <div className=\"flex items-center justify-between mb-2\">\n                                  <Title level={5} className=\"mb-0\">{template.name}</Title>\n                                  <Space>\n                                    <Tag color={categoryConfig.color}>\n                                      {categoryConfig.label}\n                                    </Tag>\n                                    <Tag color={template.isActive ? 'green' : 'default'}>\n                                      {template.isActive ? '启用' : '禁用'}\n                                    </Tag>\n                                    <Text type=\"secondary\">v{template.version}</Text>\n                                  </Space>\n                                </div>\n\n                                <Paragraph\n                                  ellipsis={{ rows: 2 }}\n                                  type=\"secondary\"\n                                  className=\"mb-3\"\n                                >\n                                  {template.template}\n                                </Paragraph>\n\n                                <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                                  <span>变量: {template.variables.length} 个</span>\n                                  <span>使用: {template.performance.usage} 次</span>\n                                  <div className=\"flex items-center space-x-1\">\n                                    <Rate\n                                      disabled\n                                      value={template.performance.rating}\n                                      allowHalf\n                                      style={{ fontSize: 12 }}\n                                    />\n                                    <Text type=\"secondary\">({template.performance.rating})</Text>\n                                  </div>\n                                </div>\n                              </div>\n                            </Col>\n                            <Col span={8}>\n                              <div className=\"text-center\">\n                                <div className=\"mb-2\">\n                                  <Text strong className=\"text-lg\">{template.performance.rating}</Text>\n                                  <Text type=\"secondary\" className=\"ml-1\">/5.0</Text>\n                                </div>\n                                <Progress\n                                  percent={template.performance.rating * 20}\n                                  size=\"small\"\n                                  showInfo={false}\n                                />\n                                <div className=\"mt-2\">\n                                  <Badge count={template.performance.usage} showZero>\n                                    <Text type=\"secondary\" className=\"text-sm\">使用次数</Text>\n                                  </Badge>\n                                </div>\n                              </div>\n                            </Col>\n                          </Row>\n                        </Card>\n                      </List.Item>\n                    );\n                  }}\n                />\n              </div>\n            ),\n          },\n          {\n            key: 'analytics',\n            label: (\n              <span>\n                <BarChartOutlined />\n                性能分析\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <BarChartOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">性能分析功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持模板使用统计和效果分析</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n\n      {/* 创建/编辑模板模态框 */}\n      <Modal\n        title={editingTemplate ? '编辑提示词模板' : '创建提示词模板'}\n        open={isModalVisible}\n        onOk={async () => {\n          try {\n            const values = await form.validateFields();\n            message.success(editingTemplate ? '模板已更新' : '模板已创建');\n            setIsModalVisible(false);\n            form.resetFields();\n          } catch (error) {\n            console.error('表单验证失败:', error);\n          }\n        }}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n        }}\n        width={800}\n        okText={editingTemplate ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"name\"\n                label=\"模板名称\"\n                rules={[{ required: true, message: '请输入模板名称' }]}\n              >\n                <Input placeholder=\"请输入模板名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"category\"\n                label=\"分类\"\n                rules={[{ required: true, message: '请选择分类' }]}\n              >\n                <Select placeholder=\"请选择分类\">\n                  {categories.map((category) => (\n                    <Option key={category.key} value={category.key}>\n                      {category.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"template\"\n            label=\"提示词模板\"\n            rules={[{ required: true, message: '请输入提示词模板' }]}\n            help=\"使用 {变量名} 的格式定义变量，如 {genre}、{character_name} 等\"\n          >\n            <TextArea\n              rows={8}\n              placeholder=\"请输入提示词模板，使用 {变量名} 定义可替换的变量...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default PromptManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAlCA;;;;;;AAqCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,gLAAA,CAAA,QAAK;AAE1B,MAAM,gBAA0B;IAC9B,MAAM,EACJ,eAAe,EACf,iBAAiB,EACjB,oBAAoB,EACpB,oBAAoB,EACpB,4BAA4B,EAC7B,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,QAAQ;IACR,MAAM,aAAa;QACjB;YAAE,KAAK;YAAa,OAAO;YAAQ,OAAO;QAAO;QACjD;YAAE,KAAK;YAAQ,OAAO;YAAQ,OAAO;QAAQ;QAC7C;YAAE,KAAK;YAAY,OAAO;YAAQ,OAAO;QAAS;QAClD;YAAE,KAAK;YAAe,OAAO;YAAQ,OAAO;QAAS;QACrD;YAAE,KAAK;YAAS,OAAO;YAAQ,OAAO;QAAM;QAC5C;YAAE,KAAK;YAAW,OAAO;YAAQ,OAAO;QAAO;QAC/C;YAAE,KAAK;YAAiB,OAAO;YAAS,OAAO;QAAO;QACtD;YAAE,KAAK;YAAU,OAAO;YAAQ,OAAO;QAAO;KAC/C;IAED,YAAY;IACZ,MAAM,kBAAoC;QACxC;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;gBACT;oBAAE,MAAM;oBAAS,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAQ,UAAU;gBAAK;gBAC3F;oBAAE,MAAM;oBAAO,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAI,UAAU;gBAAK;gBACrF;oBAAE,MAAM;oBAAU,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAK,UAAU;gBAAK;gBACzF;oBAAE,MAAM;oBAAe,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAS,UAAU;gBAAK;gBAClG;oBAAE,MAAM;oBAAc,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAQ,UAAU;gBAAM;aAClG;YACD,SAAS;YACT,UAAU;YACV,aAAa;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;oBAAC;oBAAa;oBAAU;iBAAW;YAC/C;QACF;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;gBACT;oBAAE,MAAM;oBAAS,MAAM;oBAAU,aAAa;oBAAQ,UAAU;gBAAK;gBACrE;oBAAE,MAAM;oBAAiB,MAAM;oBAAU,aAAa;oBAAQ,UAAU;gBAAK;gBAC7E;oBAAE,MAAM;oBAAc,MAAM;oBAAS,aAAa;oBAAQ,UAAU;gBAAK;gBACzE;oBAAE,MAAM;oBAAW,MAAM;oBAAU,aAAa;oBAAQ,UAAU;gBAAK;aACxE;YACD,SAAS;YACT,UAAU;YACV,aAAa;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;oBAAC;oBAAU;iBAAO;YAC9B;QACF;KACD;IAED,MAAM,oBAAoB,qBAAqB,QAC3C,kBACA,gBAAgB,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;IAE7D,MAAM,uBAAuB;QAC3B,mBAAmB;QACnB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,KAAK,cAAc,CAAC;YAClB,MAAM,SAAS,IAAI;YACnB,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ;QAC7B;QACA,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,aAAa,UAAU,CAAC,EAAE;IACtE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,8OAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;kCAEzB,8OAAC,gMAAA,CAAA,QAAK;kCACJ,cAAA,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4BACnB,SAAS;sCACV;;;;;;;;;;;;;;;;;0BAML,8OAAC,8KAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,8OAAC;;8CACC,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gCAAG;gCACZ,kBAAkB,MAAM;gCAAC;;;;;;;wBAGrC,wBACE,8OAAC;;8CAEC,8OAAC,8KAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,4KAAA,CAAA,MAAG;wCAAC,QAAQ;;0DACX,8OAAC,4KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAW,CAAC,8BAA8B,EACxC,qBAAqB,QAAQ,yBAAyB,IACtD;oDACF,SAAS,IAAM,oBAAoB;8DAEnC,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0NAAA,CAAA,mBAAgB;gEAAC,WAAU;;;;;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,MAAM;kFAAC;;;;;;kFACb,8OAAC;wEAAI,WAAU;;4EACZ,gBAAgB,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAMjC,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,GAAG,EAAE,MAAM;gDAC7E,qBACE,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAW,CAAC,8BAA8B,EACxC,qBAAqB,SAAS,GAAG,GAAG,yBAAyB,IAC7D;wDACF,SAAS,IAAM,oBAAoB,SAAS,GAAG;kEAE/C,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0NAAA,CAAA,mBAAgB;oEAAC,WAAW,CAAC,cAAc,EAAE,SAAS,KAAK,CAAC,IAAI,CAAC;;;;;;8EAClE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,MAAM;sFAAE,SAAS,KAAK;;;;;;sFAC5B,8OAAC;4EAAI,WAAU;;gFACZ;gFAAM;;;;;;;;;;;;;;;;;;;;;;;;mDAbE,SAAS,GAAG;;;;;4CAoBnC;;;;;;;;;;;;8CAKJ,8OAAC,8KAAA,CAAA,OAAI;oCACH,MAAM;wCAAE,QAAQ;wCAAI,QAAQ;oCAAE;oCAC9B,YAAY;oCACZ,YAAY,CAAC;wCACX,MAAM,iBAAiB,kBAAkB,SAAS,QAAQ;wCAC1D,qBACE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;sDACR,cAAA,8OAAC,8KAAA,CAAA,OAAI;gDACH,WAAU;gDACV,SAAS;kEACP,8OAAC,kMAAA,CAAA,SAAM;wDAEL,MAAK;wDACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wDACnB,SAAS;4DACP,UAAU,SAAS,CAAC,SAAS,CAAC,SAAS,QAAQ;4DAC/C,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;wDAClB;kEACD;uDAPK;;;;;kEAUN,8OAAC,kMAAA,CAAA,SAAM;wDAEL,MAAK;wDACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wDACnB,SAAS,IAAM,mBAAmB;kEACnC;uDAJK;;;;;kEAON,8OAAC,kMAAA,CAAA,SAAM;wDAEL,MAAK;wDACL,MAAM;wDACN,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wDACrB,SAAS;4DACP,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;wDACf;kEACD;uDAPK;;;;;iDAUP;0DAED,cAAA,8OAAC,4KAAA,CAAA,MAAG;oDAAC,QAAQ;;sEACX,8OAAC,4KAAA,CAAA,MAAG;4DAAC,MAAM;sEACT,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAM,OAAO;gFAAG,WAAU;0FAAQ,SAAS,IAAI;;;;;;0FAChD,8OAAC,gMAAA,CAAA,QAAK;;kGACJ,8OAAC,4KAAA,CAAA,MAAG;wFAAC,OAAO,eAAe,KAAK;kGAC7B,eAAe,KAAK;;;;;;kGAEvB,8OAAC,4KAAA,CAAA,MAAG;wFAAC,OAAO,SAAS,QAAQ,GAAG,UAAU;kGACvC,SAAS,QAAQ,GAAG,OAAO;;;;;;kGAE9B,8OAAC;wFAAK,MAAK;;4FAAY;4FAAE,SAAS,OAAO;;;;;;;;;;;;;;;;;;;kFAI7C,8OAAC;wEACC,UAAU;4EAAE,MAAM;wEAAE;wEACpB,MAAK;wEACL,WAAU;kFAET,SAAS,QAAQ;;;;;;kFAGpB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;oFAAK;oFAAK,SAAS,SAAS,CAAC,MAAM;oFAAC;;;;;;;0FACrC,8OAAC;;oFAAK;oFAAK,SAAS,WAAW,CAAC,KAAK;oFAAC;;;;;;;0FACtC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,8KAAA,CAAA,OAAI;wFACH,QAAQ;wFACR,OAAO,SAAS,WAAW,CAAC,MAAM;wFAClC,SAAS;wFACT,OAAO;4FAAE,UAAU;wFAAG;;;;;;kGAExB,8OAAC;wFAAK,MAAK;;4FAAY;4FAAE,SAAS,WAAW,CAAC,MAAM;4FAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAK7D,8OAAC,4KAAA,CAAA,MAAG;4DAAC,MAAM;sEACT,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,MAAM;gFAAC,WAAU;0FAAW,SAAS,WAAW,CAAC,MAAM;;;;;;0FAC7D,8OAAC;gFAAK,MAAK;gFAAY,WAAU;0FAAO;;;;;;;;;;;;kFAE1C,8OAAC,sLAAA,CAAA,WAAQ;wEACP,SAAS,SAAS,WAAW,CAAC,MAAM,GAAG;wEACvC,MAAK;wEACL,UAAU;;;;;;kFAEZ,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,gLAAA,CAAA,QAAK;4EAAC,OAAO,SAAS,WAAW,CAAC,KAAK;4EAAE,QAAQ;sFAChD,cAAA,8OAAC;gFAAK,MAAK;gFAAY,WAAU;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAS7D;;;;;;;;;;;;oBAIR;oBACA;wBACE,KAAK;wBACL,qBACE,8OAAC;;8CACC,8OAAC,0NAAA,CAAA,mBAAgB;;;;;gCAAG;;;;;;;wBAIxB,wBACE,8OAAC,8KAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,0NAAA,CAAA,mBAAgB;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CAC1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,8OAAC;;;;;sDACD,8OAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;0BAIH,8OAAC,gLAAA,CAAA,QAAK;gBACJ,OAAO,kBAAkB,YAAY;gBACrC,MAAM;gBACN,MAAM;oBACJ,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBACxC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,kBAAkB,UAAU;wBAC5C,kBAAkB;wBAClB,KAAK,WAAW;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B;gBACF;gBACA,UAAU;oBACR,kBAAkB;oBAClB,KAAK,WAAW;gBAClB;gBACA,OAAO;gBACP,QAAQ,kBAAkB,OAAO;gBACjC,YAAW;0BAEX,cAAA,8OAAC,8KAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,8OAAC,4KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,8OAAC,4KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;yCAAE;kDAE7C,cAAA,8OAAC,kLAAA,CAAA,SAAM;4CAAC,aAAY;sDACjB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;oDAA0B,OAAO,SAAS,GAAG;8DAC3C,SAAS,KAAK;mDADJ,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;sCASnC,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAW;6BAAE;4BAChD,MAAK;sCAEL,cAAA,8OAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;uCAEe", "debugId": null}}]}