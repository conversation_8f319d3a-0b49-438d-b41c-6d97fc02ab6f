{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { \n  Project, \n  WorkflowNode, \n  Character, \n  WorldBuilding, \n  Outline, \n  PlotLine, \n  BookTitle, \n  Chapter,\n  PromptTemplate,\n  DocumentStructure,\n  ExecutionContext,\n  UIState,\n  Notification\n} from '@/types';\n\n// 主应用状态接口\ninterface AppState {\n  // UI状态\n  ui: UIState;\n  setTheme: (theme: 'light' | 'dark') => void;\n  setLanguage: (language: 'zh-CN' | 'en-US') => void;\n  toggleSidebar: () => void;\n  setActiveTab: (tab: string) => void;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markNotificationRead: (id: string) => void;\n  clearNotifications: () => void;\n\n  // 项目管理\n  projects: Project[];\n  currentProject: Project | null;\n  createProject: (project: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateProject: (id: string, updates: Partial<Project>) => void;\n  deleteProject: (id: string) => void;\n  setCurrentProject: (id: string) => void;\n\n  // 工作流管理\n  workflows: Record<string, WorkflowNode[]>;\n  currentWorkflow: WorkflowNode[];\n  addNode: (node: Omit<WorkflowNode, 'id'>) => void;\n  updateNode: (id: string, updates: Partial<WorkflowNode>) => void;\n  deleteNode: (id: string) => void;\n  connectNodes: (sourceId: string, targetId: string) => void;\n  disconnectNodes: (sourceId: string, targetId: string) => void;\n  loadWorkflow: (projectId: string) => void;\n  saveWorkflow: (projectId: string) => void;\n\n  // 角色管理\n  characters: Record<string, Character[]>;\n  addCharacter: (projectId: string, character: Omit<Character, 'id'>) => void;\n  updateCharacter: (projectId: string, id: string, updates: Partial<Character>) => void;\n  deleteCharacter: (projectId: string, id: string) => void;\n  getCharacters: (projectId: string) => Character[];\n\n  // 世界观管理\n  worldBuilding: Record<string, WorldBuilding[]>;\n  addWorldElement: (projectId: string, element: Omit<WorldBuilding, 'id'>) => void;\n  updateWorldElement: (projectId: string, id: string, updates: Partial<WorldBuilding>) => void;\n  deleteWorldElement: (projectId: string, id: string) => void;\n  getWorldElements: (projectId: string) => WorldBuilding[];\n\n  // 大纲管理\n  outlines: Record<string, Outline[]>;\n  addOutline: (projectId: string, outline: Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateOutline: (projectId: string, id: string, updates: Partial<Outline>) => void;\n  deleteOutline: (projectId: string, id: string) => void;\n  getOutlines: (projectId: string) => Outline[];\n\n  // 主线管理\n  plotLines: Record<string, PlotLine[]>;\n  addPlotLine: (projectId: string, plotLine: Omit<PlotLine, 'id'>) => void;\n  updatePlotLine: (projectId: string, id: string, updates: Partial<PlotLine>) => void;\n  deletePlotLine: (projectId: string, id: string) => void;\n  getPlotLines: (projectId: string) => PlotLine[];\n\n  // 书名管理\n  bookTitles: Record<string, BookTitle[]>;\n  addBookTitle: (projectId: string, title: Omit<BookTitle, 'id' | 'createdAt'>) => void;\n  updateBookTitle: (projectId: string, id: string, updates: Partial<BookTitle>) => void;\n  deleteBookTitle: (projectId: string, id: string) => void;\n  toggleTitleFavorite: (projectId: string, id: string) => void;\n  getBookTitles: (projectId: string) => BookTitle[];\n\n  // 章节管理\n  chapters: Record<string, Chapter[]>;\n  addChapter: (projectId: string, chapter: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>) => void;\n  updateChapter: (projectId: string, id: string, updates: Partial<Chapter>) => void;\n  deleteChapter: (projectId: string, id: string) => void;\n  reorderChapters: (projectId: string, fromIndex: number, toIndex: number) => void;\n  getChapters: (projectId: string) => Chapter[];\n\n  // 提示词管理\n  promptTemplates: PromptTemplate[];\n  addPromptTemplate: (template: Omit<PromptTemplate, 'id'>) => void;\n  updatePromptTemplate: (id: string, updates: Partial<PromptTemplate>) => void;\n  deletePromptTemplate: (id: string) => void;\n  getPromptTemplatesByCategory: (category: string) => PromptTemplate[];\n\n  // 文档管理\n  documentStructures: Record<string, DocumentStructure>;\n  initializeDocumentStructure: (projectId: string) => void;\n  updateDocumentStructure: (projectId: string, structure: Partial<DocumentStructure>) => void;\n\n  // 执行引擎\n  executionContexts: Record<string, ExecutionContext>;\n  startExecution: (projectId: string, workflowId: string) => void;\n  pauseExecution: (projectId: string) => void;\n  resumeExecution: (projectId: string) => void;\n  stopExecution: (projectId: string) => void;\n  updateExecutionProgress: (projectId: string, progress: Partial<ExecutionContext>) => void;\n}\n\n// 生成唯一ID的工具函数\nconst generateId = () => Math.random().toString(36).substr(2, 9);\n\n// 创建Zustand store\nexport const useAppStore = create<AppState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        // 初始UI状态\n        ui: {\n          theme: 'light',\n          language: 'zh-CN',\n          sidebarCollapsed: false,\n          activeTab: 'workflow',\n          selectedProject: undefined,\n          notifications: [],\n        },\n\n        // UI状态管理\n        setTheme: (theme) => set((state) => ({ \n          ui: { ...state.ui, theme } \n        })),\n        \n        setLanguage: (language) => set((state) => ({ \n          ui: { ...state.ui, language } \n        })),\n        \n        toggleSidebar: () => set((state) => ({ \n          ui: { ...state.ui, sidebarCollapsed: !state.ui.sidebarCollapsed } \n        })),\n        \n        setActiveTab: (activeTab) => set((state) => ({ \n          ui: { ...state.ui, activeTab } \n        })),\n        \n        addNotification: (notification) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: [\n              ...state.ui.notifications,\n              {\n                ...notification,\n                id: generateId(),\n                timestamp: new Date(),\n                read: false,\n              }\n            ]\n          }\n        })),\n        \n        markNotificationRead: (id) => set((state) => ({\n          ui: {\n            ...state.ui,\n            notifications: state.ui.notifications.map(n => \n              n.id === id ? { ...n, read: true } : n\n            )\n          }\n        })),\n        \n        clearNotifications: () => set((state) => ({\n          ui: { ...state.ui, notifications: [] }\n        })),\n\n        // 项目管理\n        projects: [],\n        currentProject: null,\n        \n        createProject: (project) => set((state) => {\n          const newProject: Project = {\n            ...project,\n            id: generateId(),\n            createdAt: new Date(),\n            updatedAt: new Date(),\n          };\n          return {\n            projects: [...state.projects, newProject],\n            currentProject: newProject,\n            ui: { ...state.ui, selectedProject: newProject.id }\n          };\n        }),\n        \n        updateProject: (id, updates) => set((state) => ({\n          projects: state.projects.map(p => \n            p.id === id ? { ...p, ...updates, updatedAt: new Date() } : p\n          ),\n          currentProject: state.currentProject?.id === id \n            ? { ...state.currentProject, ...updates, updatedAt: new Date() }\n            : state.currentProject\n        })),\n        \n        deleteProject: (id) => set((state) => ({\n          projects: state.projects.filter(p => p.id !== id),\n          currentProject: state.currentProject?.id === id ? null : state.currentProject,\n          ui: { \n            ...state.ui, \n            selectedProject: state.ui.selectedProject === id ? undefined : state.ui.selectedProject \n          }\n        })),\n        \n        setCurrentProject: (id) => set((state) => {\n          const project = state.projects.find(p => p.id === id);\n          return {\n            currentProject: project || null,\n            ui: { ...state.ui, selectedProject: id }\n          };\n        }),\n\n        // 工作流管理\n        workflows: {},\n        currentWorkflow: [],\n        \n        addNode: (node) => set((state) => ({\n          currentWorkflow: [...state.currentWorkflow, { ...node, id: generateId() }]\n        })),\n        \n        updateNode: (id, updates) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === id ? { ...n, ...updates } : n\n          )\n        })),\n        \n        deleteNode: (id) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.filter(n => n.id !== id)\n        })),\n        \n        connectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: [...n.connections, { sourceId, targetId }] }\n              : n\n          )\n        })),\n        \n        disconnectNodes: (sourceId, targetId) => set((state) => ({\n          currentWorkflow: state.currentWorkflow.map(n => \n            n.id === sourceId \n              ? { ...n, connections: n.connections.filter(c => !(c.sourceId === sourceId && c.targetId === targetId)) }\n              : n\n          )\n        })),\n        \n        loadWorkflow: (projectId) => set((state) => ({\n          currentWorkflow: state.workflows[projectId] || []\n        })),\n        \n        saveWorkflow: (projectId) => set((state) => ({\n          workflows: { ...state.workflows, [projectId]: state.currentWorkflow }\n        })),\n\n        // 角色管理\n        characters: {},\n        \n        addCharacter: (projectId, character) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: [\n              ...(state.characters[projectId] || []),\n              { ...character, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateCharacter: (projectId, id, updates) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates } : c\n            )\n          }\n        })),\n        \n        deleteCharacter: (projectId, id) => set((state) => ({\n          characters: {\n            ...state.characters,\n            [projectId]: (state.characters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        \n        getCharacters: (projectId) => get().characters[projectId] || [],\n\n        // 世界观管理\n        worldBuilding: {},\n        \n        addWorldElement: (projectId, element) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: [\n              ...(state.worldBuilding[projectId] || []),\n              { ...element, id: generateId() }\n            ]\n          }\n        })),\n        \n        updateWorldElement: (projectId, id, updates) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).map(w => \n              w.id === id ? { ...w, ...updates } : w\n            )\n          }\n        })),\n        \n        deleteWorldElement: (projectId, id) => set((state) => ({\n          worldBuilding: {\n            ...state.worldBuilding,\n            [projectId]: (state.worldBuilding[projectId] || []).filter(w => w.id !== id)\n          }\n        })),\n        \n        getWorldElements: (projectId) => get().worldBuilding[projectId] || [],\n\n        // 其他管理功能的占位符实现\n        outlines: {},\n        addOutline: (projectId, outline) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: [\n              ...(state.outlines[projectId] || []),\n              { \n                ...outline, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateOutline: (projectId, id, updates) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).map(o => \n              o.id === id ? { ...o, ...updates, updatedAt: new Date() } : o\n            )\n          }\n        })),\n        deleteOutline: (projectId, id) => set((state) => ({\n          outlines: {\n            ...state.outlines,\n            [projectId]: (state.outlines[projectId] || []).filter(o => o.id !== id)\n          }\n        })),\n        getOutlines: (projectId) => get().outlines[projectId] || [],\n\n        plotLines: {},\n        addPlotLine: (projectId, plotLine) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: [\n              ...(state.plotLines[projectId] || []),\n              { ...plotLine, id: generateId() }\n            ]\n          }\n        })),\n        updatePlotLine: (projectId, id, updates) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).map(p => \n              p.id === id ? { ...p, ...updates } : p\n            )\n          }\n        })),\n        deletePlotLine: (projectId, id) => set((state) => ({\n          plotLines: {\n            ...state.plotLines,\n            [projectId]: (state.plotLines[projectId] || []).filter(p => p.id !== id)\n          }\n        })),\n        getPlotLines: (projectId) => get().plotLines[projectId] || [],\n\n        bookTitles: {},\n        addBookTitle: (projectId, title) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: [\n              ...(state.bookTitles[projectId] || []),\n              { ...title, id: generateId(), createdAt: new Date() }\n            ]\n          }\n        })),\n        updateBookTitle: (projectId, id, updates) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, ...updates } : t\n            )\n          }\n        })),\n        deleteBookTitle: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).filter(t => t.id !== id)\n          }\n        })),\n        toggleTitleFavorite: (projectId, id) => set((state) => ({\n          bookTitles: {\n            ...state.bookTitles,\n            [projectId]: (state.bookTitles[projectId] || []).map(t => \n              t.id === id ? { ...t, isFavorite: !t.isFavorite } : t\n            )\n          }\n        })),\n        getBookTitles: (projectId) => get().bookTitles[projectId] || [],\n\n        chapters: {},\n        addChapter: (projectId, chapter) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: [\n              ...(state.chapters[projectId] || []),\n              { \n                ...chapter, \n                id: generateId(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n              }\n            ]\n          }\n        })),\n        updateChapter: (projectId, id, updates) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).map(c => \n              c.id === id ? { ...c, ...updates, updatedAt: new Date() } : c\n            )\n          }\n        })),\n        deleteChapter: (projectId, id) => set((state) => ({\n          chapters: {\n            ...state.chapters,\n            [projectId]: (state.chapters[projectId] || []).filter(c => c.id !== id)\n          }\n        })),\n        reorderChapters: (projectId, fromIndex, toIndex) => set((state) => {\n          const chapters = [...(state.chapters[projectId] || [])];\n          const [removed] = chapters.splice(fromIndex, 1);\n          chapters.splice(toIndex, 0, removed);\n          // 重新设置order\n          chapters.forEach((chapter, index) => {\n            chapter.order = index + 1;\n          });\n          return {\n            chapters: {\n              ...state.chapters,\n              [projectId]: chapters\n            }\n          };\n        }),\n        getChapters: (projectId) => get().chapters[projectId] || [],\n\n        promptTemplates: [],\n        addPromptTemplate: (template) => set((state) => ({\n          promptTemplates: [...state.promptTemplates, { ...template, id: generateId() }]\n        })),\n        updatePromptTemplate: (id, updates) => set((state) => ({\n          promptTemplates: state.promptTemplates.map(t => \n            t.id === id ? { ...t, ...updates } : t\n          )\n        })),\n        deletePromptTemplate: (id) => set((state) => ({\n          promptTemplates: state.promptTemplates.filter(t => t.id !== id)\n        })),\n        getPromptTemplatesByCategory: (category) => \n          get().promptTemplates.filter(t => t.category === category),\n\n        documentStructures: {},\n        initializeDocumentStructure: (projectId) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              projectId,\n              folders: [],\n              files: [],\n              lastBackup: new Date()\n            }\n          }\n        })),\n        updateDocumentStructure: (projectId, structure) => set((state) => ({\n          documentStructures: {\n            ...state.documentStructures,\n            [projectId]: {\n              ...state.documentStructures[projectId],\n              ...structure\n            }\n          }\n        })),\n\n        executionContexts: {},\n        startExecution: (projectId, workflowId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              projectId,\n              workflowId,\n              status: 'running',\n              progress: {\n                totalSteps: 0,\n                completedSteps: 0,\n                currentStep: '',\n                percentage: 0\n              },\n              queue: [],\n              results: {},\n              startTime: new Date()\n            }\n          }\n        })),\n        pauseExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'paused'\n            }\n          }\n        })),\n        resumeExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'running'\n            }\n          }\n        })),\n        stopExecution: (projectId) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              status: 'idle',\n              endTime: new Date()\n            }\n          }\n        })),\n        updateExecutionProgress: (projectId, progress) => set((state) => ({\n          executionContexts: {\n            ...state.executionContexts,\n            [projectId]: {\n              ...state.executionContexts[projectId],\n              ...progress\n            }\n          }\n        })),\n      }),\n      {\n        name: 'ai-novel-workflow-storage',\n        partialize: (state) => ({\n          projects: state.projects,\n          workflows: state.workflows,\n          characters: state.characters,\n          worldBuilding: state.worldBuilding,\n          outlines: state.outlines,\n          plotLines: state.plotLines,\n          bookTitles: state.bookTitles,\n          chapters: state.chapters,\n          promptTemplates: state.promptTemplates,\n          documentStructures: state.documentStructures,\n          ui: {\n            theme: state.ui.theme,\n            language: state.ui.language,\n            sidebarCollapsed: state.ui.sidebarCollapsed,\n          }\n        }),\n      }\n    ),\n    { name: 'ai-novel-workflow' }\n  )\n);\n\nexport default useAppStore;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAiHA,cAAc;AACd,MAAM,aAAa,IAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAGvD,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,SAAS;QACT,IAAI;YACF,OAAO;YACP,UAAU;YACV,kBAAkB;YAClB,WAAW;YACX,iBAAiB;YACjB,eAAe,EAAE;QACnB;QAEA,SAAS;QACT,UAAU,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAM;gBAC3B,CAAC;QAED,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAS;gBAC9B,CAAC;QAED,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBACnC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,kBAAkB,CAAC,MAAM,EAAE,CAAC,gBAAgB;oBAAC;gBAClE,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE;oBAAU;gBAC/B,CAAC;QAED,iBAAiB,CAAC,eAAiB,IAAI,CAAC,QAAU,CAAC;oBACjD,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe;+BACV,MAAM,EAAE,CAAC,aAAa;4BACzB;gCACE,GAAG,YAAY;gCACf,IAAI;gCACJ,WAAW,IAAI;gCACf,MAAM;4BACR;yBACD;oBACH;gBACF,CAAC;QAED,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,eAAe,MAAM,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,MAAM;4BAAK,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,IAAM,IAAI,CAAC,QAAU,CAAC;oBACxC,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,eAAe,EAAE;oBAAC;gBACvC,CAAC;QAED,OAAO;QACP,UAAU,EAAE;QACZ,gBAAgB;QAEhB,eAAe,CAAC,UAAY,IAAI,CAAC;gBAC/B,MAAM,aAAsB;oBAC1B,GAAG,OAAO;oBACV,IAAI;oBACJ,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBACA,OAAO;oBACL,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAW;oBACzC,gBAAgB;oBAChB,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB,WAAW,EAAE;oBAAC;gBACpD;YACF;QAEA,eAAe,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9C,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAC3B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI;wBAAO,IAAI;oBAE9D,gBAAgB,MAAM,cAAc,EAAE,OAAO,KACzC;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;wBAAE,WAAW,IAAI;oBAAO,IAC7D,MAAM,cAAc;gBAC1B,CAAC;QAED,eAAe,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACrC,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC9C,gBAAgB,MAAM,cAAc,EAAE,OAAO,KAAK,OAAO,MAAM,cAAc;oBAC7E,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,iBAAiB,MAAM,EAAE,CAAC,eAAe,KAAK,KAAK,YAAY,MAAM,EAAE,CAAC,eAAe;oBACzF;gBACF,CAAC;QAED,mBAAmB,CAAC,KAAO,IAAI,CAAC;gBAC9B,MAAM,UAAU,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAClD,OAAO;oBACL,gBAAgB,WAAW;oBAC3B,IAAI;wBAAE,GAAG,MAAM,EAAE;wBAAE,iBAAiB;oBAAG;gBACzC;YACF;QAEA,QAAQ;QACR,WAAW,CAAC;QACZ,iBAAiB,EAAE;QAEnB,SAAS,CAAC,OAAS,IAAI,CAAC,QAAU,CAAC;oBACjC,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,IAAI;4BAAE,IAAI;wBAAa;qBAAE;gBAC5E,CAAC;QAED,YAAY,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QAED,YAAY,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClC,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QAED,cAAc,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa;mCAAI,EAAE,WAAW;gCAAE;oCAAE;oCAAU;gCAAS;6BAAE;wBAAC,IAChE;gBAER,CAAC;QAED,iBAAiB,CAAC,UAAU,WAAa,IAAI,CAAC,QAAU,CAAC;oBACvD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,WACL;4BAAE,GAAG,CAAC;4BAAE,aAAa,EAAE,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,QAAQ,KAAK,YAAY,EAAE,QAAQ,KAAK,QAAQ;wBAAG,IACtG;gBAER,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,iBAAiB,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;gBACnD,CAAC;QAED,cAAc,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC3C,WAAW;wBAAE,GAAG,MAAM,SAAS;wBAAE,CAAC,UAAU,EAAE,MAAM,eAAe;oBAAC;gBACtE,CAAC;QAED,OAAO;QACP,YAAY,CAAC;QAEb,cAAc,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,SAAS;gCAAE,IAAI;4BAAa;yBAClC;oBACH;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QAED,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,QAAQ;QACR,eAAe,CAAC;QAEhB,iBAAiB,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBACvD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE;+BACP,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;4BACxC;gCAAE,GAAG,OAAO;gCAAE,IAAI;4BAAa;yBAChC;oBACH;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9D,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACtD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QAED,oBAAoB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACrD,eAAe;wBACb,GAAG,MAAM,aAAa;wBACtB,CAAC,UAAU,EAAE,CAAC,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAC3E;gBACF,CAAC;QAED,kBAAkB,CAAC,YAAc,MAAM,aAAa,CAAC,UAAU,IAAI,EAAE;QAErE,eAAe;QACf,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,WAAW,CAAC;QACZ,aAAa,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBACpD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE;+BACP,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;4BACpC;gCAAE,GAAG,QAAQ;gCAAE,IAAI;4BAAa;yBACjC;oBACH;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC1D,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IAClD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,gBAAgB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACjD,WAAW;wBACT,GAAG,MAAM,SAAS;wBAClB,CAAC,UAAU,EAAE,CAAC,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACvE;gBACF,CAAC;QACD,cAAc,CAAC,YAAc,MAAM,SAAS,CAAC,UAAU,IAAI,EAAE;QAE7D,YAAY,CAAC;QACb,cAAc,CAAC,WAAW,QAAU,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE;+BACP,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;4BACrC;gCAAE,GAAG,KAAK;gCAAE,IAAI;gCAAc,WAAW,IAAI;4BAAO;yBACrD;oBACH;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC3D,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAEzC;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAClD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxE;gBACF,CAAC;QACD,qBAAqB,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtD,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,CAAC,UAAU,EAAE,CAAC,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACnD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,YAAY,CAAC,EAAE,UAAU;4BAAC,IAAI;oBAExD;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,MAAM,UAAU,CAAC,UAAU,IAAI,EAAE;QAE/D,UAAU,CAAC;QACX,YAAY,CAAC,WAAW,UAAY,IAAI,CAAC,QAAU,CAAC;oBAClD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;+BACP,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;4BACnC;gCACE,GAAG,OAAO;gCACV,IAAI;gCACJ,WAAW,IAAI;gCACf,WAAW,IAAI;4BACjB;yBACD;oBACH;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACzD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,IACjD,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,GAAG,OAAO;gCAAE,WAAW,IAAI;4BAAO,IAAI;oBAEhE;gBACF,CAAC;QACD,eAAe,CAAC,WAAW,KAAO,IAAI,CAAC,QAAU,CAAC;oBAChD,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE,CAAC,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACtE;gBACF,CAAC;QACD,iBAAiB,CAAC,WAAW,WAAW,UAAY,IAAI,CAAC;gBACvD,MAAM,WAAW;uBAAK,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;iBAAE;gBACvD,MAAM,CAAC,QAAQ,GAAG,SAAS,MAAM,CAAC,WAAW;gBAC7C,SAAS,MAAM,CAAC,SAAS,GAAG;gBAC5B,YAAY;gBACZ,SAAS,OAAO,CAAC,CAAC,SAAS;oBACzB,QAAQ,KAAK,GAAG,QAAQ;gBAC1B;gBACA,OAAO;oBACL,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,CAAC,UAAU,EAAE;oBACf;gBACF;YACF;QACA,aAAa,CAAC,YAAc,MAAM,QAAQ,CAAC,UAAU,IAAI,EAAE;QAE3D,iBAAiB,EAAE;QACnB,mBAAmB,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBAC/C,iBAAiB;2BAAI,MAAM,eAAe;wBAAE;4BAAE,GAAG,QAAQ;4BAAE,IAAI;wBAAa;qBAAE;gBAChF,CAAC;QACD,sBAAsB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBACrD,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,IACzC,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QACD,sBAAsB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBAC5C,iBAAiB,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC9D,CAAC;QACD,8BAA8B,CAAC,WAC7B,MAAM,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QAEnD,oBAAoB,CAAC;QACrB,6BAA6B,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC1D,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX;4BACA,SAAS,EAAE;4BACX,OAAO,EAAE;4BACT,YAAY,IAAI;wBAClB;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,YAAc,IAAI,CAAC,QAAU,CAAC;oBACjE,oBAAoB;wBAClB,GAAG,MAAM,kBAAkB;wBAC3B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,kBAAkB,CAAC,UAAU;4BACtC,GAAG,SAAS;wBACd;oBACF;gBACF,CAAC;QAED,mBAAmB,CAAC;QACpB,gBAAgB,CAAC,WAAW,aAAe,IAAI,CAAC,QAAU,CAAC;oBACzD,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX;4BACA;4BACA,QAAQ;4BACR,UAAU;gCACR,YAAY;gCACZ,gBAAgB;gCAChB,aAAa;gCACb,YAAY;4BACd;4BACA,OAAO,EAAE;4BACT,SAAS,CAAC;4BACV,WAAW,IAAI;wBACjB;oBACF;gBACF,CAAC;QACD,gBAAgB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC7C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,iBAAiB,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC9C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;wBACV;oBACF;gBACF,CAAC;QACD,eAAe,CAAC,YAAc,IAAI,CAAC,QAAU,CAAC;oBAC5C,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,QAAQ;4BACR,SAAS,IAAI;wBACf;oBACF;gBACF,CAAC;QACD,yBAAyB,CAAC,WAAW,WAAa,IAAI,CAAC,QAAU,CAAC;oBAChE,mBAAmB;wBACjB,GAAG,MAAM,iBAAiB;wBAC1B,CAAC,UAAU,EAAE;4BACX,GAAG,MAAM,iBAAiB,CAAC,UAAU;4BACrC,GAAG,QAAQ;wBACb;oBACF;gBACF,CAAC;IACH,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,eAAe,MAAM,aAAa;YAClC,UAAU,MAAM,QAAQ;YACxB,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;YAC5B,UAAU,MAAM,QAAQ;YACxB,iBAAiB,MAAM,eAAe;YACtC,oBAAoB,MAAM,kBAAkB;YAC5C,IAAI;gBACF,OAAO,MAAM,EAAE,CAAC,KAAK;gBACrB,UAAU,MAAM,EAAE,CAAC,QAAQ;gBAC3B,kBAAkB,MAAM,EAAE,CAAC,gBAAgB;YAC7C;QACF,CAAC;AACH,IAEF;IAAE,MAAM;AAAoB;uCAIjB", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/utils/aiService.ts"], "sourcesContent": ["// AI服务集成\ninterface AIConfig {\n  provider: 'openai' | 'claude' | 'gemini' | 'custom';\n  apiKey: string;\n  baseUrl?: string;\n  model: string;\n  temperature?: number;\n  maxTokens?: number;\n}\n\ninterface AIResponse {\n  success: boolean;\n  data?: any;\n  error?: string;\n}\n\n// AI服务类\nexport class AIService {\n  private config: AIConfig | null = null;\n\n  // 设置AI配置\n  setConfig(config: AIConfig) {\n    this.config = config;\n    // 保存到localStorage (仅在客户端)\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('ai-config', JSON.stringify(config));\n    }\n  }\n\n  // 获取AI配置\n  getConfig(): AIConfig | null {\n    if (this.config) return this.config;\n\n    // 仅在客户端访问localStorage\n    if (typeof window !== 'undefined') {\n      const saved = localStorage.getItem('ai-config');\n      if (saved) {\n        this.config = JSON.parse(saved);\n        return this.config;\n      }\n    }\n    return null;\n  }\n\n  // 检查是否已配置\n  isConfigured(): boolean {\n    return this.getConfig() !== null;\n  }\n\n  // 通用AI请求方法\n  private async makeRequest(prompt: string, systemPrompt?: string): Promise<AIResponse> {\n    const config = this.getConfig();\n    if (!config) {\n      return { success: false, error: '请先配置AI API' };\n    }\n\n    try {\n      let response;\n      \n      switch (config.provider) {\n        case 'openai':\n          response = await this.callOpenAI(prompt, systemPrompt, config);\n          break;\n        case 'claude':\n          response = await this.callClaude(prompt, systemPrompt, config);\n          break;\n        case 'gemini':\n          response = await this.callGemini(prompt, systemPrompt, config);\n          break;\n        case 'custom':\n          response = await this.callCustomAPI(prompt, systemPrompt, config);\n          break;\n        default:\n          return { success: false, error: '不支持的AI提供商' };\n      }\n\n      return { success: true, data: response };\n    } catch (error: any) {\n      return { success: false, error: error.message || '请求失败' };\n    }\n  }\n\n  // OpenAI API调用\n  private async callOpenAI(prompt: string, systemPrompt: string = '', config: AIConfig) {\n    const messages = [];\n    if (systemPrompt) {\n      messages.push({ role: 'system', content: systemPrompt });\n    }\n    messages.push({ role: 'user', content: prompt });\n\n    const response = await fetch(config.baseUrl || 'https://api.openai.com/v1/chat/completions', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${config.apiKey}`,\n      },\n      body: JSON.stringify({\n        model: config.model || 'gpt-3.5-turbo',\n        messages,\n        temperature: config.temperature || 0.7,\n        max_tokens: config.maxTokens || 2000,\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`OpenAI API错误: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.choices[0].message.content;\n  }\n\n  // Claude API调用\n  private async callClaude(prompt: string, systemPrompt: string = '', config: AIConfig) {\n    const response = await fetch(config.baseUrl || 'https://api.anthropic.com/v1/messages', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-api-key': config.apiKey,\n        'anthropic-version': '2023-06-01',\n      },\n      body: JSON.stringify({\n        model: config.model || 'claude-3-sonnet-20240229',\n        max_tokens: config.maxTokens || 2000,\n        temperature: config.temperature || 0.7,\n        system: systemPrompt,\n        messages: [{ role: 'user', content: prompt }],\n      }),\n    });\n\n    if (!response.ok) {\n      throw new Error(`Claude API错误: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.content[0].text;\n  }\n\n  // Gemini API调用\n  private async callGemini(prompt: string, systemPrompt: string = '', config: AIConfig) {\n    const fullPrompt = systemPrompt ? `${systemPrompt}\\n\\n${prompt}` : prompt;\n    \n    const response = await fetch(\n      `${config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta/models'}/${config.model || 'gemini-pro'}:generateContent?key=${config.apiKey}`,\n      {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contents: [{ parts: [{ text: fullPrompt }] }],\n          generationConfig: {\n            temperature: config.temperature || 0.7,\n            maxOutputTokens: config.maxTokens || 2000,\n          },\n        }),\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error(`Gemini API错误: ${response.status} ${response.statusText}`);\n    }\n\n    const data = await response.json();\n    return data.candidates[0].content.parts[0].text;\n  }\n\n  // 自定义API调用\n  private async callCustomAPI(prompt: string, systemPrompt: string = '', config: AIConfig) {\n    // 验证和修正API地址\n    let apiUrl = config.baseUrl!;\n\n    // 确保API地址格式正确\n    if (!apiUrl.startsWith('http://') && !apiUrl.startsWith('https://')) {\n      apiUrl = 'https://' + apiUrl;\n    }\n\n    // 如果地址不包含端点，尝试添加常见端点\n    if (!apiUrl.includes('/chat/completions') && !apiUrl.includes('/completions') && !apiUrl.includes('/generate')) {\n      if (apiUrl.endsWith('/')) {\n        apiUrl = apiUrl + 'v1/chat/completions';\n      } else if (apiUrl.endsWith('/v1')) {\n        apiUrl = apiUrl + '/chat/completions';\n      } else {\n        apiUrl = apiUrl + '/v1/chat/completions';\n      }\n    }\n\n    console.log('使用API地址:', apiUrl);\n\n    // 尝试OpenAI兼容格式\n    const messages = [];\n    if (systemPrompt) {\n      messages.push({ role: 'system', content: systemPrompt });\n    }\n    messages.push({ role: 'user', content: prompt });\n\n    const requestBody = {\n      model: config.model || 'gpt-3.5-turbo',\n      messages,\n      temperature: config.temperature || 0.7,\n      max_tokens: config.maxTokens || 2000,\n    };\n\n    try {\n      console.log('发送请求到:', apiUrl);\n      console.log('请求体:', JSON.stringify(requestBody, null, 2));\n\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${config.apiKey}`,\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      console.log('响应状态:', response.status, response.statusText);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.log('错误响应:', errorText);\n        throw new Error(`自定义API错误: ${response.status} ${response.statusText} - ${errorText}`);\n      }\n\n      const data = await response.json();\n      console.log('响应数据:', data);\n\n      // 尝试不同的响应格式\n      if (data.choices && data.choices[0] && data.choices[0].message) {\n        return data.choices[0].message.content;\n      } else if (data.response) {\n        return data.response;\n      } else if (data.content) {\n        return data.content;\n      } else if (data.text) {\n        return data.text;\n      } else {\n        throw new Error('无法解析API响应格式: ' + JSON.stringify(data));\n      }\n    } catch (error: any) {\n      console.log('OpenAI格式失败，尝试简单格式:', error.message);\n\n      // 如果OpenAI格式失败，尝试简单格式\n      try {\n        const simpleRequestBody = {\n          prompt: systemPrompt ? `${systemPrompt}\\n\\n${prompt}` : prompt,\n          model: config.model,\n          temperature: config.temperature || 0.7,\n          max_tokens: config.maxTokens || 2000,\n        };\n\n        // 尝试不同的端点\n        const simpleUrl = apiUrl.replace('/chat/completions', '/completions');\n        console.log('尝试简单格式，URL:', simpleUrl);\n\n        const response = await fetch(simpleUrl, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${config.apiKey}`,\n          },\n          body: JSON.stringify(simpleRequestBody),\n        });\n\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.log('简单格式错误响应:', errorText);\n          throw new Error(`自定义API错误: ${response.status} ${response.statusText} - ${errorText}`);\n        }\n\n        const data = await response.json();\n        console.log('简单格式响应数据:', data);\n        return data.response || data.content || data.text || data.choices?.[0]?.text || JSON.stringify(data);\n      } catch (secondError: any) {\n        console.log('所有格式都失败:', secondError.message);\n        throw new Error(`自定义API调用失败: ${error.message}. 备用格式也失败: ${secondError.message}`);\n      }\n    }\n  }\n\n  // 分析工作流需求\n  async analyzeWorkflowRequirements(requirements: {\n    genre: string;\n    style: string;\n    length: string;\n    customWordCount?: number;\n    experience: string;\n    features: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个专业的小说创作工作流设计专家。根据用户的需求，分析并推荐最适合的工作流类型。\n\n可选的工作流类型：\n1. quick-novel - 快速小说生成（适合新手，简单流程）\n2. professional-novel - 专业小说创作（完整流程，适合有经验的作者）\n3. fantasy-novel - 奇幻小说专用（重点关注世界观和魔法体系）\n4. romance-novel - 言情小说工作流（专注情感描写和角色关系）\n\n请返回JSON格式的推荐结果，包含：\n- recommended: 推荐的工作流ID\n- confidence: 推荐置信度(0-100)\n- reasons: 推荐理由数组\n- alternatives: 备选方案数组`;\n\n    const lengthInfo = requirements.length === '自定义' && requirements.customWordCount\n      ? `${requirements.length} (${requirements.customWordCount.toLocaleString()}字)`\n      : requirements.length;\n\n    const prompt = `用户需求：\n- 小说类型：${requirements.genre}\n- 写作风格：${requirements.style}\n- 作品长度：${lengthInfo}\n- 创作经验：${requirements.experience}\n- 特殊需求：${requirements.features.join(', ')}\n\n请分析并推荐最适合的工作流。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成自定义工作流\n  async generateCustomWorkflow(requirements: {\n    genre: string;\n    style: string;\n    length: string;\n    customWordCount?: number;\n    features: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个AI小说创作工作流设计师。根据用户需求设计一个定制化的工作流。\n\n可用的节点类型：\n- input: 用户参数输入\n- title-generator: 书名生成\n- detail-generator: 详情生成\n- character-creator: 角色创建\n- worldbuilding: 世界观构建\n- plotline-planner: 主线规划\n- outline-generator: 大纲生成\n- chapter-count-input: 章节数设定\n- detailed-outline: 详细大纲\n- chapter-generator: 章节生成\n- content-polisher: 内容润色\n- consistency-checker: 一致性检查\n- condition: 条件分支\n- loop: 循环执行\n- output: 结果输出\n\n请返回JSON格式的工作流定义，包含：\n- name: 工作流名称\n- description: 工作流描述\n- nodes: 节点数组，每个节点包含 {type, label, position: {x, y}}\n- connections: 连接数组，每个连接包含 {source: 节点索引, target: 节点索引}\n- complexity: 复杂度 (simple/medium/complex)`;\n\n    const lengthInfo = requirements.length === '自定义' && requirements.customWordCount\n      ? `${requirements.length} (${requirements.customWordCount.toLocaleString()}字)`\n      : requirements.length;\n\n    const prompt = `请为以下需求设计一个定制化的小说创作工作流：\n- 小说类型：${requirements.genre}\n- 写作风格：${requirements.style}\n- 作品长度：${lengthInfo}\n- 特殊需求：${requirements.features.join(', ')}\n\n请确保工作流逻辑合理，节点连接有序，适合用户的具体需求。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 优化现有工作流\n  async optimizeWorkflow(currentNodes: any[], requirements: any): Promise<AIResponse> {\n    const systemPrompt = `你是一个工作流优化专家。分析现有的工作流并提供优化建议。\n\n请返回JSON格式的优化建议，包含：\n- issues: 发现的问题数组\n- suggestions: 优化建议数组\n- optimized_workflow: 优化后的工作流定义（如果需要重构）\n- improvement_score: 改进评分(0-100)`;\n\n    const nodeTypes = currentNodes.map(node => node.data?.type || 'unknown');\n    const prompt = `当前工作流包含以下节点：${nodeTypes.join(', ')}\n\n用户需求：${JSON.stringify(requirements)}\n\n请分析这个工作流的问题并提供优化建议。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成书名\n  async generateTitles(params: {\n    genre: string;\n    style: string;\n    keywords?: string[];\n    count?: number;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个专业的书名创作专家。根据小说类型、风格和关键词生成吸引人的书名。\n\n请返回JSON格式的结果，包含：\n- titles: 书名数组，每个包含 {title, score, analysis: {appeal, memorability, genre_match, uniqueness, marketability}, suggestions: 优化建议数组}`;\n\n    const prompt = `请为以下小说生成${params.count || 5}个书名：\n- 类型：${params.genre}\n- 风格：${params.style}\n- 关键词：${params.keywords?.join(', ') || '无'}\n\n要求书名要有吸引力、易记忆、符合类型特征。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成角色\n  async generateCharacter(params: {\n    genre: string;\n    role: string;\n    background?: string;\n    personality?: string;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个角色设计专家。根据小说类型和角色定位创建详细的角色设定。\n\n请返回JSON格式的角色信息，包含：\n- name: 角色姓名\n- age: 年龄\n- gender: 性别\n- appearance: 外貌描述\n- personality: 性格特征\n- background: 背景故事\n- skills: 技能特长\n- relationships: 人际关系\n- goals: 目标动机\n- flaws: 性格缺陷`;\n\n    const prompt = `请创建一个${params.genre}小说中的${params.role}角色：\n${params.background ? `背景要求：${params.background}` : ''}\n${params.personality ? `性格要求：${params.personality}` : ''}\n\n请提供详细的角色设定。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成世界观\n  async generateWorldbuilding(params: {\n    genre: string;\n    style: string;\n    scope?: string;\n    elements?: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个世界观构建专家。根据小说类型和风格创建详细的世界设定。\n\n请返回JSON格式的世界观信息，包含：\n- name: 世界名称\n- overview: 世界概述\n- geography: 地理环境\n- history: 历史背景\n- culture: 文化特色\n- politics: 政治体系\n- economy: 经济体系\n- technology: 科技水平\n- magic_system: 魔法/超能力体系（如适用）\n- races: 种族设定\n- languages: 语言系统\n- religions: 宗教信仰\n- conflicts: 主要冲突`;\n\n    const prompt = `请为${params.genre}类型的小说创建世界观设定：\n- 风格：${params.style}\n- 范围：${params.scope || '中等规模'}\n- 特殊元素：${params.elements?.join(', ') || '无'}\n\n请提供详细的世界观设定。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成主线规划\n  async generatePlotline(params: {\n    genre: string;\n    style: string;\n    characters: any[];\n    worldbuilding?: any;\n    themes?: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个故事情节规划专家。根据角色和世界观创建引人入胜的主线情节。\n\n请返回JSON格式的主线规划，包含：\n- title: 主线标题\n- premise: 故事前提\n- inciting_incident: 引发事件\n- plot_points: 关键情节点数组，每个包含 {act, description, characters_involved, conflicts, outcomes}\n- climax: 高潮设定\n- resolution: 结局安排\n- themes: 主题表达\n- character_arcs: 角色成长弧线\n- subplots: 支线情节建议`;\n\n    const charactersInfo = params.characters.map(char => `${char.name}(${char.role})`).join(', ');\n    const prompt = `请为${params.genre}小说规划主线情节：\n- 风格：${params.style}\n- 主要角色：${charactersInfo}\n- 世界背景：${params.worldbuilding?.name || '待定'}\n- 主题方向：${params.themes?.join(', ') || '待定'}\n\n请创建完整的故事主线规划。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成大纲\n  async generateOutline(params: {\n    genre: string;\n    plotline: any;\n    characters: any[];\n    targetLength: string;\n    chapterCount?: number;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个小说大纲创作专家。根据主线情节创建详细的章节大纲。\n\n请返回JSON格式的大纲信息，包含：\n- title: 大纲标题\n- structure: 结构类型（三幕式、英雄之旅等）\n- chapters: 章节数组，每个包含 {number, title, summary, key_events, character_focus, word_count_target, notes}\n- pacing: 节奏安排\n- tension_curve: 张力曲线描述\n- themes_distribution: 主题分布`;\n\n    const prompt = `请为${params.genre}小说创建详细大纲：\n- 目标长度：${params.targetLength}\n- 预计章节数：${params.chapterCount || '待定'}\n- 主线情节：${params.plotline.title || '待定'}\n- 主要角色：${params.characters.map(c => c.name).join(', ')}\n\n请创建完整的章节大纲。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成详细大纲\n  async generateDetailedOutline(params: {\n    outline: any;\n    characters: any[];\n    worldbuilding?: any;\n    selectedChapters?: number[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个详细大纲创作专家。将基础大纲扩展为详细的情节要点。\n\n请返回JSON格式的详细大纲，包含：\n- chapters: 详细章节数组，每个包含 {\n    number, title, detailed_summary, scenes: [{\n      scene_number, location, characters, objective, conflict, outcome, mood, pov\n    }], character_development, plot_advancement, foreshadowing, themes\n  }`;\n\n    const chapterInfo = params.selectedChapters ?\n      `重点章节：${params.selectedChapters.join(', ')}` :\n      '所有章节';\n\n    const prompt = `请为以下大纲创建详细的情节要点：\n- 基础大纲：${params.outline.title}\n- 角色信息：${params.characters.map(c => c.name).join(', ')}\n- 世界设定：${params.worldbuilding?.name || '待定'}\n- 处理范围：${chapterInfo}\n\n请提供详细的场景和情节要点。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成章节内容\n  async generateChapter(params: {\n    chapterNumber: number;\n    chapterOutline: any;\n    characters: any[];\n    worldbuilding?: any;\n    previousChapters?: string[];\n    style: string;\n    targetWordCount?: number;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个专业的小说创作者。根据章节大纲创作具体的章节内容。\n\n请返回JSON格式的章节内容，包含：\n- title: 章节标题\n- content: 章节正文内容\n- word_count: 字数统计\n- scenes: 场景分解\n- character_moments: 角色重要时刻\n- plot_advancement: 情节推进要点\n- foreshadowing: 伏笔设置\n- themes_explored: 探讨的主题`;\n\n    const previousContext = params.previousChapters?.length ?\n      `前文概要：${params.previousChapters.slice(-2).join('\\n')}` :\n      '这是开篇章节';\n\n    const prompt = `请创作第${params.chapterNumber}章的具体内容：\n- 章节大纲：${JSON.stringify(params.chapterOutline)}\n- 主要角色：${params.characters.map(c => c.name).join(', ')}\n- 写作风格：${params.style}\n- 目标字数：${params.targetWordCount || 2000}字\n- ${previousContext}\n\n请创作完整的章节内容。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 内容润色\n  async polishContent(params: {\n    content: string;\n    style: string;\n    focusAreas?: string[];\n    targetAudience?: string;\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个专业的文本润色专家。优化文本的表达、节奏和文学性。\n\n请返回JSON格式的润色结果，包含：\n- polished_content: 润色后的内容\n- improvements: 改进说明数组\n- style_analysis: 风格分析\n- readability_score: 可读性评分\n- suggestions: 进一步优化建议`;\n\n    const focusInfo = params.focusAreas?.length ?\n      `重点关注：${params.focusAreas.join(', ')}` :\n      '全面优化';\n\n    const prompt = `请润色以下文本内容：\n- 目标风格：${params.style}\n- 目标读者：${params.targetAudience || '一般读者'}\n- ${focusInfo}\n\n原文内容：\n${params.content}\n\n请提供润色后的版本和改进说明。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 一致性检查\n  async checkConsistency(params: {\n    content: string[];\n    characters: any[];\n    worldbuilding?: any;\n    checkTypes?: string[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个内容一致性检查专家。检查文本中的逻辑、角色、设定等一致性问题。\n\n请返回JSON格式的检查结果，包含：\n- consistency_score: 一致性评分(0-100)\n- issues: 问题数组，每个包含 {type, severity, description, location, suggestion}\n- character_consistency: 角色一致性分析\n- plot_consistency: 情节一致性分析\n- world_consistency: 世界观一致性分析\n- timeline_issues: 时间线问题\n- recommendations: 修改建议`;\n\n    const checkInfo = params.checkTypes?.length ?\n      `检查类型：${params.checkTypes.join(', ')}` :\n      '全面检查';\n\n    const prompt = `请检查以下内容的一致性：\n- 角色设定：${params.characters.map(c => c.name).join(', ')}\n- 世界设定：${params.worldbuilding?.name || '待定'}\n- ${checkInfo}\n\n内容文本：\n${params.content.join('\\n\\n---\\n\\n')}\n\n请提供详细的一致性分析报告。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n\n  // 生成小说详情\n  async generateNovelDetails(params: {\n    title: string;\n    genre: string;\n    style: string;\n    outline?: any;\n    characters?: any[];\n  }): Promise<AIResponse> {\n    const systemPrompt = `你是一个小说包装专家。根据小说内容生成吸引人的详情信息。\n\n请返回JSON格式的详情信息，包含：\n- synopsis: 内容简介\n- tagline: 宣传语\n- keywords: 关键词数组\n- target_audience: 目标读者群体\n- selling_points: 卖点分析\n- genre_tags: 类型标签\n- mood_tags: 氛围标签\n- content_warnings: 内容提醒\n- marketing_description: 营销描述`;\n\n    const prompt = `请为小说《${params.title}》生成详情信息：\n- 类型：${params.genre}\n- 风格：${params.style}\n- 大纲概要：${params.outline?.title || '待定'}\n- 主要角色：${params.characters?.map(c => c.name).join(', ') || '待定'}\n\n请生成完整的小说详情包装。`;\n\n    return this.makeRequest(prompt, systemPrompt);\n  }\n}\n\n// 导出单例实例\nexport const aiService = new AIService();\n"], "names": [], "mappings": "AAAA,SAAS;;;;;AAiBF,MAAM;IACH,SAA0B,KAAK;IAEvC,SAAS;IACT,UAAU,MAAgB,EAAE;QAC1B,IAAI,CAAC,MAAM,GAAG;QACd,0BAA0B;QAC1B;;IAGF;IAEA,SAAS;IACT,YAA6B;QAC3B,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,MAAM;QAEnC,sBAAsB;QACtB;;QAOA,OAAO;IACT;IAEA,UAAU;IACV,eAAwB;QACtB,OAAO,IAAI,CAAC,SAAS,OAAO;IAC9B;IAEA,WAAW;IACX,MAAc,YAAY,MAAc,EAAE,YAAqB,EAAuB;QACpF,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,IAAI,CAAC,QAAQ;YACX,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAa;QAC/C;QAEA,IAAI;YACF,IAAI;YAEJ,OAAQ,OAAO,QAAQ;gBACrB,KAAK;oBACH,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,cAAc;oBACvD;gBACF,KAAK;oBACH,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,cAAc;oBACvD;gBACF,KAAK;oBACH,WAAW,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,cAAc;oBACvD;gBACF,KAAK;oBACH,WAAW,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,cAAc;oBAC1D;gBACF;oBACE,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAY;YAChD;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAS;QACzC,EAAE,OAAO,OAAY;YACnB,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO,IAAI;YAAO;QAC1D;IACF;IAEA,eAAe;IACf,MAAc,WAAW,MAAc,EAAE,eAAuB,EAAE,EAAE,MAAgB,EAAE;QACpF,MAAM,WAAW,EAAE;QACnB,IAAI,cAAc;YAChB,SAAS,IAAI,CAAC;gBAAE,MAAM;gBAAU,SAAS;YAAa;QACxD;QACA,SAAS,IAAI,CAAC;YAAE,MAAM;YAAQ,SAAS;QAAO;QAE9C,MAAM,WAAW,MAAM,MAAM,OAAO,OAAO,IAAI,8CAA8C;YAC3F,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;YAC5C;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK,IAAI;gBACvB;gBACA,aAAa,OAAO,WAAW,IAAI;gBACnC,YAAY,OAAO,SAAS,IAAI;YAClC;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;IACxC;IAEA,eAAe;IACf,MAAc,WAAW,MAAc,EAAE,eAAuB,EAAE,EAAE,MAAgB,EAAE;QACpF,MAAM,WAAW,MAAM,MAAM,OAAO,OAAO,IAAI,yCAAyC;YACtF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,aAAa,OAAO,MAAM;gBAC1B,qBAAqB;YACvB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,OAAO,OAAO,KAAK,IAAI;gBACvB,YAAY,OAAO,SAAS,IAAI;gBAChC,aAAa,OAAO,WAAW,IAAI;gBACnC,QAAQ;gBACR,UAAU;oBAAC;wBAAE,MAAM;wBAAQ,SAAS;oBAAO;iBAAE;YAC/C;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,IAAI;IAC7B;IAEA,eAAe;IACf,MAAc,WAAW,MAAc,EAAE,eAAuB,EAAE,EAAE,MAAgB,EAAE;QACpF,MAAM,aAAa,eAAe,GAAG,aAAa,IAAI,EAAE,QAAQ,GAAG;QAEnE,MAAM,WAAW,MAAM,MACrB,GAAG,OAAO,OAAO,IAAI,0DAA0D,CAAC,EAAE,OAAO,KAAK,IAAI,aAAa,qBAAqB,EAAE,OAAO,MAAM,EAAE,EACrJ;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,UAAU;oBAAC;wBAAE,OAAO;4BAAC;gCAAE,MAAM;4BAAW;yBAAE;oBAAC;iBAAE;gBAC7C,kBAAkB;oBAChB,aAAa,OAAO,WAAW,IAAI;oBACnC,iBAAiB,OAAO,SAAS,IAAI;gBACvC;YACF;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;IACjD;IAEA,WAAW;IACX,MAAc,cAAc,MAAc,EAAE,eAAuB,EAAE,EAAE,MAAgB,EAAE;QACvF,aAAa;QACb,IAAI,SAAS,OAAO,OAAO;QAE3B,cAAc;QACd,IAAI,CAAC,OAAO,UAAU,CAAC,cAAc,CAAC,OAAO,UAAU,CAAC,aAAa;YACnE,SAAS,aAAa;QACxB;QAEA,qBAAqB;QACrB,IAAI,CAAC,OAAO,QAAQ,CAAC,wBAAwB,CAAC,OAAO,QAAQ,CAAC,mBAAmB,CAAC,OAAO,QAAQ,CAAC,cAAc;YAC9G,IAAI,OAAO,QAAQ,CAAC,MAAM;gBACxB,SAAS,SAAS;YACpB,OAAO,IAAI,OAAO,QAAQ,CAAC,QAAQ;gBACjC,SAAS,SAAS;YACpB,OAAO;gBACL,SAAS,SAAS;YACpB;QACF;QAEA,QAAQ,GAAG,CAAC,YAAY;QAExB,eAAe;QACf,MAAM,WAAW,EAAE;QACnB,IAAI,cAAc;YAChB,SAAS,IAAI,CAAC;gBAAE,MAAM;gBAAU,SAAS;YAAa;QACxD;QACA,SAAS,IAAI,CAAC;YAAE,MAAM;YAAQ,SAAS;QAAO;QAE9C,MAAM,cAAc;YAClB,OAAO,OAAO,KAAK,IAAI;YACvB;YACA,aAAa,OAAO,WAAW,IAAI;YACnC,YAAY,OAAO,SAAS,IAAI;QAClC;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,UAAU;YACtB,QAAQ,GAAG,CAAC,QAAQ,KAAK,SAAS,CAAC,aAAa,MAAM;YAEtD,MAAM,WAAW,MAAM,MAAM,QAAQ;gBACnC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;gBAC5C;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,QAAQ,GAAG,CAAC,SAAS,SAAS,MAAM,EAAE,SAAS,UAAU;YAEzD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,GAAG,CAAC,SAAS;gBACrB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACtF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,SAAS;YAErB,YAAY;YACZ,IAAI,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;gBAC9D,OAAO,KAAK,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO;YACxC,OAAO,IAAI,KAAK,QAAQ,EAAE;gBACxB,OAAO,KAAK,QAAQ;YACtB,OAAO,IAAI,KAAK,OAAO,EAAE;gBACvB,OAAO,KAAK,OAAO;YACrB,OAAO,IAAI,KAAK,IAAI,EAAE;gBACpB,OAAO,KAAK,IAAI;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM,kBAAkB,KAAK,SAAS,CAAC;YACnD;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,GAAG,CAAC,sBAAsB,MAAM,OAAO;YAE/C,sBAAsB;YACtB,IAAI;gBACF,MAAM,oBAAoB;oBACxB,QAAQ,eAAe,GAAG,aAAa,IAAI,EAAE,QAAQ,GAAG;oBACxD,OAAO,OAAO,KAAK;oBACnB,aAAa,OAAO,WAAW,IAAI;oBACnC,YAAY,OAAO,SAAS,IAAI;gBAClC;gBAEA,UAAU;gBACV,MAAM,YAAY,OAAO,OAAO,CAAC,qBAAqB;gBACtD,QAAQ,GAAG,CAAC,eAAe;gBAE3B,MAAM,WAAW,MAAM,MAAM,WAAW;oBACtC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE;oBAC5C;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,GAAG,CAAC,aAAa;oBACzB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;gBACtF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,QAAQ,GAAG,CAAC,aAAa;gBACzB,OAAO,KAAK,QAAQ,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC,EAAE,EAAE,QAAQ,KAAK,SAAS,CAAC;YACjG,EAAE,OAAO,aAAkB;gBACzB,QAAQ,GAAG,CAAC,YAAY,YAAY,OAAO;gBAC3C,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,MAAM,OAAO,CAAC,WAAW,EAAE,YAAY,OAAO,EAAE;YACjF;QACF;IACF;IAEA,UAAU;IACV,MAAM,4BAA4B,YAOjC,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;;;;;;sBAYJ,CAAC;QAEnB,MAAM,aAAa,aAAa,MAAM,KAAK,SAAS,aAAa,eAAe,GAC5E,GAAG,aAAa,MAAM,CAAC,EAAE,EAAE,aAAa,eAAe,CAAC,cAAc,GAAG,EAAE,CAAC,GAC5E,aAAa,MAAM;QAEvB,MAAM,SAAS,CAAC;OACb,EAAE,aAAa,KAAK,CAAC;OACrB,EAAE,aAAa,KAAK,CAAC;OACrB,EAAE,WAAW;OACb,EAAE,aAAa,UAAU,CAAC;OAC1B,EAAE,aAAa,QAAQ,CAAC,IAAI,CAAC,MAAM;;cAE5B,CAAC;QAEX,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,WAAW;IACX,MAAM,uBAAuB,YAM5B,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;yCAwBe,CAAC;QAEtC,MAAM,aAAa,aAAa,MAAM,KAAK,SAAS,aAAa,eAAe,GAC5E,GAAG,aAAa,MAAM,CAAC,EAAE,EAAE,aAAa,eAAe,CAAC,cAAc,GAAG,EAAE,CAAC,GAC5E,aAAa,MAAM;QAEvB,MAAM,SAAS,CAAC;OACb,EAAE,aAAa,KAAK,CAAC;OACrB,EAAE,aAAa,KAAK,CAAC;OACrB,EAAE,WAAW;OACb,EAAE,aAAa,QAAQ,CAAC,IAAI,CAAC,MAAM;;4BAEd,CAAC;QAEzB,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,UAAU;IACV,MAAM,iBAAiB,YAAmB,EAAE,YAAiB,EAAuB;QAClF,MAAM,eAAe,CAAC;;;;;;gCAMM,CAAC;QAE7B,MAAM,YAAY,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,QAAQ;QAC9D,MAAM,SAAS,CAAC,YAAY,EAAE,UAAU,IAAI,CAAC,MAAM;;KAElD,EAAE,KAAK,SAAS,CAAC,cAAc;;mBAEjB,CAAC;QAEhB,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,OAAO;IACP,MAAM,eAAe,MAKpB,EAAuB;QACtB,MAAM,eAAe,CAAC;;;iIAGuG,CAAC;QAE9H,MAAM,SAAS,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,EAAE;KAC3C,EAAE,OAAO,KAAK,CAAC;KACf,EAAE,OAAO,KAAK,CAAC;MACd,EAAE,OAAO,QAAQ,EAAE,KAAK,SAAS,IAAI;;qBAEtB,CAAC;QAElB,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,OAAO;IACP,MAAM,kBAAkB,MAKvB,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;;;;;;aAYb,CAAC;QAEV,MAAM,SAAS,CAAC,KAAK,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC;AAC1D,EAAE,OAAO,UAAU,GAAG,CAAC,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,GAAG;AACvD,EAAE,OAAO,WAAW,GAAG,CAAC,KAAK,EAAE,OAAO,WAAW,EAAE,GAAG,GAAG;;WAE9C,CAAC;QAER,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,QAAQ;IACR,MAAM,sBAAsB,MAK3B,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;iBAeT,CAAC;QAEd,MAAM,SAAS,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC;KAChC,EAAE,OAAO,KAAK,CAAC;KACf,EAAE,OAAO,KAAK,IAAI,OAAO;OACvB,EAAE,OAAO,QAAQ,EAAE,KAAK,SAAS,IAAI;;YAEhC,CAAC;QAET,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,SAAS;IACT,MAAM,iBAAiB,MAMtB,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;;;;;kBAWR,CAAC;QAEf,MAAM,iBAAiB,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,OAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QACxF,MAAM,SAAS,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC;KAChC,EAAE,OAAO,KAAK,CAAC;OACb,EAAE,eAAe;OACjB,EAAE,OAAO,aAAa,EAAE,QAAQ,KAAK;OACrC,EAAE,OAAO,MAAM,EAAE,KAAK,SAAS,KAAK;;aAE9B,CAAC;QAEV,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,OAAO;IACP,MAAM,gBAAgB,MAMrB,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;;2BAQC,CAAC;QAExB,MAAM,SAAS,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC;OAC9B,EAAE,OAAO,YAAY,CAAC;QACrB,EAAE,OAAO,YAAY,IAAI,KAAK;OAC/B,EAAE,OAAO,QAAQ,CAAC,KAAK,IAAI,KAAK;OAChC,EAAE,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;;WAE5C,CAAC;QAER,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,SAAS;IACT,MAAM,wBAAwB,MAK7B,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;GAOvB,CAAC;QAEA,MAAM,cAAc,OAAO,gBAAgB,GACzC,CAAC,KAAK,EAAE,OAAO,gBAAgB,CAAC,IAAI,CAAC,OAAO,GAC5C;QAEF,MAAM,SAAS,CAAC;OACb,EAAE,OAAO,OAAO,CAAC,KAAK,CAAC;OACvB,EAAE,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;OAChD,EAAE,OAAO,aAAa,EAAE,QAAQ,KAAK;OACrC,EAAE,YAAY;;cAEP,CAAC;QAEX,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,SAAS;IACT,MAAM,gBAAgB,MAQrB,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;;;;wBAUF,CAAC;QAErB,MAAM,kBAAkB,OAAO,gBAAgB,EAAE,SAC/C,CAAC,KAAK,EAAE,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,GACtD;QAEF,MAAM,SAAS,CAAC,IAAI,EAAE,OAAO,aAAa,CAAC;OACxC,EAAE,KAAK,SAAS,CAAC,OAAO,cAAc,EAAE;OACxC,EAAE,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;OAChD,EAAE,OAAO,KAAK,CAAC;OACf,EAAE,OAAO,eAAe,IAAI,KAAK;EACtC,EAAE,gBAAgB;;WAET,CAAC;QAER,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,OAAO;IACP,MAAM,cAAc,MAKnB,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;sBAOJ,CAAC;QAEnB,MAAM,YAAY,OAAO,UAAU,EAAE,SACnC,CAAC,KAAK,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,OAAO,GACtC;QAEF,MAAM,SAAS,CAAC;OACb,EAAE,OAAO,KAAK,CAAC;OACf,EAAE,OAAO,cAAc,IAAI,OAAO;EACvC,EAAE,UAAU;;;AAGd,EAAE,OAAO,OAAO,CAAC;;eAEF,CAAC;QAEZ,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,QAAQ;IACR,MAAM,iBAAiB,MAKtB,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;;;uBASH,CAAC;QAEpB,MAAM,YAAY,OAAO,UAAU,EAAE,SACnC,CAAC,KAAK,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,OAAO,GACtC;QAEF,MAAM,SAAS,CAAC;OACb,EAAE,OAAO,UAAU,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;OAChD,EAAE,OAAO,aAAa,EAAE,QAAQ,KAAK;EAC1C,EAAE,UAAU;;;AAGd,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,eAAe;;cAEvB,CAAC;QAEX,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;IAEA,SAAS;IACT,MAAM,qBAAqB,MAM1B,EAAuB;QACtB,MAAM,eAAe,CAAC;;;;;;;;;;;6BAWG,CAAC;QAE1B,MAAM,SAAS,CAAC,KAAK,EAAE,OAAO,KAAK,CAAC;KACnC,EAAE,OAAO,KAAK,CAAC;KACf,EAAE,OAAO,KAAK,CAAC;OACb,EAAE,OAAO,OAAO,EAAE,SAAS,KAAK;OAChC,EAAE,OAAO,UAAU,EAAE,IAAI,CAAA,IAAK,EAAE,IAAI,EAAE,KAAK,SAAS,KAAK;;aAEnD,CAAC;QAEV,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IAClC;AACF;AAGO,MAAM,YAAY,IAAI", "debugId": null}}, {"offset": {"line": 1079, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/utils/workflowAI.ts"], "sourcesContent": ["// AI工作流生成器\nimport { WorkflowNode } from '@/types';\n\n// 工作流模板定义\ninterface WorkflowTemplate {\n  id: string;\n  name: string;\n  description: string;\n  nodes: Array<{\n    type: string;\n    label: string;\n    position: { x: number; y: number };\n    config?: Record<string, any>;\n  }>;\n  connections: Array<{\n    source: number;\n    target: number;\n  }>;\n  tags: string[];\n  complexity: 'simple' | 'medium' | 'complex';\n}\n\n// 预定义工作流模板\nconst WORKFLOW_TEMPLATES: WorkflowTemplate[] = [\n  {\n    id: 'quick-novel',\n    name: '快速小说生成',\n    description: '适合新手的简单工作流，快速生成短篇小说',\n    nodes: [\n      { type: 'input', label: '创作输入', position: { x: 100, y: 100 } },\n      { type: 'title-generator', label: '生成书名', position: { x: 100, y: 250 } },\n      { type: 'character-creator', label: '创建角色', position: { x: 350, y: 250 } },\n      { type: 'outline-generator', label: '生成大纲', position: { x: 225, y: 400 } },\n      { type: 'chapter-generator', label: '生成章节', position: { x: 225, y: 550 } },\n      { type: 'output', label: '完成输出', position: { x: 225, y: 700 } }\n    ],\n    connections: [\n      { source: 0, target: 1 },\n      { source: 0, target: 2 },\n      { source: 1, target: 3 },\n      { source: 2, target: 3 },\n      { source: 3, target: 4 },\n      { source: 4, target: 5 }\n    ],\n    tags: ['简单', '快速', '新手'],\n    complexity: 'simple'\n  },\n  {\n    id: 'professional-novel',\n    name: '专业小说创作',\n    description: '完整的专业级工作流，包含详细的世界观和角色设定',\n    nodes: [\n      { type: 'input', label: '创作参数', position: { x: 100, y: 50 } },\n      { type: 'title-generator', label: '书名生成', position: { x: 50, y: 200 } },\n      { type: 'detail-generator', label: '详情生成', position: { x: 200, y: 200 } },\n      { type: 'character-creator', label: '角色创建', position: { x: 350, y: 200 } },\n      { type: 'worldbuilding', label: '世界观构建', position: { x: 500, y: 200 } },\n      { type: 'plotline-planner', label: '主线规划', position: { x: 150, y: 350 } },\n      { type: 'outline-generator', label: '大纲生成', position: { x: 350, y: 350 } },\n      { type: 'detailed-outline', label: '详细大纲', position: { x: 250, y: 500 } },\n      { type: 'chapter-generator', label: '章节生成', position: { x: 250, y: 650 } },\n      { type: 'content-polisher', label: '内容润色', position: { x: 100, y: 800 } },\n      { type: 'consistency-checker', label: '一致性检查', position: { x: 400, y: 800 } },\n      { type: 'output', label: '最终输出', position: { x: 250, y: 950 } }\n    ],\n    connections: [\n      { source: 0, target: 1 }, { source: 0, target: 2 }, { source: 0, target: 3 }, { source: 0, target: 4 },\n      { source: 1, target: 5 }, { source: 2, target: 5 }, { source: 3, target: 5 }, { source: 4, target: 5 },\n      { source: 3, target: 6 }, { source: 4, target: 6 }, { source: 5, target: 6 },\n      { source: 6, target: 7 }, { source: 7, target: 8 }, { source: 8, target: 9 }, { source: 8, target: 10 },\n      { source: 9, target: 11 }, { source: 10, target: 11 }\n    ],\n    tags: ['专业', '完整', '高质量'],\n    complexity: 'complex'\n  },\n  {\n    id: 'fantasy-novel',\n    name: '奇幻小说专用',\n    description: '专为奇幻类小说设计的工作流，重点关注世界观和魔法体系',\n    nodes: [\n      { type: 'input', label: '奇幻设定', position: { x: 100, y: 100 } },\n      { type: 'worldbuilding', label: '魔法世界', position: { x: 100, y: 250 } },\n      { type: 'character-creator', label: '角色种族', position: { x: 300, y: 250 } },\n      { type: 'plotline-planner', label: '冒险主线', position: { x: 200, y: 400 } },\n      { type: 'outline-generator', label: '冒险大纲', position: { x: 200, y: 550 } },\n      { type: 'chapter-generator', label: '章节创作', position: { x: 200, y: 700 } },\n      { type: 'consistency-checker', label: '设定检查', position: { x: 200, y: 850 } },\n      { type: 'output', label: '奇幻小说', position: { x: 200, y: 1000 } }\n    ],\n    connections: [\n      { source: 0, target: 1 }, { source: 0, target: 2 },\n      { source: 1, target: 3 }, { source: 2, target: 3 },\n      { source: 3, target: 4 }, { source: 4, target: 5 },\n      { source: 5, target: 6 }, { source: 6, target: 7 }\n    ],\n    tags: ['奇幻', '魔法', '冒险'],\n    complexity: 'medium'\n  },\n  {\n    id: 'romance-novel',\n    name: '言情小说工作流',\n    description: '专注于情感描写和角色关系的言情小说创作流程',\n    nodes: [\n      { type: 'input', label: '言情设定', position: { x: 100, y: 100 } },\n      { type: 'character-creator', label: '主角设定', position: { x: 50, y: 250 } },\n      { type: 'character-creator', label: '配角设定', position: { x: 200, y: 250 } },\n      { type: 'plotline-planner', label: '情感主线', position: { x: 125, y: 400 } },\n      { type: 'outline-generator', label: '情节大纲', position: { x: 125, y: 550 } },\n      { type: 'chapter-generator', label: '章节创作', position: { x: 125, y: 700 } },\n      { type: 'content-polisher', label: '情感润色', position: { x: 125, y: 850 } },\n      { type: 'output', label: '言情小说', position: { x: 125, y: 1000 } }\n    ],\n    connections: [\n      { source: 0, target: 1 }, { source: 0, target: 2 },\n      { source: 1, target: 3 }, { source: 2, target: 3 },\n      { source: 3, target: 4 }, { source: 4, target: 5 },\n      { source: 5, target: 6 }, { source: 6, target: 7 }\n    ],\n    tags: ['言情', '情感', '关系'],\n    complexity: 'medium'\n  }\n];\n\n// AI工作流分析器\nexport class WorkflowAI {\n  // 分析用户需求并推荐工作流\n  static analyzeRequirements(requirements: {\n    genre?: string;\n    style?: string;\n    length?: string;\n    customWordCount?: number;\n    experience?: string;\n    features?: string[];\n  }): WorkflowTemplate[] {\n    const { genre, style, length, customWordCount, experience, features = [] } = requirements;\n    \n    let recommendations: Array<{ template: WorkflowTemplate; score: number }> = [];\n    \n    WORKFLOW_TEMPLATES.forEach(template => {\n      let score = 0;\n      \n      // 根据类型匹配\n      if (genre) {\n        if (genre.includes('奇幻') && template.tags.includes('奇幻')) score += 30;\n        if (genre.includes('言情') && template.tags.includes('言情')) score += 30;\n        if (genre.includes('现代') && template.tags.includes('简单')) score += 20;\n      }\n      \n      // 根据经验水平匹配\n      if (experience) {\n        if (experience === '新手' && template.complexity === 'simple') score += 25;\n        if (experience === '进阶' && template.complexity === 'medium') score += 25;\n        if (experience === '专业' && template.complexity === 'complex') score += 25;\n      }\n      \n      // 根据长度匹配\n      if (length) {\n        if (length === '短篇' && template.complexity === 'simple') score += 20;\n        if (length === '中篇' && template.complexity === 'medium') score += 20;\n        if (length === '长篇' && template.complexity === 'complex') score += 20;\n\n        // 处理自定义字数\n        if (length === '自定义' && customWordCount) {\n          if (customWordCount <= 50000 && template.complexity === 'simple') score += 20;\n          else if (customWordCount <= 150000 && template.complexity === 'medium') score += 20;\n          else if (customWordCount > 150000 && template.complexity === 'complex') score += 20;\n        }\n      }\n      \n      // 根据特殊需求匹配\n      features.forEach(feature => {\n        if (template.tags.some(tag => tag.includes(feature))) {\n          score += 15;\n        }\n      });\n      \n      recommendations.push({ template, score });\n    });\n    \n    // 按分数排序并返回前3个\n    return recommendations\n      .sort((a, b) => b.score - a.score)\n      .slice(0, 3)\n      .map(r => r.template);\n  }\n  \n  // 生成自定义工作流\n  static generateCustomWorkflow(requirements: {\n    genre: string;\n    style: string;\n    length: string;\n    customWordCount?: number;\n    features: string[];\n  }): WorkflowTemplate {\n    const { genre, style, length, customWordCount, features } = requirements;\n    \n    // 基础节点\n    const nodes = [\n      { type: 'input', label: '创作输入', position: { x: 200, y: 100 } }\n    ];\n    \n    const connections: Array<{ source: number; target: number }> = [];\n    let currentY = 250;\n    let nodeIndex = 1;\n    \n    // 根据需求添加节点\n    const addNode = (type: string, label: string, x: number = 200) => {\n      nodes.push({ type, label, position: { x, y: currentY } });\n      return nodeIndex++;\n    };\n    \n    // 书名生成（总是需要）\n    const titleNode = addNode('title-generator', '书名生成', 100);\n    connections.push({ source: 0, target: titleNode });\n    \n    // 根据类型添加特定节点\n    if (genre.includes('奇幻') || features.includes('世界观')) {\n      const worldNode = addNode('worldbuilding', '世界观构建', 300);\n      connections.push({ source: 0, target: worldNode });\n    }\n    \n    // 角色创建（总是需要）\n    currentY += 150;\n    const charNode = addNode('character-creator', '角色创建');\n    connections.push({ source: 0, target: charNode });\n    \n    // 主线规划\n    currentY += 150;\n    const plotNode = addNode('plotline-planner', '主线规划');\n    connections.push({ source: titleNode, target: plotNode });\n    connections.push({ source: charNode, target: plotNode });\n    \n    // 大纲生成\n    currentY += 150;\n    const outlineNode = addNode('outline-generator', '大纲生成');\n    connections.push({ source: plotNode, target: outlineNode });\n    \n    // 根据长度决定是否需要详细大纲\n    if (length === '长篇') {\n      currentY += 150;\n      const detailOutlineNode = addNode('detailed-outline', '详细大纲');\n      connections.push({ source: outlineNode, target: detailOutlineNode });\n    }\n    \n    // 章节生成\n    currentY += 150;\n    const chapterNode = addNode('chapter-generator', '章节生成');\n    const prevNode = length === '长篇' ? nodeIndex - 2 : outlineNode;\n    connections.push({ source: prevNode, target: chapterNode });\n    \n    // 根据需求添加润色和检查\n    if (features.includes('高质量') || style.includes('文艺')) {\n      currentY += 150;\n      const polishNode = addNode('content-polisher', '内容润色', 100);\n      connections.push({ source: chapterNode, target: polishNode });\n      \n      const checkNode = addNode('consistency-checker', '一致性检查', 300);\n      connections.push({ source: chapterNode, target: checkNode });\n      \n      // 最终输出\n      currentY += 150;\n      const outputNode = addNode('output', '最终输出');\n      connections.push({ source: polishNode, target: outputNode });\n      connections.push({ source: checkNode, target: outputNode });\n    } else {\n      // 简单输出\n      currentY += 150;\n      const outputNode = addNode('output', '最终输出');\n      connections.push({ source: chapterNode, target: outputNode });\n    }\n    \n    // 生成描述和复杂度\n    const lengthInfo = length === '自定义' && customWordCount\n      ? `自定义(${customWordCount.toLocaleString()}字)`\n      : length;\n\n    const getComplexity = () => {\n      if (length === '自定义' && customWordCount) {\n        if (customWordCount <= 50000) return 'simple';\n        if (customWordCount <= 150000) return 'medium';\n        return 'complex';\n      }\n      return length === '短篇' ? 'simple' : length === '中篇' ? 'medium' : 'complex';\n    };\n\n    return {\n      id: 'custom-' + Date.now(),\n      name: '自定义工作流',\n      description: `为${genre}类型的${lengthInfo}小说定制的工作流`,\n      nodes,\n      connections,\n      tags: ['自定义', genre, length],\n      complexity: getComplexity()\n    };\n  }\n  \n  // 优化现有工作流\n  static optimizeWorkflow(currentNodes: any[], requirements: any): WorkflowTemplate {\n    // 分析现有节点\n    const nodeTypes = currentNodes.map(node => node.data.type);\n    \n    // 检查缺失的关键节点\n    const missingNodes = [];\n    if (!nodeTypes.includes('input')) missingNodes.push('input');\n    if (!nodeTypes.includes('output')) missingNodes.push('output');\n    if (!nodeTypes.includes('character-creator')) missingNodes.push('character-creator');\n    if (!nodeTypes.includes('outline-generator')) missingNodes.push('outline-generator');\n    \n    // 生成优化建议\n    const optimizedNodes = [...currentNodes];\n    const connections: Array<{ source: number; target: number }> = [];\n    \n    // 添加缺失的节点\n    missingNodes.forEach((nodeType, index) => {\n      optimizedNodes.push({\n        type: nodeType,\n        label: this.getNodeLabel(nodeType),\n        position: { x: 200 + index * 150, y: 100 + index * 150 }\n      });\n    });\n    \n    return {\n      id: 'optimized-' + Date.now(),\n      name: '优化工作流',\n      description: '基于现有工作流的优化版本',\n      nodes: optimizedNodes,\n      connections,\n      tags: ['优化', '改进'],\n      complexity: 'medium'\n    };\n  }\n  \n  private static getNodeLabel(nodeType: string): string {\n    const labels: Record<string, string> = {\n      'input': '输入节点',\n      'title-generator': '书名生成',\n      'detail-generator': '详情生成',\n      'character-creator': '角色创建',\n      'worldbuilding': '世界观构建',\n      'plotline-planner': '主线规划',\n      'outline-generator': '大纲生成',\n      'chapter-count-input': '章节数设定',\n      'detailed-outline': '详细大纲',\n      'chapter-generator': '章节生成',\n      'content-polisher': '内容润色',\n      'consistency-checker': '一致性检查',\n      'condition': '条件分支',\n      'loop': '循环执行',\n      'output': '结果输出'\n    };\n    return labels[nodeType] || nodeType;\n  }\n}\n\nexport { WORKFLOW_TEMPLATES };\nexport type { WorkflowTemplate };\n"], "names": [], "mappings": "AAAA,WAAW;;;;;AAsBX,WAAW;AACX,MAAM,qBAAyC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC7D;gBAAE,MAAM;gBAAmB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACvE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAU,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;SAC/D;QACD,aAAa;YACX;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACvB;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;SACxB;QACD,MAAM;YAAC;YAAM;YAAM;SAAK;QACxB,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAG;YAAE;YAC5D;gBAAE,MAAM;gBAAmB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAI,GAAG;gBAAI;YAAE;YACtE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAiB,OAAO;gBAAS,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACtE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAuB,OAAO;gBAAS,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC5E;gBAAE,MAAM;gBAAU,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;SAC/D;QACD,aAAa;YACX;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACrG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACrG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAC3E;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAG;YACtG;gBAAE,QAAQ;gBAAG,QAAQ;YAAG;YAAG;gBAAE,QAAQ;gBAAI,QAAQ;YAAG;SACrD;QACD,MAAM;YAAC;YAAM;YAAM;SAAM;QACzB,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC7D;gBAAE,MAAM;gBAAiB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACrE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAuB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC3E;gBAAE,MAAM;gBAAU,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAK;YAAE;SAChE;QACD,aAAa;YACX;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;SAClD;QACD,MAAM;YAAC;YAAM;YAAM;SAAK;QACxB,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YAC7D;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAI,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAqB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACzE;gBAAE,MAAM;gBAAoB,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;YACxE;gBAAE,MAAM;gBAAU,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAK;YAAE;SAChE;QACD,aAAa;YACX;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YACjD;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;YAAG;gBAAE,QAAQ;gBAAG,QAAQ;YAAE;SAClD;QACD,MAAM;YAAC;YAAM;YAAM;SAAK;QACxB,YAAY;IACd;CACD;AAGM,MAAM;IACX,eAAe;IACf,OAAO,oBAAoB,YAO1B,EAAsB;QACrB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,GAAG;QAE7E,IAAI,kBAAwE,EAAE;QAE9E,mBAAmB,OAAO,CAAC,CAAA;YACzB,IAAI,QAAQ;YAEZ,SAAS;YACT,IAAI,OAAO;gBACT,IAAI,MAAM,QAAQ,CAAC,SAAS,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,SAAS;gBACnE,IAAI,MAAM,QAAQ,CAAC,SAAS,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,SAAS;gBACnE,IAAI,MAAM,QAAQ,CAAC,SAAS,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,SAAS;YACrE;YAEA,WAAW;YACX,IAAI,YAAY;gBACd,IAAI,eAAe,QAAQ,SAAS,UAAU,KAAK,UAAU,SAAS;gBACtE,IAAI,eAAe,QAAQ,SAAS,UAAU,KAAK,UAAU,SAAS;gBACtE,IAAI,eAAe,QAAQ,SAAS,UAAU,KAAK,WAAW,SAAS;YACzE;YAEA,SAAS;YACT,IAAI,QAAQ;gBACV,IAAI,WAAW,QAAQ,SAAS,UAAU,KAAK,UAAU,SAAS;gBAClE,IAAI,WAAW,QAAQ,SAAS,UAAU,KAAK,UAAU,SAAS;gBAClE,IAAI,WAAW,QAAQ,SAAS,UAAU,KAAK,WAAW,SAAS;gBAEnE,UAAU;gBACV,IAAI,WAAW,SAAS,iBAAiB;oBACvC,IAAI,mBAAmB,SAAS,SAAS,UAAU,KAAK,UAAU,SAAS;yBACtE,IAAI,mBAAmB,UAAU,SAAS,UAAU,KAAK,UAAU,SAAS;yBAC5E,IAAI,kBAAkB,UAAU,SAAS,UAAU,KAAK,WAAW,SAAS;gBACnF;YACF;YAEA,WAAW;YACX,SAAS,OAAO,CAAC,CAAA;gBACf,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC,WAAW;oBACpD,SAAS;gBACX;YACF;YAEA,gBAAgB,IAAI,CAAC;gBAAE;gBAAU;YAAM;QACzC;QAEA,cAAc;QACd,OAAO,gBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IACxB;IAEA,WAAW;IACX,OAAO,uBAAuB,YAM7B,EAAoB;QACnB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG;QAE5D,OAAO;QACP,MAAM,QAAQ;YACZ;gBAAE,MAAM;gBAAS,OAAO;gBAAQ,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;YAAE;SAC9D;QAED,MAAM,cAAyD,EAAE;QACjE,IAAI,WAAW;QACf,IAAI,YAAY;QAEhB,WAAW;QACX,MAAM,UAAU,CAAC,MAAc,OAAe,IAAY,GAAG;YAC3D,MAAM,IAAI,CAAC;gBAAE;gBAAM;gBAAO,UAAU;oBAAE;oBAAG,GAAG;gBAAS;YAAE;YACvD,OAAO;QACT;QAEA,aAAa;QACb,MAAM,YAAY,QAAQ,mBAAmB,QAAQ;QACrD,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAG,QAAQ;QAAU;QAEhD,aAAa;QACb,IAAI,MAAM,QAAQ,CAAC,SAAS,SAAS,QAAQ,CAAC,QAAQ;YACpD,MAAM,YAAY,QAAQ,iBAAiB,SAAS;YACpD,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAG,QAAQ;YAAU;QAClD;QAEA,aAAa;QACb,YAAY;QACZ,MAAM,WAAW,QAAQ,qBAAqB;QAC9C,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAG,QAAQ;QAAS;QAE/C,OAAO;QACP,YAAY;QACZ,MAAM,WAAW,QAAQ,oBAAoB;QAC7C,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAW,QAAQ;QAAS;QACvD,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAU,QAAQ;QAAS;QAEtD,OAAO;QACP,YAAY;QACZ,MAAM,cAAc,QAAQ,qBAAqB;QACjD,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAU,QAAQ;QAAY;QAEzD,iBAAiB;QACjB,IAAI,WAAW,MAAM;YACnB,YAAY;YACZ,MAAM,oBAAoB,QAAQ,oBAAoB;YACtD,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAa,QAAQ;YAAkB;QACpE;QAEA,OAAO;QACP,YAAY;QACZ,MAAM,cAAc,QAAQ,qBAAqB;QACjD,MAAM,WAAW,WAAW,OAAO,YAAY,IAAI;QACnD,YAAY,IAAI,CAAC;YAAE,QAAQ;YAAU,QAAQ;QAAY;QAEzD,cAAc;QACd,IAAI,SAAS,QAAQ,CAAC,UAAU,MAAM,QAAQ,CAAC,OAAO;YACpD,YAAY;YACZ,MAAM,aAAa,QAAQ,oBAAoB,QAAQ;YACvD,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAa,QAAQ;YAAW;YAE3D,MAAM,YAAY,QAAQ,uBAAuB,SAAS;YAC1D,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAa,QAAQ;YAAU;YAE1D,OAAO;YACP,YAAY;YACZ,MAAM,aAAa,QAAQ,UAAU;YACrC,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAY,QAAQ;YAAW;YAC1D,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAW,QAAQ;YAAW;QAC3D,OAAO;YACL,OAAO;YACP,YAAY;YACZ,MAAM,aAAa,QAAQ,UAAU;YACrC,YAAY,IAAI,CAAC;gBAAE,QAAQ;gBAAa,QAAQ;YAAW;QAC7D;QAEA,WAAW;QACX,MAAM,aAAa,WAAW,SAAS,kBACnC,CAAC,IAAI,EAAE,gBAAgB,cAAc,GAAG,EAAE,CAAC,GAC3C;QAEJ,MAAM,gBAAgB;YACpB,IAAI,WAAW,SAAS,iBAAiB;gBACvC,IAAI,mBAAmB,OAAO,OAAO;gBACrC,IAAI,mBAAmB,QAAQ,OAAO;gBACtC,OAAO;YACT;YACA,OAAO,WAAW,OAAO,WAAW,WAAW,OAAO,WAAW;QACnE;QAEA,OAAO;YACL,IAAI,YAAY,KAAK,GAAG;YACxB,MAAM;YACN,aAAa,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,WAAW,QAAQ,CAAC;YAChD;YACA;YACA,MAAM;gBAAC;gBAAO;gBAAO;aAAO;YAC5B,YAAY;QACd;IACF;IAEA,UAAU;IACV,OAAO,iBAAiB,YAAmB,EAAE,YAAiB,EAAoB;QAChF,SAAS;QACT,MAAM,YAAY,aAAa,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,IAAI;QAEzD,YAAY;QACZ,MAAM,eAAe,EAAE;QACvB,IAAI,CAAC,UAAU,QAAQ,CAAC,UAAU,aAAa,IAAI,CAAC;QACpD,IAAI,CAAC,UAAU,QAAQ,CAAC,WAAW,aAAa,IAAI,CAAC;QACrD,IAAI,CAAC,UAAU,QAAQ,CAAC,sBAAsB,aAAa,IAAI,CAAC;QAChE,IAAI,CAAC,UAAU,QAAQ,CAAC,sBAAsB,aAAa,IAAI,CAAC;QAEhE,SAAS;QACT,MAAM,iBAAiB;eAAI;SAAa;QACxC,MAAM,cAAyD,EAAE;QAEjE,UAAU;QACV,aAAa,OAAO,CAAC,CAAC,UAAU;YAC9B,eAAe,IAAI,CAAC;gBAClB,MAAM;gBACN,OAAO,IAAI,CAAC,YAAY,CAAC;gBACzB,UAAU;oBAAE,GAAG,MAAM,QAAQ;oBAAK,GAAG,MAAM,QAAQ;gBAAI;YACzD;QACF;QAEA,OAAO;YACL,IAAI,eAAe,KAAK,GAAG;YAC3B,MAAM;YACN,aAAa;YACb,OAAO;YACP;YACA,MAAM;gBAAC;gBAAM;aAAK;YAClB,YAAY;QACd;IACF;IAEA,OAAe,aAAa,QAAgB,EAAU;QACpD,MAAM,SAAiC;YACrC,SAAS;YACT,mBAAmB;YACnB,oBAAoB;YACpB,qBAAqB;YACrB,iBAAiB;YACjB,oBAAoB;YACpB,qBAAqB;YACrB,uBAAuB;YACvB,oBAAoB;YACpB,qBAAqB;YACrB,oBAAoB;YACpB,uBAAuB;YACvB,aAAa;YACb,QAAQ;YACR,UAAU;QACZ;QACA,OAAO,MAAM,CAAC,SAAS,IAAI;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 1827, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/utils/workflowExecutor.ts"], "sourcesContent": ["// 工作流执行引擎 - 所有功能调用AI模型\nimport { aiService } from './aiService';\nimport { message } from 'antd';\n\n// 节点执行结果接口\ninterface NodeExecutionResult {\n  success: boolean;\n  data?: any;\n  error?: string;\n  duration?: number;\n}\n\n// 执行上下文接口\ninterface ExecutionContext {\n  projectId: string;\n  workflowId: string;\n  variables: Record<string, any>;\n  results: Record<string, any>;\n  currentNode?: string;\n}\n\n// 工作流执行器类\nexport class WorkflowExecutor {\n  private context: ExecutionContext;\n  private onProgress?: (nodeId: string, status: 'running' | 'completed' | 'error', result?: any) => void;\n\n  constructor(context: ExecutionContext, onProgress?: (nodeId: string, status: string, result?: any) => void) {\n    this.context = context;\n    this.onProgress = onProgress;\n  }\n\n  // 执行单个节点\n  async executeNode(node: any): Promise<NodeExecutionResult> {\n    const startTime = Date.now();\n    this.context.currentNode = node.id;\n    \n    // 通知开始执行\n    this.onProgress?.(node.id, 'running');\n\n    try {\n      let result: any;\n\n      switch (node.data.type) {\n        case 'input':\n          result = await this.executeInputNode(node);\n          break;\n        case 'title-generator':\n          result = await this.executeTitleGeneratorNode(node);\n          break;\n        case 'detail-generator':\n          result = await this.executeDetailGeneratorNode(node);\n          break;\n        case 'character-creator':\n          result = await this.executeCharacterCreatorNode(node);\n          break;\n        case 'worldbuilding':\n          result = await this.executeWorldbuildingNode(node);\n          break;\n        case 'plotline-planner':\n          result = await this.executePlotlinePlannerNode(node);\n          break;\n        case 'outline-generator':\n          result = await this.executeOutlineGeneratorNode(node);\n          break;\n        case 'detailed-outline':\n          result = await this.executeDetailedOutlineNode(node);\n          break;\n        case 'chapter-generator':\n          result = await this.executeChapterGeneratorNode(node);\n          break;\n        case 'content-polisher':\n          result = await this.executeContentPolisherNode(node);\n          break;\n        case 'consistency-checker':\n          result = await this.executeConsistencyCheckerNode(node);\n          break;\n        case 'output':\n          result = await this.executeOutputNode(node);\n          break;\n        default:\n          throw new Error(`不支持的节点类型: ${node.data.type}`);\n      }\n\n      const duration = Date.now() - startTime;\n      \n      // 保存结果到上下文\n      this.context.results[node.id] = result;\n      \n      // 通知执行完成\n      this.onProgress?.(node.id, 'completed', result);\n\n      return {\n        success: true,\n        data: result,\n        duration\n      };\n\n    } catch (error: any) {\n      const duration = Date.now() - startTime;\n      \n      // 通知执行错误\n      this.onProgress?.(node.id, 'error', error.message);\n\n      return {\n        success: false,\n        error: error.message,\n        duration\n      };\n    }\n  }\n\n  // 输入节点执行\n  private async executeInputNode(node: any): Promise<any> {\n    // 输入节点通常从用户界面获取数据\n    const inputData = node.data.config || this.context.variables.userInput || {};\n    \n    return {\n      type: 'input',\n      data: inputData,\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  // 书名生成节点执行\n  private async executeTitleGeneratorNode(node: any): Promise<any> {\n    // 检查AI配置\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    // 获取输入数据\n    const inputData = this.getInputData(['input']);\n    const config = node.data.config || {};\n\n    const params = {\n      genre: inputData.genre || config.genre || '现代都市',\n      style: inputData.style || config.style || '轻松幽默',\n      keywords: inputData.keywords || config.keywords || [],\n      count: config.count || 5\n    };\n\n    const response = await aiService.generateTitles(params);\n    \n    if (!response.success) {\n      throw new Error(`AI书名生成失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      message.success(`AI成功生成${result.titles?.length || 0}个书名候选`);\n      return {\n        type: 'titles',\n        titles: result.titles || [],\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 详情生成节点执行\n  private async executeDetailGeneratorNode(node: any): Promise<any> {\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    const inputData = this.getInputData(['input', 'title-generator']);\n    const config = node.data.config || {};\n\n    const params = {\n      title: inputData.selectedTitle || inputData.titles?.[0]?.title || config.title || '未命名小说',\n      genre: inputData.genre || config.genre || '现代都市',\n      style: inputData.style || config.style || '轻松幽默',\n      outline: inputData.outline,\n      characters: inputData.characters\n    };\n\n    const response = await aiService.generateNovelDetails(params);\n    \n    if (!response.success) {\n      throw new Error(`AI详情生成失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      message.success('AI成功生成小说详情');\n      return {\n        type: 'novel_details',\n        details: result,\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 角色创建节点执行\n  private async executeCharacterCreatorNode(node: any): Promise<any> {\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    const inputData = this.getInputData(['input']);\n    const config = node.data.config || {};\n\n    const params = {\n      genre: inputData.genre || config.genre || '现代都市',\n      role: config.role || '主角',\n      background: config.background,\n      personality: config.personality\n    };\n\n    const response = await aiService.generateCharacter(params);\n    \n    if (!response.success) {\n      throw new Error(`AI角色生成失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      message.success(`AI成功生成角色: ${result.name}`);\n      return {\n        type: 'character',\n        character: result,\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 世界观构建节点执行\n  private async executeWorldbuildingNode(node: any): Promise<any> {\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    const inputData = this.getInputData(['input']);\n    const config = node.data.config || {};\n\n    const params = {\n      genre: inputData.genre || config.genre || '现代都市',\n      style: inputData.style || config.style || '轻松幽默',\n      scope: config.scope || '中等规模',\n      elements: config.elements || []\n    };\n\n    const response = await aiService.generateWorldbuilding(params);\n    \n    if (!response.success) {\n      throw new Error(`AI世界观生成失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      message.success(`AI成功生成世界观: ${result.name}`);\n      return {\n        type: 'worldbuilding',\n        worldbuilding: result,\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 主线规划节点执行\n  private async executePlotlinePlannerNode(node: any): Promise<any> {\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    const inputData = this.getInputData(['input', 'character-creator', 'worldbuilding']);\n    const config = node.data.config || {};\n\n    const params = {\n      genre: inputData.genre || config.genre || '现代都市',\n      style: inputData.style || config.style || '轻松幽默',\n      characters: this.getAllCharacters(inputData),\n      worldbuilding: inputData.worldbuilding,\n      themes: config.themes || []\n    };\n\n    const response = await aiService.generatePlotline(params);\n    \n    if (!response.success) {\n      throw new Error(`AI主线规划失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      message.success(`AI成功生成主线规划: ${result.title}`);\n      return {\n        type: 'plotline',\n        plotline: result,\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 获取输入数据的辅助方法\n  private getInputData(nodeTypes: string[]): any {\n    const data: any = {};\n    \n    for (const nodeId in this.context.results) {\n      const result = this.context.results[nodeId];\n      if (nodeTypes.some(type => result.type === type || nodeId.includes(type))) {\n        Object.assign(data, result.data || result);\n      }\n    }\n    \n    return { ...this.context.variables, ...data };\n  }\n\n  // 大纲生成节点执行\n  private async executeOutlineGeneratorNode(node: any): Promise<any> {\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    const inputData = this.getInputData(['input', 'character-creator', 'plotline-planner']);\n    const config = node.data.config || {};\n\n    const params = {\n      genre: inputData.genre || config.genre || '现代都市',\n      plotline: inputData.plotline || {},\n      characters: this.getAllCharacters(inputData),\n      targetLength: inputData.length || config.targetLength || '中篇',\n      chapterCount: config.chapterCount\n    };\n\n    const response = await aiService.generateOutline(params);\n\n    if (!response.success) {\n      throw new Error(`AI大纲生成失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      message.success(`AI成功生成大纲，共${result.chapters?.length || 0}章`);\n      return {\n        type: 'outline',\n        outline: result,\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 详细大纲节点执行\n  private async executeDetailedOutlineNode(node: any): Promise<any> {\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    const inputData = this.getInputData(['outline-generator', 'character-creator', 'worldbuilding']);\n    const config = node.data.config || {};\n\n    const params = {\n      outline: inputData.outline || {},\n      characters: this.getAllCharacters(inputData),\n      worldbuilding: inputData.worldbuilding,\n      selectedChapters: config.selectedChapters\n    };\n\n    const response = await aiService.generateDetailedOutline(params);\n\n    if (!response.success) {\n      throw new Error(`AI详细大纲生成失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      message.success('AI成功生成详细大纲');\n      return {\n        type: 'detailed_outline',\n        detailedOutline: result,\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 章节生成节点执行\n  private async executeChapterGeneratorNode(node: any): Promise<any> {\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    const inputData = this.getInputData(['detailed-outline', 'outline-generator', 'character-creator', 'worldbuilding']);\n    const config = node.data.config || {};\n\n    const chapterNumber = config.chapterNumber || 1;\n    const outline = inputData.detailedOutline || inputData.outline || {};\n    const chapterOutline = outline.chapters?.[chapterNumber - 1] || {};\n\n    const params = {\n      chapterNumber,\n      chapterOutline,\n      characters: this.getAllCharacters(inputData),\n      worldbuilding: inputData.worldbuilding,\n      previousChapters: inputData.previousChapters || [],\n      style: inputData.style || config.style || '轻松幽默',\n      targetWordCount: config.targetWordCount || 2000\n    };\n\n    const response = await aiService.generateChapter(params);\n\n    if (!response.success) {\n      throw new Error(`AI章节生成失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      message.success(`AI成功生成第${chapterNumber}章，约${result.word_count || 0}字`);\n      return {\n        type: 'chapter',\n        chapter: result,\n        chapterNumber,\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 内容润色节点执行\n  private async executeContentPolisherNode(node: any): Promise<any> {\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    const inputData = this.getInputData(['chapter-generator']);\n    const config = node.data.config || {};\n\n    const content = inputData.chapter?.content || config.content || '';\n    if (!content) {\n      throw new Error('没有找到需要润色的内容');\n    }\n\n    const params = {\n      content,\n      style: inputData.style || config.style || '轻松幽默',\n      focusAreas: config.focusAreas || ['表达优化', '节奏调整'],\n      targetAudience: config.targetAudience || '一般读者'\n    };\n\n    const response = await aiService.polishContent(params);\n\n    if (!response.success) {\n      throw new Error(`AI内容润色失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      message.success(`AI成功润色内容，改进评分: ${result.readability_score || 'N/A'}`);\n      return {\n        type: 'polished_content',\n        polishedContent: result,\n        originalContent: content,\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 一致性检查节点执行\n  private async executeConsistencyCheckerNode(node: any): Promise<any> {\n    if (!aiService.isConfigured()) {\n      throw new Error('请先配置AI API');\n    }\n\n    const inputData = this.getInputData(['chapter-generator', 'character-creator', 'worldbuilding']);\n    const config = node.data.config || {};\n\n    const content = [];\n\n    // 收集所有章节内容\n    for (const nodeId in this.context.results) {\n      const result = this.context.results[nodeId];\n      if (result.type === 'chapter' && result.chapter?.content) {\n        content.push(result.chapter.content);\n      }\n    }\n\n    if (content.length === 0) {\n      throw new Error('没有找到需要检查的内容');\n    }\n\n    const params = {\n      content,\n      characters: this.getAllCharacters(inputData),\n      worldbuilding: inputData.worldbuilding,\n      checkTypes: config.checkTypes || ['角色一致性', '情节一致性', '世界观一致性']\n    };\n\n    const response = await aiService.checkConsistency(params);\n\n    if (!response.success) {\n      throw new Error(`AI一致性检查失败: ${response.error}`);\n    }\n\n    try {\n      const result = JSON.parse(response.data);\n      const score = result.consistency_score || 0;\n      const issueCount = result.issues?.length || 0;\n\n      if (score >= 80) {\n        message.success(`一致性检查完成，评分: ${score}分，发现${issueCount}个问题`);\n      } else {\n        message.warning(`一致性检查完成，评分: ${score}分，发现${issueCount}个问题`);\n      }\n\n      return {\n        type: 'consistency_check',\n        consistencyReport: result,\n        params,\n        timestamp: new Date().toISOString()\n      };\n    } catch (parseError) {\n      throw new Error('AI返回数据格式错误');\n    }\n  }\n\n  // 输出节点执行\n  private async executeOutputNode(node: any): Promise<any> {\n    const inputData = this.getInputData(['title-generator', 'detail-generator', 'chapter-generator', 'polished_content', 'consistency_check']);\n\n    // 整理最终输出\n    const output = {\n      title: inputData.selectedTitle || inputData.titles?.[0]?.title || '未命名小说',\n      details: inputData.details || {},\n      chapters: [],\n      consistencyReport: inputData.consistencyReport,\n      metadata: {\n        generatedAt: new Date().toISOString(),\n        totalChapters: 0,\n        totalWords: 0,\n        aiGenerated: true\n      }\n    };\n\n    // 收集所有章节\n    for (const nodeId in this.context.results) {\n      const result = this.context.results[nodeId];\n      if (result.type === 'chapter') {\n        output.chapters.push(result.chapter);\n        output.metadata.totalWords += result.chapter.word_count || 0;\n      }\n    }\n\n    output.metadata.totalChapters = output.chapters.length;\n\n    message.success(`小说创作完成！共${output.metadata.totalChapters}章，约${output.metadata.totalWords}字`);\n\n    return {\n      type: 'final_output',\n      output,\n      timestamp: new Date().toISOString()\n    };\n  }\n\n  // 获取所有角色的辅助方法\n  private getAllCharacters(inputData: any): any[] {\n    const characters = [];\n\n    // 从角色创建节点获取角色\n    if (inputData.character) {\n      characters.push(inputData.character);\n    }\n\n    // 从其他来源获取角色\n    if (inputData.characters && Array.isArray(inputData.characters)) {\n      characters.push(...inputData.characters);\n    }\n\n    return characters;\n  }\n}\n\n// 导出执行器工厂函数\nexport const createWorkflowExecutor = (\n  context: ExecutionContext,\n  onProgress?: (nodeId: string, status: string, result?: any) => void\n) => {\n  return new WorkflowExecutor(context, onProgress);\n};\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;AACvB;AACA;;;AAoBO,MAAM;IACH,QAA0B;IAC1B,WAA+F;IAEvG,YAAY,OAAyB,EAAE,UAAmE,CAAE;QAC1G,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA,SAAS;IACT,MAAM,YAAY,IAAS,EAAgC;QACzD,MAAM,YAAY,KAAK,GAAG;QAC1B,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,KAAK,EAAE;QAElC,SAAS;QACT,IAAI,CAAC,UAAU,GAAG,KAAK,EAAE,EAAE;QAE3B,IAAI;YACF,IAAI;YAEJ,OAAQ,KAAK,IAAI,CAAC,IAAI;gBACpB,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,gBAAgB,CAAC;oBACrC;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,yBAAyB,CAAC;oBAC9C;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,0BAA0B,CAAC;oBAC/C;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,2BAA2B,CAAC;oBAChD;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,wBAAwB,CAAC;oBAC7C;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,0BAA0B,CAAC;oBAC/C;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,2BAA2B,CAAC;oBAChD;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,0BAA0B,CAAC;oBAC/C;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,2BAA2B,CAAC;oBAChD;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,0BAA0B,CAAC;oBAC/C;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,6BAA6B,CAAC;oBAClD;gBACF,KAAK;oBACH,SAAS,MAAM,IAAI,CAAC,iBAAiB,CAAC;oBACtC;gBACF;oBACE,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE;YACjD;YAEA,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,GAAG;YAEhC,SAAS;YACT,IAAI,CAAC,UAAU,GAAG,KAAK,EAAE,EAAE,aAAa;YAExC,OAAO;gBACL,SAAS;gBACT,MAAM;gBACN;YACF;QAEF,EAAE,OAAO,OAAY;YACnB,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,SAAS;YACT,IAAI,CAAC,UAAU,GAAG,KAAK,EAAE,EAAE,SAAS,MAAM,OAAO;YAEjD,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO;gBACpB;YACF;QACF;IACF;IAEA,SAAS;IACT,MAAc,iBAAiB,IAAS,EAAgB;QACtD,kBAAkB;QAClB,MAAM,YAAY,KAAK,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,IAAI,CAAC;QAE3E,OAAO;YACL,MAAM;YACN,MAAM;YACN,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,WAAW;IACX,MAAc,0BAA0B,IAAS,EAAgB;QAC/D,SAAS;QACT,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,SAAS;QACT,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;SAAQ;QAC7C,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,SAAS;YACb,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,UAAU,UAAU,QAAQ,IAAI,OAAO,QAAQ,IAAI,EAAE;YACrD,OAAO,OAAO,KAAK,IAAI;QACzB;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,cAAc,CAAC;QAEhD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,KAAK,EAAE;QAC/C;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC;YAC1D,OAAO;gBACL,MAAM;gBACN,QAAQ,OAAO,MAAM,IAAI,EAAE;gBAC3B;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,WAAW;IACX,MAAc,2BAA2B,IAAS,EAAgB;QAChE,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;YAAS;SAAkB;QAChE,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,SAAS;YACb,OAAO,UAAU,aAAa,IAAI,UAAU,MAAM,EAAE,CAAC,EAAE,EAAE,SAAS,OAAO,KAAK,IAAI;YAClF,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,SAAS,UAAU,OAAO;YAC1B,YAAY,UAAU,UAAU;QAClC;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC;QAEtD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,KAAK,EAAE;QAC/C;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,WAAW;IACX,MAAc,4BAA4B,IAAS,EAAgB;QACjE,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;SAAQ;QAC7C,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,SAAS;YACb,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,MAAM,OAAO,IAAI,IAAI;YACrB,YAAY,OAAO,UAAU;YAC7B,aAAa,OAAO,WAAW;QACjC;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC;QAEnD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,KAAK,EAAE;QAC/C;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,OAAO,IAAI,EAAE;YAC1C,OAAO;gBACL,MAAM;gBACN,WAAW;gBACX;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,YAAY;IACZ,MAAc,yBAAyB,IAAS,EAAgB;QAC9D,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;SAAQ;QAC7C,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,SAAS;YACb,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,OAAO,OAAO,KAAK,IAAI;YACvB,UAAU,OAAO,QAAQ,IAAI,EAAE;QACjC;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,qBAAqB,CAAC;QAEvD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,KAAK,EAAE;QAChD;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,OAAO,IAAI,EAAE;YAC3C,OAAO;gBACL,MAAM;gBACN,eAAe;gBACf;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,WAAW;IACX,MAAc,2BAA2B,IAAS,EAAgB;QAChE,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;YAAS;YAAqB;SAAgB;QACnF,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,SAAS;YACb,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAClC,eAAe,UAAU,aAAa;YACtC,QAAQ,OAAO,MAAM,IAAI,EAAE;QAC7B;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;QAElD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,KAAK,EAAE;QAC/C;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,OAAO,KAAK,EAAE;YAC7C,OAAO;gBACL,MAAM;gBACN,UAAU;gBACV;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,cAAc;IACN,aAAa,SAAmB,EAAO;QAC7C,MAAM,OAAY,CAAC;QAEnB,IAAK,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,OAAO,CAAE;YACzC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO;YAC3C,IAAI,UAAU,IAAI,CAAC,CAAA,OAAQ,OAAO,IAAI,KAAK,QAAQ,OAAO,QAAQ,CAAC,QAAQ;gBACzE,OAAO,MAAM,CAAC,MAAM,OAAO,IAAI,IAAI;YACrC;QACF;QAEA,OAAO;YAAE,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;YAAE,GAAG,IAAI;QAAC;IAC9C;IAEA,WAAW;IACX,MAAc,4BAA4B,IAAS,EAAgB;QACjE,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;YAAS;YAAqB;SAAmB;QACtF,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,SAAS;YACb,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,UAAU,UAAU,QAAQ,IAAI,CAAC;YACjC,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAClC,cAAc,UAAU,MAAM,IAAI,OAAO,YAAY,IAAI;YACzD,cAAc,OAAO,YAAY;QACnC;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,eAAe,CAAC;QAEjD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,KAAK,EAAE;QAC/C;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,OAAO,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;YAC5D,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,WAAW;IACX,MAAc,2BAA2B,IAAS,EAAgB;QAChE,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;YAAqB;YAAqB;SAAgB;QAC/F,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,SAAS;YACb,SAAS,UAAU,OAAO,IAAI,CAAC;YAC/B,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAClC,eAAe,UAAU,aAAa;YACtC,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,uBAAuB,CAAC;QAEzD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,SAAS,KAAK,EAAE;QACjD;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM;gBACN,iBAAiB;gBACjB;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,WAAW;IACX,MAAc,4BAA4B,IAAS,EAAgB;QACjE,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;YAAoB;YAAqB;YAAqB;SAAgB;QACnH,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,gBAAgB,OAAO,aAAa,IAAI;QAC9C,MAAM,UAAU,UAAU,eAAe,IAAI,UAAU,OAAO,IAAI,CAAC;QACnE,MAAM,iBAAiB,QAAQ,QAAQ,EAAE,CAAC,gBAAgB,EAAE,IAAI,CAAC;QAEjE,MAAM,SAAS;YACb;YACA;YACA,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAClC,eAAe,UAAU,aAAa;YACtC,kBAAkB,UAAU,gBAAgB,IAAI,EAAE;YAClD,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,iBAAiB,OAAO,eAAe,IAAI;QAC7C;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,eAAe,CAAC;QAEjD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,KAAK,EAAE;QAC/C;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,cAAc,GAAG,EAAE,OAAO,UAAU,IAAI,EAAE,CAAC,CAAC;YACtE,OAAO;gBACL,MAAM;gBACN,SAAS;gBACT;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,WAAW;IACX,MAAc,2BAA2B,IAAS,EAAgB;QAChE,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;SAAoB;QACzD,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,UAAU,UAAU,OAAO,EAAE,WAAW,OAAO,OAAO,IAAI;QAChE,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS;YACb;YACA,OAAO,UAAU,KAAK,IAAI,OAAO,KAAK,IAAI;YAC1C,YAAY,OAAO,UAAU,IAAI;gBAAC;gBAAQ;aAAO;YACjD,gBAAgB,OAAO,cAAc,IAAI;QAC3C;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,aAAa,CAAC;QAE/C,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,SAAS,KAAK,EAAE;QAC/C;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,OAAO,iBAAiB,IAAI,OAAO;YACrE,OAAO;gBACL,MAAM;gBACN,iBAAiB;gBACjB,iBAAiB;gBACjB;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,YAAY;IACZ,MAAc,8BAA8B,IAAS,EAAgB;QACnE,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;YAAqB;YAAqB;SAAgB;QAC/F,MAAM,SAAS,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC;QAEpC,MAAM,UAAU,EAAE;QAElB,WAAW;QACX,IAAK,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,OAAO,CAAE;YACzC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO;YAC3C,IAAI,OAAO,IAAI,KAAK,aAAa,OAAO,OAAO,EAAE,SAAS;gBACxD,QAAQ,IAAI,CAAC,OAAO,OAAO,CAAC,OAAO;YACrC;QACF;QAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS;YACb;YACA,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAClC,eAAe,UAAU,aAAa;YACtC,YAAY,OAAO,UAAU,IAAI;gBAAC;gBAAS;gBAAS;aAAS;QAC/D;QAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;QAElD,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS,KAAK,EAAE;QAChD;QAEA,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,IAAI;YACvC,MAAM,QAAQ,OAAO,iBAAiB,IAAI;YAC1C,MAAM,aAAa,OAAO,MAAM,EAAE,UAAU;YAE5C,IAAI,SAAS,IAAI;gBACf,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,MAAM,IAAI,EAAE,WAAW,GAAG,CAAC;YAC5D,OAAO;gBACL,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,MAAM,IAAI,EAAE,WAAW,GAAG,CAAC;YAC5D;YAEA,OAAO;gBACL,MAAM;gBACN,mBAAmB;gBACnB;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,YAAY;YACnB,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,SAAS;IACT,MAAc,kBAAkB,IAAS,EAAgB;QACvD,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;YAAC;YAAmB;YAAoB;YAAqB;YAAoB;SAAoB;QAEzI,SAAS;QACT,MAAM,SAAS;YACb,OAAO,UAAU,aAAa,IAAI,UAAU,MAAM,EAAE,CAAC,EAAE,EAAE,SAAS;YAClE,SAAS,UAAU,OAAO,IAAI,CAAC;YAC/B,UAAU,EAAE;YACZ,mBAAmB,UAAU,iBAAiB;YAC9C,UAAU;gBACR,aAAa,IAAI,OAAO,WAAW;gBACnC,eAAe;gBACf,YAAY;gBACZ,aAAa;YACf;QACF;QAEA,SAAS;QACT,IAAK,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,OAAO,CAAE;YACzC,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO;YAC3C,IAAI,OAAO,IAAI,KAAK,WAAW;gBAC7B,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,OAAO;gBACnC,OAAO,QAAQ,CAAC,UAAU,IAAI,OAAO,OAAO,CAAC,UAAU,IAAI;YAC7D;QACF;QAEA,OAAO,QAAQ,CAAC,aAAa,GAAG,OAAO,QAAQ,CAAC,MAAM;QAEtD,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,QAAQ,CAAC,aAAa,CAAC,GAAG,EAAE,OAAO,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;QAE3F,OAAO;YACL,MAAM;YACN;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;IAEA,cAAc;IACN,iBAAiB,SAAc,EAAS;QAC9C,MAAM,aAAa,EAAE;QAErB,cAAc;QACd,IAAI,UAAU,SAAS,EAAE;YACvB,WAAW,IAAI,CAAC,UAAU,SAAS;QACrC;QAEA,YAAY;QACZ,IAAI,UAAU,UAAU,IAAI,MAAM,OAAO,CAAC,UAAU,UAAU,GAAG;YAC/D,WAAW,IAAI,IAAI,UAAU,UAAU;QACzC;QAEA,OAAO;IACT;AACF;AAGO,MAAM,yBAAyB,CACpC,SACA;IAEA,OAAO,IAAI,iBAAiB,SAAS;AACvC", "debugId": null}}, {"offset": {"line": 2368, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport WorkflowEditor from '@/components/workflow/WorkflowEditor';\nimport ProjectOverview from '@/components/project/ProjectOverview';\nimport OutlineManager from '@/components/outline/OutlineManager';\nimport CharacterManager from '@/components/character/CharacterManager';\nimport WorldBuildingManager from '@/components/worldbuilding/WorldBuildingManager';\nimport PlotLineManager from '@/components/plotline/PlotLineManager';\nimport TitleManager from '@/components/title/TitleManager';\nimport DocumentManager from '@/components/document/DocumentManager';\nimport PromptManager from '@/components/prompt/PromptManager';\nimport { useAppStore } from '@/store';\n\nexport default function Home() {\n  const { ui } = useAppStore();\n\n  const renderContent = () => {\n    switch (ui.activeTab) {\n      case 'workflow':\n        return <WorkflowEditor />;\n      case 'projects':\n        return <ProjectOverview />;\n      case 'outlines':\n        return <OutlineManager />;\n      case 'characters':\n        return <CharacterManager />;\n      case 'worldbuilding':\n        return <WorldBuildingManager />;\n      case 'plotlines':\n        return <PlotLineManager />;\n      case 'titles':\n        return <TitleManager />;\n      case 'documents':\n        return <DocumentManager />;\n      case 'prompts':\n        return <PromptManager />;\n      default:\n        return <WorkflowEditor />;\n    }\n  };\n\n  return (\n    <MainLayout>\n      {renderContent()}\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEzB,MAAM,gBAAgB;QACpB,OAAQ,GAAG,SAAS;YAClB,KAAK;gBACH,qBAAO,8OAAC,gJAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,gJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,+IAAA,CAAA,UAAc;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,mJAAA,CAAA,UAAgB;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2JAAA,CAAA,UAAoB;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,iJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,2IAAA,CAAA,UAAY;;;;;YACtB,KAAK;gBACH,qBAAO,8OAAC,iJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,6IAAA,CAAA,UAAa;;;;;YACvB;gBACE,qBAAO,8OAAC,gJAAA,CAAA,UAAc;;;;;QAC1B;IACF;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACR;;;;;;AAGP", "debugId": null}}]}