# AI小说生成工作流平台 - 用户手册

## 欢迎使用AI小说生成工作流平台！

这是一个专为小说创作者设计的智能创作工具，通过可视化工作流和AI辅助，帮助您高效地创作出优质的小说作品。

## 快速开始

### 第一步：创建您的第一个项目

1. 打开应用后，点击右上角的"创建新项目"按钮
2. 填写项目信息：
   - **项目名称**: 给您的小说起一个名字
   - **项目描述**: 简要描述您的小说内容和主题
   - **小说类型**: 选择适合的类型（现代都市、古代言情、玄幻修仙等）
   - **写作风格**: 选择您偏好的风格（轻松幽默、深沉严肃等）
   - **目标字数**: 设定您希望达到的总字数
   - **预计章节数**: 规划大概的章节数量
3. 点击"创建"完成项目创建

### 第二步：设计您的创作工作流

1. 项目创建后，系统会自动跳转到"工作流编辑器"
2. 在左侧的节点库中，您可以看到各种预设的创作节点
3. 点击任意节点即可添加到工作流画布中
4. 建议的基础工作流顺序：
   - 输入节点 → 书名生成 → 详情生成 → 角色创建 → 世界观构建 → 大纲规划 → 章节生成 → 输出节点

### 第三步：执行工作流

1. 完成工作流设计后，点击"保存工作流"
2. 点击"开始执行"按钮启动AI创作流程
3. 系统会按照您设计的流程逐步生成内容
4. 您可以随时暂停或停止执行过程

## 功能详解

### 🎯 项目管理

#### 项目总览
- 查看所有创建的项目
- 项目状态管理（草稿、进行中、已完成）
- 项目信息编辑和删除
- 快速切换当前工作项目

#### 项目设置
- **类型选择**: 8种主流小说类型可选
- **风格定义**: 6种写作风格可选
- **字数规划**: 灵活设置目标字数（1万-100万字）
- **章节规划**: 自定义章节数量（1-500章）

### ⚡ 工作流编辑器

#### 节点类型说明

**输入类节点**
- **输入节点**: 设置创作参数，如主题、风格要求等
- **章节数量节点**: 指定具体的章节数量

**生成类节点**
- **书名生成**: AI生成多个书名候选供您选择
- **详情生成**: 基于书名生成小说简介、背景等基础信息
- **角色创建**: AI创建主要角色的设定和背景故事
- **世界观构建**: 构建故事发生的世界背景和设定
- **主线规划**: 规划主要故事线和情节发展
- **大纲规划**: 生成整体故事结构和章节安排
- **细纲生成**: 为每个章节生成详细的情节要点
- **章节生成**: 生成具体的章节内容

**优化类节点**
- **内容润色**: AI优化和修改文本质量
- **一致性检查**: 检查角色、世界观、主线的一致性

**控制类节点**
- **条件分支**: 根据条件选择不同的执行路径
- **循环节点**: 重复执行特定的创作步骤
- **输出节点**: 展示和导出最终创作结果

#### 工作流操作
- **添加节点**: 从左侧节点库点击添加
- **删除节点**: 选中节点后点击删除按钮
- **节点配置**: 点击节点查看和修改属性
- **执行控制**: 开始、暂停、停止工作流执行
- **进度监控**: 实时查看执行进度和状态

### 📚 内容管理系统

#### 大纲管理（开发中）
- 树形结构展示故事大纲
- 章节和小节的层级管理
- 拖拽重排章节顺序
- 大纲版本管理和历史对比

#### 角色管理（开发中）
- 角色卡片式展示
- 角色关系图谱可视化
- 角色发展时间线
- 角色模板库和快速生成

#### 世界观管理（开发中）
- 世界设定分类管理
- 设定元素关联图
- 世界观一致性检查
- 多种世界观模板

#### 主线管理（开发中）
- 故事线时间轴展示
- 主线与支线关联管理
- 情节冲突和高潮点标记
- 故事节奏分析

#### 书名管理（开发中）
- 智能书名生成器
- 书名评分和分析
- 书名收藏和分类
- 重复度检查

### 🛠️ 系统设置

#### 主题设置
- 亮色主题：适合白天使用
- 暗色主题：适合夜间创作

#### 语言设置
- 简体中文（默认）
- English（计划支持）

#### 通知管理
- 创作进度通知
- 系统消息提醒
- 错误和警告信息

## 创作流程建议

### 标准创作流程

1. **前期准备**
   - 创建项目并设置基本信息
   - 确定小说类型和风格

2. **创意生成**
   - 使用书名生成节点获得灵感
   - 生成基础详情和背景设定

3. **角色设计**
   - 创建主要角色
   - 建立角色关系网络

4. **世界构建**
   - 设定故事背景世界
   - 建立世界观体系

5. **情节规划**
   - 设计主要故事线
   - 规划章节结构

6. **内容创作**
   - 生成详细大纲
   - 逐章创作内容

7. **优化完善**
   - 内容润色和修改
   - 一致性检查和调整

### 高效创作技巧

1. **模块化创作**: 将创作过程分解为独立的模块，便于管理和修改
2. **版本管理**: 定期保存工作流和内容，便于回溯和比较
3. **批量处理**: 利用循环节点批量生成章节内容
4. **质量控制**: 使用一致性检查节点确保内容质量

## 常见问题

### Q: 如何修改已生成的内容？
A: 在对应的管理页面中找到需要修改的内容，点击编辑按钮进行修改。

### Q: 工作流执行失败怎么办？
A: 检查节点配置是否正确，确保所有必需的参数都已设置。可以尝试重新执行或跳过问题节点。

### Q: 如何备份我的项目？
A: 目前数据自动保存在浏览器本地存储中。建议定期导出项目数据进行备份。

### Q: 可以同时进行多个项目吗？
A: 可以创建多个项目，但同时只能有一个活动项目进行工作流编辑。

### Q: 生成的内容质量不满意怎么办？
A: 可以调整节点参数重新生成，或使用内容润色节点进行优化。

## 技术支持

如果您在使用过程中遇到问题，可以：

1. 查看本用户手册的相关章节
2. 检查浏览器控制台是否有错误信息
3. 尝试刷新页面或重启应用
4. 联系技术支持团队

## 更新日志

### v0.1.0 (当前版本)
- ✅ 基础项目管理功能
- ✅ 工作流编辑器框架
- ✅ 主布局和导航系统
- ✅ 基础节点类型支持

### 即将推出
- 🔄 React Flow集成的可视化编辑
- 🔄 完整的内容管理系统
- 🔄 AI接口集成
- 🔄 导入导出功能

---

感谢您选择AI小说生成工作流平台！祝您创作愉快！
