// AI服务集成
interface AIConfig {
  provider: 'openai' | 'claude' | 'gemini' | 'custom';
  apiKey: string;
  baseUrl?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
}

interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// AI服务类
export class AIService {
  private config: AIConfig | null = null;

  // 设置AI配置
  setConfig(config: AIConfig) {
    this.config = config;
    // 保存到localStorage (仅在客户端)
    if (typeof window !== 'undefined') {
      localStorage.setItem('ai-config', JSON.stringify(config));
    }
  }

  // 获取AI配置
  getConfig(): AIConfig | null {
    if (this.config) return this.config;

    // 仅在客户端访问localStorage
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('ai-config');
      if (saved) {
        this.config = JSON.parse(saved);
        return this.config;
      }
    }
    return null;
  }

  // 检查是否已配置
  isConfigured(): boolean {
    return this.getConfig() !== null;
  }

  // 通用AI请求方法
  private async makeRequest(prompt: string, systemPrompt?: string): Promise<AIResponse> {
    const config = this.getConfig();
    if (!config) {
      return { success: false, error: '请先配置AI API' };
    }

    try {
      let response;
      
      switch (config.provider) {
        case 'openai':
          response = await this.callOpenAI(prompt, systemPrompt, config);
          break;
        case 'claude':
          response = await this.callClaude(prompt, systemPrompt, config);
          break;
        case 'gemini':
          response = await this.callGemini(prompt, systemPrompt, config);
          break;
        case 'custom':
          response = await this.callCustomAPI(prompt, systemPrompt, config);
          break;
        default:
          return { success: false, error: '不支持的AI提供商' };
      }

      return { success: true, data: response };
    } catch (error: any) {
      return { success: false, error: error.message || '请求失败' };
    }
  }

  // OpenAI API调用
  private async callOpenAI(prompt: string, systemPrompt: string = '', config: AIConfig) {
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    messages.push({ role: 'user', content: prompt });

    const response = await fetch(config.baseUrl || 'https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify({
        model: config.model || 'gpt-3.5-turbo',
        messages,
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  // Claude API调用
  private async callClaude(prompt: string, systemPrompt: string = '', config: AIConfig) {
    const response = await fetch(config.baseUrl || 'https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: config.model || 'claude-3-sonnet-20240229',
        max_tokens: config.maxTokens || 2000,
        temperature: config.temperature || 0.7,
        system: systemPrompt,
        messages: [{ role: 'user', content: prompt }],
      }),
    });

    if (!response.ok) {
      throw new Error(`Claude API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.content[0].text;
  }

  // Gemini API调用
  private async callGemini(prompt: string, systemPrompt: string = '', config: AIConfig) {
    const fullPrompt = systemPrompt ? `${systemPrompt}\n\n${prompt}` : prompt;
    
    const response = await fetch(
      `${config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta/models'}/${config.model || 'gemini-pro'}:generateContent?key=${config.apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{ parts: [{ text: fullPrompt }] }],
          generationConfig: {
            temperature: config.temperature || 0.7,
            maxOutputTokens: config.maxTokens || 2000,
          },
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Gemini API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  }

  // 自定义API调用
  private async callCustomAPI(prompt: string, systemPrompt: string = '', config: AIConfig) {
    const response = await fetch(config.baseUrl!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify({
        prompt,
        system_prompt: systemPrompt,
        model: config.model,
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`自定义API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.response || data.content || data.text;
  }

  // 分析工作流需求
  async analyzeWorkflowRequirements(requirements: {
    genre: string;
    style: string;
    length: string;
    experience: string;
    features: string[];
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个专业的小说创作工作流设计专家。根据用户的需求，分析并推荐最适合的工作流类型。

可选的工作流类型：
1. quick-novel - 快速小说生成（适合新手，简单流程）
2. professional-novel - 专业小说创作（完整流程，适合有经验的作者）
3. fantasy-novel - 奇幻小说专用（重点关注世界观和魔法体系）
4. romance-novel - 言情小说工作流（专注情感描写和角色关系）

请返回JSON格式的推荐结果，包含：
- recommended: 推荐的工作流ID
- confidence: 推荐置信度(0-100)
- reasons: 推荐理由数组
- alternatives: 备选方案数组`;

    const prompt = `用户需求：
- 小说类型：${requirements.genre}
- 写作风格：${requirements.style}
- 作品长度：${requirements.length}
- 创作经验：${requirements.experience}
- 特殊需求：${requirements.features.join(', ')}

请分析并推荐最适合的工作流。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成自定义工作流
  async generateCustomWorkflow(requirements: {
    genre: string;
    style: string;
    length: string;
    features: string[];
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个AI小说创作工作流设计师。根据用户需求设计一个定制化的工作流。

可用的节点类型：
- input: 用户参数输入
- title-generator: 书名生成
- detail-generator: 详情生成
- character-creator: 角色创建
- worldbuilding: 世界观构建
- plotline-planner: 主线规划
- outline-generator: 大纲生成
- chapter-count-input: 章节数设定
- detailed-outline: 详细大纲
- chapter-generator: 章节生成
- content-polisher: 内容润色
- consistency-checker: 一致性检查
- condition: 条件分支
- loop: 循环执行
- output: 结果输出

请返回JSON格式的工作流定义，包含：
- name: 工作流名称
- description: 工作流描述
- nodes: 节点数组，每个节点包含 {type, label, position: {x, y}}
- connections: 连接数组，每个连接包含 {source: 节点索引, target: 节点索引}
- complexity: 复杂度 (simple/medium/complex)`;

    const prompt = `请为以下需求设计一个定制化的小说创作工作流：
- 小说类型：${requirements.genre}
- 写作风格：${requirements.style}
- 作品长度：${requirements.length}
- 特殊需求：${requirements.features.join(', ')}

请确保工作流逻辑合理，节点连接有序，适合用户的具体需求。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 优化现有工作流
  async optimizeWorkflow(currentNodes: any[], requirements: any): Promise<AIResponse> {
    const systemPrompt = `你是一个工作流优化专家。分析现有的工作流并提供优化建议。

请返回JSON格式的优化建议，包含：
- issues: 发现的问题数组
- suggestions: 优化建议数组
- optimized_workflow: 优化后的工作流定义（如果需要重构）
- improvement_score: 改进评分(0-100)`;

    const nodeTypes = currentNodes.map(node => node.data?.type || 'unknown');
    const prompt = `当前工作流包含以下节点：${nodeTypes.join(', ')}

用户需求：${JSON.stringify(requirements)}

请分析这个工作流的问题并提供优化建议。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成书名
  async generateTitles(params: {
    genre: string;
    style: string;
    keywords?: string[];
    count?: number;
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个专业的书名创作专家。根据小说类型、风格和关键词生成吸引人的书名。

请返回JSON格式的结果，包含：
- titles: 书名数组，每个包含 {title, score, analysis: {appeal, memorability, genre_match, uniqueness, marketability}, suggestions: 优化建议数组}`;

    const prompt = `请为以下小说生成${params.count || 5}个书名：
- 类型：${params.genre}
- 风格：${params.style}
- 关键词：${params.keywords?.join(', ') || '无'}

要求书名要有吸引力、易记忆、符合类型特征。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成角色
  async generateCharacter(params: {
    genre: string;
    role: string;
    background?: string;
    personality?: string;
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个角色设计专家。根据小说类型和角色定位创建详细的角色设定。

请返回JSON格式的角色信息，包含：
- name: 角色姓名
- age: 年龄
- gender: 性别
- appearance: 外貌描述
- personality: 性格特征
- background: 背景故事
- skills: 技能特长
- relationships: 人际关系
- goals: 目标动机
- flaws: 性格缺陷`;

    const prompt = `请创建一个${params.genre}小说中的${params.role}角色：
${params.background ? `背景要求：${params.background}` : ''}
${params.personality ? `性格要求：${params.personality}` : ''}

请提供详细的角色设定。`;

    return this.makeRequest(prompt, systemPrompt);
  }
}

// 导出单例实例
export const aiService = new AIService();
