// AI服务集成
interface AIConfig {
  provider: 'openai' | 'claude' | 'gemini' | 'custom';
  apiKey: string;
  baseUrl?: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
}

interface AIResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// AI服务类
export class AIService {
  private config: AIConfig | null = null;

  // 设置AI配置
  setConfig(config: AIConfig) {
    this.config = config;
    // 保存到localStorage (仅在客户端)
    if (typeof window !== 'undefined') {
      localStorage.setItem('ai-config', JSON.stringify(config));
    }
  }

  // 获取AI配置
  getConfig(): AIConfig | null {
    if (this.config) return this.config;

    // 仅在客户端访问localStorage
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('ai-config');
      if (saved) {
        this.config = JSON.parse(saved);
        return this.config;
      }
    }
    return null;
  }

  // 检查是否已配置
  isConfigured(): boolean {
    return this.getConfig() !== null;
  }

  // 通用AI请求方法
  private async makeRequest(prompt: string, systemPrompt?: string): Promise<AIResponse> {
    const config = this.getConfig();
    if (!config) {
      return { success: false, error: '请先配置AI API' };
    }

    try {
      let response;
      
      switch (config.provider) {
        case 'openai':
          response = await this.callOpenAI(prompt, systemPrompt, config);
          break;
        case 'claude':
          response = await this.callClaude(prompt, systemPrompt, config);
          break;
        case 'gemini':
          response = await this.callGemini(prompt, systemPrompt, config);
          break;
        case 'custom':
          response = await this.callCustomAPI(prompt, systemPrompt, config);
          break;
        default:
          return { success: false, error: '不支持的AI提供商' };
      }

      return { success: true, data: response };
    } catch (error: any) {
      return { success: false, error: error.message || '请求失败' };
    }
  }

  // OpenAI API调用
  private async callOpenAI(prompt: string, systemPrompt: string = '', config: AIConfig) {
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    messages.push({ role: 'user', content: prompt });

    const response = await fetch(config.baseUrl || 'https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
      },
      body: JSON.stringify({
        model: config.model || 'gpt-3.5-turbo',
        messages,
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  }

  // Claude API调用
  private async callClaude(prompt: string, systemPrompt: string = '', config: AIConfig) {
    const response = await fetch(config.baseUrl || 'https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: config.model || 'claude-3-sonnet-20240229',
        max_tokens: config.maxTokens || 2000,
        temperature: config.temperature || 0.7,
        system: systemPrompt,
        messages: [{ role: 'user', content: prompt }],
      }),
    });

    if (!response.ok) {
      throw new Error(`Claude API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.content[0].text;
  }

  // Gemini API调用
  private async callGemini(prompt: string, systemPrompt: string = '', config: AIConfig) {
    const fullPrompt = systemPrompt ? `${systemPrompt}\n\n${prompt}` : prompt;
    
    const response = await fetch(
      `${config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta/models'}/${config.model || 'gemini-pro'}:generateContent?key=${config.apiKey}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{ parts: [{ text: fullPrompt }] }],
          generationConfig: {
            temperature: config.temperature || 0.7,
            maxOutputTokens: config.maxTokens || 2000,
          },
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Gemini API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  }

  // 自定义API调用
  private async callCustomAPI(prompt: string, systemPrompt: string = '', config: AIConfig) {
    // 尝试OpenAI兼容格式
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    messages.push({ role: 'user', content: prompt });

    const requestBody = {
      model: config.model || 'gpt-3.5-turbo',
      messages,
      temperature: config.temperature || 0.7,
      max_tokens: config.maxTokens || 2000,
    };

    try {
      const response = await fetch(config.baseUrl!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`自定义API错误: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      // 尝试不同的响应格式
      if (data.choices && data.choices[0] && data.choices[0].message) {
        return data.choices[0].message.content;
      } else if (data.response) {
        return data.response;
      } else if (data.content) {
        return data.content;
      } else if (data.text) {
        return data.text;
      } else {
        throw new Error('无法解析API响应格式');
      }
    } catch (error: any) {
      // 如果OpenAI格式失败，尝试简单格式
      try {
        const simpleRequestBody = {
          prompt,
          system_prompt: systemPrompt,
          model: config.model,
          temperature: config.temperature || 0.7,
          max_tokens: config.maxTokens || 2000,
        };

        const response = await fetch(config.baseUrl!, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.apiKey}`,
          },
          body: JSON.stringify(simpleRequestBody),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`自定义API错误: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const data = await response.json();
        return data.response || data.content || data.text || JSON.stringify(data);
      } catch (secondError: any) {
        throw new Error(`自定义API调用失败: ${error.message}. 备用格式也失败: ${secondError.message}`);
      }
    }
  }

  // 分析工作流需求
  async analyzeWorkflowRequirements(requirements: {
    genre: string;
    style: string;
    length: string;
    experience: string;
    features: string[];
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个专业的小说创作工作流设计专家。根据用户的需求，分析并推荐最适合的工作流类型。

可选的工作流类型：
1. quick-novel - 快速小说生成（适合新手，简单流程）
2. professional-novel - 专业小说创作（完整流程，适合有经验的作者）
3. fantasy-novel - 奇幻小说专用（重点关注世界观和魔法体系）
4. romance-novel - 言情小说工作流（专注情感描写和角色关系）

请返回JSON格式的推荐结果，包含：
- recommended: 推荐的工作流ID
- confidence: 推荐置信度(0-100)
- reasons: 推荐理由数组
- alternatives: 备选方案数组`;

    const prompt = `用户需求：
- 小说类型：${requirements.genre}
- 写作风格：${requirements.style}
- 作品长度：${requirements.length}
- 创作经验：${requirements.experience}
- 特殊需求：${requirements.features.join(', ')}

请分析并推荐最适合的工作流。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成自定义工作流
  async generateCustomWorkflow(requirements: {
    genre: string;
    style: string;
    length: string;
    features: string[];
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个AI小说创作工作流设计师。根据用户需求设计一个定制化的工作流。

可用的节点类型：
- input: 用户参数输入
- title-generator: 书名生成
- detail-generator: 详情生成
- character-creator: 角色创建
- worldbuilding: 世界观构建
- plotline-planner: 主线规划
- outline-generator: 大纲生成
- chapter-count-input: 章节数设定
- detailed-outline: 详细大纲
- chapter-generator: 章节生成
- content-polisher: 内容润色
- consistency-checker: 一致性检查
- condition: 条件分支
- loop: 循环执行
- output: 结果输出

请返回JSON格式的工作流定义，包含：
- name: 工作流名称
- description: 工作流描述
- nodes: 节点数组，每个节点包含 {type, label, position: {x, y}}
- connections: 连接数组，每个连接包含 {source: 节点索引, target: 节点索引}
- complexity: 复杂度 (simple/medium/complex)`;

    const prompt = `请为以下需求设计一个定制化的小说创作工作流：
- 小说类型：${requirements.genre}
- 写作风格：${requirements.style}
- 作品长度：${requirements.length}
- 特殊需求：${requirements.features.join(', ')}

请确保工作流逻辑合理，节点连接有序，适合用户的具体需求。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 优化现有工作流
  async optimizeWorkflow(currentNodes: any[], requirements: any): Promise<AIResponse> {
    const systemPrompt = `你是一个工作流优化专家。分析现有的工作流并提供优化建议。

请返回JSON格式的优化建议，包含：
- issues: 发现的问题数组
- suggestions: 优化建议数组
- optimized_workflow: 优化后的工作流定义（如果需要重构）
- improvement_score: 改进评分(0-100)`;

    const nodeTypes = currentNodes.map(node => node.data?.type || 'unknown');
    const prompt = `当前工作流包含以下节点：${nodeTypes.join(', ')}

用户需求：${JSON.stringify(requirements)}

请分析这个工作流的问题并提供优化建议。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成书名
  async generateTitles(params: {
    genre: string;
    style: string;
    keywords?: string[];
    count?: number;
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个专业的书名创作专家。根据小说类型、风格和关键词生成吸引人的书名。

请返回JSON格式的结果，包含：
- titles: 书名数组，每个包含 {title, score, analysis: {appeal, memorability, genre_match, uniqueness, marketability}, suggestions: 优化建议数组}`;

    const prompt = `请为以下小说生成${params.count || 5}个书名：
- 类型：${params.genre}
- 风格：${params.style}
- 关键词：${params.keywords?.join(', ') || '无'}

要求书名要有吸引力、易记忆、符合类型特征。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成角色
  async generateCharacter(params: {
    genre: string;
    role: string;
    background?: string;
    personality?: string;
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个角色设计专家。根据小说类型和角色定位创建详细的角色设定。

请返回JSON格式的角色信息，包含：
- name: 角色姓名
- age: 年龄
- gender: 性别
- appearance: 外貌描述
- personality: 性格特征
- background: 背景故事
- skills: 技能特长
- relationships: 人际关系
- goals: 目标动机
- flaws: 性格缺陷`;

    const prompt = `请创建一个${params.genre}小说中的${params.role}角色：
${params.background ? `背景要求：${params.background}` : ''}
${params.personality ? `性格要求：${params.personality}` : ''}

请提供详细的角色设定。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成世界观
  async generateWorldbuilding(params: {
    genre: string;
    style: string;
    scope?: string;
    elements?: string[];
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个世界观构建专家。根据小说类型和风格创建详细的世界设定。

请返回JSON格式的世界观信息，包含：
- name: 世界名称
- overview: 世界概述
- geography: 地理环境
- history: 历史背景
- culture: 文化特色
- politics: 政治体系
- economy: 经济体系
- technology: 科技水平
- magic_system: 魔法/超能力体系（如适用）
- races: 种族设定
- languages: 语言系统
- religions: 宗教信仰
- conflicts: 主要冲突`;

    const prompt = `请为${params.genre}类型的小说创建世界观设定：
- 风格：${params.style}
- 范围：${params.scope || '中等规模'}
- 特殊元素：${params.elements?.join(', ') || '无'}

请提供详细的世界观设定。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成主线规划
  async generatePlotline(params: {
    genre: string;
    style: string;
    characters: any[];
    worldbuilding?: any;
    themes?: string[];
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个故事情节规划专家。根据角色和世界观创建引人入胜的主线情节。

请返回JSON格式的主线规划，包含：
- title: 主线标题
- premise: 故事前提
- inciting_incident: 引发事件
- plot_points: 关键情节点数组，每个包含 {act, description, characters_involved, conflicts, outcomes}
- climax: 高潮设定
- resolution: 结局安排
- themes: 主题表达
- character_arcs: 角色成长弧线
- subplots: 支线情节建议`;

    const charactersInfo = params.characters.map(char => `${char.name}(${char.role})`).join(', ');
    const prompt = `请为${params.genre}小说规划主线情节：
- 风格：${params.style}
- 主要角色：${charactersInfo}
- 世界背景：${params.worldbuilding?.name || '待定'}
- 主题方向：${params.themes?.join(', ') || '待定'}

请创建完整的故事主线规划。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成大纲
  async generateOutline(params: {
    genre: string;
    plotline: any;
    characters: any[];
    targetLength: string;
    chapterCount?: number;
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个小说大纲创作专家。根据主线情节创建详细的章节大纲。

请返回JSON格式的大纲信息，包含：
- title: 大纲标题
- structure: 结构类型（三幕式、英雄之旅等）
- chapters: 章节数组，每个包含 {number, title, summary, key_events, character_focus, word_count_target, notes}
- pacing: 节奏安排
- tension_curve: 张力曲线描述
- themes_distribution: 主题分布`;

    const prompt = `请为${params.genre}小说创建详细大纲：
- 目标长度：${params.targetLength}
- 预计章节数：${params.chapterCount || '待定'}
- 主线情节：${params.plotline.title || '待定'}
- 主要角色：${params.characters.map(c => c.name).join(', ')}

请创建完整的章节大纲。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成详细大纲
  async generateDetailedOutline(params: {
    outline: any;
    characters: any[];
    worldbuilding?: any;
    selectedChapters?: number[];
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个详细大纲创作专家。将基础大纲扩展为详细的情节要点。

请返回JSON格式的详细大纲，包含：
- chapters: 详细章节数组，每个包含 {
    number, title, detailed_summary, scenes: [{
      scene_number, location, characters, objective, conflict, outcome, mood, pov
    }], character_development, plot_advancement, foreshadowing, themes
  }`;

    const chapterInfo = params.selectedChapters ?
      `重点章节：${params.selectedChapters.join(', ')}` :
      '所有章节';

    const prompt = `请为以下大纲创建详细的情节要点：
- 基础大纲：${params.outline.title}
- 角色信息：${params.characters.map(c => c.name).join(', ')}
- 世界设定：${params.worldbuilding?.name || '待定'}
- 处理范围：${chapterInfo}

请提供详细的场景和情节要点。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成章节内容
  async generateChapter(params: {
    chapterNumber: number;
    chapterOutline: any;
    characters: any[];
    worldbuilding?: any;
    previousChapters?: string[];
    style: string;
    targetWordCount?: number;
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个专业的小说创作者。根据章节大纲创作具体的章节内容。

请返回JSON格式的章节内容，包含：
- title: 章节标题
- content: 章节正文内容
- word_count: 字数统计
- scenes: 场景分解
- character_moments: 角色重要时刻
- plot_advancement: 情节推进要点
- foreshadowing: 伏笔设置
- themes_explored: 探讨的主题`;

    const previousContext = params.previousChapters?.length ?
      `前文概要：${params.previousChapters.slice(-2).join('\n')}` :
      '这是开篇章节';

    const prompt = `请创作第${params.chapterNumber}章的具体内容：
- 章节大纲：${JSON.stringify(params.chapterOutline)}
- 主要角色：${params.characters.map(c => c.name).join(', ')}
- 写作风格：${params.style}
- 目标字数：${params.targetWordCount || 2000}字
- ${previousContext}

请创作完整的章节内容。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 内容润色
  async polishContent(params: {
    content: string;
    style: string;
    focusAreas?: string[];
    targetAudience?: string;
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个专业的文本润色专家。优化文本的表达、节奏和文学性。

请返回JSON格式的润色结果，包含：
- polished_content: 润色后的内容
- improvements: 改进说明数组
- style_analysis: 风格分析
- readability_score: 可读性评分
- suggestions: 进一步优化建议`;

    const focusInfo = params.focusAreas?.length ?
      `重点关注：${params.focusAreas.join(', ')}` :
      '全面优化';

    const prompt = `请润色以下文本内容：
- 目标风格：${params.style}
- 目标读者：${params.targetAudience || '一般读者'}
- ${focusInfo}

原文内容：
${params.content}

请提供润色后的版本和改进说明。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 一致性检查
  async checkConsistency(params: {
    content: string[];
    characters: any[];
    worldbuilding?: any;
    checkTypes?: string[];
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个内容一致性检查专家。检查文本中的逻辑、角色、设定等一致性问题。

请返回JSON格式的检查结果，包含：
- consistency_score: 一致性评分(0-100)
- issues: 问题数组，每个包含 {type, severity, description, location, suggestion}
- character_consistency: 角色一致性分析
- plot_consistency: 情节一致性分析
- world_consistency: 世界观一致性分析
- timeline_issues: 时间线问题
- recommendations: 修改建议`;

    const checkInfo = params.checkTypes?.length ?
      `检查类型：${params.checkTypes.join(', ')}` :
      '全面检查';

    const prompt = `请检查以下内容的一致性：
- 角色设定：${params.characters.map(c => c.name).join(', ')}
- 世界设定：${params.worldbuilding?.name || '待定'}
- ${checkInfo}

内容文本：
${params.content.join('\n\n---\n\n')}

请提供详细的一致性分析报告。`;

    return this.makeRequest(prompt, systemPrompt);
  }

  // 生成小说详情
  async generateNovelDetails(params: {
    title: string;
    genre: string;
    style: string;
    outline?: any;
    characters?: any[];
  }): Promise<AIResponse> {
    const systemPrompt = `你是一个小说包装专家。根据小说内容生成吸引人的详情信息。

请返回JSON格式的详情信息，包含：
- synopsis: 内容简介
- tagline: 宣传语
- keywords: 关键词数组
- target_audience: 目标读者群体
- selling_points: 卖点分析
- genre_tags: 类型标签
- mood_tags: 氛围标签
- content_warnings: 内容提醒
- marketing_description: 营销描述`;

    const prompt = `请为小说《${params.title}》生成详情信息：
- 类型：${params.genre}
- 风格：${params.style}
- 大纲概要：${params.outline?.title || '待定'}
- 主要角色：${params.characters?.map(c => c.name).join(', ') || '待定'}

请生成完整的小说详情包装。`;

    return this.makeRequest(prompt, systemPrompt);
  }
}

// 导出单例实例
export const aiService = new AIService();
