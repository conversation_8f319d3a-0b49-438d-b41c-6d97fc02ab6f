module.exports = {

"[project]/src/store/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "useAppStore": ()=>useAppStore
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
// 生成唯一ID的工具函数
const generateId = ()=>Math.random().toString(36).substr(2, 9);
const useAppStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // 初始UI状态
        ui: {
            theme: 'light',
            language: 'zh-CN',
            sidebarCollapsed: false,
            activeTab: 'workflow',
            selectedProject: undefined,
            notifications: []
        },
        // UI状态管理
        setTheme: (theme)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        theme
                    }
                })),
        setLanguage: (language)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        language
                    }
                })),
        toggleSidebar: ()=>set((state)=>({
                    ui: {
                        ...state.ui,
                        sidebarCollapsed: !state.ui.sidebarCollapsed
                    }
                })),
        setActiveTab: (activeTab)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        activeTab
                    }
                })),
        addNotification: (notification)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        notifications: [
                            ...state.ui.notifications,
                            {
                                ...notification,
                                id: generateId(),
                                timestamp: new Date(),
                                read: false
                            }
                        ]
                    }
                })),
        markNotificationRead: (id)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        notifications: state.ui.notifications.map((n)=>n.id === id ? {
                                ...n,
                                read: true
                            } : n)
                    }
                })),
        clearNotifications: ()=>set((state)=>({
                    ui: {
                        ...state.ui,
                        notifications: []
                    }
                })),
        // 项目管理
        projects: [],
        currentProject: null,
        createProject: (project)=>set((state)=>{
                const newProject = {
                    ...project,
                    id: generateId(),
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                return {
                    projects: [
                        ...state.projects,
                        newProject
                    ],
                    currentProject: newProject,
                    ui: {
                        ...state.ui,
                        selectedProject: newProject.id
                    }
                };
            }),
        updateProject: (id, updates)=>set((state)=>({
                    projects: state.projects.map((p)=>p.id === id ? {
                            ...p,
                            ...updates,
                            updatedAt: new Date()
                        } : p),
                    currentProject: state.currentProject?.id === id ? {
                        ...state.currentProject,
                        ...updates,
                        updatedAt: new Date()
                    } : state.currentProject
                })),
        deleteProject: (id)=>set((state)=>({
                    projects: state.projects.filter((p)=>p.id !== id),
                    currentProject: state.currentProject?.id === id ? null : state.currentProject,
                    ui: {
                        ...state.ui,
                        selectedProject: state.ui.selectedProject === id ? undefined : state.ui.selectedProject
                    }
                })),
        setCurrentProject: (id)=>set((state)=>{
                const project = state.projects.find((p)=>p.id === id);
                return {
                    currentProject: project || null,
                    ui: {
                        ...state.ui,
                        selectedProject: id
                    }
                };
            }),
        // 工作流管理
        workflows: {},
        currentWorkflow: [],
        addNode: (node)=>set((state)=>({
                    currentWorkflow: [
                        ...state.currentWorkflow,
                        {
                            ...node,
                            id: generateId()
                        }
                    ]
                })),
        updateNode: (id, updates)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.map((n)=>n.id === id ? {
                            ...n,
                            ...updates
                        } : n)
                })),
        deleteNode: (id)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.filter((n)=>n.id !== id)
                })),
        connectNodes: (sourceId, targetId)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.map((n)=>n.id === sourceId ? {
                            ...n,
                            connections: [
                                ...n.connections,
                                {
                                    sourceId,
                                    targetId
                                }
                            ]
                        } : n)
                })),
        disconnectNodes: (sourceId, targetId)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.map((n)=>n.id === sourceId ? {
                            ...n,
                            connections: n.connections.filter((c)=>!(c.sourceId === sourceId && c.targetId === targetId))
                        } : n)
                })),
        loadWorkflow: (projectId)=>set((state)=>({
                    currentWorkflow: state.workflows[projectId] || []
                })),
        saveWorkflow: (projectId)=>set((state)=>({
                    workflows: {
                        ...state.workflows,
                        [projectId]: state.currentWorkflow
                    }
                })),
        // 角色管理
        characters: {},
        addCharacter: (projectId, character)=>set((state)=>({
                    characters: {
                        ...state.characters,
                        [projectId]: [
                            ...state.characters[projectId] || [],
                            {
                                ...character,
                                id: generateId()
                            }
                        ]
                    }
                })),
        updateCharacter: (projectId, id, updates)=>set((state)=>({
                    characters: {
                        ...state.characters,
                        [projectId]: (state.characters[projectId] || []).map((c)=>c.id === id ? {
                                ...c,
                                ...updates
                            } : c)
                    }
                })),
        deleteCharacter: (projectId, id)=>set((state)=>({
                    characters: {
                        ...state.characters,
                        [projectId]: (state.characters[projectId] || []).filter((c)=>c.id !== id)
                    }
                })),
        getCharacters: (projectId)=>get().characters[projectId] || [],
        // 世界观管理
        worldBuilding: {},
        addWorldElement: (projectId, element)=>set((state)=>({
                    worldBuilding: {
                        ...state.worldBuilding,
                        [projectId]: [
                            ...state.worldBuilding[projectId] || [],
                            {
                                ...element,
                                id: generateId()
                            }
                        ]
                    }
                })),
        updateWorldElement: (projectId, id, updates)=>set((state)=>({
                    worldBuilding: {
                        ...state.worldBuilding,
                        [projectId]: (state.worldBuilding[projectId] || []).map((w)=>w.id === id ? {
                                ...w,
                                ...updates
                            } : w)
                    }
                })),
        deleteWorldElement: (projectId, id)=>set((state)=>({
                    worldBuilding: {
                        ...state.worldBuilding,
                        [projectId]: (state.worldBuilding[projectId] || []).filter((w)=>w.id !== id)
                    }
                })),
        getWorldElements: (projectId)=>get().worldBuilding[projectId] || [],
        // 其他管理功能的占位符实现
        outlines: {},
        addOutline: (projectId, outline)=>set((state)=>({
                    outlines: {
                        ...state.outlines,
                        [projectId]: [
                            ...state.outlines[projectId] || [],
                            {
                                ...outline,
                                id: generateId(),
                                createdAt: new Date(),
                                updatedAt: new Date()
                            }
                        ]
                    }
                })),
        updateOutline: (projectId, id, updates)=>set((state)=>({
                    outlines: {
                        ...state.outlines,
                        [projectId]: (state.outlines[projectId] || []).map((o)=>o.id === id ? {
                                ...o,
                                ...updates,
                                updatedAt: new Date()
                            } : o)
                    }
                })),
        deleteOutline: (projectId, id)=>set((state)=>({
                    outlines: {
                        ...state.outlines,
                        [projectId]: (state.outlines[projectId] || []).filter((o)=>o.id !== id)
                    }
                })),
        getOutlines: (projectId)=>get().outlines[projectId] || [],
        plotLines: {},
        addPlotLine: (projectId, plotLine)=>set((state)=>({
                    plotLines: {
                        ...state.plotLines,
                        [projectId]: [
                            ...state.plotLines[projectId] || [],
                            {
                                ...plotLine,
                                id: generateId()
                            }
                        ]
                    }
                })),
        updatePlotLine: (projectId, id, updates)=>set((state)=>({
                    plotLines: {
                        ...state.plotLines,
                        [projectId]: (state.plotLines[projectId] || []).map((p)=>p.id === id ? {
                                ...p,
                                ...updates
                            } : p)
                    }
                })),
        deletePlotLine: (projectId, id)=>set((state)=>({
                    plotLines: {
                        ...state.plotLines,
                        [projectId]: (state.plotLines[projectId] || []).filter((p)=>p.id !== id)
                    }
                })),
        getPlotLines: (projectId)=>get().plotLines[projectId] || [],
        bookTitles: {},
        addBookTitle: (projectId, title)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: [
                            ...state.bookTitles[projectId] || [],
                            {
                                ...title,
                                id: generateId(),
                                createdAt: new Date()
                            }
                        ]
                    }
                })),
        updateBookTitle: (projectId, id, updates)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: (state.bookTitles[projectId] || []).map((t)=>t.id === id ? {
                                ...t,
                                ...updates
                            } : t)
                    }
                })),
        deleteBookTitle: (projectId, id)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: (state.bookTitles[projectId] || []).filter((t)=>t.id !== id)
                    }
                })),
        toggleTitleFavorite: (projectId, id)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: (state.bookTitles[projectId] || []).map((t)=>t.id === id ? {
                                ...t,
                                isFavorite: !t.isFavorite
                            } : t)
                    }
                })),
        getBookTitles: (projectId)=>get().bookTitles[projectId] || [],
        chapters: {},
        addChapter: (projectId, chapter)=>set((state)=>({
                    chapters: {
                        ...state.chapters,
                        [projectId]: [
                            ...state.chapters[projectId] || [],
                            {
                                ...chapter,
                                id: generateId(),
                                createdAt: new Date(),
                                updatedAt: new Date()
                            }
                        ]
                    }
                })),
        updateChapter: (projectId, id, updates)=>set((state)=>({
                    chapters: {
                        ...state.chapters,
                        [projectId]: (state.chapters[projectId] || []).map((c)=>c.id === id ? {
                                ...c,
                                ...updates,
                                updatedAt: new Date()
                            } : c)
                    }
                })),
        deleteChapter: (projectId, id)=>set((state)=>({
                    chapters: {
                        ...state.chapters,
                        [projectId]: (state.chapters[projectId] || []).filter((c)=>c.id !== id)
                    }
                })),
        reorderChapters: (projectId, fromIndex, toIndex)=>set((state)=>{
                const chapters = [
                    ...state.chapters[projectId] || []
                ];
                const [removed] = chapters.splice(fromIndex, 1);
                chapters.splice(toIndex, 0, removed);
                // 重新设置order
                chapters.forEach((chapter, index)=>{
                    chapter.order = index + 1;
                });
                return {
                    chapters: {
                        ...state.chapters,
                        [projectId]: chapters
                    }
                };
            }),
        getChapters: (projectId)=>get().chapters[projectId] || [],
        promptTemplates: [],
        addPromptTemplate: (template)=>set((state)=>({
                    promptTemplates: [
                        ...state.promptTemplates,
                        {
                            ...template,
                            id: generateId()
                        }
                    ]
                })),
        updatePromptTemplate: (id, updates)=>set((state)=>({
                    promptTemplates: state.promptTemplates.map((t)=>t.id === id ? {
                            ...t,
                            ...updates
                        } : t)
                })),
        deletePromptTemplate: (id)=>set((state)=>({
                    promptTemplates: state.promptTemplates.filter((t)=>t.id !== id)
                })),
        getPromptTemplatesByCategory: (category)=>get().promptTemplates.filter((t)=>t.category === category),
        documentStructures: {},
        initializeDocumentStructure: (projectId)=>set((state)=>({
                    documentStructures: {
                        ...state.documentStructures,
                        [projectId]: {
                            projectId,
                            folders: [],
                            files: [],
                            lastBackup: new Date()
                        }
                    }
                })),
        updateDocumentStructure: (projectId, structure)=>set((state)=>({
                    documentStructures: {
                        ...state.documentStructures,
                        [projectId]: {
                            ...state.documentStructures[projectId],
                            ...structure
                        }
                    }
                })),
        executionContexts: {},
        startExecution: (projectId, workflowId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            projectId,
                            workflowId,
                            status: 'running',
                            progress: {
                                totalSteps: 0,
                                completedSteps: 0,
                                currentStep: '',
                                percentage: 0
                            },
                            queue: [],
                            results: {},
                            startTime: new Date()
                        }
                    }
                })),
        pauseExecution: (projectId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            status: 'paused'
                        }
                    }
                })),
        resumeExecution: (projectId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            status: 'running'
                        }
                    }
                })),
        stopExecution: (projectId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            status: 'idle',
                            endTime: new Date()
                        }
                    }
                })),
        updateExecutionProgress: (projectId, progress)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            ...progress
                        }
                    }
                }))
    }), {
    name: 'ai-novel-workflow-storage',
    partialize: (state)=>({
            projects: state.projects,
            workflows: state.workflows,
            characters: state.characters,
            worldBuilding: state.worldBuilding,
            outlines: state.outlines,
            plotLines: state.plotLines,
            bookTitles: state.bookTitles,
            chapters: state.chapters,
            promptTemplates: state.promptTemplates,
            documentStructures: state.documentStructures,
            ui: {
                theme: state.ui.theme,
                language: state.ui.language,
                sidebarCollapsed: state.ui.sidebarCollapsed
            }
        })
}), {
    name: 'ai-novel-workflow'
}));
const __TURBOPACK__default__export__ = useAppStore;
}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>Home
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$MainLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/MainLayout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$workflow$2f$WorkflowEditor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/workflow/WorkflowEditor.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$project$2f$ProjectOverview$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/project/ProjectOverview.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$outline$2f$OutlineManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/outline/OutlineManager.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$character$2f$CharacterManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/character/CharacterManager.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$worldbuilding$2f$WorldBuildingManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/worldbuilding/WorldBuildingManager.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$plotline$2f$PlotLineManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/plotline/PlotLineManager.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$title$2f$TitleManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/title/TitleManager.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$document$2f$DocumentManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/document/DocumentManager.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$prompt$2f$PromptManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/prompt/PromptManager.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/index.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
function Home() {
    const { ui } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])();
    const renderContent = ()=>{
        switch(ui.activeTab){
            case 'workflow':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$workflow$2f$WorkflowEditor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 22,
                    columnNumber: 16
                }, this);
            case 'projects':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$project$2f$ProjectOverview$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 24,
                    columnNumber: 16
                }, this);
            case 'outlines':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$outline$2f$OutlineManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 26,
                    columnNumber: 16
                }, this);
            case 'characters':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$character$2f$CharacterManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 28,
                    columnNumber: 16
                }, this);
            case 'worldbuilding':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$worldbuilding$2f$WorldBuildingManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 30,
                    columnNumber: 16
                }, this);
            case 'plotlines':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$plotline$2f$PlotLineManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 32,
                    columnNumber: 16
                }, this);
            case 'titles':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$title$2f$TitleManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 34,
                    columnNumber: 16
                }, this);
            case 'documents':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$document$2f$DocumentManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 36,
                    columnNumber: 16
                }, this);
            case 'prompts':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$prompt$2f$PromptManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 38,
                    columnNumber: 16
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$workflow$2f$WorkflowEditor$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 40,
                    columnNumber: 16
                }, this);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$MainLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        children: renderContent()
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 45,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=src_124b3d16._.js.map