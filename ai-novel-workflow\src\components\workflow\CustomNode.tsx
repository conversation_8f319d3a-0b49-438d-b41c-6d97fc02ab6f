'use client';

import React from 'react';
import { Handle, Position, NodeProps } from '@xyflow/react';
import { Card, Typography, Tag, Button } from 'antd';
import { 
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  DeleteOutlined
} from '@ant-design/icons';

const { Text } = Typography;

interface CustomNodeData {
  label: string;
  type: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  description?: string;
  config?: Record<string, any>;
  onEdit?: () => void;
  onDelete?: () => void;
}

const CustomNode: React.FC<NodeProps<CustomNodeData>> = ({ data, selected }) => {
  const getStatusIcon = () => {
    switch (data.status) {
      case 'running':
        return <PauseCircleOutlined className="text-blue-500" />;
      case 'completed':
        return <CheckCircleOutlined className="text-green-500" />;
      case 'error':
        return <ExclamationCircleOutlined className="text-red-500" />;
      default:
        return <PlayCircleOutlined className="text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (data.status) {
      case 'running':
        return 'processing';
      case 'completed':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const getNodeIcon = () => {
    switch (data.type) {
      case 'input':
        return '📝';
      case 'title-generator':
        return '📚';
      case 'detail-generator':
        return '📄';
      case 'character-creator':
        return '👥';
      case 'worldbuilding':
        return '🌍';
      case 'plotline-planner':
        return '📈';
      case 'outline-generator':
        return '📋';
      case 'chapter-count-input':
        return '🔢';
      case 'detailed-outline':
        return '📝';
      case 'chapter-generator':
        return '📖';
      case 'content-polisher':
        return '✨';
      case 'consistency-checker':
        return '✅';
      case 'condition':
        return '🔀';
      case 'loop':
        return '🔄';
      case 'output':
        return '📤';
      default:
        return '⚙️';
    }
  };

  return (
    <div className={`custom-node ${selected ? 'selected' : ''}`}>
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-gray-400 border-2 border-white"
      />
      
      <Card
        size="small"
        className={`min-w-48 shadow-md transition-all duration-200 ${
          selected ? 'ring-2 ring-blue-500 shadow-lg' : ''
        } ${data.status === 'running' ? 'animate-pulse' : ''}`}
        bodyStyle={{ padding: '12px' }}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{getNodeIcon()}</span>
            <div>
              <Text strong className="text-sm block">
                {data.label}
              </Text>
              {data.description && (
                <Text type="secondary" className="text-xs">
                  {data.description}
                </Text>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {getStatusIcon()}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <Tag color={getStatusColor()} size="small">
            {data.status === 'idle' && '等待'}
            {data.status === 'running' && '运行中'}
            {data.status === 'completed' && '已完成'}
            {data.status === 'error' && '错误'}
          </Tag>
          
          <div className="flex space-x-1">
            <Button
              type="text"
              size="small"
              icon={<SettingOutlined />}
              onClick={data.onEdit}
              className="text-xs"
            />
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              onClick={data.onDelete}
              className="text-xs"
            />
          </div>
        </div>

        {/* 配置信息 */}
        {data.config && Object.keys(data.config).length > 0 && (
          <div className="mt-2 pt-2 border-t border-gray-100">
            <Text type="secondary" className="text-xs">
              已配置 {Object.keys(data.config).length} 个参数
            </Text>
          </div>
        )}
      </Card>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-gray-400 border-2 border-white"
      />

      <style jsx>{`
        .custom-node.selected {
          z-index: 1000;
        }
      `}</style>
    </div>
  );
};

export default CustomNode;
