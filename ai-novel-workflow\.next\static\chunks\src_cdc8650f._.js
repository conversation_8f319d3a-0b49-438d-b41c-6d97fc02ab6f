(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/store/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "useAppStore": ()=>useAppStore
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
;
;
// 生成唯一ID的工具函数
const generateId = ()=>Math.random().toString(36).substr(2, 9);
const useAppStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // 初始UI状态
        ui: {
            theme: 'light',
            language: 'zh-CN',
            sidebarCollapsed: false,
            activeTab: 'workflow',
            selectedProject: undefined,
            notifications: []
        },
        // UI状态管理
        setTheme: (theme)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        theme
                    }
                })),
        setLanguage: (language)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        language
                    }
                })),
        toggleSidebar: ()=>set((state)=>({
                    ui: {
                        ...state.ui,
                        sidebarCollapsed: !state.ui.sidebarCollapsed
                    }
                })),
        setActiveTab: (activeTab)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        activeTab
                    }
                })),
        addNotification: (notification)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        notifications: [
                            ...state.ui.notifications,
                            {
                                ...notification,
                                id: generateId(),
                                timestamp: new Date(),
                                read: false
                            }
                        ]
                    }
                })),
        markNotificationRead: (id)=>set((state)=>({
                    ui: {
                        ...state.ui,
                        notifications: state.ui.notifications.map((n)=>n.id === id ? {
                                ...n,
                                read: true
                            } : n)
                    }
                })),
        clearNotifications: ()=>set((state)=>({
                    ui: {
                        ...state.ui,
                        notifications: []
                    }
                })),
        // 项目管理
        projects: [],
        currentProject: null,
        createProject: (project)=>set((state)=>{
                const newProject = {
                    ...project,
                    id: generateId(),
                    createdAt: new Date(),
                    updatedAt: new Date()
                };
                return {
                    projects: [
                        ...state.projects,
                        newProject
                    ],
                    currentProject: newProject,
                    ui: {
                        ...state.ui,
                        selectedProject: newProject.id
                    }
                };
            }),
        updateProject: (id, updates)=>set((state)=>{
                var _state_currentProject;
                return {
                    projects: state.projects.map((p)=>p.id === id ? {
                            ...p,
                            ...updates,
                            updatedAt: new Date()
                        } : p),
                    currentProject: ((_state_currentProject = state.currentProject) === null || _state_currentProject === void 0 ? void 0 : _state_currentProject.id) === id ? {
                        ...state.currentProject,
                        ...updates,
                        updatedAt: new Date()
                    } : state.currentProject
                };
            }),
        deleteProject: (id)=>set((state)=>{
                var _state_currentProject;
                return {
                    projects: state.projects.filter((p)=>p.id !== id),
                    currentProject: ((_state_currentProject = state.currentProject) === null || _state_currentProject === void 0 ? void 0 : _state_currentProject.id) === id ? null : state.currentProject,
                    ui: {
                        ...state.ui,
                        selectedProject: state.ui.selectedProject === id ? undefined : state.ui.selectedProject
                    }
                };
            }),
        setCurrentProject: (id)=>set((state)=>{
                const project = state.projects.find((p)=>p.id === id);
                return {
                    currentProject: project || null,
                    ui: {
                        ...state.ui,
                        selectedProject: id
                    }
                };
            }),
        // 工作流管理
        workflows: {},
        currentWorkflow: [],
        addNode: (node)=>set((state)=>({
                    currentWorkflow: [
                        ...state.currentWorkflow,
                        {
                            ...node,
                            id: generateId()
                        }
                    ]
                })),
        updateNode: (id, updates)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.map((n)=>n.id === id ? {
                            ...n,
                            ...updates
                        } : n)
                })),
        deleteNode: (id)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.filter((n)=>n.id !== id)
                })),
        connectNodes: (sourceId, targetId)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.map((n)=>n.id === sourceId ? {
                            ...n,
                            connections: [
                                ...n.connections,
                                {
                                    sourceId,
                                    targetId
                                }
                            ]
                        } : n)
                })),
        disconnectNodes: (sourceId, targetId)=>set((state)=>({
                    currentWorkflow: state.currentWorkflow.map((n)=>n.id === sourceId ? {
                            ...n,
                            connections: n.connections.filter((c)=>!(c.sourceId === sourceId && c.targetId === targetId))
                        } : n)
                })),
        loadWorkflow: (projectId)=>set((state)=>({
                    currentWorkflow: state.workflows[projectId] || []
                })),
        saveWorkflow: (projectId)=>set((state)=>({
                    workflows: {
                        ...state.workflows,
                        [projectId]: state.currentWorkflow
                    }
                })),
        // 角色管理
        characters: {},
        addCharacter: (projectId, character)=>set((state)=>({
                    characters: {
                        ...state.characters,
                        [projectId]: [
                            ...state.characters[projectId] || [],
                            {
                                ...character,
                                id: generateId()
                            }
                        ]
                    }
                })),
        updateCharacter: (projectId, id, updates)=>set((state)=>({
                    characters: {
                        ...state.characters,
                        [projectId]: (state.characters[projectId] || []).map((c)=>c.id === id ? {
                                ...c,
                                ...updates
                            } : c)
                    }
                })),
        deleteCharacter: (projectId, id)=>set((state)=>({
                    characters: {
                        ...state.characters,
                        [projectId]: (state.characters[projectId] || []).filter((c)=>c.id !== id)
                    }
                })),
        getCharacters: (projectId)=>get().characters[projectId] || [],
        // 世界观管理
        worldBuilding: {},
        addWorldElement: (projectId, element)=>set((state)=>({
                    worldBuilding: {
                        ...state.worldBuilding,
                        [projectId]: [
                            ...state.worldBuilding[projectId] || [],
                            {
                                ...element,
                                id: generateId()
                            }
                        ]
                    }
                })),
        updateWorldElement: (projectId, id, updates)=>set((state)=>({
                    worldBuilding: {
                        ...state.worldBuilding,
                        [projectId]: (state.worldBuilding[projectId] || []).map((w)=>w.id === id ? {
                                ...w,
                                ...updates
                            } : w)
                    }
                })),
        deleteWorldElement: (projectId, id)=>set((state)=>({
                    worldBuilding: {
                        ...state.worldBuilding,
                        [projectId]: (state.worldBuilding[projectId] || []).filter((w)=>w.id !== id)
                    }
                })),
        getWorldElements: (projectId)=>get().worldBuilding[projectId] || [],
        // 其他管理功能的占位符实现
        outlines: {},
        addOutline: (projectId, outline)=>set((state)=>({
                    outlines: {
                        ...state.outlines,
                        [projectId]: [
                            ...state.outlines[projectId] || [],
                            {
                                ...outline,
                                id: generateId(),
                                createdAt: new Date(),
                                updatedAt: new Date()
                            }
                        ]
                    }
                })),
        updateOutline: (projectId, id, updates)=>set((state)=>({
                    outlines: {
                        ...state.outlines,
                        [projectId]: (state.outlines[projectId] || []).map((o)=>o.id === id ? {
                                ...o,
                                ...updates,
                                updatedAt: new Date()
                            } : o)
                    }
                })),
        deleteOutline: (projectId, id)=>set((state)=>({
                    outlines: {
                        ...state.outlines,
                        [projectId]: (state.outlines[projectId] || []).filter((o)=>o.id !== id)
                    }
                })),
        getOutlines: (projectId)=>get().outlines[projectId] || [],
        plotLines: {},
        addPlotLine: (projectId, plotLine)=>set((state)=>({
                    plotLines: {
                        ...state.plotLines,
                        [projectId]: [
                            ...state.plotLines[projectId] || [],
                            {
                                ...plotLine,
                                id: generateId()
                            }
                        ]
                    }
                })),
        updatePlotLine: (projectId, id, updates)=>set((state)=>({
                    plotLines: {
                        ...state.plotLines,
                        [projectId]: (state.plotLines[projectId] || []).map((p)=>p.id === id ? {
                                ...p,
                                ...updates
                            } : p)
                    }
                })),
        deletePlotLine: (projectId, id)=>set((state)=>({
                    plotLines: {
                        ...state.plotLines,
                        [projectId]: (state.plotLines[projectId] || []).filter((p)=>p.id !== id)
                    }
                })),
        getPlotLines: (projectId)=>get().plotLines[projectId] || [],
        bookTitles: {},
        addBookTitle: (projectId, title)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: [
                            ...state.bookTitles[projectId] || [],
                            {
                                ...title,
                                id: generateId(),
                                createdAt: new Date()
                            }
                        ]
                    }
                })),
        updateBookTitle: (projectId, id, updates)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: (state.bookTitles[projectId] || []).map((t)=>t.id === id ? {
                                ...t,
                                ...updates
                            } : t)
                    }
                })),
        deleteBookTitle: (projectId, id)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: (state.bookTitles[projectId] || []).filter((t)=>t.id !== id)
                    }
                })),
        toggleTitleFavorite: (projectId, id)=>set((state)=>({
                    bookTitles: {
                        ...state.bookTitles,
                        [projectId]: (state.bookTitles[projectId] || []).map((t)=>t.id === id ? {
                                ...t,
                                isFavorite: !t.isFavorite
                            } : t)
                    }
                })),
        getBookTitles: (projectId)=>get().bookTitles[projectId] || [],
        chapters: {},
        addChapter: (projectId, chapter)=>set((state)=>({
                    chapters: {
                        ...state.chapters,
                        [projectId]: [
                            ...state.chapters[projectId] || [],
                            {
                                ...chapter,
                                id: generateId(),
                                createdAt: new Date(),
                                updatedAt: new Date()
                            }
                        ]
                    }
                })),
        updateChapter: (projectId, id, updates)=>set((state)=>({
                    chapters: {
                        ...state.chapters,
                        [projectId]: (state.chapters[projectId] || []).map((c)=>c.id === id ? {
                                ...c,
                                ...updates,
                                updatedAt: new Date()
                            } : c)
                    }
                })),
        deleteChapter: (projectId, id)=>set((state)=>({
                    chapters: {
                        ...state.chapters,
                        [projectId]: (state.chapters[projectId] || []).filter((c)=>c.id !== id)
                    }
                })),
        reorderChapters: (projectId, fromIndex, toIndex)=>set((state)=>{
                const chapters = [
                    ...state.chapters[projectId] || []
                ];
                const [removed] = chapters.splice(fromIndex, 1);
                chapters.splice(toIndex, 0, removed);
                // 重新设置order
                chapters.forEach((chapter, index)=>{
                    chapter.order = index + 1;
                });
                return {
                    chapters: {
                        ...state.chapters,
                        [projectId]: chapters
                    }
                };
            }),
        getChapters: (projectId)=>get().chapters[projectId] || [],
        promptTemplates: [],
        addPromptTemplate: (template)=>set((state)=>({
                    promptTemplates: [
                        ...state.promptTemplates,
                        {
                            ...template,
                            id: generateId()
                        }
                    ]
                })),
        updatePromptTemplate: (id, updates)=>set((state)=>({
                    promptTemplates: state.promptTemplates.map((t)=>t.id === id ? {
                            ...t,
                            ...updates
                        } : t)
                })),
        deletePromptTemplate: (id)=>set((state)=>({
                    promptTemplates: state.promptTemplates.filter((t)=>t.id !== id)
                })),
        getPromptTemplatesByCategory: (category)=>get().promptTemplates.filter((t)=>t.category === category),
        documentStructures: {},
        initializeDocumentStructure: (projectId)=>set((state)=>({
                    documentStructures: {
                        ...state.documentStructures,
                        [projectId]: {
                            projectId,
                            folders: [],
                            files: [],
                            lastBackup: new Date()
                        }
                    }
                })),
        updateDocumentStructure: (projectId, structure)=>set((state)=>({
                    documentStructures: {
                        ...state.documentStructures,
                        [projectId]: {
                            ...state.documentStructures[projectId],
                            ...structure
                        }
                    }
                })),
        executionContexts: {},
        startExecution: (projectId, workflowId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            projectId,
                            workflowId,
                            status: 'running',
                            progress: {
                                totalSteps: 0,
                                completedSteps: 0,
                                currentStep: '',
                                percentage: 0
                            },
                            queue: [],
                            results: {},
                            startTime: new Date()
                        }
                    }
                })),
        pauseExecution: (projectId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            status: 'paused'
                        }
                    }
                })),
        resumeExecution: (projectId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            status: 'running'
                        }
                    }
                })),
        stopExecution: (projectId)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            status: 'idle',
                            endTime: new Date()
                        }
                    }
                })),
        updateExecutionProgress: (projectId, progress)=>set((state)=>({
                    executionContexts: {
                        ...state.executionContexts,
                        [projectId]: {
                            ...state.executionContexts[projectId],
                            ...progress
                        }
                    }
                }))
    }), {
    name: 'ai-novel-workflow-storage',
    partialize: (state)=>({
            projects: state.projects,
            workflows: state.workflows,
            characters: state.characters,
            worldBuilding: state.worldBuilding,
            outlines: state.outlines,
            plotLines: state.plotLines,
            bookTitles: state.bookTitles,
            chapters: state.chapters,
            promptTemplates: state.promptTemplates,
            documentStructures: state.documentStructures,
            ui: {
                theme: state.ui.theme,
                language: state.ui.language,
                sidebarCollapsed: state.ui.sidebarCollapsed
            }
        })
}), {
    name: 'ai-novel-workflow'
}));
const __TURBOPACK__default__export__ = useAppStore;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/aiService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// AI服务集成
__turbopack_context__.s({
    "AIService": ()=>AIService,
    "aiService": ()=>aiService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class AIService {
    // 设置AI配置
    setConfig(config) {
        this.config = config;
        // 保存到localStorage (仅在客户端)
        if ("TURBOPACK compile-time truthy", 1) {
            localStorage.setItem('ai-config', JSON.stringify(config));
        }
    }
    // 获取AI配置
    getConfig() {
        if (this.config) return this.config;
        // 仅在客户端访问localStorage
        if ("TURBOPACK compile-time truthy", 1) {
            const saved = localStorage.getItem('ai-config');
            if (saved) {
                this.config = JSON.parse(saved);
                return this.config;
            }
        }
        return null;
    }
    // 检查是否已配置
    isConfigured() {
        return this.getConfig() !== null;
    }
    // 通用AI请求方法
    async makeRequest(prompt, systemPrompt) {
        const config = this.getConfig();
        if (!config) {
            return {
                success: false,
                error: '请先配置AI API'
            };
        }
        try {
            let response;
            switch(config.provider){
                case 'openai':
                    response = await this.callOpenAI(prompt, systemPrompt, config);
                    break;
                case 'claude':
                    response = await this.callClaude(prompt, systemPrompt, config);
                    break;
                case 'gemini':
                    response = await this.callGemini(prompt, systemPrompt, config);
                    break;
                case 'custom':
                    response = await this.callCustomAPI(prompt, systemPrompt, config);
                    break;
                default:
                    return {
                        success: false,
                        error: '不支持的AI提供商'
                    };
            }
            return {
                success: true,
                data: response
            };
        } catch (error) {
            return {
                success: false,
                error: error.message || '请求失败'
            };
        }
    }
    // OpenAI API调用
    async callOpenAI(prompt) {
        let systemPrompt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '', config = arguments.length > 2 ? arguments[2] : void 0;
        const messages = [];
        if (systemPrompt) {
            messages.push({
                role: 'system',
                content: systemPrompt
            });
        }
        messages.push({
            role: 'user',
            content: prompt
        });
        const response = await fetch(config.baseUrl || 'https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(config.apiKey)
            },
            body: JSON.stringify({
                model: config.model || 'gpt-3.5-turbo',
                messages,
                temperature: config.temperature || 0.7,
                max_tokens: config.maxTokens || 2000
            })
        });
        if (!response.ok) {
            throw new Error("OpenAI API错误: ".concat(response.status, " ").concat(response.statusText));
        }
        const data = await response.json();
        return data.choices[0].message.content;
    }
    // Claude API调用
    async callClaude(prompt) {
        let systemPrompt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '', config = arguments.length > 2 ? arguments[2] : void 0;
        const response = await fetch(config.baseUrl || 'https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key': config.apiKey,
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: config.model || 'claude-3-sonnet-20240229',
                max_tokens: config.maxTokens || 2000,
                temperature: config.temperature || 0.7,
                system: systemPrompt,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ]
            })
        });
        if (!response.ok) {
            throw new Error("Claude API错误: ".concat(response.status, " ").concat(response.statusText));
        }
        const data = await response.json();
        return data.content[0].text;
    }
    // Gemini API调用
    async callGemini(prompt) {
        let systemPrompt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '', config = arguments.length > 2 ? arguments[2] : void 0;
        const fullPrompt = systemPrompt ? "".concat(systemPrompt, "\n\n").concat(prompt) : prompt;
        const response = await fetch("".concat(config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta/models', "/").concat(config.model || 'gemini-pro', ":generateContent?key=").concat(config.apiKey), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                contents: [
                    {
                        parts: [
                            {
                                text: fullPrompt
                            }
                        ]
                    }
                ],
                generationConfig: {
                    temperature: config.temperature || 0.7,
                    maxOutputTokens: config.maxTokens || 2000
                }
            })
        });
        if (!response.ok) {
            throw new Error("Gemini API错误: ".concat(response.status, " ").concat(response.statusText));
        }
        const data = await response.json();
        return data.candidates[0].content.parts[0].text;
    }
    // 自定义API调用
    async callCustomAPI(prompt) {
        let systemPrompt = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '', config = arguments.length > 2 ? arguments[2] : void 0;
        // 验证和修正API地址
        let apiUrl = config.baseUrl;
        // 确保API地址格式正确
        if (!apiUrl.startsWith('http://') && !apiUrl.startsWith('https://')) {
            apiUrl = 'https://' + apiUrl;
        }
        // 如果地址不包含端点，尝试添加常见端点
        if (!apiUrl.includes('/chat/completions') && !apiUrl.includes('/completions') && !apiUrl.includes('/generate')) {
            if (apiUrl.endsWith('/')) {
                apiUrl = apiUrl + 'v1/chat/completions';
            } else if (apiUrl.endsWith('/v1')) {
                apiUrl = apiUrl + '/chat/completions';
            } else {
                apiUrl = apiUrl + '/v1/chat/completions';
            }
        }
        console.log('使用API地址:', apiUrl);
        // 尝试OpenAI兼容格式
        const messages = [];
        if (systemPrompt) {
            messages.push({
                role: 'system',
                content: systemPrompt
            });
        }
        messages.push({
            role: 'user',
            content: prompt
        });
        const requestBody = {
            model: config.model || 'gpt-3.5-turbo',
            messages,
            temperature: config.temperature || 0.7,
            max_tokens: config.maxTokens || 2000
        };
        try {
            console.log('发送请求到:', apiUrl);
            console.log('请求体:', JSON.stringify(requestBody, null, 2));
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': "Bearer ".concat(config.apiKey)
                },
                body: JSON.stringify(requestBody)
            });
            console.log('响应状态:', response.status, response.statusText);
            if (!response.ok) {
                const errorText = await response.text();
                console.log('错误响应:', errorText);
                throw new Error("自定义API错误: ".concat(response.status, " ").concat(response.statusText, " - ").concat(errorText));
            }
            const data = await response.json();
            console.log('响应数据:', data);
            // 尝试不同的响应格式
            if (data.choices && data.choices[0] && data.choices[0].message) {
                return data.choices[0].message.content;
            } else if (data.response) {
                return data.response;
            } else if (data.content) {
                return data.content;
            } else if (data.text) {
                return data.text;
            } else {
                throw new Error('无法解析API响应格式: ' + JSON.stringify(data));
            }
        } catch (error) {
            console.log('OpenAI格式失败，尝试简单格式:', error.message);
            // 如果OpenAI格式失败，尝试简单格式
            try {
                var _data_choices_, _data_choices;
                const simpleRequestBody = {
                    prompt: systemPrompt ? "".concat(systemPrompt, "\n\n").concat(prompt) : prompt,
                    model: config.model,
                    temperature: config.temperature || 0.7,
                    max_tokens: config.maxTokens || 2000
                };
                // 尝试不同的端点
                const simpleUrl = apiUrl.replace('/chat/completions', '/completions');
                console.log('尝试简单格式，URL:', simpleUrl);
                const response = await fetch(simpleUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': "Bearer ".concat(config.apiKey)
                    },
                    body: JSON.stringify(simpleRequestBody)
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    console.log('简单格式错误响应:', errorText);
                    throw new Error("自定义API错误: ".concat(response.status, " ").concat(response.statusText, " - ").concat(errorText));
                }
                const data = await response.json();
                console.log('简单格式响应数据:', data);
                return data.response || data.content || data.text || ((_data_choices = data.choices) === null || _data_choices === void 0 ? void 0 : (_data_choices_ = _data_choices[0]) === null || _data_choices_ === void 0 ? void 0 : _data_choices_.text) || JSON.stringify(data);
            } catch (secondError) {
                console.log('所有格式都失败:', secondError.message);
                throw new Error("自定义API调用失败: ".concat(error.message, ". 备用格式也失败: ").concat(secondError.message));
            }
        }
    }
    // 分析工作流需求
    async analyzeWorkflowRequirements(requirements) {
        const systemPrompt = "你是一个专业的小说创作工作流设计专家。根据用户的需求，分析并推荐最适合的工作流类型。\n\n可选的工作流类型：\n1. quick-novel - 快速小说生成（适合新手，简单流程）\n2. professional-novel - 专业小说创作（完整流程，适合有经验的作者）\n3. fantasy-novel - 奇幻小说专用（重点关注世界观和魔法体系）\n4. romance-novel - 言情小说工作流（专注情感描写和角色关系）\n\n请返回JSON格式的推荐结果，包含：\n- recommended: 推荐的工作流ID\n- confidence: 推荐置信度(0-100)\n- reasons: 推荐理由数组\n- alternatives: 备选方案数组";
        const lengthInfo = requirements.length === '自定义' && requirements.customWordCount ? "".concat(requirements.length, " (").concat(requirements.customWordCount.toLocaleString(), "字)") : requirements.length;
        const prompt = "用户需求：\n- 小说类型：".concat(requirements.genre, "\n- 写作风格：").concat(requirements.style, "\n- 作品长度：").concat(lengthInfo, "\n- 创作经验：").concat(requirements.experience, "\n- 特殊需求：").concat(requirements.features.join(', '), "\n\n请分析并推荐最适合的工作流。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成自定义工作流
    async generateCustomWorkflow(requirements) {
        const systemPrompt = "你是一个AI小说创作工作流设计师。根据用户需求设计一个定制化的工作流。\n\n可用的节点类型：\n- input: 用户参数输入\n- title-generator: 书名生成\n- detail-generator: 详情生成\n- character-creator: 角色创建\n- worldbuilding: 世界观构建\n- plotline-planner: 主线规划\n- outline-generator: 大纲生成\n- chapter-count-input: 章节数设定\n- detailed-outline: 详细大纲\n- chapter-generator: 章节生成\n- content-polisher: 内容润色\n- consistency-checker: 一致性检查\n- condition: 条件分支\n- loop: 循环执行\n- output: 结果输出\n\n请返回JSON格式的工作流定义，包含：\n- name: 工作流名称\n- description: 工作流描述\n- nodes: 节点数组，每个节点包含 {type, label, position: {x, y}}\n- connections: 连接数组，每个连接包含 {source: 节点索引, target: 节点索引}\n- complexity: 复杂度 (simple/medium/complex)";
        const lengthInfo = requirements.length === '自定义' && requirements.customWordCount ? "".concat(requirements.length, " (").concat(requirements.customWordCount.toLocaleString(), "字)") : requirements.length;
        const prompt = "请为以下需求设计一个定制化的小说创作工作流：\n- 小说类型：".concat(requirements.genre, "\n- 写作风格：").concat(requirements.style, "\n- 作品长度：").concat(lengthInfo, "\n- 特殊需求：").concat(requirements.features.join(', '), "\n\n请确保工作流逻辑合理，节点连接有序，适合用户的具体需求。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 优化现有工作流
    async optimizeWorkflow(currentNodes, requirements) {
        const systemPrompt = "你是一个工作流优化专家。分析现有的工作流并提供优化建议。\n\n请返回JSON格式的优化建议，包含：\n- issues: 发现的问题数组\n- suggestions: 优化建议数组\n- optimized_workflow: 优化后的工作流定义（如果需要重构）\n- improvement_score: 改进评分(0-100)";
        const nodeTypes = currentNodes.map((node)=>{
            var _node_data;
            return ((_node_data = node.data) === null || _node_data === void 0 ? void 0 : _node_data.type) || 'unknown';
        });
        const prompt = "当前工作流包含以下节点：".concat(nodeTypes.join(', '), "\n\n用户需求：").concat(JSON.stringify(requirements), "\n\n请分析这个工作流的问题并提供优化建议。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成书名
    async generateTitles(params) {
        var _params_keywords;
        const systemPrompt = "你是一个专业的书名创作专家。根据小说类型、风格和关键词生成吸引人的书名。\n\n请返回JSON格式的结果，包含：\n- titles: 书名数组，每个包含 {title, score, analysis: {appeal, memorability, genre_match, uniqueness, marketability}, suggestions: 优化建议数组}";
        const prompt = "请为以下小说生成".concat(params.count || 5, "个书名：\n- 类型：").concat(params.genre, "\n- 风格：").concat(params.style, "\n- 关键词：").concat(((_params_keywords = params.keywords) === null || _params_keywords === void 0 ? void 0 : _params_keywords.join(', ')) || '无', "\n\n要求书名要有吸引力、易记忆、符合类型特征。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成角色
    async generateCharacter(params) {
        const systemPrompt = "你是一个角色设计专家。根据小说类型和角色定位创建详细的角色设定。\n\n请返回JSON格式的角色信息，包含：\n- name: 角色姓名\n- age: 年龄\n- gender: 性别\n- appearance: 外貌描述\n- personality: 性格特征\n- background: 背景故事\n- skills: 技能特长\n- relationships: 人际关系\n- goals: 目标动机\n- flaws: 性格缺陷";
        const prompt = "请创建一个".concat(params.genre, "小说中的").concat(params.role, "角色：\n").concat(params.background ? "背景要求：".concat(params.background) : '', "\n").concat(params.personality ? "性格要求：".concat(params.personality) : '', "\n\n请提供详细的角色设定。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成世界观
    async generateWorldbuilding(params) {
        var _params_elements;
        const systemPrompt = "你是一个世界观构建专家。根据小说类型和风格创建详细的世界设定。\n\n请返回JSON格式的世界观信息，包含：\n- name: 世界名称\n- overview: 世界概述\n- geography: 地理环境\n- history: 历史背景\n- culture: 文化特色\n- politics: 政治体系\n- economy: 经济体系\n- technology: 科技水平\n- magic_system: 魔法/超能力体系（如适用）\n- races: 种族设定\n- languages: 语言系统\n- religions: 宗教信仰\n- conflicts: 主要冲突";
        const prompt = "请为".concat(params.genre, "类型的小说创建世界观设定：\n- 风格：").concat(params.style, "\n- 范围：").concat(params.scope || '中等规模', "\n- 特殊元素：").concat(((_params_elements = params.elements) === null || _params_elements === void 0 ? void 0 : _params_elements.join(', ')) || '无', "\n\n请提供详细的世界观设定。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成主线规划
    async generatePlotline(params) {
        var _params_worldbuilding, _params_themes;
        const systemPrompt = "你是一个故事情节规划专家。根据角色和世界观创建引人入胜的主线情节。\n\n请返回JSON格式的主线规划，包含：\n- title: 主线标题\n- premise: 故事前提\n- inciting_incident: 引发事件\n- plot_points: 关键情节点数组，每个包含 {act, description, characters_involved, conflicts, outcomes}\n- climax: 高潮设定\n- resolution: 结局安排\n- themes: 主题表达\n- character_arcs: 角色成长弧线\n- subplots: 支线情节建议";
        const charactersInfo = params.characters.map((char)=>"".concat(char.name, "(").concat(char.role, ")")).join(', ');
        const prompt = "请为".concat(params.genre, "小说规划主线情节：\n- 风格：").concat(params.style, "\n- 主要角色：").concat(charactersInfo, "\n- 世界背景：").concat(((_params_worldbuilding = params.worldbuilding) === null || _params_worldbuilding === void 0 ? void 0 : _params_worldbuilding.name) || '待定', "\n- 主题方向：").concat(((_params_themes = params.themes) === null || _params_themes === void 0 ? void 0 : _params_themes.join(', ')) || '待定', "\n\n请创建完整的故事主线规划。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成大纲
    async generateOutline(params) {
        const systemPrompt = "你是一个小说大纲创作专家。根据主线情节创建详细的章节大纲。\n\n请返回JSON格式的大纲信息，包含：\n- title: 大纲标题\n- structure: 结构类型（三幕式、英雄之旅等）\n- chapters: 章节数组，每个包含 {number, title, summary, key_events, character_focus, word_count_target, notes}\n- pacing: 节奏安排\n- tension_curve: 张力曲线描述\n- themes_distribution: 主题分布";
        const prompt = "请为".concat(params.genre, "小说创建详细大纲：\n- 目标长度：").concat(params.targetLength, "\n- 预计章节数：").concat(params.chapterCount || '待定', "\n- 主线情节：").concat(params.plotline.title || '待定', "\n- 主要角色：").concat(params.characters.map((c)=>c.name).join(', '), "\n\n请创建完整的章节大纲。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成详细大纲
    async generateDetailedOutline(params) {
        var _params_worldbuilding;
        const systemPrompt = "你是一个详细大纲创作专家。将基础大纲扩展为详细的情节要点。\n\n请返回JSON格式的详细大纲，包含：\n- chapters: 详细章节数组，每个包含 {\n    number, title, detailed_summary, scenes: [{\n      scene_number, location, characters, objective, conflict, outcome, mood, pov\n    }], character_development, plot_advancement, foreshadowing, themes\n  }";
        const chapterInfo = params.selectedChapters ? "重点章节：".concat(params.selectedChapters.join(', ')) : '所有章节';
        const prompt = "请为以下大纲创建详细的情节要点：\n- 基础大纲：".concat(params.outline.title, "\n- 角色信息：").concat(params.characters.map((c)=>c.name).join(', '), "\n- 世界设定：").concat(((_params_worldbuilding = params.worldbuilding) === null || _params_worldbuilding === void 0 ? void 0 : _params_worldbuilding.name) || '待定', "\n- 处理范围：").concat(chapterInfo, "\n\n请提供详细的场景和情节要点。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成章节内容
    async generateChapter(params) {
        var _params_previousChapters;
        const systemPrompt = "你是一个专业的小说创作者。根据章节大纲创作具体的章节内容。\n\n请返回JSON格式的章节内容，包含：\n- title: 章节标题\n- content: 章节正文内容\n- word_count: 字数统计\n- scenes: 场景分解\n- character_moments: 角色重要时刻\n- plot_advancement: 情节推进要点\n- foreshadowing: 伏笔设置\n- themes_explored: 探讨的主题";
        const previousContext = ((_params_previousChapters = params.previousChapters) === null || _params_previousChapters === void 0 ? void 0 : _params_previousChapters.length) ? "前文概要：".concat(params.previousChapters.slice(-2).join('\n')) : '这是开篇章节';
        const prompt = "请创作第".concat(params.chapterNumber, "章的具体内容：\n- 章节大纲：").concat(JSON.stringify(params.chapterOutline), "\n- 主要角色：").concat(params.characters.map((c)=>c.name).join(', '), "\n- 写作风格：").concat(params.style, "\n- 目标字数：").concat(params.targetWordCount || 2000, "字\n- ").concat(previousContext, "\n\n请创作完整的章节内容。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 内容润色
    async polishContent(params) {
        var _params_focusAreas;
        const systemPrompt = "你是一个专业的文本润色专家。优化文本的表达、节奏和文学性。\n\n请返回JSON格式的润色结果，包含：\n- polished_content: 润色后的内容\n- improvements: 改进说明数组\n- style_analysis: 风格分析\n- readability_score: 可读性评分\n- suggestions: 进一步优化建议";
        const focusInfo = ((_params_focusAreas = params.focusAreas) === null || _params_focusAreas === void 0 ? void 0 : _params_focusAreas.length) ? "重点关注：".concat(params.focusAreas.join(', ')) : '全面优化';
        const prompt = "请润色以下文本内容：\n- 目标风格：".concat(params.style, "\n- 目标读者：").concat(params.targetAudience || '一般读者', "\n- ").concat(focusInfo, "\n\n原文内容：\n").concat(params.content, "\n\n请提供润色后的版本和改进说明。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 一致性检查
    async checkConsistency(params) {
        var _params_checkTypes, _params_worldbuilding;
        const systemPrompt = "你是一个内容一致性检查专家。检查文本中的逻辑、角色、设定等一致性问题。\n\n请返回JSON格式的检查结果，包含：\n- consistency_score: 一致性评分(0-100)\n- issues: 问题数组，每个包含 {type, severity, description, location, suggestion}\n- character_consistency: 角色一致性分析\n- plot_consistency: 情节一致性分析\n- world_consistency: 世界观一致性分析\n- timeline_issues: 时间线问题\n- recommendations: 修改建议";
        const checkInfo = ((_params_checkTypes = params.checkTypes) === null || _params_checkTypes === void 0 ? void 0 : _params_checkTypes.length) ? "检查类型：".concat(params.checkTypes.join(', ')) : '全面检查';
        const prompt = "请检查以下内容的一致性：\n- 角色设定：".concat(params.characters.map((c)=>c.name).join(', '), "\n- 世界设定：").concat(((_params_worldbuilding = params.worldbuilding) === null || _params_worldbuilding === void 0 ? void 0 : _params_worldbuilding.name) || '待定', "\n- ").concat(checkInfo, "\n\n内容文本：\n").concat(params.content.join('\n\n---\n\n'), "\n\n请提供详细的一致性分析报告。");
        return this.makeRequest(prompt, systemPrompt);
    }
    // 生成小说详情
    async generateNovelDetails(params) {
        var _params_outline, _params_characters;
        const systemPrompt = "你是一个小说包装专家。根据小说内容生成吸引人的详情信息。\n\n请返回JSON格式的详情信息，包含：\n- synopsis: 内容简介\n- tagline: 宣传语\n- keywords: 关键词数组\n- target_audience: 目标读者群体\n- selling_points: 卖点分析\n- genre_tags: 类型标签\n- mood_tags: 氛围标签\n- content_warnings: 内容提醒\n- marketing_description: 营销描述";
        const prompt = "请为小说《".concat(params.title, "》生成详情信息：\n- 类型：").concat(params.genre, "\n- 风格：").concat(params.style, "\n- 大纲概要：").concat(((_params_outline = params.outline) === null || _params_outline === void 0 ? void 0 : _params_outline.title) || '待定', "\n- 主要角色：").concat(((_params_characters = params.characters) === null || _params_characters === void 0 ? void 0 : _params_characters.map((c)=>c.name).join(', ')) || '待定', "\n\n请生成完整的小说详情包装。");
        return this.makeRequest(prompt, systemPrompt);
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "config", null);
    }
}
const aiService = new AIService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/workflowAI.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// AI工作流生成器
__turbopack_context__.s({
    "WORKFLOW_TEMPLATES": ()=>WORKFLOW_TEMPLATES,
    "WorkflowAI": ()=>WorkflowAI
});
// 预定义工作流模板
const WORKFLOW_TEMPLATES = [
    {
        id: 'quick-novel',
        name: '快速小说生成',
        description: '适合新手的简单工作流，快速生成短篇小说',
        nodes: [
            {
                type: 'input',
                label: '创作输入',
                position: {
                    x: 100,
                    y: 100
                }
            },
            {
                type: 'title-generator',
                label: '生成书名',
                position: {
                    x: 100,
                    y: 250
                }
            },
            {
                type: 'character-creator',
                label: '创建角色',
                position: {
                    x: 350,
                    y: 250
                }
            },
            {
                type: 'outline-generator',
                label: '生成大纲',
                position: {
                    x: 225,
                    y: 400
                }
            },
            {
                type: 'chapter-generator',
                label: '生成章节',
                position: {
                    x: 225,
                    y: 550
                }
            },
            {
                type: 'output',
                label: '完成输出',
                position: {
                    x: 225,
                    y: 700
                }
            }
        ],
        connections: [
            {
                source: 0,
                target: 1
            },
            {
                source: 0,
                target: 2
            },
            {
                source: 1,
                target: 3
            },
            {
                source: 2,
                target: 3
            },
            {
                source: 3,
                target: 4
            },
            {
                source: 4,
                target: 5
            }
        ],
        tags: [
            '简单',
            '快速',
            '新手'
        ],
        complexity: 'simple'
    },
    {
        id: 'professional-novel',
        name: '专业小说创作',
        description: '完整的专业级工作流，包含详细的世界观和角色设定',
        nodes: [
            {
                type: 'input',
                label: '创作参数',
                position: {
                    x: 100,
                    y: 50
                }
            },
            {
                type: 'title-generator',
                label: '书名生成',
                position: {
                    x: 50,
                    y: 200
                }
            },
            {
                type: 'detail-generator',
                label: '详情生成',
                position: {
                    x: 200,
                    y: 200
                }
            },
            {
                type: 'character-creator',
                label: '角色创建',
                position: {
                    x: 350,
                    y: 200
                }
            },
            {
                type: 'worldbuilding',
                label: '世界观构建',
                position: {
                    x: 500,
                    y: 200
                }
            },
            {
                type: 'plotline-planner',
                label: '主线规划',
                position: {
                    x: 150,
                    y: 350
                }
            },
            {
                type: 'outline-generator',
                label: '大纲生成',
                position: {
                    x: 350,
                    y: 350
                }
            },
            {
                type: 'detailed-outline',
                label: '详细大纲',
                position: {
                    x: 250,
                    y: 500
                }
            },
            {
                type: 'chapter-generator',
                label: '章节生成',
                position: {
                    x: 250,
                    y: 650
                }
            },
            {
                type: 'content-polisher',
                label: '内容润色',
                position: {
                    x: 100,
                    y: 800
                }
            },
            {
                type: 'consistency-checker',
                label: '一致性检查',
                position: {
                    x: 400,
                    y: 800
                }
            },
            {
                type: 'output',
                label: '最终输出',
                position: {
                    x: 250,
                    y: 950
                }
            }
        ],
        connections: [
            {
                source: 0,
                target: 1
            },
            {
                source: 0,
                target: 2
            },
            {
                source: 0,
                target: 3
            },
            {
                source: 0,
                target: 4
            },
            {
                source: 1,
                target: 5
            },
            {
                source: 2,
                target: 5
            },
            {
                source: 3,
                target: 5
            },
            {
                source: 4,
                target: 5
            },
            {
                source: 3,
                target: 6
            },
            {
                source: 4,
                target: 6
            },
            {
                source: 5,
                target: 6
            },
            {
                source: 6,
                target: 7
            },
            {
                source: 7,
                target: 8
            },
            {
                source: 8,
                target: 9
            },
            {
                source: 8,
                target: 10
            },
            {
                source: 9,
                target: 11
            },
            {
                source: 10,
                target: 11
            }
        ],
        tags: [
            '专业',
            '完整',
            '高质量'
        ],
        complexity: 'complex'
    },
    {
        id: 'fantasy-novel',
        name: '奇幻小说专用',
        description: '专为奇幻类小说设计的工作流，重点关注世界观和魔法体系',
        nodes: [
            {
                type: 'input',
                label: '奇幻设定',
                position: {
                    x: 100,
                    y: 100
                }
            },
            {
                type: 'worldbuilding',
                label: '魔法世界',
                position: {
                    x: 100,
                    y: 250
                }
            },
            {
                type: 'character-creator',
                label: '角色种族',
                position: {
                    x: 300,
                    y: 250
                }
            },
            {
                type: 'plotline-planner',
                label: '冒险主线',
                position: {
                    x: 200,
                    y: 400
                }
            },
            {
                type: 'outline-generator',
                label: '冒险大纲',
                position: {
                    x: 200,
                    y: 550
                }
            },
            {
                type: 'chapter-generator',
                label: '章节创作',
                position: {
                    x: 200,
                    y: 700
                }
            },
            {
                type: 'consistency-checker',
                label: '设定检查',
                position: {
                    x: 200,
                    y: 850
                }
            },
            {
                type: 'output',
                label: '奇幻小说',
                position: {
                    x: 200,
                    y: 1000
                }
            }
        ],
        connections: [
            {
                source: 0,
                target: 1
            },
            {
                source: 0,
                target: 2
            },
            {
                source: 1,
                target: 3
            },
            {
                source: 2,
                target: 3
            },
            {
                source: 3,
                target: 4
            },
            {
                source: 4,
                target: 5
            },
            {
                source: 5,
                target: 6
            },
            {
                source: 6,
                target: 7
            }
        ],
        tags: [
            '奇幻',
            '魔法',
            '冒险'
        ],
        complexity: 'medium'
    },
    {
        id: 'romance-novel',
        name: '言情小说工作流',
        description: '专注于情感描写和角色关系的言情小说创作流程',
        nodes: [
            {
                type: 'input',
                label: '言情设定',
                position: {
                    x: 100,
                    y: 100
                }
            },
            {
                type: 'character-creator',
                label: '主角设定',
                position: {
                    x: 50,
                    y: 250
                }
            },
            {
                type: 'character-creator',
                label: '配角设定',
                position: {
                    x: 200,
                    y: 250
                }
            },
            {
                type: 'plotline-planner',
                label: '情感主线',
                position: {
                    x: 125,
                    y: 400
                }
            },
            {
                type: 'outline-generator',
                label: '情节大纲',
                position: {
                    x: 125,
                    y: 550
                }
            },
            {
                type: 'chapter-generator',
                label: '章节创作',
                position: {
                    x: 125,
                    y: 700
                }
            },
            {
                type: 'content-polisher',
                label: '情感润色',
                position: {
                    x: 125,
                    y: 850
                }
            },
            {
                type: 'output',
                label: '言情小说',
                position: {
                    x: 125,
                    y: 1000
                }
            }
        ],
        connections: [
            {
                source: 0,
                target: 1
            },
            {
                source: 0,
                target: 2
            },
            {
                source: 1,
                target: 3
            },
            {
                source: 2,
                target: 3
            },
            {
                source: 3,
                target: 4
            },
            {
                source: 4,
                target: 5
            },
            {
                source: 5,
                target: 6
            },
            {
                source: 6,
                target: 7
            }
        ],
        tags: [
            '言情',
            '情感',
            '关系'
        ],
        complexity: 'medium'
    }
];
class WorkflowAI {
    // 分析用户需求并推荐工作流
    static analyzeRequirements(requirements) {
        const { genre, style, length, customWordCount, experience, features = [] } = requirements;
        let recommendations = [];
        WORKFLOW_TEMPLATES.forEach((template)=>{
            let score = 0;
            // 根据类型匹配
            if (genre) {
                if (genre.includes('奇幻') && template.tags.includes('奇幻')) score += 30;
                if (genre.includes('言情') && template.tags.includes('言情')) score += 30;
                if (genre.includes('现代') && template.tags.includes('简单')) score += 20;
            }
            // 根据经验水平匹配
            if (experience) {
                if (experience === '新手' && template.complexity === 'simple') score += 25;
                if (experience === '进阶' && template.complexity === 'medium') score += 25;
                if (experience === '专业' && template.complexity === 'complex') score += 25;
            }
            // 根据长度匹配
            if (length) {
                if (length === '短篇' && template.complexity === 'simple') score += 20;
                if (length === '中篇' && template.complexity === 'medium') score += 20;
                if (length === '长篇' && template.complexity === 'complex') score += 20;
                // 处理自定义字数
                if (length === '自定义' && customWordCount) {
                    if (customWordCount <= 50000 && template.complexity === 'simple') score += 20;
                    else if (customWordCount <= 150000 && template.complexity === 'medium') score += 20;
                    else if (customWordCount > 150000 && template.complexity === 'complex') score += 20;
                }
            }
            // 根据特殊需求匹配
            features.forEach((feature)=>{
                if (template.tags.some((tag)=>tag.includes(feature))) {
                    score += 15;
                }
            });
            recommendations.push({
                template,
                score
            });
        });
        // 按分数排序并返回前3个
        return recommendations.sort((a, b)=>b.score - a.score).slice(0, 3).map((r)=>r.template);
    }
    // 生成自定义工作流
    static generateCustomWorkflow(requirements) {
        const { genre, style, length, customWordCount, features } = requirements;
        // 基础节点
        const nodes = [
            {
                type: 'input',
                label: '创作输入',
                position: {
                    x: 200,
                    y: 100
                }
            }
        ];
        const connections = [];
        let currentY = 250;
        let nodeIndex = 1;
        // 根据需求添加节点
        const addNode = function(type, label) {
            let x = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 200;
            nodes.push({
                type,
                label,
                position: {
                    x,
                    y: currentY
                }
            });
            return nodeIndex++;
        };
        // 书名生成（总是需要）
        const titleNode = addNode('title-generator', '书名生成', 100);
        connections.push({
            source: 0,
            target: titleNode
        });
        // 根据类型添加特定节点
        if (genre.includes('奇幻') || features.includes('世界观')) {
            const worldNode = addNode('worldbuilding', '世界观构建', 300);
            connections.push({
                source: 0,
                target: worldNode
            });
        }
        // 角色创建（总是需要）
        currentY += 150;
        const charNode = addNode('character-creator', '角色创建');
        connections.push({
            source: 0,
            target: charNode
        });
        // 主线规划
        currentY += 150;
        const plotNode = addNode('plotline-planner', '主线规划');
        connections.push({
            source: titleNode,
            target: plotNode
        });
        connections.push({
            source: charNode,
            target: plotNode
        });
        // 大纲生成
        currentY += 150;
        const outlineNode = addNode('outline-generator', '大纲生成');
        connections.push({
            source: plotNode,
            target: outlineNode
        });
        // 根据长度决定是否需要详细大纲
        if (length === '长篇') {
            currentY += 150;
            const detailOutlineNode = addNode('detailed-outline', '详细大纲');
            connections.push({
                source: outlineNode,
                target: detailOutlineNode
            });
        }
        // 章节生成
        currentY += 150;
        const chapterNode = addNode('chapter-generator', '章节生成');
        const prevNode = length === '长篇' ? nodeIndex - 2 : outlineNode;
        connections.push({
            source: prevNode,
            target: chapterNode
        });
        // 根据需求添加润色和检查
        if (features.includes('高质量') || style.includes('文艺')) {
            currentY += 150;
            const polishNode = addNode('content-polisher', '内容润色', 100);
            connections.push({
                source: chapterNode,
                target: polishNode
            });
            const checkNode = addNode('consistency-checker', '一致性检查', 300);
            connections.push({
                source: chapterNode,
                target: checkNode
            });
            // 最终输出
            currentY += 150;
            const outputNode = addNode('output', '最终输出');
            connections.push({
                source: polishNode,
                target: outputNode
            });
            connections.push({
                source: checkNode,
                target: outputNode
            });
        } else {
            // 简单输出
            currentY += 150;
            const outputNode = addNode('output', '最终输出');
            connections.push({
                source: chapterNode,
                target: outputNode
            });
        }
        // 生成描述和复杂度
        const lengthInfo = length === '自定义' && customWordCount ? "自定义(".concat(customWordCount.toLocaleString(), "字)") : length;
        const getComplexity = ()=>{
            if (length === '自定义' && customWordCount) {
                if (customWordCount <= 50000) return 'simple';
                if (customWordCount <= 150000) return 'medium';
                return 'complex';
            }
            return length === '短篇' ? 'simple' : length === '中篇' ? 'medium' : 'complex';
        };
        return {
            id: 'custom-' + Date.now(),
            name: '自定义工作流',
            description: "为".concat(genre, "类型的").concat(lengthInfo, "小说定制的工作流"),
            nodes,
            connections,
            tags: [
                '自定义',
                genre,
                length
            ],
            complexity: getComplexity()
        };
    }
    // 优化现有工作流
    static optimizeWorkflow(currentNodes, requirements) {
        // 分析现有节点
        const nodeTypes = currentNodes.map((node)=>node.data.type);
        // 检查缺失的关键节点
        const missingNodes = [];
        if (!nodeTypes.includes('input')) missingNodes.push('input');
        if (!nodeTypes.includes('output')) missingNodes.push('output');
        if (!nodeTypes.includes('character-creator')) missingNodes.push('character-creator');
        if (!nodeTypes.includes('outline-generator')) missingNodes.push('outline-generator');
        // 生成优化建议
        const optimizedNodes = [
            ...currentNodes
        ];
        const connections = [];
        // 添加缺失的节点
        missingNodes.forEach((nodeType, index)=>{
            optimizedNodes.push({
                type: nodeType,
                label: this.getNodeLabel(nodeType),
                position: {
                    x: 200 + index * 150,
                    y: 100 + index * 150
                }
            });
        });
        return {
            id: 'optimized-' + Date.now(),
            name: '优化工作流',
            description: '基于现有工作流的优化版本',
            nodes: optimizedNodes,
            connections,
            tags: [
                '优化',
                '改进'
            ],
            complexity: 'medium'
        };
    }
    static getNodeLabel(nodeType) {
        const labels = {
            'input': '输入节点',
            'title-generator': '书名生成',
            'detail-generator': '详情生成',
            'character-creator': '角色创建',
            'worldbuilding': '世界观构建',
            'plotline-planner': '主线规划',
            'outline-generator': '大纲生成',
            'chapter-count-input': '章节数设定',
            'detailed-outline': '详细大纲',
            'chapter-generator': '章节生成',
            'content-polisher': '内容润色',
            'consistency-checker': '一致性检查',
            'condition': '条件分支',
            'loop': '循环执行',
            'output': '结果输出'
        };
        return labels[nodeType] || nodeType;
    }
}
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/workflowExecutor.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// 工作流执行引擎 - 所有功能调用AI模型
__turbopack_context__.s({
    "WorkflowExecutor": ()=>WorkflowExecutor,
    "createWorkflowExecutor": ()=>createWorkflowExecutor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/aiService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/message/index.js [app-client] (ecmascript) <export default as message>");
;
;
;
class WorkflowExecutor {
    // 执行单个节点
    async executeNode(node) {
        var // 通知开始执行
        _this_onProgress, _this;
        const startTime = Date.now();
        this.context.currentNode = node.id;
        (_this_onProgress = (_this = this).onProgress) === null || _this_onProgress === void 0 ? void 0 : _this_onProgress.call(_this, node.id, 'running');
        try {
            var // 通知执行完成
            _this_onProgress1, _this1;
            let result;
            switch(node.data.type){
                case 'input':
                    result = await this.executeInputNode(node);
                    break;
                case 'title-generator':
                    result = await this.executeTitleGeneratorNode(node);
                    break;
                case 'detail-generator':
                    result = await this.executeDetailGeneratorNode(node);
                    break;
                case 'character-creator':
                    result = await this.executeCharacterCreatorNode(node);
                    break;
                case 'worldbuilding':
                    result = await this.executeWorldbuildingNode(node);
                    break;
                case 'plotline-planner':
                    result = await this.executePlotlinePlannerNode(node);
                    break;
                case 'outline-generator':
                    result = await this.executeOutlineGeneratorNode(node);
                    break;
                case 'detailed-outline':
                    result = await this.executeDetailedOutlineNode(node);
                    break;
                case 'chapter-generator':
                    result = await this.executeChapterGeneratorNode(node);
                    break;
                case 'content-polisher':
                    result = await this.executeContentPolisherNode(node);
                    break;
                case 'consistency-checker':
                    result = await this.executeConsistencyCheckerNode(node);
                    break;
                case 'output':
                    result = await this.executeOutputNode(node);
                    break;
                default:
                    throw new Error("不支持的节点类型: ".concat(node.data.type));
            }
            const duration = Date.now() - startTime;
            // 保存结果到上下文
            this.context.results[node.id] = result;
            (_this_onProgress1 = (_this1 = this).onProgress) === null || _this_onProgress1 === void 0 ? void 0 : _this_onProgress1.call(_this1, node.id, 'completed', result);
            return {
                success: true,
                data: result,
                duration
            };
        } catch (error) {
            var // 通知执行错误
            _this_onProgress2, _this2;
            const duration = Date.now() - startTime;
            (_this_onProgress2 = (_this2 = this).onProgress) === null || _this_onProgress2 === void 0 ? void 0 : _this_onProgress2.call(_this2, node.id, 'error', error.message);
            return {
                success: false,
                error: error.message,
                duration
            };
        }
    }
    // 输入节点执行
    async executeInputNode(node) {
        // 输入节点通常从用户界面获取数据
        const inputData = node.data.config || this.context.variables.userInput || {};
        return {
            type: 'input',
            data: inputData,
            timestamp: new Date().toISOString()
        };
    }
    // 书名生成节点执行
    async executeTitleGeneratorNode(node) {
        // 检查AI配置
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        // 获取输入数据
        const inputData = this.getInputData([
            'input'
        ]);
        const config = node.data.config || {};
        const params = {
            genre: inputData.genre || config.genre || '现代都市',
            style: inputData.style || config.style || '轻松幽默',
            keywords: inputData.keywords || config.keywords || [],
            count: config.count || 5
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].generateTitles(params);
        if (!response.success) {
            throw new Error("AI书名生成失败: ".concat(response.error));
        }
        try {
            var _result_titles;
            const result = JSON.parse(response.data);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success("AI成功生成".concat(((_result_titles = result.titles) === null || _result_titles === void 0 ? void 0 : _result_titles.length) || 0, "个书名候选"));
            return {
                type: 'titles',
                titles: result.titles || [],
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 详情生成节点执行
    async executeDetailGeneratorNode(node) {
        var _inputData_titles_, _inputData_titles;
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        const inputData = this.getInputData([
            'input',
            'title-generator'
        ]);
        const config = node.data.config || {};
        const params = {
            title: inputData.selectedTitle || ((_inputData_titles = inputData.titles) === null || _inputData_titles === void 0 ? void 0 : (_inputData_titles_ = _inputData_titles[0]) === null || _inputData_titles_ === void 0 ? void 0 : _inputData_titles_.title) || config.title || '未命名小说',
            genre: inputData.genre || config.genre || '现代都市',
            style: inputData.style || config.style || '轻松幽默',
            outline: inputData.outline,
            characters: inputData.characters
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].generateNovelDetails(params);
        if (!response.success) {
            throw new Error("AI详情生成失败: ".concat(response.error));
        }
        try {
            const result = JSON.parse(response.data);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('AI成功生成小说详情');
            return {
                type: 'novel_details',
                details: result,
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 角色创建节点执行
    async executeCharacterCreatorNode(node) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        const inputData = this.getInputData([
            'input'
        ]);
        const config = node.data.config || {};
        const params = {
            genre: inputData.genre || config.genre || '现代都市',
            role: config.role || '主角',
            background: config.background,
            personality: config.personality
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].generateCharacter(params);
        if (!response.success) {
            throw new Error("AI角色生成失败: ".concat(response.error));
        }
        try {
            const result = JSON.parse(response.data);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success("AI成功生成角色: ".concat(result.name));
            return {
                type: 'character',
                character: result,
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 世界观构建节点执行
    async executeWorldbuildingNode(node) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        const inputData = this.getInputData([
            'input'
        ]);
        const config = node.data.config || {};
        const params = {
            genre: inputData.genre || config.genre || '现代都市',
            style: inputData.style || config.style || '轻松幽默',
            scope: config.scope || '中等规模',
            elements: config.elements || []
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].generateWorldbuilding(params);
        if (!response.success) {
            throw new Error("AI世界观生成失败: ".concat(response.error));
        }
        try {
            const result = JSON.parse(response.data);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success("AI成功生成世界观: ".concat(result.name));
            return {
                type: 'worldbuilding',
                worldbuilding: result,
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 主线规划节点执行
    async executePlotlinePlannerNode(node) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        const inputData = this.getInputData([
            'input',
            'character-creator',
            'worldbuilding'
        ]);
        const config = node.data.config || {};
        const params = {
            genre: inputData.genre || config.genre || '现代都市',
            style: inputData.style || config.style || '轻松幽默',
            characters: this.getAllCharacters(inputData),
            worldbuilding: inputData.worldbuilding,
            themes: config.themes || []
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].generatePlotline(params);
        if (!response.success) {
            throw new Error("AI主线规划失败: ".concat(response.error));
        }
        try {
            const result = JSON.parse(response.data);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success("AI成功生成主线规划: ".concat(result.title));
            return {
                type: 'plotline',
                plotline: result,
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 获取输入数据的辅助方法
    getInputData(nodeTypes) {
        const data = {};
        for(const nodeId in this.context.results){
            const result = this.context.results[nodeId];
            if (nodeTypes.some((type)=>result.type === type || nodeId.includes(type))) {
                Object.assign(data, result.data || result);
            }
        }
        return {
            ...this.context.variables,
            ...data
        };
    }
    // 大纲生成节点执行
    async executeOutlineGeneratorNode(node) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        const inputData = this.getInputData([
            'input',
            'character-creator',
            'plotline-planner'
        ]);
        const config = node.data.config || {};
        const params = {
            genre: inputData.genre || config.genre || '现代都市',
            plotline: inputData.plotline || {},
            characters: this.getAllCharacters(inputData),
            targetLength: inputData.length || config.targetLength || '中篇',
            chapterCount: config.chapterCount
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].generateOutline(params);
        if (!response.success) {
            throw new Error("AI大纲生成失败: ".concat(response.error));
        }
        try {
            var _result_chapters;
            const result = JSON.parse(response.data);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success("AI成功生成大纲，共".concat(((_result_chapters = result.chapters) === null || _result_chapters === void 0 ? void 0 : _result_chapters.length) || 0, "章"));
            return {
                type: 'outline',
                outline: result,
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 详细大纲节点执行
    async executeDetailedOutlineNode(node) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        const inputData = this.getInputData([
            'outline-generator',
            'character-creator',
            'worldbuilding'
        ]);
        const config = node.data.config || {};
        const params = {
            outline: inputData.outline || {},
            characters: this.getAllCharacters(inputData),
            worldbuilding: inputData.worldbuilding,
            selectedChapters: config.selectedChapters
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].generateDetailedOutline(params);
        if (!response.success) {
            throw new Error("AI详细大纲生成失败: ".concat(response.error));
        }
        try {
            const result = JSON.parse(response.data);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success('AI成功生成详细大纲');
            return {
                type: 'detailed_outline',
                detailedOutline: result,
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 章节生成节点执行
    async executeChapterGeneratorNode(node) {
        var _outline_chapters;
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        const inputData = this.getInputData([
            'detailed-outline',
            'outline-generator',
            'character-creator',
            'worldbuilding'
        ]);
        const config = node.data.config || {};
        const chapterNumber = config.chapterNumber || 1;
        const outline = inputData.detailedOutline || inputData.outline || {};
        const chapterOutline = ((_outline_chapters = outline.chapters) === null || _outline_chapters === void 0 ? void 0 : _outline_chapters[chapterNumber - 1]) || {};
        const params = {
            chapterNumber,
            chapterOutline,
            characters: this.getAllCharacters(inputData),
            worldbuilding: inputData.worldbuilding,
            previousChapters: inputData.previousChapters || [],
            style: inputData.style || config.style || '轻松幽默',
            targetWordCount: config.targetWordCount || 2000
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].generateChapter(params);
        if (!response.success) {
            throw new Error("AI章节生成失败: ".concat(response.error));
        }
        try {
            const result = JSON.parse(response.data);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success("AI成功生成第".concat(chapterNumber, "章，约").concat(result.word_count || 0, "字"));
            return {
                type: 'chapter',
                chapter: result,
                chapterNumber,
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 内容润色节点执行
    async executeContentPolisherNode(node) {
        var _inputData_chapter;
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        const inputData = this.getInputData([
            'chapter-generator'
        ]);
        const config = node.data.config || {};
        const content = ((_inputData_chapter = inputData.chapter) === null || _inputData_chapter === void 0 ? void 0 : _inputData_chapter.content) || config.content || '';
        if (!content) {
            throw new Error('没有找到需要润色的内容');
        }
        const params = {
            content,
            style: inputData.style || config.style || '轻松幽默',
            focusAreas: config.focusAreas || [
                '表达优化',
                '节奏调整'
            ],
            targetAudience: config.targetAudience || '一般读者'
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].polishContent(params);
        if (!response.success) {
            throw new Error("AI内容润色失败: ".concat(response.error));
        }
        try {
            const result = JSON.parse(response.data);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success("AI成功润色内容，改进评分: ".concat(result.readability_score || 'N/A'));
            return {
                type: 'polished_content',
                polishedContent: result,
                originalContent: content,
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 一致性检查节点执行
    async executeConsistencyCheckerNode(node) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].isConfigured()) {
            throw new Error('请先配置AI API');
        }
        const inputData = this.getInputData([
            'chapter-generator',
            'character-creator',
            'worldbuilding'
        ]);
        const config = node.data.config || {};
        const content = [];
        // 收集所有章节内容
        for(const nodeId in this.context.results){
            var _result_chapter;
            const result = this.context.results[nodeId];
            if (result.type === 'chapter' && ((_result_chapter = result.chapter) === null || _result_chapter === void 0 ? void 0 : _result_chapter.content)) {
                content.push(result.chapter.content);
            }
        }
        if (content.length === 0) {
            throw new Error('没有找到需要检查的内容');
        }
        const params = {
            content,
            characters: this.getAllCharacters(inputData),
            worldbuilding: inputData.worldbuilding,
            checkTypes: config.checkTypes || [
                '角色一致性',
                '情节一致性',
                '世界观一致性'
            ]
        };
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$aiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["aiService"].checkConsistency(params);
        if (!response.success) {
            throw new Error("AI一致性检查失败: ".concat(response.error));
        }
        try {
            var _result_issues;
            const result = JSON.parse(response.data);
            const score = result.consistency_score || 0;
            const issueCount = ((_result_issues = result.issues) === null || _result_issues === void 0 ? void 0 : _result_issues.length) || 0;
            if (score >= 80) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success("一致性检查完成，评分: ".concat(score, "分，发现").concat(issueCount, "个问题"));
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].warning("一致性检查完成，评分: ".concat(score, "分，发现").concat(issueCount, "个问题"));
            }
            return {
                type: 'consistency_check',
                consistencyReport: result,
                params,
                timestamp: new Date().toISOString()
            };
        } catch (parseError) {
            throw new Error('AI返回数据格式错误');
        }
    }
    // 输出节点执行
    async executeOutputNode(node) {
        var _inputData_titles_, _inputData_titles;
        const inputData = this.getInputData([
            'title-generator',
            'detail-generator',
            'chapter-generator',
            'polished_content',
            'consistency_check'
        ]);
        // 整理最终输出
        const output = {
            title: inputData.selectedTitle || ((_inputData_titles = inputData.titles) === null || _inputData_titles === void 0 ? void 0 : (_inputData_titles_ = _inputData_titles[0]) === null || _inputData_titles_ === void 0 ? void 0 : _inputData_titles_.title) || '未命名小说',
            details: inputData.details || {},
            chapters: [],
            consistencyReport: inputData.consistencyReport,
            metadata: {
                generatedAt: new Date().toISOString(),
                totalChapters: 0,
                totalWords: 0,
                aiGenerated: true
            }
        };
        // 收集所有章节
        for(const nodeId in this.context.results){
            const result = this.context.results[nodeId];
            if (result.type === 'chapter') {
                output.chapters.push(result.chapter);
                output.metadata.totalWords += result.chapter.word_count || 0;
            }
        }
        output.metadata.totalChapters = output.chapters.length;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$message$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__message$3e$__["message"].success("小说创作完成！共".concat(output.metadata.totalChapters, "章，约").concat(output.metadata.totalWords, "字"));
        return {
            type: 'final_output',
            output,
            timestamp: new Date().toISOString()
        };
    }
    // 获取所有角色的辅助方法
    getAllCharacters(inputData) {
        const characters = [];
        // 从角色创建节点获取角色
        if (inputData.character) {
            characters.push(inputData.character);
        }
        // 从其他来源获取角色
        if (inputData.characters && Array.isArray(inputData.characters)) {
            characters.push(...inputData.characters);
        }
        return characters;
    }
    constructor(context, onProgress){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "context", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "onProgress", void 0);
        this.context = context;
        this.onProgress = onProgress;
    }
}
const createWorkflowExecutor = (context, onProgress)=>{
    return new WorkflowExecutor(context, onProgress);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Home
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$MainLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/MainLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$workflow$2f$WorkflowEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/workflow/WorkflowEditor.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$project$2f$ProjectOverview$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/project/ProjectOverview.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$outline$2f$OutlineManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/outline/OutlineManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$character$2f$CharacterManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/character/CharacterManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$worldbuilding$2f$WorldBuildingManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/worldbuilding/WorldBuildingManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$plotline$2f$PlotLineManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/plotline/PlotLineManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$title$2f$TitleManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/title/TitleManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$document$2f$DocumentManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/document/DocumentManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$prompt$2f$PromptManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/prompt/PromptManager.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/store/index.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
function Home() {
    _s();
    const { ui } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])();
    const renderContent = ()=>{
        switch(ui.activeTab){
            case 'workflow':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$workflow$2f$WorkflowEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 22,
                    columnNumber: 16
                }, this);
            case 'projects':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$project$2f$ProjectOverview$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 24,
                    columnNumber: 16
                }, this);
            case 'outlines':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$outline$2f$OutlineManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 26,
                    columnNumber: 16
                }, this);
            case 'characters':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$character$2f$CharacterManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 28,
                    columnNumber: 16
                }, this);
            case 'worldbuilding':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$worldbuilding$2f$WorldBuildingManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 30,
                    columnNumber: 16
                }, this);
            case 'plotlines':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$plotline$2f$PlotLineManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 32,
                    columnNumber: 16
                }, this);
            case 'titles':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$title$2f$TitleManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 34,
                    columnNumber: 16
                }, this);
            case 'documents':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$document$2f$DocumentManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 36,
                    columnNumber: 16
                }, this);
            case 'prompts':
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$prompt$2f$PromptManager$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 38,
                    columnNumber: 16
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$workflow$2f$WorkflowEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 40,
                    columnNumber: 16
                }, this);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$MainLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: renderContent()
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 45,
        columnNumber: 5
    }, this);
}
_s(Home, "yqECtPIhn+ICmQUwF5lWA7aHPuU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_cdc8650f._.js.map