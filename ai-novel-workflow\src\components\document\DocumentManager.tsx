'use client';

import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Tree,
  Tabs,
  List,
  Tag,
  message,
  Row,
  Col,
  Progress,
  Tooltip,
  Badge
} from 'antd';
import {
  FolderOutlined,
  FileOutlined,
  DownloadOutlined,
  UploadOutlined,
  SearchOutlined,
  HistoryOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/store';

const { Title, Text } = Typography;

const DocumentManager: React.FC = () => {
  const { currentProject } = useAppStore();
  const [activeTab, setActiveTab] = useState('structure');

  // 模拟文档结构
  const documentStructure = [
    {
      title: 'workflow-configs',
      key: 'workflow-configs',
      icon: <FolderOutlined />,
      children: [
        { title: 'main-workflow.json', key: 'main-workflow', icon: <FileOutlined /> },
        { title: 'backup-workflow.json', key: 'backup-workflow', icon: <FileOutlined /> },
      ]
    },
    {
      title: 'prompts',
      key: 'prompts',
      icon: <FolderOutlined />,
      children: [
        { title: 'character-prompts.md', key: 'character-prompts', icon: <FileTextOutlined /> },
        { title: 'plot-prompts.md', key: 'plot-prompts', icon: <FileTextOutlined /> },
      ]
    },
    {
      title: 'generated-content',
      key: 'generated-content',
      icon: <FolderOutlined />,
      children: [
        {
          title: 'outlines',
          key: 'outlines',
          icon: <FolderOutlined />,
          children: [
            { title: 'main-outline.md', key: 'main-outline', icon: <FileTextOutlined /> },
            { title: 'detailed-outline.md', key: 'detailed-outline', icon: <FileTextOutlined /> },
          ]
        },
        {
          title: 'characters',
          key: 'characters',
          icon: <FolderOutlined />,
          children: [
            { title: 'protagonist.json', key: 'protagonist', icon: <FileOutlined /> },
            { title: 'supporting-characters.json', key: 'supporting', icon: <FileOutlined /> },
          ]
        },
        {
          title: 'chapters',
          key: 'chapters',
          icon: <FolderOutlined />,
          children: [
            { title: 'chapter-01.md', key: 'chapter-01', icon: <FileTextOutlined /> },
            { title: 'chapter-02.md', key: 'chapter-02', icon: <FileTextOutlined /> },
            { title: 'chapter-03.md', key: 'chapter-03', icon: <FileTextOutlined /> },
          ]
        }
      ]
    },
    {
      title: 'exports',
      key: 'exports',
      icon: <FolderOutlined />,
      children: [
        { title: 'novel-draft.docx', key: 'novel-docx', icon: <FileWordOutlined /> },
        { title: 'novel-draft.pdf', key: 'novel-pdf', icon: <FilePdfOutlined /> },
      ]
    }
  ];

  if (!currentProject) {
    return (
      <div className="p-8 text-center">
        <Title level={3}>请先选择或创建一个项目</Title>
        <Text type="secondary">
          您需要先在项目总览中创建或选择一个项目，然后才能管理文档。
        </Text>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Title level={2}>文档管理</Title>
          <Text type="secondary">管理项目文档和文件 - 项目: {currentProject.name}</Text>
        </div>
        <Space>
          <Button icon={<UploadOutlined />}>
            导入文档
          </Button>
          <Button type="primary" icon={<DownloadOutlined />}>
            导出项目
          </Button>
        </Space>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'structure',
            label: (
              <span>
                <FolderOutlined />
                文档结构
              </span>
            ),
            children: (
              <Row gutter={16}>
                <Col span={8}>
                  <Card title="项目文档树">
                    <Tree
                      showIcon
                      defaultExpandAll
                      treeData={documentStructure}
                      onSelect={(keys, info) => {
                        if (keys.length > 0) {
                          message.info(`选中文件: ${info.node.title}`);
                        }
                      }}
                    />
                  </Card>
                </Col>
                <Col span={16}>
                  <Card title="文档预览">
                    <div className="text-center py-12">
                      <FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                      <div className="mt-4">
                        <Text type="secondary">选择左侧文档查看内容</Text>
                        <br />
                        <Text type="secondary">支持Markdown、JSON、文本文件预览</Text>
                      </div>
                    </div>
                  </Card>
                </Col>
              </Row>
            ),
          },
          {
            key: 'versions',
            label: (
              <span>
                <HistoryOutlined />
                版本历史
              </span>
            ),
            children: (
              <Card className="text-center py-12">
                <HistoryOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                <div className="mt-4">
                  <Text type="secondary">版本控制功能开发中</Text>
                  <br />
                  <Text type="secondary">即将支持文档版本管理和历史对比</Text>
                </div>
              </Card>
            ),
          },
        ]}
      />
    </div>
  );
};

export default DocumentManager;
