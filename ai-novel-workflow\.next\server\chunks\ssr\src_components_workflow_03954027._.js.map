{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/workflow/CustomNode.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Handle, Position, NodeProps } from '@xyflow/react';\nimport { Card, Typography, Tag, Button } from 'antd';\nimport { \n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  SettingOutlined,\n  DeleteOutlined\n} from '@ant-design/icons';\n\nconst { Text } = Typography;\n\ninterface CustomNodeData {\n  label: string;\n  type: string;\n  status: 'idle' | 'running' | 'completed' | 'error';\n  description?: string;\n  config?: Record<string, any>;\n  onEdit?: () => void;\n  onDelete?: () => void;\n}\n\nconst CustomNode: React.FC<NodeProps<CustomNodeData>> = ({ data, selected }) => {\n  const getStatusIcon = () => {\n    switch (data.status) {\n      case 'running':\n        return <PauseCircleOutlined className=\"text-blue-500\" />;\n      case 'completed':\n        return <CheckCircleOutlined className=\"text-green-500\" />;\n      case 'error':\n        return <ExclamationCircleOutlined className=\"text-red-500\" />;\n      default:\n        return <PlayCircleOutlined className=\"text-gray-400\" />;\n    }\n  };\n\n  const getStatusColor = () => {\n    switch (data.status) {\n      case 'running':\n        return 'processing';\n      case 'completed':\n        return 'success';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  const getNodeIcon = () => {\n    switch (data.type) {\n      case 'input':\n        return '📝';\n      case 'title-generator':\n        return '📚';\n      case 'detail-generator':\n        return '📄';\n      case 'character-creator':\n        return '👥';\n      case 'worldbuilding':\n        return '🌍';\n      case 'plotline-planner':\n        return '📈';\n      case 'outline-generator':\n        return '📋';\n      case 'chapter-count-input':\n        return '🔢';\n      case 'detailed-outline':\n        return '📝';\n      case 'chapter-generator':\n        return '📖';\n      case 'content-polisher':\n        return '✨';\n      case 'consistency-checker':\n        return '✅';\n      case 'condition':\n        return '🔀';\n      case 'loop':\n        return '🔄';\n      case 'output':\n        return '📤';\n      default:\n        return '⚙️';\n    }\n  };\n\n  return (\n    <div className={`custom-node ${selected ? 'selected' : ''}`}>\n      <Handle\n        type=\"target\"\n        position={Position.Top}\n        className=\"w-3 h-3 !bg-gray-400 border-2 border-white\"\n      />\n      \n      <Card\n        size=\"small\"\n        className={`min-w-48 shadow-md transition-all duration-200 ${\n          selected ? 'ring-2 ring-blue-500 shadow-lg' : ''\n        } ${data.status === 'running' ? 'animate-pulse' : ''}`}\n        bodyStyle={{ padding: '12px' }}\n      >\n        <div className=\"flex items-start justify-between mb-2\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-lg\">{getNodeIcon()}</span>\n            <div>\n              <Text strong className=\"text-sm block\">\n                {data.label}\n              </Text>\n              {data.description && (\n                <Text type=\"secondary\" className=\"text-xs\">\n                  {data.description}\n                </Text>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            {getStatusIcon()}\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <Tag color={getStatusColor()} size=\"small\">\n            {data.status === 'idle' && '等待'}\n            {data.status === 'running' && '运行中'}\n            {data.status === 'completed' && '已完成'}\n            {data.status === 'error' && '错误'}\n          </Tag>\n          \n          <div className=\"flex space-x-1\">\n            <Button\n              type=\"text\"\n              size=\"small\"\n              icon={<SettingOutlined />}\n              onClick={data.onEdit}\n              className=\"text-xs\"\n            />\n            <Button\n              type=\"text\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              onClick={data.onDelete}\n              className=\"text-xs\"\n            />\n          </div>\n        </div>\n\n        {/* 配置信息 */}\n        {data.config && Object.keys(data.config).length > 0 && (\n          <div className=\"mt-2 pt-2 border-t border-gray-100\">\n            <Text type=\"secondary\" className=\"text-xs\">\n              已配置 {Object.keys(data.config).length} 个参数\n            </Text>\n          </div>\n        )}\n      </Card>\n\n      <Handle\n        type=\"source\"\n        position={Position.Bottom}\n        className=\"w-3 h-3 !bg-gray-400 border-2 border-white\"\n      />\n\n      <style jsx>{`\n        .custom-node.selected {\n          z-index: 1000;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default CustomNode;\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAcA,MAAM,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAY3B,MAAM,aAAkD,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;IACzE,MAAM,gBAAgB;QACpB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,qBAAO,8OAAC,gOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACxC,KAAK;gBACH,qBAAO,8OAAC,gOAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACxC,KAAK;gBACH,qBAAO,8OAAC,4OAAA,CAAA,4BAAyB;oBAAC,WAAU;;;;;;YAC9C;gBACE,qBAAO,8OAAC,8NAAA,CAAA,qBAAkB;oBAAC,WAAU;;;;;;QACzC;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ,KAAK,MAAM;YACjB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc;QAClB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;kDAAe,CAAC,YAAY,EAAE,WAAW,aAAa,IAAI;;0BACzD,8OAAC,yKAAA,CAAA,SAAM;gBACL,MAAK;gBACL,UAAU,0JAAA,CAAA,WAAQ,CAAC,GAAG;gBACtB,WAAU;;;;;;0BAGZ,8OAAC,8KAAA,CAAA,OAAI;gBACH,MAAK;gBACL,WAAW,CAAC,+CAA+C,EACzD,WAAW,mCAAmC,GAC/C,CAAC,EAAE,KAAK,MAAM,KAAK,YAAY,kBAAkB,IAAI;gBACtD,WAAW;oBAAE,SAAS;gBAAO;;kCAE7B,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAe;kDAAW;;;;;;kDAC3B,8OAAC;;;0DACC,8OAAC;gDAAK,MAAM;gDAAC,WAAU;0DACpB,KAAK,KAAK;;;;;;4CAEZ,KAAK,WAAW,kBACf,8OAAC;gDAAK,MAAK;gDAAY,WAAU;0DAC9B,KAAK,WAAW;;;;;;;;;;;;;;;;;;0CAKzB,8OAAC;0EAAc;0CACZ;;;;;;;;;;;;kCAIL,8OAAC;kEAAc;;0CACb,8OAAC,4KAAA,CAAA,MAAG;gCAAC,OAAO;gCAAkB,MAAK;;oCAChC,KAAK,MAAM,KAAK,UAAU;oCAC1B,KAAK,MAAM,KAAK,aAAa;oCAC7B,KAAK,MAAM,KAAK,eAAe;oCAC/B,KAAK,MAAM,KAAK,WAAW;;;;;;;0CAG9B,8OAAC;0EAAc;;kDACb,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAK;wCACL,oBAAM,8OAAC,wNAAA,CAAA,kBAAe;;;;;wCACtB,SAAS,KAAK,MAAM;wCACpB,WAAU;;;;;;kDAEZ,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAK;wCACL,MAAM;wCACN,oBAAM,8OAAC,sNAAA,CAAA,iBAAc;;;;;wCACrB,SAAS,KAAK,QAAQ;wCACtB,WAAU;;;;;;;;;;;;;;;;;;oBAMf,KAAK,MAAM,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE,MAAM,GAAG,mBAChD,8OAAC;kEAAc;kCACb,cAAA,8OAAC;4BAAK,MAAK;4BAAY,WAAU;;gCAAU;gCACpC,OAAO,IAAI,CAAC,KAAK,MAAM,EAAE,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAM7C,8OAAC,yKAAA,CAAA,SAAM;gBACL,MAAK;gBACL,UAAU,0JAAA,CAAA,WAAQ,CAAC,MAAM;gBACzB,WAAU;;;;;;;;;;;;;;;;AAUlB;uCAEe", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/workflow/WorkflowAIAssistant.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Modal,\n  Form,\n  Select,\n  Button,\n  Card,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Tag,\n  Divider,\n  Steps,\n  Radio,\n  Checkbox,\n  message,\n  Badge\n} from 'antd';\nimport {\n  RobotOutlined,\n  BulbOutlined,\n  ThunderboltOutlined,\n  StarOutlined,\n  CheckCircleOutlined,\n  SettingOutlined\n} from '@ant-design/icons';\nimport { WorkflowAI, WorkflowTemplate, WORKFLOW_TEMPLATES } from '@/utils/workflowAI';\nimport { aiService } from '@/utils/aiService';\nimport AIConfigModal from '@/components/settings/AIConfigModal';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { Step } = Steps;\n\ninterface WorkflowAIAssistantProps {\n  visible: boolean;\n  onClose: () => void;\n  onApplyWorkflow: (template: WorkflowTemplate) => void;\n  currentNodes?: any[];\n}\n\nconst WorkflowAIAssistant: React.FC<WorkflowAIAssistantProps> = ({\n  visible,\n  onClose,\n  onApplyWorkflow,\n  currentNodes = []\n}) => {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [form] = Form.useForm();\n  const [recommendations, setRecommendations] = useState<WorkflowTemplate[]>([]);\n  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);\n  const [mode, setMode] = useState<'recommend' | 'custom' | 'optimize'>('recommend');\n  const [loading, setLoading] = useState(false);\n  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);\n\n  const handleAnalyze = async () => {\n    // 检查AI配置\n    if (!aiService.isConfigured()) {\n      message.warning('请先配置AI API');\n      setIsConfigModalVisible(true);\n      return;\n    }\n\n    try {\n      setLoading(true);\n      const values = await form.validateFields();\n\n      if (mode === 'recommend') {\n        // 使用AI分析需求\n        const aiResponse = await aiService.analyzeWorkflowRequirements(values);\n\n        if (aiResponse.success) {\n          try {\n            const aiResult = JSON.parse(aiResponse.data);\n            // 根据AI推荐结果获取对应模板\n            const recommendedTemplate = WORKFLOW_TEMPLATES.find(t => t.id === aiResult.recommended);\n            const alternatives = aiResult.alternatives?.map((id: string) =>\n              WORKFLOW_TEMPLATES.find(t => t.id === id)\n            ).filter(Boolean) || [];\n\n            const recs = recommendedTemplate ? [recommendedTemplate, ...alternatives] : WorkflowAI.analyzeRequirements(values);\n            setRecommendations(recs);\n            setCurrentStep(1);\n\n            if (aiResult.confidence && aiResult.confidence > 80) {\n              message.success(`AI推荐置信度: ${aiResult.confidence}%`);\n            }\n          } catch (parseError) {\n            // AI返回格式不正确，使用本地分析\n            const recs = WorkflowAI.analyzeRequirements(values);\n            setRecommendations(recs);\n            setCurrentStep(1);\n            message.info('使用本地智能分析');\n          }\n        } else {\n          // AI请求失败，使用本地分析\n          const recs = WorkflowAI.analyzeRequirements(values);\n          setRecommendations(recs);\n          setCurrentStep(1);\n          message.warning(`AI分析失败: ${aiResponse.error}，使用本地分析`);\n        }\n      } else if (mode === 'custom') {\n        // 使用AI生成自定义工作流\n        const aiResponse = await aiService.generateCustomWorkflow(values);\n\n        if (aiResponse.success) {\n          try {\n            const customTemplate = JSON.parse(aiResponse.data);\n            setSelectedTemplate(customTemplate);\n            setCurrentStep(2);\n            message.success('AI已生成定制化工作流');\n          } catch (parseError) {\n            // AI返回格式不正确，使用本地生成\n            const customTemplate = WorkflowAI.generateCustomWorkflow(values);\n            setSelectedTemplate(customTemplate);\n            setCurrentStep(2);\n            message.info('使用本地生成器');\n          }\n        } else {\n          // AI请求失败，使用本地生成\n          const customTemplate = WorkflowAI.generateCustomWorkflow(values);\n          setSelectedTemplate(customTemplate);\n          setCurrentStep(2);\n          message.warning(`AI生成失败: ${aiResponse.error}，使用本地生成`);\n        }\n      } else if (mode === 'optimize') {\n        // 使用AI优化现有工作流\n        const aiResponse = await aiService.optimizeWorkflow(currentNodes, values);\n\n        if (aiResponse.success) {\n          try {\n            const optimizationResult = JSON.parse(aiResponse.data);\n            if (optimizationResult.optimized_workflow) {\n              setSelectedTemplate(optimizationResult.optimized_workflow);\n              setCurrentStep(2);\n              message.success(`AI优化完成，改进评分: ${optimizationResult.improvement_score || 'N/A'}`);\n            } else {\n              message.info('AI建议当前工作流已经很好，无需大幅调整');\n            }\n          } catch (parseError) {\n            // AI返回格式不正确，使用本地优化\n            const optimizedTemplate = WorkflowAI.optimizeWorkflow(currentNodes, values);\n            setSelectedTemplate(optimizedTemplate);\n            setCurrentStep(2);\n            message.info('使用本地优化器');\n          }\n        } else {\n          // AI请求失败，使用本地优化\n          const optimizedTemplate = WorkflowAI.optimizeWorkflow(currentNodes, values);\n          setSelectedTemplate(optimizedTemplate);\n          setCurrentStep(2);\n          message.warning(`AI优化失败: ${aiResponse.error}，使用本地优化`);\n        }\n      }\n    } catch (error) {\n      console.error('分析失败:', error);\n      message.error('分析过程中出现错误');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSelectTemplate = (template: WorkflowTemplate) => {\n    setSelectedTemplate(template);\n    setCurrentStep(2);\n  };\n\n  const handleApply = () => {\n    if (selectedTemplate) {\n      onApplyWorkflow(selectedTemplate);\n      message.success('工作流已应用！');\n      onClose();\n      resetState();\n    }\n  };\n\n  const resetState = () => {\n    setCurrentStep(0);\n    setRecommendations([]);\n    setSelectedTemplate(null);\n    form.resetFields();\n  };\n\n  const getComplexityColor = (complexity: string) => {\n    switch (complexity) {\n      case 'simple': return 'green';\n      case 'medium': return 'orange';\n      case 'complex': return 'red';\n      default: return 'default';\n    }\n  };\n\n  const getComplexityText = (complexity: string) => {\n    switch (complexity) {\n      case 'simple': return '简单';\n      case 'medium': return '中等';\n      case 'complex': return '复杂';\n      default: return '未知';\n    }\n  };\n\n  return (\n    <Modal\n      title={\n        <div className=\"flex items-center space-x-2\">\n          <RobotOutlined className=\"text-blue-500\" />\n          <span>AI工作流助手</span>\n        </div>\n      }\n      open={visible}\n      onCancel={() => {\n        onClose();\n        resetState();\n      }}\n      width={800}\n      footer={null}\n      destroyOnClose\n    >\n      <div className=\"space-y-6\">\n        {/* 模式选择 */}\n        <Card size=\"small\">\n          <Radio.Group \n            value={mode} \n            onChange={(e) => {\n              setMode(e.target.value);\n              resetState();\n            }}\n            className=\"w-full\"\n          >\n            <Row gutter={16}>\n              <Col span={8}>\n                <Radio.Button value=\"recommend\" className=\"w-full text-center\">\n                  <div className=\"py-2\">\n                    <BulbOutlined className=\"text-lg block mb-1\" />\n                    <div>智能推荐</div>\n                    <Text type=\"secondary\" className=\"text-xs\">基于需求推荐模板</Text>\n                  </div>\n                </Radio.Button>\n              </Col>\n              <Col span={8}>\n                <Radio.Button value=\"custom\" className=\"w-full text-center\">\n                  <div className=\"py-2\">\n                    <SettingOutlined className=\"text-lg block mb-1\" />\n                    <div>自定义生成</div>\n                    <Text type=\"secondary\" className=\"text-xs\">AI生成定制工作流</Text>\n                  </div>\n                </Radio.Button>\n              </Col>\n              <Col span={8}>\n                <Radio.Button value=\"optimize\" className=\"w-full text-center\">\n                  <div className=\"py-2\">\n                    <ThunderboltOutlined className=\"text-lg block mb-1\" />\n                    <div>优化现有</div>\n                    <Text type=\"secondary\" className=\"text-xs\">优化当前工作流</Text>\n                  </div>\n                </Radio.Button>\n              </Col>\n            </Row>\n          </Radio.Group>\n        </Card>\n\n        {/* 步骤指示器 */}\n        <Steps current={currentStep} size=\"small\">\n          <Step title=\"需求分析\" icon={<BulbOutlined />} />\n          {mode === 'recommend' && <Step title=\"模板推荐\" icon={<StarOutlined />} />}\n          <Step title=\"预览确认\" icon={<CheckCircleOutlined />} />\n        </Steps>\n\n        {/* 步骤内容 */}\n        {currentStep === 0 && (\n          <Card title=\"告诉我您的创作需求\">\n            <Form form={form} layout=\"vertical\">\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"genre\"\n                    label=\"小说类型\"\n                    rules={[{ required: true, message: '请选择小说类型' }]}\n                  >\n                    <Select placeholder=\"请选择类型\">\n                      <Option value=\"现代都市\">现代都市</Option>\n                      <Option value=\"古代言情\">古代言情</Option>\n                      <Option value=\"玄幻修仙\">玄幻修仙</Option>\n                      <Option value=\"科幻未来\">科幻未来</Option>\n                      <Option value=\"悬疑推理\">悬疑推理</Option>\n                      <Option value=\"历史军事\">历史军事</Option>\n                      <Option value=\"奇幻冒险\">奇幻冒险</Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"style\"\n                    label=\"写作风格\"\n                    rules={[{ required: true, message: '请选择写作风格' }]}\n                  >\n                    <Select placeholder=\"请选择风格\">\n                      <Option value=\"轻松幽默\">轻松幽默</Option>\n                      <Option value=\"深沉严肃\">深沉严肃</Option>\n                      <Option value=\"浪漫温馨\">浪漫温馨</Option>\n                      <Option value=\"紧张刺激\">紧张刺激</Option>\n                      <Option value=\"文艺清新\">文艺清新</Option>\n                      <Option value=\"热血激昂\">热血激昂</Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"length\"\n                    label=\"作品长度\"\n                    rules={[{ required: true, message: '请选择作品长度' }]}\n                  >\n                    <Select placeholder=\"请选择长度\">\n                      <Option value=\"短篇\">短篇 (1-5万字)</Option>\n                      <Option value=\"中篇\">中篇 (5-15万字)</Option>\n                      <Option value=\"长篇\">长篇 (15万字以上)</Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item\n                    name=\"experience\"\n                    label=\"创作经验\"\n                    rules={[{ required: true, message: '请选择创作经验' }]}\n                  >\n                    <Select placeholder=\"请选择经验水平\">\n                      <Option value=\"新手\">新手 (第一次创作)</Option>\n                      <Option value=\"进阶\">进阶 (有一定经验)</Option>\n                      <Option value=\"专业\">专业 (经验丰富)</Option>\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item name=\"features\" label=\"特殊需求\">\n                <Checkbox.Group>\n                  <Row>\n                    <Col span={8}><Checkbox value=\"世界观\">复杂世界观</Checkbox></Col>\n                    <Col span={8}><Checkbox value=\"高质量\">高质量润色</Checkbox></Col>\n                    <Col span={8}><Checkbox value=\"快速\">快速生成</Checkbox></Col>\n                    <Col span={8}><Checkbox value=\"详细\">详细大纲</Checkbox></Col>\n                    <Col span={8}><Checkbox value=\"多角色\">多角色设定</Checkbox></Col>\n                    <Col span={8}><Checkbox value=\"一致性\">一致性检查</Checkbox></Col>\n                  </Row>\n                </Checkbox.Group>\n              </Form.Item>\n\n              <div className=\"text-center space-x-4\">\n                <Button type=\"primary\" onClick={handleAnalyze} size=\"large\" loading={loading}>\n                  <RobotOutlined />\n                  {loading ? 'AI分析中...' : 'AI分析需求'}\n                </Button>\n\n                {!aiService.isConfigured() && (\n                  <Button onClick={() => setIsConfigModalVisible(true)}>\n                    <SettingOutlined />\n                    配置AI API\n                  </Button>\n                )}\n              </div>\n            </Form>\n          </Card>\n        )}\n\n        {/* 模板推荐步骤 */}\n        {currentStep === 1 && mode === 'recommend' && (\n          <Card title=\"AI为您推荐以下工作流模板\">\n            <div className=\"space-y-4\">\n              {recommendations.map((template, index) => (\n                <Card\n                  key={template.id}\n                  size=\"small\"\n                  className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                  onClick={() => handleSelectTemplate(template)}\n                  extra={\n                    <Badge count={index + 1} style={{ backgroundColor: '#52c41a' }} />\n                  }\n                >\n                  <Row gutter={16}>\n                    <Col span={16}>\n                      <div>\n                        <Title level={5} className=\"mb-1\">{template.name}</Title>\n                        <Paragraph type=\"secondary\" className=\"mb-2\">\n                          {template.description}\n                        </Paragraph>\n                        <Space>\n                          {template.tags.map(tag => (\n                            <Tag key={tag} color=\"blue\">{tag}</Tag>\n                          ))}\n                        </Space>\n                      </div>\n                    </Col>\n                    <Col span={8} className=\"text-right\">\n                      <div className=\"space-y-2\">\n                        <div>\n                          <Tag color={getComplexityColor(template.complexity)}>\n                            {getComplexityText(template.complexity)}\n                          </Tag>\n                        </div>\n                        <div>\n                          <Text type=\"secondary\">\n                            {template.nodes.length} 个节点\n                          </Text>\n                        </div>\n                        <div>\n                          <Text type=\"secondary\">\n                            {template.connections.length} 个连接\n                          </Text>\n                        </div>\n                      </div>\n                    </Col>\n                  </Row>\n                </Card>\n              ))}\n            </div>\n          </Card>\n        )}\n\n        {/* 预览确认步骤 */}\n        {currentStep === 2 && selectedTemplate && (\n          <Card title=\"工作流预览\">\n            <div className=\"space-y-4\">\n              <div>\n                <Title level={4}>{selectedTemplate.name}</Title>\n                <Paragraph>{selectedTemplate.description}</Paragraph>\n                <Space>\n                  {selectedTemplate.tags.map(tag => (\n                    <Tag key={tag} color=\"blue\">{tag}</Tag>\n                  ))}\n                  <Tag color={getComplexityColor(selectedTemplate.complexity)}>\n                    复杂度: {getComplexityText(selectedTemplate.complexity)}\n                  </Tag>\n                </Space>\n              </div>\n\n              <Divider />\n\n              <div>\n                <Title level={5}>工作流结构</Title>\n                <Row gutter={16}>\n                  <Col span={12}>\n                    <Card size=\"small\" title=\"节点列表\">\n                      <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                        {selectedTemplate.nodes.map((node, index) => (\n                          <div key={index} className=\"flex items-center justify-between\">\n                            <Text>{node.label}</Text>\n                            <Tag>{node.type}</Tag>\n                          </div>\n                        ))}\n                      </div>\n                    </Card>\n                  </Col>\n                  <Col span={12}>\n                    <Card size=\"small\" title=\"统计信息\">\n                      <div className=\"space-y-2\">\n                        <div className=\"flex justify-between\">\n                          <Text>节点数量:</Text>\n                          <Text strong>{selectedTemplate.nodes.length}</Text>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <Text>连接数量:</Text>\n                          <Text strong>{selectedTemplate.connections.length}</Text>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <Text>预估时间:</Text>\n                          <Text strong>\n                            {selectedTemplate.complexity === 'simple' ? '10-20分钟' :\n                             selectedTemplate.complexity === 'medium' ? '30-60分钟' : '1-2小时'}\n                          </Text>\n                        </div>\n                      </div>\n                    </Card>\n                  </Col>\n                </Row>\n              </div>\n\n              <div className=\"text-center space-x-4\">\n                <Button onClick={() => setCurrentStep(currentStep - 1)}>\n                  返回修改\n                </Button>\n                <Button type=\"primary\" onClick={handleApply} size=\"large\">\n                  <CheckCircleOutlined />\n                  应用此工作流\n                </Button>\n              </div>\n            </div>\n          </Card>\n        )}\n      </div>\n\n      {/* AI配置模态框 */}\n      <AIConfigModal\n        visible={isConfigModalVisible}\n        onClose={() => setIsConfigModalVisible(false)}\n        onConfigured={() => {\n          setIsConfigModalVisible(false);\n          message.success('AI配置已完成，现在可以使用AI功能了！');\n        }}\n      />\n    </Modal>\n  );\n};\n\nexport default WorkflowAIAssistant;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AA/BA;;;;;;;;AAiCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,kLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,IAAI,EAAE,GAAG,gLAAA,CAAA,QAAK;AAStB,MAAM,sBAA0D,CAAC,EAC/D,OAAO,EACP,OAAO,EACP,eAAe,EACf,eAAe,EAAE,EAClB;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IAC7E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAClF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuC;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,MAAM,gBAAgB;QACpB,SAAS;QACT,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB,wBAAwB;YACxB;QACF;QAEA,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,SAAS,aAAa;gBACxB,WAAW;gBACX,MAAM,aAAa,MAAM,yHAAA,CAAA,YAAS,CAAC,2BAA2B,CAAC;gBAE/D,IAAI,WAAW,OAAO,EAAE;oBACtB,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,WAAW,IAAI;wBAC3C,iBAAiB;wBACjB,MAAM,sBAAsB,0HAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,WAAW;wBACtF,MAAM,eAAe,SAAS,YAAY,EAAE,IAAI,CAAC,KAC/C,0HAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KACtC,OAAO,YAAY,EAAE;wBAEvB,MAAM,OAAO,sBAAsB;4BAAC;+BAAwB;yBAAa,GAAG,0HAAA,CAAA,aAAU,CAAC,mBAAmB,CAAC;wBAC3G,mBAAmB;wBACnB,eAAe;wBAEf,IAAI,SAAS,UAAU,IAAI,SAAS,UAAU,GAAG,IAAI;4BACnD,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,SAAS,UAAU,CAAC,CAAC,CAAC;wBACpD;oBACF,EAAE,OAAO,YAAY;wBACnB,mBAAmB;wBACnB,MAAM,OAAO,0HAAA,CAAA,aAAU,CAAC,mBAAmB,CAAC;wBAC5C,mBAAmB;wBACnB,eAAe;wBACf,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;oBACf;gBACF,OAAO;oBACL,gBAAgB;oBAChB,MAAM,OAAO,0HAAA,CAAA,aAAU,CAAC,mBAAmB,CAAC;oBAC5C,mBAAmB;oBACnB,eAAe;oBACf,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,WAAW,KAAK,CAAC,OAAO,CAAC;gBACtD;YACF,OAAO,IAAI,SAAS,UAAU;gBAC5B,eAAe;gBACf,MAAM,aAAa,MAAM,yHAAA,CAAA,YAAS,CAAC,sBAAsB,CAAC;gBAE1D,IAAI,WAAW,OAAO,EAAE;oBACtB,IAAI;wBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC,WAAW,IAAI;wBACjD,oBAAoB;wBACpB,eAAe;wBACf,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;oBAClB,EAAE,OAAO,YAAY;wBACnB,mBAAmB;wBACnB,MAAM,iBAAiB,0HAAA,CAAA,aAAU,CAAC,sBAAsB,CAAC;wBACzD,oBAAoB;wBACpB,eAAe;wBACf,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;oBACf;gBACF,OAAO;oBACL,gBAAgB;oBAChB,MAAM,iBAAiB,0HAAA,CAAA,aAAU,CAAC,sBAAsB,CAAC;oBACzD,oBAAoB;oBACpB,eAAe;oBACf,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,WAAW,KAAK,CAAC,OAAO,CAAC;gBACtD;YACF,OAAO,IAAI,SAAS,YAAY;gBAC9B,cAAc;gBACd,MAAM,aAAa,MAAM,yHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,cAAc;gBAElE,IAAI,WAAW,OAAO,EAAE;oBACtB,IAAI;wBACF,MAAM,qBAAqB,KAAK,KAAK,CAAC,WAAW,IAAI;wBACrD,IAAI,mBAAmB,kBAAkB,EAAE;4BACzC,oBAAoB,mBAAmB,kBAAkB;4BACzD,eAAe;4BACf,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,mBAAmB,iBAAiB,IAAI,OAAO;wBACjF,OAAO;4BACL,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;wBACf;oBACF,EAAE,OAAO,YAAY;wBACnB,mBAAmB;wBACnB,MAAM,oBAAoB,0HAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,cAAc;wBACpE,oBAAoB;wBACpB,eAAe;wBACf,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;oBACf;gBACF,OAAO;oBACL,gBAAgB;oBAChB,MAAM,oBAAoB,0HAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,cAAc;oBACpE,oBAAoB;oBACpB,eAAe;oBACf,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,WAAW,KAAK,CAAC,OAAO,CAAC;gBACtD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;YACvB,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,eAAe;IACjB;IAEA,MAAM,cAAc;QAClB,IAAI,kBAAkB;YACpB,gBAAgB;YAChB,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;YACA;QACF;IACF;IAEA,MAAM,aAAa;QACjB,eAAe;QACf,mBAAmB,EAAE;QACrB,oBAAoB;QACpB,KAAK,WAAW;IAClB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC,gLAAA,CAAA,QAAK;QACJ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC;8BAAK;;;;;;;;;;;;QAGV,MAAM;QACN,UAAU;YACR;YACA;QACF;QACA,OAAO;QACP,QAAQ;QACR,cAAc;;0BAEd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,8KAAA,CAAA,OAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,gMAAA,CAAA,QAAK,CAAC,KAAK;4BACV,OAAO;4BACP,UAAU,CAAC;gCACT,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACtB;4BACF;4BACA,WAAU;sCAEV,cAAA,8OAAC,4KAAA,CAAA,MAAG;gCAAC,QAAQ;;kDACX,8OAAC,4KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,8OAAC,gMAAA,CAAA,QAAK,CAAC,MAAM;4CAAC,OAAM;4CAAY,WAAU;sDACxC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;kDAIjD,8OAAC,4KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,8OAAC,gMAAA,CAAA,QAAK,CAAC,MAAM;4CAAC,OAAM;4CAAS,WAAU;sDACrC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,wNAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;kEAC3B,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;kDAIjD,8OAAC,4KAAA,CAAA,MAAG;wCAAC,MAAM;kDACT,cAAA,8OAAC,gMAAA,CAAA,QAAK,CAAC,MAAM;4CAAC,OAAM;4CAAW,WAAU;sDACvC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gOAAA,CAAA,sBAAmB;wDAAC,WAAU;;;;;;kEAC/B,8OAAC;kEAAI;;;;;;kEACL,8OAAC;wDAAK,MAAK;wDAAY,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASvD,8OAAC,gLAAA,CAAA,QAAK;wBAAC,SAAS;wBAAa,MAAK;;0CAChC,8OAAC;gCAAK,OAAM;gCAAO,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;4BACrC,SAAS,6BAAe,8OAAC;gCAAK,OAAM;gCAAO,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;;;;;;0CAC/D,8OAAC;gCAAK,OAAM;gCAAO,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;;;;;;;;;;;;oBAI9C,gBAAgB,mBACf,8OAAC,8KAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,8OAAC,8KAAA,CAAA,OAAI;4BAAC,MAAM;4BAAM,QAAO;;8CACvB,8OAAC,4KAAA,CAAA,MAAG;oCAAC,QAAQ;;sDACX,8OAAC,4KAAA,CAAA,MAAG;4CAAC,MAAM;sDACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAU;iDAAE;0DAE/C,cAAA,8OAAC,kLAAA,CAAA,SAAM;oDAAC,aAAY;;sEAClB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;sDAI3B,8OAAC,4KAAA,CAAA,MAAG;4CAAC,MAAM;sDACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAU;iDAAE;0DAE/C,cAAA,8OAAC,kLAAA,CAAA,SAAM;oDAAC,aAAY;;sEAClB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM7B,8OAAC,4KAAA,CAAA,MAAG;oCAAC,QAAQ;;sDACX,8OAAC,4KAAA,CAAA,MAAG;4CAAC,MAAM;sDACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAU;iDAAE;0DAE/C,cAAA,8OAAC,kLAAA,CAAA,SAAM;oDAAC,aAAY;;sEAClB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;;;;;;;;;;;;;;;;;sDAIzB,8OAAC,4KAAA,CAAA,MAAG;4CAAC,MAAM;sDACT,cAAA,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;gDACR,MAAK;gDACL,OAAM;gDACN,OAAO;oDAAC;wDAAE,UAAU;wDAAM,SAAS;oDAAU;iDAAE;0DAE/C,cAAA,8OAAC,kLAAA,CAAA,SAAM;oDAAC,aAAY;;sEAClB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;sEACnB,8OAAC;4DAAO,OAAM;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM3B,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAW,OAAM;8CAC/B,cAAA,8OAAC,sLAAA,CAAA,WAAQ,CAAC,KAAK;kDACb,cAAA,8OAAC,4KAAA,CAAA,MAAG;;8DACF,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DAAG,cAAA,8OAAC,sLAAA,CAAA,WAAQ;wDAAC,OAAM;kEAAM;;;;;;;;;;;8DACpC,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DAAG,cAAA,8OAAC,sLAAA,CAAA,WAAQ;wDAAC,OAAM;kEAAM;;;;;;;;;;;8DACpC,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DAAG,cAAA,8OAAC,sLAAA,CAAA,WAAQ;wDAAC,OAAM;kEAAK;;;;;;;;;;;8DACnC,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DAAG,cAAA,8OAAC,sLAAA,CAAA,WAAQ;wDAAC,OAAM;kEAAK;;;;;;;;;;;8DACnC,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DAAG,cAAA,8OAAC,sLAAA,CAAA,WAAQ;wDAAC,OAAM;kEAAM;;;;;;;;;;;8DACpC,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DAAG,cAAA,8OAAC,sLAAA,CAAA,WAAQ;wDAAC,OAAM;kEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAK1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,SAAM;4CAAC,MAAK;4CAAU,SAAS;4CAAe,MAAK;4CAAQ,SAAS;;8DACnE,8OAAC,oNAAA,CAAA,gBAAa;;;;;gDACb,UAAU,aAAa;;;;;;;wCAGzB,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,oBACtB,8OAAC,kMAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,wBAAwB;;8DAC7C,8OAAC,wNAAA,CAAA,kBAAe;;;;;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAU9B,gBAAgB,KAAK,SAAS,6BAC7B,8OAAC,8KAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,sBAC9B,8OAAC,8KAAA,CAAA,OAAI;oCAEH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,qBAAqB;oCACpC,qBACE,8OAAC,gLAAA,CAAA,QAAK;wCAAC,OAAO,QAAQ;wCAAG,OAAO;4CAAE,iBAAiB;wCAAU;;;;;;8CAG/D,cAAA,8OAAC,4KAAA,CAAA,MAAG;wCAAC,QAAQ;;0DACX,8OAAC,4KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,8OAAC;;sEACC,8OAAC;4DAAM,OAAO;4DAAG,WAAU;sEAAQ,SAAS,IAAI;;;;;;sEAChD,8OAAC;4DAAU,MAAK;4DAAY,WAAU;sEACnC,SAAS,WAAW;;;;;;sEAEvB,8OAAC,gMAAA,CAAA,QAAK;sEACH,SAAS,IAAI,CAAC,GAAG,CAAC,CAAA,oBACjB,8OAAC,4KAAA,CAAA,MAAG;oEAAW,OAAM;8EAAQ;mEAAnB;;;;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC,4KAAA,CAAA,MAAG;gDAAC,MAAM;gDAAG,WAAU;0DACtB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEACC,cAAA,8OAAC,4KAAA,CAAA,MAAG;gEAAC,OAAO,mBAAmB,SAAS,UAAU;0EAC/C,kBAAkB,SAAS,UAAU;;;;;;;;;;;sEAG1C,8OAAC;sEACC,cAAA,8OAAC;gEAAK,MAAK;;oEACR,SAAS,KAAK,CAAC,MAAM;oEAAC;;;;;;;;;;;;sEAG3B,8OAAC;sEACC,cAAA,8OAAC;gEAAK,MAAK;;oEACR,SAAS,WAAW,CAAC,MAAM;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCApClC,SAAS,EAAE;;;;;;;;;;;;;;;oBAiDzB,gBAAgB,KAAK,kCACpB,8OAAC,8KAAA,CAAA,OAAI;wBAAC,OAAM;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,OAAO;sDAAI,iBAAiB,IAAI;;;;;;sDACvC,8OAAC;sDAAW,iBAAiB,WAAW;;;;;;sDACxC,8OAAC,gMAAA,CAAA,QAAK;;gDACH,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAA,oBACzB,8OAAC,4KAAA,CAAA,MAAG;wDAAW,OAAM;kEAAQ;uDAAnB;;;;;8DAEZ,8OAAC,4KAAA,CAAA,MAAG;oDAAC,OAAO,mBAAmB,iBAAiB,UAAU;;wDAAG;wDACrD,kBAAkB,iBAAiB,UAAU;;;;;;;;;;;;;;;;;;;8CAKzD,8OAAC,oLAAA,CAAA,UAAO;;;;;8CAER,8OAAC;;sDACC,8OAAC;4CAAM,OAAO;sDAAG;;;;;;sDACjB,8OAAC,4KAAA,CAAA,MAAG;4CAAC,QAAQ;;8DACX,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAQ,OAAM;kEACvB,cAAA,8OAAC;4DAAI,WAAU;sEACZ,iBAAiB,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjC,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;sFAAM,KAAK,KAAK;;;;;;sFACjB,8OAAC,4KAAA,CAAA,MAAG;sFAAE,KAAK,IAAI;;;;;;;mEAFP;;;;;;;;;;;;;;;;;;;;8DAQlB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,MAAM;8DACT,cAAA,8OAAC,8KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAQ,OAAM;kEACvB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;4EAAK,MAAM;sFAAE,iBAAiB,KAAK,CAAC,MAAM;;;;;;;;;;;;8EAE7C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;4EAAK,MAAM;sFAAE,iBAAiB,WAAW,CAAC,MAAM;;;;;;;;;;;;8EAEnD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAK;;;;;;sFACN,8OAAC;4EAAK,MAAM;sFACT,iBAAiB,UAAU,KAAK,WAAW,YAC3C,iBAAiB,UAAU,KAAK,WAAW,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAStE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,eAAe,cAAc;sDAAI;;;;;;sDAGxD,8OAAC,kMAAA,CAAA,SAAM;4CAAC,MAAK;4CAAU,SAAS;4CAAa,MAAK;;8DAChD,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnC,8OAAC,+IAAA,CAAA,UAAa;gBACZ,SAAS;gBACT,SAAS,IAAM,wBAAwB;gBACvC,cAAc;oBACZ,wBAAwB;oBACxB,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;;;;;;;;;;;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/workflow/WorkflowRunner.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback } from 'react';\nimport {\n  Modal,\n  Button,\n  Progress,\n  Card,\n  List,\n  Typography,\n  Space,\n  Tag,\n  Alert,\n  Divider,\n  Spin,\n  message\n} from 'antd';\nimport {\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ClockCircleOutlined,\n  RobotOutlined\n} from '@ant-design/icons';\nimport { createWorkflowExecutor } from '@/utils/workflowExecutor';\nimport { aiService } from '@/utils/aiService';\n\nconst { Title, Text, Paragraph } = Typography;\n\ninterface WorkflowRunnerProps {\n  visible: boolean;\n  onClose: () => void;\n  nodes: any[];\n  edges: any[];\n  projectId: string;\n}\n\ninterface NodeStatus {\n  id: string;\n  status: 'pending' | 'running' | 'completed' | 'error';\n  result?: any;\n  error?: string;\n  duration?: number;\n}\n\nconst WorkflowRunner: React.FC<WorkflowRunnerProps> = ({\n  visible,\n  onClose,\n  nodes,\n  edges,\n  projectId\n}) => {\n  const [isRunning, setIsRunning] = useState(false);\n  const [isPaused, setIsPaused] = useState(false);\n  const [nodeStatuses, setNodeStatuses] = useState<Record<string, NodeStatus>>({});\n  const [currentNodeId, setCurrentNodeId] = useState<string | null>(null);\n  const [progress, setProgress] = useState(0);\n  const [executionResults, setExecutionResults] = useState<any>({});\n  const [executionLog, setExecutionLog] = useState<string[]>([]);\n\n  // 初始化节点状态\n  const initializeNodeStatuses = useCallback(() => {\n    const statuses: Record<string, NodeStatus> = {};\n    nodes.forEach(node => {\n      statuses[node.id] = {\n        id: node.id,\n        status: 'pending'\n      };\n    });\n    setNodeStatuses(statuses);\n    setProgress(0);\n    setExecutionResults({});\n    setExecutionLog([]);\n  }, [nodes]);\n\n  // 获取节点执行顺序\n  const getExecutionOrder = useCallback(() => {\n    // 简单的拓扑排序实现\n    const visited = new Set<string>();\n    const order: string[] = [];\n    \n    const visit = (nodeId: string) => {\n      if (visited.has(nodeId)) return;\n      visited.add(nodeId);\n      \n      // 找到所有依赖的节点\n      const dependencies = edges\n        .filter(edge => edge.target === nodeId)\n        .map(edge => edge.source);\n      \n      dependencies.forEach(depId => visit(depId));\n      order.push(nodeId);\n    };\n\n    nodes.forEach(node => visit(node.id));\n    return order;\n  }, [nodes, edges]);\n\n  // 节点进度回调\n  const onNodeProgress = useCallback((nodeId: string, status: string, result?: any) => {\n    setNodeStatuses(prev => ({\n      ...prev,\n      [nodeId]: {\n        ...prev[nodeId],\n        status: status as any,\n        result: status === 'completed' ? result : prev[nodeId].result,\n        error: status === 'error' ? result : undefined\n      }\n    }));\n\n    if (status === 'running') {\n      setCurrentNodeId(nodeId);\n      const node = nodes.find(n => n.id === nodeId);\n      const logMessage = `开始执行: ${node?.data?.label || nodeId}`;\n      setExecutionLog(prev => [...prev, logMessage]);\n    } else if (status === 'completed') {\n      const node = nodes.find(n => n.id === nodeId);\n      const logMessage = `完成执行: ${node?.data?.label || nodeId}`;\n      setExecutionLog(prev => [...prev, logMessage]);\n      \n      // 更新进度\n      const completedCount = Object.values(nodeStatuses).filter(s => s.status === 'completed').length + 1;\n      setProgress((completedCount / nodes.length) * 100);\n    } else if (status === 'error') {\n      const node = nodes.find(n => n.id === nodeId);\n      const logMessage = `执行失败: ${node?.data?.label || nodeId} - ${result}`;\n      setExecutionLog(prev => [...prev, logMessage]);\n    }\n  }, [nodes, nodeStatuses]);\n\n  // 开始执行工作流\n  const handleStartExecution = useCallback(async () => {\n    // 检查AI配置\n    if (!aiService.isConfigured()) {\n      message.warning('请先配置AI API');\n      return;\n    }\n\n    setIsRunning(true);\n    setIsPaused(false);\n    initializeNodeStatuses();\n\n    const executionOrder = getExecutionOrder();\n    const context = {\n      projectId,\n      workflowId: 'default',\n      variables: {},\n      results: {}\n    };\n\n    const executor = createWorkflowExecutor(context, onNodeProgress);\n\n    try {\n      for (const nodeId of executionOrder) {\n        if (isPaused) {\n          message.info('工作流执行已暂停');\n          break;\n        }\n\n        const node = nodes.find(n => n.id === nodeId);\n        if (!node) continue;\n\n        const result = await executor.executeNode(node);\n        \n        if (!result.success) {\n          message.error(`节点执行失败: ${node.data.label}`);\n          break;\n        }\n\n        // 保存执行结果\n        setExecutionResults(prev => ({\n          ...prev,\n          [nodeId]: result.data\n        }));\n      }\n\n      if (!isPaused) {\n        message.success('工作流执行完成！');\n        setProgress(100);\n      }\n\n    } catch (error: any) {\n      message.error(`工作流执行出错: ${error.message}`);\n    } finally {\n      setIsRunning(false);\n      setCurrentNodeId(null);\n    }\n  }, [projectId, nodes, isPaused, initializeNodeStatuses, getExecutionOrder, onNodeProgress]);\n\n  // 暂停执行\n  const handlePauseExecution = useCallback(() => {\n    setIsPaused(true);\n    setIsRunning(false);\n    message.info('工作流执行已暂停');\n  }, []);\n\n  // 停止执行\n  const handleStopExecution = useCallback(() => {\n    setIsRunning(false);\n    setIsPaused(false);\n    setCurrentNodeId(null);\n    message.info('工作流执行已停止');\n  }, []);\n\n  // 获取节点状态图标\n  const getNodeStatusIcon = (status: string) => {\n    switch (status) {\n      case 'running':\n        return <Spin size=\"small\" />;\n      case 'completed':\n        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;\n      case 'error':\n        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;\n      default:\n        return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;\n    }\n  };\n\n  // 获取节点状态颜色\n  const getNodeStatusColor = (status: string) => {\n    switch (status) {\n      case 'running':\n        return 'processing';\n      case 'completed':\n        return 'success';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  return (\n    <Modal\n      title={\n        <div className=\"flex items-center space-x-2\">\n          <RobotOutlined className=\"text-blue-500\" />\n          <span>AI工作流执行器</span>\n        </div>\n      }\n      open={visible}\n      onCancel={onClose}\n      width={800}\n      footer={null}\n      destroyOnClose\n    >\n      <div className=\"space-y-6\">\n        {/* 执行控制 */}\n        <Card size=\"small\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Button\n                type=\"primary\"\n                icon={<PlayCircleOutlined />}\n                onClick={handleStartExecution}\n                disabled={isRunning}\n                loading={isRunning}\n              >\n                {isRunning ? '执行中...' : '开始执行'}\n              </Button>\n              \n              <Button\n                icon={<PauseCircleOutlined />}\n                onClick={handlePauseExecution}\n                disabled={!isRunning}\n              >\n                暂停\n              </Button>\n              \n              <Button\n                icon={<StopOutlined />}\n                onClick={handleStopExecution}\n                disabled={!isRunning && !isPaused}\n              >\n                停止\n              </Button>\n            </div>\n\n            <div className=\"text-right\">\n              <Text type=\"secondary\">\n                节点总数: {nodes.length} | \n                已完成: {Object.values(nodeStatuses).filter(s => s.status === 'completed').length}\n              </Text>\n            </div>\n          </div>\n\n          {/* 进度条 */}\n          <div className=\"mt-4\">\n            <Progress \n              percent={Math.round(progress)} \n              status={isRunning ? 'active' : progress === 100 ? 'success' : 'normal'}\n              showInfo={true}\n            />\n          </div>\n        </Card>\n\n        {/* AI配置提醒 */}\n        {!aiService.isConfigured() && (\n          <Alert\n            message=\"AI API未配置\"\n            description=\"请先在设置中配置AI API，否则无法执行工作流\"\n            type=\"warning\"\n            showIcon\n            closable\n          />\n        )}\n\n        {/* 节点执行状态 */}\n        <Card title=\"节点执行状态\" size=\"small\">\n          <List\n            size=\"small\"\n            dataSource={nodes}\n            renderItem={(node) => {\n              const status = nodeStatuses[node.id];\n              return (\n                <List.Item>\n                  <div className=\"flex items-center justify-between w-full\">\n                    <div className=\"flex items-center space-x-3\">\n                      {getNodeStatusIcon(status?.status || 'pending')}\n                      <span>{node.data.label}</span>\n                      <Tag color={getNodeStatusColor(status?.status || 'pending')}>\n                        {status?.status === 'running' ? '执行中' :\n                         status?.status === 'completed' ? '已完成' :\n                         status?.status === 'error' ? '失败' : '等待中'}\n                      </Tag>\n                    </div>\n                    \n                    {status?.error && (\n                      <Text type=\"danger\" className=\"text-sm\">\n                        {status.error}\n                      </Text>\n                    )}\n                  </div>\n                </List.Item>\n              );\n            }}\n          />\n        </Card>\n\n        {/* 执行日志 */}\n        {executionLog.length > 0 && (\n          <Card title=\"执行日志\" size=\"small\">\n            <div className=\"max-h-40 overflow-y-auto\">\n              {executionLog.map((log, index) => (\n                <div key={index} className=\"text-sm text-gray-600 mb-1\">\n                  {log}\n                </div>\n              ))}\n            </div>\n          </Card>\n        )}\n      </div>\n    </Modal>\n  );\n};\n\nexport default WorkflowRunner;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AA3BA;;;;;;;AA6BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,0LAAA,CAAA,aAAU;AAkB7C,MAAM,iBAAgD,CAAC,EACrD,OAAO,EACP,OAAO,EACP,KAAK,EACL,KAAK,EACL,SAAS,EACV;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAC9E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE7D,UAAU;IACV,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,MAAM,WAAuC,CAAC;QAC9C,MAAM,OAAO,CAAC,CAAA;YACZ,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG;gBAClB,IAAI,KAAK,EAAE;gBACX,QAAQ;YACV;QACF;QACA,gBAAgB;QAChB,YAAY;QACZ,oBAAoB,CAAC;QACrB,gBAAgB,EAAE;IACpB,GAAG;QAAC;KAAM;IAEV,WAAW;IACX,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,YAAY;QACZ,MAAM,UAAU,IAAI;QACpB,MAAM,QAAkB,EAAE;QAE1B,MAAM,QAAQ,CAAC;YACb,IAAI,QAAQ,GAAG,CAAC,SAAS;YACzB,QAAQ,GAAG,CAAC;YAEZ,YAAY;YACZ,MAAM,eAAe,MAClB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAC/B,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;YAE1B,aAAa,OAAO,CAAC,CAAA,QAAS,MAAM;YACpC,MAAM,IAAI,CAAC;QACb;QAEA,MAAM,OAAO,CAAC,CAAA,OAAQ,MAAM,KAAK,EAAE;QACnC,OAAO;IACT,GAAG;QAAC;QAAO;KAAM;IAEjB,SAAS;IACT,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB,QAAgB;QAClE,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,IAAI,CAAC,OAAO;oBACf,QAAQ;oBACR,QAAQ,WAAW,cAAc,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM;oBAC7D,OAAO,WAAW,UAAU,SAAS;gBACvC;YACF,CAAC;QAED,IAAI,WAAW,WAAW;YACxB,iBAAiB;YACjB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtC,MAAM,aAAa,CAAC,MAAM,EAAE,MAAM,MAAM,SAAS,QAAQ;YACzD,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QAC/C,OAAO,IAAI,WAAW,aAAa;YACjC,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtC,MAAM,aAAa,CAAC,MAAM,EAAE,MAAM,MAAM,SAAS,QAAQ;YACzD,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YAE7C,OAAO;YACP,MAAM,iBAAiB,OAAO,MAAM,CAAC,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM,GAAG;YAClG,YAAY,AAAC,iBAAiB,MAAM,MAAM,GAAI;QAChD,OAAO,IAAI,WAAW,SAAS;YAC7B,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtC,MAAM,aAAa,CAAC,MAAM,EAAE,MAAM,MAAM,SAAS,OAAO,GAAG,EAAE,QAAQ;YACrE,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QAC/C;IACF,GAAG;QAAC;QAAO;KAAa;IAExB,UAAU;IACV,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,SAAS;QACT,IAAI,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,IAAI;YAC7B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,aAAa;QACb,YAAY;QACZ;QAEA,MAAM,iBAAiB;QACvB,MAAM,UAAU;YACd;YACA,YAAY;YACZ,WAAW,CAAC;YACZ,SAAS,CAAC;QACZ;QAEA,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE,SAAS;QAEjD,IAAI;YACF,KAAK,MAAM,UAAU,eAAgB;gBACnC,IAAI,UAAU;oBACZ,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;oBACb;gBACF;gBAEA,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACtC,IAAI,CAAC,MAAM;gBAEX,MAAM,SAAS,MAAM,SAAS,WAAW,CAAC;gBAE1C,IAAI,CAAC,OAAO,OAAO,EAAE;oBACnB,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,KAAK,EAAE;oBAC1C;gBACF;gBAEA,SAAS;gBACT,oBAAoB,CAAA,OAAQ,CAAC;wBAC3B,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE,OAAO,IAAI;oBACvB,CAAC;YACH;YAEA,IAAI,CAAC,UAAU;gBACb,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB,YAAY;YACd;QAEF,EAAE,OAAO,OAAY;YACnB,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,MAAM,OAAO,EAAE;QAC3C,SAAU;YACR,aAAa;YACb,iBAAiB;QACnB;IACF,GAAG;QAAC;QAAW;QAAO;QAAU;QAAwB;QAAmB;KAAe;IAE1F,OAAO;IACP,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,YAAY;QACZ,aAAa;QACb,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACf,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,aAAa;QACb,YAAY;QACZ,iBAAiB;QACjB,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;IACf,GAAG,EAAE;IAEL,WAAW;IACX,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,8KAAA,CAAA,OAAI;oBAAC,MAAK;;;;;;YACpB,KAAK;gBACH,qBAAO,8OAAC,gOAAA,CAAA,sBAAmB;oBAAC,OAAO;wBAAE,OAAO;oBAAU;;;;;;YACxD,KAAK;gBACH,qBAAO,8OAAC,4OAAA,CAAA,4BAAyB;oBAAC,OAAO;wBAAE,OAAO;oBAAU;;;;;;YAC9D;gBACE,qBAAO,8OAAC,gOAAA,CAAA,sBAAmB;oBAAC,OAAO;wBAAE,OAAO;oBAAU;;;;;;QAC1D;IACF;IAEA,WAAW;IACX,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,gLAAA,CAAA,QAAK;QACJ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,oNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC;8BAAK;;;;;;;;;;;;QAGV,MAAM;QACN,UAAU;QACV,OAAO;QACP,QAAQ;QACR,cAAc;kBAEd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,8KAAA,CAAA,OAAI;oBAAC,MAAK;;sCACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,SAAM;4CACL,MAAK;4CACL,oBAAM,8OAAC,8NAAA,CAAA,qBAAkB;;;;;4CACzB,SAAS;4CACT,UAAU;4CACV,SAAS;sDAER,YAAY,WAAW;;;;;;sDAG1B,8OAAC,kMAAA,CAAA,SAAM;4CACL,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;4CAC1B,SAAS;4CACT,UAAU,CAAC;sDACZ;;;;;;sDAID,8OAAC,kMAAA,CAAA,SAAM;4CACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;4CACnB,SAAS;4CACT,UAAU,CAAC,aAAa,CAAC;sDAC1B;;;;;;;;;;;;8CAKH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,MAAK;;4CAAY;4CACd,MAAM,MAAM;4CAAC;4CACd,OAAO,MAAM,CAAC,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;sCAMpF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sLAAA,CAAA,WAAQ;gCACP,SAAS,KAAK,KAAK,CAAC;gCACpB,QAAQ,YAAY,WAAW,aAAa,MAAM,YAAY;gCAC9D,UAAU;;;;;;;;;;;;;;;;;gBAMf,CAAC,yHAAA,CAAA,YAAS,CAAC,YAAY,oBACtB,8OAAC,gLAAA,CAAA,QAAK;oBACJ,SAAQ;oBACR,aAAY;oBACZ,MAAK;oBACL,QAAQ;oBACR,QAAQ;;;;;;8BAKZ,8OAAC,8KAAA,CAAA,OAAI;oBAAC,OAAM;oBAAS,MAAK;8BACxB,cAAA,8OAAC,8KAAA,CAAA,OAAI;wBACH,MAAK;wBACL,YAAY;wBACZ,YAAY,CAAC;4BACX,MAAM,SAAS,YAAY,CAAC,KAAK,EAAE,CAAC;4BACpC,qBACE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;0CACR,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,kBAAkB,QAAQ,UAAU;8DACrC,8OAAC;8DAAM,KAAK,IAAI,CAAC,KAAK;;;;;;8DACtB,8OAAC,4KAAA,CAAA,MAAG;oDAAC,OAAO,mBAAmB,QAAQ,UAAU;8DAC9C,QAAQ,WAAW,YAAY,QAC/B,QAAQ,WAAW,cAAc,QACjC,QAAQ,WAAW,UAAU,OAAO;;;;;;;;;;;;wCAIxC,QAAQ,uBACP,8OAAC;4CAAK,MAAK;4CAAS,WAAU;sDAC3B,OAAO,KAAK;;;;;;;;;;;;;;;;;wBAMzB;;;;;;;;;;;gBAKH,aAAa,MAAM,GAAG,mBACrB,8OAAC,8KAAA,CAAA,OAAI;oBAAC,OAAM;oBAAO,MAAK;8BACtB,cAAA,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,KAAK,sBACtB,8OAAC;gCAAgB,WAAU;0CACxB;+BADO;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B;uCAEe", "debugId": null}}, {"offset": {"line": 2164, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/workflow/WorkflowEditor.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState, useMemo } from 'react';\nimport { Card, Button, Space, Typography, Divider, message, Modal, Form, Input, Select } from 'antd';\nimport {\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  SaveOutlined,\n  PlusOutlined,\n  SettingOutlined,\n  DeleteOutlined,\n  LinkOutlined,\n  RobotOutlined\n} from '@ant-design/icons';\nimport {\n  ReactFlow,\n  Background,\n  Controls,\n  MiniMap,\n  useNodesState,\n  useEdgesState,\n  addEdge,\n  Connection,\n  Edge,\n  Node,\n  NodeTypes\n} from '@xyflow/react';\nimport '@xyflow/react/dist/style.css';\nimport { useAppStore } from '@/store';\nimport CustomNode from './CustomNode';\nimport WorkflowAIAssistant from './WorkflowAIAssistant';\nimport WorkflowRunner from './WorkflowRunner';\nimport { WorkflowTemplate } from '@/utils/workflowAI';\n\nconst { Title, Text } = Typography;\n\nconst WorkflowEditor: React.FC = () => {\n  const {\n    currentProject,\n    currentWorkflow,\n    addNode,\n    updateNode,\n    deleteNode,\n    connectNodes,\n    saveWorkflow,\n    executionContexts,\n    startExecution,\n    pauseExecution,\n    stopExecution\n  } = useAppStore();\n\n  const [selectedNode, setSelectedNode] = useState<string | null>(null);\n  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);\n  const [isAIAssistantVisible, setIsAIAssistantVisible] = useState(false);\n  const [isRunnerVisible, setIsRunnerVisible] = useState(false);\n  const [editingNode, setEditingNode] = useState<any>(null);\n  const [form] = Form.useForm();\n\n  const executionContext = currentProject ? executionContexts[currentProject.id] : null;\n\n  // 转换工作流数据为React Flow格式\n  const initialNodes: Node[] = currentWorkflow.map(node => ({\n    id: node.id,\n    type: 'custom',\n    position: node.position,\n    data: {\n      label: node.data.label,\n      type: node.type,\n      status: node.data.status,\n      description: getNodeDescription(node.type),\n      config: node.data.config,\n      onEdit: () => handleEditNode(node),\n      onDelete: () => handleDeleteNode(node.id),\n    },\n  }));\n\n  const initialEdges: Edge[] = currentWorkflow.flatMap(node =>\n    node.connections.map(conn => ({\n      id: `${conn.sourceId}-${conn.targetId}`,\n      source: conn.sourceId,\n      target: conn.targetId,\n      type: 'smoothstep',\n      animated: node.data.status === 'running',\n    }))\n  );\n\n  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);\n  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);\n\n  const reactFlowNodeTypes: NodeTypes = useMemo(() => ({\n    custom: CustomNode,\n  }), []);\n\n  const getNodeDescription = (nodeType: string) => {\n    const descriptions: Record<string, string> = {\n      'input': '用户参数输入',\n      'title-generator': 'AI生成书名候选',\n      'detail-generator': '生成小说基础信息',\n      'character-creator': 'AI生成角色设定',\n      'worldbuilding': 'AI生成世界背景',\n      'plotline-planner': 'AI生成主要故事线',\n      'outline-generator': 'AI生成故事结构',\n      'chapter-count-input': '用户指定章节总数',\n      'detailed-outline': '生成详细情节要点',\n      'chapter-generator': 'AI生成具体章节内容',\n      'content-polisher': 'AI优化文本质量',\n      'consistency-checker': '检查内容一致性',\n      'condition': '条件分支',\n      'loop': '循环执行',\n      'output': '最终结果展示',\n    };\n    return descriptions[nodeType] || '未知节点类型';\n  };\n\n  const handleSaveWorkflow = useCallback(() => {\n    if (currentProject) {\n      saveWorkflow(currentProject.id);\n      message.success('工作流已保存');\n    }\n  }, [currentProject, saveWorkflow]);\n\n  const handleStartExecution = useCallback(() => {\n    if (currentProject && currentWorkflow) {\n      // 检查是否有节点\n      if (!currentWorkflow || currentWorkflow.length === 0) {\n        message.warning('工作流中没有节点，请先添加节点');\n        return;\n      }\n\n      // 打开AI执行器\n      setIsRunnerVisible(true);\n    }\n  }, [currentProject, currentWorkflow]);\n\n  const handlePauseExecution = useCallback(() => {\n    if (currentProject) {\n      pauseExecution(currentProject.id);\n      message.info('工作流已暂停');\n    }\n  }, [currentProject, pauseExecution]);\n\n  const handleStopExecution = useCallback(() => {\n    if (currentProject) {\n      stopExecution(currentProject.id);\n      message.info('工作流已停止');\n    }\n  }, [currentProject, stopExecution]);\n\n  const handleEditNode = useCallback((node: any) => {\n    setEditingNode(node);\n    form.setFieldsValue({\n      label: node.data.label,\n      ...node.data.config,\n    });\n    setIsConfigModalVisible(true);\n  }, [form]);\n\n  const handleDeleteNode = useCallback((nodeId: string) => {\n    deleteNode(nodeId);\n    setNodes(nodes => nodes.filter(n => n.id !== nodeId));\n    setEdges(edges => edges.filter(e => e.source !== nodeId && e.target !== nodeId));\n    message.success('节点已删除');\n  }, [deleteNode, setNodes, setEdges]);\n\n  const handleCreateSampleWorkflow = useCallback(() => {\n    // 清空现有节点和连接\n    setNodes([]);\n    setEdges([]);\n\n    // 创建示例节点\n    const sampleNodes = [\n      {\n        id: 'input-1',\n        type: 'custom',\n        position: { x: 100, y: 100 },\n        data: {\n          label: '用户输入',\n          type: 'input',\n          status: 'idle' as const,\n          description: '输入创作主题和风格',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('input-1'),\n        },\n      },\n      {\n        id: 'title-gen-1',\n        type: 'custom',\n        position: { x: 100, y: 250 },\n        data: {\n          label: '书名生成',\n          type: 'title-generator',\n          status: 'idle' as const,\n          description: 'AI生成书名候选',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('title-gen-1'),\n        },\n      },\n      {\n        id: 'character-1',\n        type: 'custom',\n        position: { x: 350, y: 250 },\n        data: {\n          label: '角色创建',\n          type: 'character-creator',\n          status: 'idle' as const,\n          description: 'AI生成角色设定',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('character-1'),\n        },\n      },\n      {\n        id: 'outline-1',\n        type: 'custom',\n        position: { x: 225, y: 400 },\n        data: {\n          label: '大纲生成',\n          type: 'outline-generator',\n          status: 'idle' as const,\n          description: 'AI生成故事结构',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('outline-1'),\n        },\n      },\n      {\n        id: 'output-1',\n        type: 'custom',\n        position: { x: 225, y: 550 },\n        data: {\n          label: '结果输出',\n          type: 'output',\n          status: 'idle' as const,\n          description: '最终结果展示',\n          onEdit: () => {},\n          onDelete: () => handleDeleteNode('output-1'),\n        },\n      },\n    ];\n\n    // 创建示例连接\n    const sampleEdges: Edge[] = [\n      {\n        id: 'input-1-title-gen-1',\n        source: 'input-1',\n        target: 'title-gen-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'input-1-character-1',\n        source: 'input-1',\n        target: 'character-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'title-gen-1-outline-1',\n        source: 'title-gen-1',\n        target: 'outline-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'character-1-outline-1',\n        source: 'character-1',\n        target: 'outline-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n      {\n        id: 'outline-1-output-1',\n        source: 'outline-1',\n        target: 'output-1',\n        type: 'smoothstep',\n        animated: false,\n        style: { stroke: '#3b82f6', strokeWidth: 2 },\n      },\n    ];\n\n    setNodes(sampleNodes);\n    setEdges(sampleEdges);\n\n    message.success('示例工作流已创建！您可以拖拽节点来连接它们');\n  }, [setNodes, setEdges, handleDeleteNode]);\n\n  // 处理AI工作流应用\n  const handleApplyAIWorkflow = useCallback((template: WorkflowTemplate) => {\n    // 清空现有节点和连接\n    setNodes([]);\n    setEdges([]);\n\n    // 转换模板节点为React Flow格式\n    const reactFlowNodes = template.nodes.map((node, index) => ({\n      id: `node-${index}`,\n      type: 'custom',\n      position: node.position,\n      data: {\n        label: node.label,\n        type: node.type,\n        status: 'idle' as const,\n        description: getNodeDescription(node.type),\n        config: node.config || {},\n        onEdit: () => {},\n        onDelete: () => handleDeleteNode(`node-${index}`),\n      },\n    }));\n\n    // 转换模板连接为React Flow格式\n    const reactFlowEdges = template.connections.map((conn, index) => ({\n      id: `edge-${index}`,\n      source: `node-${conn.source}`,\n      target: `node-${conn.target}`,\n      type: 'smoothstep',\n      animated: false,\n      style: { stroke: '#3b82f6', strokeWidth: 2 },\n    }));\n\n    setNodes(reactFlowNodes);\n    setEdges(reactFlowEdges);\n\n    message.success(`AI工作流\"${template.name}\"已应用成功！`);\n  }, [setNodes, setEdges, handleDeleteNode, getNodeDescription]);\n\n\n\n  const handleAddNode = useCallback((nodeType: string) => {\n    const nodeId = `node-${Date.now()}`;\n    const position = { x: Math.random() * 400, y: Math.random() * 300 };\n\n    // 同时添加到React Flow\n    const reactFlowNode: Node = {\n      id: nodeId,\n      type: 'custom',\n      position,\n      data: {\n        label: `新${nodeType}节点`,\n        type: nodeType,\n        status: 'idle' as const,\n        description: getNodeDescription(nodeType),\n        config: {},\n        onEdit: () => handleEditNode({ id: nodeId, type: nodeType, data: { label: `新${nodeType}节点`, config: {}, status: 'idle' as const } }),\n        onDelete: () => handleDeleteNode(nodeId),\n      },\n    };\n    setNodes(nodes => [...nodes, reactFlowNode]);\n    message.success('节点已添加');\n  }, [setNodes, handleEditNode, handleDeleteNode]);\n\n  const onConnect = useCallback((params: Connection) => {\n    // 检查连接的有效性\n    if (!params.source || !params.target) {\n      message.error('连接失败：源节点或目标节点无效');\n      return;\n    }\n\n    // 防止自连接\n    if (params.source === params.target) {\n      message.warning('不能连接到自身节点');\n      return;\n    }\n\n    // 创建带样式的连接\n    const newEdge = {\n      ...params,\n      type: 'smoothstep',\n      animated: false,\n      style: {\n        stroke: '#3b82f6',\n        strokeWidth: 2\n      },\n    };\n\n    setEdges((eds) => addEdge(newEdge, eds));\n\n    // 同时更新store中的连接信息\n    if (currentProject && params.source && params.target) {\n      connectNodes(params.source, params.target);\n    }\n\n    message.success('节点连接成功');\n  }, [setEdges, connectNodes, currentProject]);\n\n  const nodeTypeOptions = [\n    { key: 'input', label: '输入节点', description: '用户参数输入' },\n    { key: 'title-generator', label: '书名生成', description: 'AI生成书名候选' },\n    { key: 'detail-generator', label: '详情生成', description: '生成小说基础信息' },\n    { key: 'character-creator', label: '角色创建', description: 'AI生成角色设定' },\n    { key: 'worldbuilding', label: '世界观构建', description: 'AI生成世界背景' },\n    { key: 'plotline-planner', label: '主线规划', description: 'AI生成主要故事线' },\n    { key: 'outline-generator', label: '大纲规划', description: 'AI生成故事结构' },\n    { key: 'chapter-count-input', label: '章节数量', description: '用户指定章节总数' },\n    { key: 'detailed-outline', label: '细纲生成', description: '生成详细情节要点' },\n    { key: 'chapter-generator', label: '章节生成', description: 'AI生成具体章节内容' },\n    { key: 'content-polisher', label: '内容润色', description: 'AI优化文本质量' },\n    { key: 'consistency-checker', label: '一致性检查', description: '检查内容一致性' },\n    { key: 'output', label: '输出节点', description: '最终结果展示' },\n  ];\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能编辑工作流。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* 工具栏 */}\n      <div className=\"bg-white border-b border-gray-200 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <Title level={4} className=\"mb-0\">工作流编辑器</Title>\n            <Text type=\"secondary\">项目: {currentProject.name}</Text>\n          </div>\n          \n          <Space>\n            <Button\n              icon={<SaveOutlined />}\n              onClick={handleSaveWorkflow}\n            >\n              保存工作流\n            </Button>\n\n            <Button\n              onClick={handleCreateSampleWorkflow}\n            >\n              创建示例工作流\n            </Button>\n\n            <Button\n              type=\"primary\"\n              icon={<RobotOutlined />}\n              onClick={() => setIsAIAssistantVisible(true)}\n            >\n              AI工作流助手\n            </Button>\n            \n            {executionContext?.status === 'running' ? (\n              <>\n                <Button \n                  icon={<PauseCircleOutlined />} \n                  onClick={handlePauseExecution}\n                >\n                  暂停\n                </Button>\n                <Button \n                  icon={<StopOutlined />} \n                  danger\n                  onClick={handleStopExecution}\n                >\n                  停止\n                </Button>\n              </>\n            ) : (\n              <Button \n                type=\"primary\"\n                icon={<PlayCircleOutlined />} \n                onClick={handleStartExecution}\n                disabled={currentWorkflow.length === 0}\n              >\n                开始执行\n              </Button>\n            )}\n          </Space>\n        </div>\n\n        {/* 执行状态 */}\n        {executionContext && (\n          <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <Text strong>执行状态: {\n                executionContext.status === 'running' ? '运行中' :\n                executionContext.status === 'paused' ? '已暂停' :\n                executionContext.status === 'completed' ? '已完成' :\n                executionContext.status === 'error' ? '错误' : '空闲'\n              }</Text>\n              <Text>进度: {executionContext.progress.percentage.toFixed(1)}%</Text>\n            </div>\n            {executionContext.progress.currentStep && (\n              <Text type=\"secondary\">当前步骤: {executionContext.progress.currentStep}</Text>\n            )}\n          </div>\n        )}\n      </div>\n\n      <div className=\"flex-1 flex\">\n        {/* 节点面板 */}\n        <div className=\"w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto\">\n          <Title level={5}>节点库</Title>\n          <div className=\"space-y-2\">\n            {nodeTypeOptions.map((nodeType) => (\n              <Card \n                key={nodeType.key}\n                size=\"small\" \n                className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                onClick={() => handleAddNode(nodeType.key)}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <Text strong className=\"text-sm\">{nodeType.label}</Text>\n                    <div className=\"text-xs text-gray-500 mt-1\">\n                      {nodeType.description}\n                    </div>\n                  </div>\n                  <PlusOutlined className=\"text-blue-500\" />\n                </div>\n              </Card>\n            ))}\n          </div>\n        </div>\n\n        {/* 工作流画布 */}\n        <div className=\"flex-1 bg-gray-50 relative\">\n          <div className=\"absolute inset-0\">\n            {nodes.length === 0 ? (\n              <div className=\"w-full h-full flex items-center justify-center bg-white\">\n                <div className=\"text-center\">\n                  <Title level={4} type=\"secondary\">工作流画布</Title>\n                  <Text type=\"secondary\">\n                    从左侧节点库中选择节点开始构建您的AI小说生成工作流\n                  </Text>\n                </div>\n              </div>\n            ) : (\n              <ReactFlow\n                nodes={nodes}\n                edges={edges}\n                onNodesChange={onNodesChange}\n                onEdgesChange={onEdgesChange}\n                onConnect={onConnect}\n                nodeTypes={reactFlowNodeTypes}\n                fitView\n                className=\"bg-gray-50\"\n              >\n                <Background />\n                <Controls />\n                <MiniMap\n                  nodeColor={(node) => {\n                    switch (node.data?.status) {\n                      case 'running': return '#3b82f6';\n                      case 'completed': return '#10b981';\n                      case 'error': return '#ef4444';\n                      default: return '#6b7280';\n                    }\n                  }}\n                />\n              </ReactFlow>\n            )}\n          </div>\n        </div>\n\n      </div>\n\n      {/* 节点配置模态框 */}\n      <Modal\n        title=\"节点配置\"\n        open={isConfigModalVisible}\n        onOk={async () => {\n          try {\n            const values = await form.validateFields();\n            if (editingNode) {\n              // 更新节点配置\n              setNodes(nodes =>\n                nodes.map(node =>\n                  node.id === editingNode.id\n                    ? {\n                        ...node,\n                        data: {\n                          ...node.data,\n                          label: values.label,\n                          config: { ...(node.data.config || {}), ...values }\n                        }\n                      }\n                    : node\n                )\n              );\n              message.success('节点配置已更新');\n            }\n            setIsConfigModalVisible(false);\n            form.resetFields();\n          } catch (error) {\n            console.error('配置验证失败:', error);\n          }\n        }}\n        onCancel={() => {\n          setIsConfigModalVisible(false);\n          form.resetFields();\n        }}\n        width={600}\n      >\n        <Form form={form} layout=\"vertical\">\n          <Form.Item\n            name=\"label\"\n            label=\"节点名称\"\n            rules={[{ required: true, message: '请输入节点名称' }]}\n          >\n            <Input placeholder=\"请输入节点名称\" />\n          </Form.Item>\n\n          {editingNode?.type === 'input' && (\n            <>\n              <Form.Item name=\"theme\" label=\"创作主题\">\n                <Input placeholder=\"请输入创作主题\" />\n              </Form.Item>\n              <Form.Item name=\"style\" label=\"写作风格\">\n                <Select placeholder=\"请选择写作风格\">\n                  <Select.Option value=\"轻松幽默\">轻松幽默</Select.Option>\n                  <Select.Option value=\"深沉严肃\">深沉严肃</Select.Option>\n                  <Select.Option value=\"浪漫温馨\">浪漫温馨</Select.Option>\n                </Select>\n              </Form.Item>\n            </>\n          )}\n\n          {editingNode?.type === 'chapter-count-input' && (\n            <Form.Item\n              name=\"chapterCount\"\n              label=\"章节数量\"\n              rules={[{ required: true, message: '请输入章节数量' }]}\n            >\n              <Input type=\"number\" min={1} max={500} placeholder=\"请输入章节数量\" />\n            </Form.Item>\n          )}\n\n          {editingNode?.type === 'title-generator' && (\n            <>\n              <Form.Item name=\"count\" label=\"生成数量\">\n                <Input type=\"number\" min={1} max={20} placeholder=\"生成书名数量\" />\n              </Form.Item>\n              <Form.Item name=\"genre\" label=\"小说类型\">\n                <Select placeholder=\"请选择小说类型\">\n                  <Select.Option value=\"现代都市\">现代都市</Select.Option>\n                  <Select.Option value=\"古代言情\">古代言情</Select.Option>\n                  <Select.Option value=\"玄幻修仙\">玄幻修仙</Select.Option>\n                </Select>\n              </Form.Item>\n            </>\n          )}\n        </Form>\n      </Modal>\n\n      {/* AI工作流助手 */}\n      <WorkflowAIAssistant\n        visible={isAIAssistantVisible}\n        onClose={() => setIsAIAssistantVisible(false)}\n        onApplyWorkflow={handleApplyAIWorkflow}\n        currentNodes={nodes}\n      />\n\n      {/* AI工作流执行器 */}\n      <WorkflowRunner\n        visible={isRunnerVisible}\n        onClose={() => setIsRunnerVisible(false)}\n        nodes={nodes}\n        edges={edges}\n        projectId={currentProject?.id || ''}\n      />\n    </div>\n  );\n};\n\nexport default WorkflowEditor;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAcA;AACA;AACA;AACA;AAhCA;;;;;;;;;;;AAmCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,0LAAA,CAAA,aAAU;AAElC,MAAM,iBAA2B;IAC/B,MAAM,EACJ,cAAc,EACd,eAAe,EACf,OAAO,EACP,UAAU,EACV,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,aAAa,EACd,GAAG,CAAA,GAAA,qHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,KAAK,GAAG,8KAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,mBAAmB,iBAAiB,iBAAiB,CAAC,eAAe,EAAE,CAAC,GAAG;IAEjF,uBAAuB;IACvB,MAAM,eAAuB,gBAAgB,GAAG,CAAC,CAAA,OAAQ,CAAC;YACxD,IAAI,KAAK,EAAE;YACX,MAAM;YACN,UAAU,KAAK,QAAQ;YACvB,MAAM;gBACJ,OAAO,KAAK,IAAI,CAAC,KAAK;gBACtB,MAAM,KAAK,IAAI;gBACf,QAAQ,KAAK,IAAI,CAAC,MAAM;gBACxB,aAAa,mBAAmB,KAAK,IAAI;gBACzC,QAAQ,KAAK,IAAI,CAAC,MAAM;gBACxB,QAAQ,IAAM,eAAe;gBAC7B,UAAU,IAAM,iBAAiB,KAAK,EAAE;YAC1C;QACF,CAAC;IAED,MAAM,eAAuB,gBAAgB,OAAO,CAAC,CAAA,OACnD,KAAK,WAAW,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC5B,IAAI,GAAG,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;gBACvC,QAAQ,KAAK,QAAQ;gBACrB,QAAQ,KAAK,QAAQ;gBACrB,MAAM;gBACN,UAAU,KAAK,IAAI,CAAC,MAAM,KAAK;YACjC,CAAC;IAGH,MAAM,CAAC,OAAO,UAAU,cAAc,GAAG,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,UAAU,cAAc,GAAG,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE;IAEvD,MAAM,qBAAgC,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACnD,QAAQ,4IAAA,CAAA,UAAU;QACpB,CAAC,GAAG,EAAE;IAEN,MAAM,qBAAqB,CAAC;QAC1B,MAAM,eAAuC;YAC3C,SAAS;YACT,mBAAmB;YACnB,oBAAoB;YACpB,qBAAqB;YACrB,iBAAiB;YACjB,oBAAoB;YACpB,qBAAqB;YACrB,uBAAuB;YACvB,oBAAoB;YACpB,qBAAqB;YACrB,oBAAoB;YACpB,uBAAuB;YACvB,aAAa;YACb,QAAQ;YACR,UAAU;QACZ;QACA,OAAO,YAAY,CAAC,SAAS,IAAI;IACnC;IAEA,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,gBAAgB;YAClB,aAAa,eAAe,EAAE;YAC9B,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;IACF,GAAG;QAAC;QAAgB;KAAa;IAEjC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,kBAAkB,iBAAiB;YACrC,UAAU;YACV,IAAI,CAAC,mBAAmB,gBAAgB,MAAM,KAAK,GAAG;gBACpD,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAChB;YACF;YAEA,UAAU;YACV,mBAAmB;QACrB;IACF,GAAG;QAAC;QAAgB;KAAgB;IAEpC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI,gBAAgB;YAClB,eAAe,eAAe,EAAE;YAChC,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf;IACF,GAAG;QAAC;QAAgB;KAAe;IAEnC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,gBAAgB;YAClB,cAAc,eAAe,EAAE;YAC/B,oLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;QACf;IACF,GAAG;QAAC;QAAgB;KAAc;IAElC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,eAAe;QACf,KAAK,cAAc,CAAC;YAClB,OAAO,KAAK,IAAI,CAAC,KAAK;YACtB,GAAG,KAAK,IAAI,CAAC,MAAM;QACrB;QACA,wBAAwB;IAC1B,GAAG;QAAC;KAAK;IAET,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,WAAW;QACX,SAAS,CAAA,QAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,SAAS,CAAA,QAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK;QACxE,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB,GAAG;QAAC;QAAY;QAAU;KAAS;IAEnC,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7C,YAAY;QACZ,SAAS,EAAE;QACX,SAAS,EAAE;QAEX,SAAS;QACT,MAAM,cAAc;YAClB;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,QAAQ;oBACR,aAAa;oBACb,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB;gBACnC;YACF;SACD;QAED,SAAS;QACT,MAAM,cAAsB;YAC1B;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C;SACD;QAED,SAAS;QACT,SAAS;QAET,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB,GAAG;QAAC;QAAU;QAAU;KAAiB;IAEzC,YAAY;IACZ,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,YAAY;QACZ,SAAS,EAAE;QACX,SAAS,EAAE;QAEX,sBAAsB;QACtB,MAAM,iBAAiB,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC1D,IAAI,CAAC,KAAK,EAAE,OAAO;gBACnB,MAAM;gBACN,UAAU,KAAK,QAAQ;gBACvB,MAAM;oBACJ,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ;oBACR,aAAa,mBAAmB,KAAK,IAAI;oBACzC,QAAQ,KAAK,MAAM,IAAI,CAAC;oBACxB,QAAQ,KAAO;oBACf,UAAU,IAAM,iBAAiB,CAAC,KAAK,EAAE,OAAO;gBAClD;YACF,CAAC;QAED,sBAAsB;QACtB,MAAM,iBAAiB,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAChE,IAAI,CAAC,KAAK,EAAE,OAAO;gBACnB,QAAQ,CAAC,KAAK,EAAE,KAAK,MAAM,EAAE;gBAC7B,QAAQ,CAAC,KAAK,EAAE,KAAK,MAAM,EAAE;gBAC7B,MAAM;gBACN,UAAU;gBACV,OAAO;oBAAE,QAAQ;oBAAW,aAAa;gBAAE;YAC7C,CAAC;QAED,SAAS;QACT,SAAS;QAET,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,SAAS,IAAI,CAAC,OAAO,CAAC;IACjD,GAAG;QAAC;QAAU;QAAU;QAAkB;KAAmB;IAI7D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;QACnC,MAAM,WAAW;YAAE,GAAG,KAAK,MAAM,KAAK;YAAK,GAAG,KAAK,MAAM,KAAK;QAAI;QAElE,kBAAkB;QAClB,MAAM,gBAAsB;YAC1B,IAAI;YACJ,MAAM;YACN;YACA,MAAM;gBACJ,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC;gBACvB,MAAM;gBACN,QAAQ;gBACR,aAAa,mBAAmB;gBAChC,QAAQ,CAAC;gBACT,QAAQ,IAAM,eAAe;wBAAE,IAAI;wBAAQ,MAAM;wBAAU,MAAM;4BAAE,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC;4BAAE,QAAQ,CAAC;4BAAG,QAAQ;wBAAgB;oBAAE;gBAClI,UAAU,IAAM,iBAAiB;YACnC;QACF;QACA,SAAS,CAAA,QAAS;mBAAI;gBAAO;aAAc;QAC3C,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB,GAAG;QAAC;QAAU;QAAgB;KAAiB;IAE/C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,WAAW;QACX,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,EAAE;YACpC,oLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YACd;QACF;QAEA,QAAQ;QACR,IAAI,OAAO,MAAM,KAAK,OAAO,MAAM,EAAE;YACnC,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF;QAEA,WAAW;QACX,MAAM,UAAU;YACd,GAAG,MAAM;YACT,MAAM;YACN,UAAU;YACV,OAAO;gBACL,QAAQ;gBACR,aAAa;YACf;QACF;QAEA,SAAS,CAAC,MAAQ,CAAA,GAAA,0JAAA,CAAA,UAAO,AAAD,EAAE,SAAS;QAEnC,kBAAkB;QAClB,IAAI,kBAAkB,OAAO,MAAM,IAAI,OAAO,MAAM,EAAE;YACpD,aAAa,OAAO,MAAM,EAAE,OAAO,MAAM;QAC3C;QAEA,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB,GAAG;QAAC;QAAU;QAAc;KAAe;IAE3C,MAAM,kBAAkB;QACtB;YAAE,KAAK;YAAS,OAAO;YAAQ,aAAa;QAAS;QACrD;YAAE,KAAK;YAAmB,OAAO;YAAQ,aAAa;QAAW;QACjE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAW;QACnE;YAAE,KAAK;YAAiB,OAAO;YAAS,aAAa;QAAW;QAChE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAY;QACnE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAW;QACnE;YAAE,KAAK;YAAuB,OAAO;YAAQ,aAAa;QAAW;QACrE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAqB,OAAO;YAAQ,aAAa;QAAa;QACrE;YAAE,KAAK;YAAoB,OAAO;YAAQ,aAAa;QAAW;QAClE;YAAE,KAAK;YAAuB,OAAO;YAAS,aAAa;QAAU;QACrE;YAAE,KAAK;YAAU,OAAO;YAAQ,aAAa;QAAS;KACvD;IAED,IAAI,CAAC,gBAAgB;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,8OAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,OAAO;wCAAG,WAAU;kDAAO;;;;;;kDAClC,8OAAC;wCAAK,MAAK;;4CAAY;4CAAK,eAAe,IAAI;;;;;;;;;;;;;0CAGjD,8OAAC,gMAAA,CAAA,QAAK;;kDACJ,8OAAC,kMAAA,CAAA,SAAM;wCACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;wCACnB,SAAS;kDACV;;;;;;kDAID,8OAAC,kMAAA,CAAA,SAAM;wCACL,SAAS;kDACV;;;;;;kDAID,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,oNAAA,CAAA,gBAAa;;;;;wCACpB,SAAS,IAAM,wBAAwB;kDACxC;;;;;;oCAIA,kBAAkB,WAAW,0BAC5B;;0DACE,8OAAC,kMAAA,CAAA,SAAM;gDACL,oBAAM,8OAAC,gOAAA,CAAA,sBAAmB;;;;;gDAC1B,SAAS;0DACV;;;;;;0DAGD,8OAAC,kMAAA,CAAA,SAAM;gDACL,oBAAM,8OAAC,kNAAA,CAAA,eAAY;;;;;gDACnB,MAAM;gDACN,SAAS;0DACV;;;;;;;qEAKH,8OAAC,kMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,oBAAM,8OAAC,8NAAA,CAAA,qBAAkB;;;;;wCACzB,SAAS;wCACT,UAAU,gBAAgB,MAAM,KAAK;kDACtC;;;;;;;;;;;;;;;;;;oBAQN,kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,MAAM;;4CAAC;4CACX,iBAAiB,MAAM,KAAK,YAAY,QACxC,iBAAiB,MAAM,KAAK,WAAW,QACvC,iBAAiB,MAAM,KAAK,cAAc,QAC1C,iBAAiB,MAAM,KAAK,UAAU,OAAO;;;;;;;kDAE/C,8OAAC;;4CAAK;4CAAK,iBAAiB,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;4BAE5D,iBAAiB,QAAQ,CAAC,WAAW,kBACpC,8OAAC;gCAAK,MAAK;;oCAAY;oCAAO,iBAAiB,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC,8KAAA,CAAA,OAAI;wCAEH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc,SAAS,GAAG;kDAEzC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,MAAM;4DAAC,WAAU;sEAAW,SAAS,KAAK;;;;;;sEAChD,8OAAC;4DAAI,WAAU;sEACZ,SAAS,WAAW;;;;;;;;;;;;8DAGzB,8OAAC,kNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;uCAZrB,SAAS,GAAG;;;;;;;;;;;;;;;;kCAoBzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,MAAM,MAAM,KAAK,kBAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,OAAO;4CAAG,MAAK;sDAAY;;;;;;sDAClC,8OAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;yFAM3B,8OAAC,yKAAA,CAAA,YAAS;gCACR,OAAO;gCACP,OAAO;gCACP,eAAe;gCACf,eAAe;gCACf,WAAW;gCACX,WAAW;gCACX,OAAO;gCACP,WAAU;;kDAEV,8OAAC,yKAAA,CAAA,aAAU;;;;;kDACX,8OAAC,yKAAA,CAAA,WAAQ;;;;;kDACT,8OAAC,yKAAA,CAAA,UAAO;wCACN,WAAW,CAAC;4CACV,OAAQ,KAAK,IAAI,EAAE;gDACjB,KAAK;oDAAW,OAAO;gDACvB,KAAK;oDAAa,OAAO;gDACzB,KAAK;oDAAS,OAAO;gDACrB;oDAAS,OAAO;4CAClB;wCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUZ,8OAAC,gLAAA,CAAA,QAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,MAAM;oBACJ,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBACxC,IAAI,aAAa;4BACf,SAAS;4BACT,SAAS,CAAA,QACP,MAAM,GAAG,CAAC,CAAA,OACR,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;wCACE,GAAG,IAAI;wCACP,MAAM;4CACJ,GAAG,KAAK,IAAI;4CACZ,OAAO,OAAO,KAAK;4CACnB,QAAQ;gDAAE,GAAI,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC;gDAAG,GAAG,MAAM;4CAAC;wCACnD;oCACF,IACA;4BAGR,oLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;wBAClB;wBACA,wBAAwB;wBACxB,KAAK,WAAW;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B;gBACF;gBACA,UAAU;oBACR,wBAAwB;oBACxB,KAAK,WAAW;gBAClB;gBACA,OAAO;0BAEP,cAAA,8OAAC,8KAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;wBAGpB,aAAa,SAAS,yBACrB;;8CACE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCAAC,aAAY;;;;;;;;;;;8CAErB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;wBAMnC,aAAa,SAAS,uCACrB,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,8OAAC,gLAAA,CAAA,QAAK;gCAAC,MAAK;gCAAS,KAAK;gCAAG,KAAK;gCAAK,aAAY;;;;;;;;;;;wBAItD,aAAa,SAAS,mCACrB;;8CACE,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,8OAAC,gLAAA,CAAA,QAAK;wCAAC,MAAK;wCAAS,KAAK;wCAAG,KAAK;wCAAI,aAAY;;;;;;;;;;;8CAEpD,8OAAC,8KAAA,CAAA,OAAI,CAAC,IAAI;oCAAC,MAAK;oCAAQ,OAAM;8CAC5B,cAAA,8OAAC,kLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;0DAC5B,8OAAC,kLAAA,CAAA,SAAM,CAAC,MAAM;gDAAC,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxC,8OAAC,qJAAA,CAAA,UAAmB;gBAClB,SAAS;gBACT,SAAS,IAAM,wBAAwB;gBACvC,iBAAiB;gBACjB,cAAc;;;;;;0BAIhB,8OAAC,gJAAA,CAAA,UAAc;gBACb,SAAS;gBACT,SAAS,IAAM,mBAAmB;gBAClC,OAAO;gBACP,OAAO;gBACP,WAAW,gBAAgB,MAAM;;;;;;;;;;;;AAIzC;uCAEe", "debugId": null}}]}