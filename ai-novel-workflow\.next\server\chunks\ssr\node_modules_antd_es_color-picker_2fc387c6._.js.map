{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/color.js"], "sourcesContent": ["import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport { Color as RcColor } from '@rc-component/color-picker';\nexport const toHexFormat = (value, alpha) => (value === null || value === void 0 ? void 0 : value.replace(/[^\\w/]/g, '').slice(0, alpha ? 8 : 6)) || '';\nexport const getHex = (value, alpha) => value ? toHexFormat(value, alpha) : '';\nexport let AggregationColor = /*#__PURE__*/function () {\n  function AggregationColor(color) {\n    _classCallCheck(this, AggregationColor);\n    var _a;\n    this.cleared = false;\n    // Clone from another AggregationColor\n    if (color instanceof AggregationColor) {\n      this.metaColor = color.metaColor.clone();\n      this.colors = (_a = color.colors) === null || _a === void 0 ? void 0 : _a.map(info => ({\n        color: new AggregationColor(info.color),\n        percent: info.percent\n      }));\n      this.cleared = color.cleared;\n      return;\n    }\n    const isArray = Array.isArray(color);\n    if (isArray && color.length) {\n      this.colors = color.map(({\n        color: c,\n        percent\n      }) => ({\n        color: new AggregationColor(c),\n        percent\n      }));\n      this.metaColor = new RcColor(this.colors[0].color.metaColor);\n    } else {\n      this.metaColor = new RcColor(isArray ? '' : color);\n    }\n    if (!color || isArray && !this.colors) {\n      this.metaColor = this.metaColor.setA(0);\n      this.cleared = true;\n    }\n  }\n  return _createClass(AggregationColor, [{\n    key: \"toHsb\",\n    value: function toHsb() {\n      return this.metaColor.toHsb();\n    }\n  }, {\n    key: \"toHsbString\",\n    value: function toHsbString() {\n      return this.metaColor.toHsbString();\n    }\n  }, {\n    key: \"toHex\",\n    value: function toHex() {\n      return getHex(this.toHexString(), this.metaColor.a < 1);\n    }\n  }, {\n    key: \"toHexString\",\n    value: function toHexString() {\n      return this.metaColor.toHexString();\n    }\n  }, {\n    key: \"toRgb\",\n    value: function toRgb() {\n      return this.metaColor.toRgb();\n    }\n  }, {\n    key: \"toRgbString\",\n    value: function toRgbString() {\n      return this.metaColor.toRgbString();\n    }\n  }, {\n    key: \"isGradient\",\n    value: function isGradient() {\n      return !!this.colors && !this.cleared;\n    }\n  }, {\n    key: \"getColors\",\n    value: function getColors() {\n      return this.colors || [{\n        color: this,\n        percent: 0\n      }];\n    }\n  }, {\n    key: \"toCssString\",\n    value: function toCssString() {\n      const {\n        colors\n      } = this;\n      // CSS line-gradient\n      if (colors) {\n        const colorsStr = colors.map(c => `${c.color.toRgbString()} ${c.percent}%`).join(', ');\n        return `linear-gradient(90deg, ${colorsStr})`;\n      }\n      return this.metaColor.toRgbString();\n    }\n  }, {\n    key: \"equals\",\n    value: function equals(color) {\n      if (!color || this.isGradient() !== color.isGradient()) {\n        return false;\n      }\n      if (!this.isGradient()) {\n        return this.toHexString() === color.toHexString();\n      }\n      return this.colors.length === color.colors.length && this.colors.every((c, i) => {\n        const target = color.colors[i];\n        return c.percent === target.percent && c.color.equals(target.color);\n      });\n    }\n  }]);\n}();"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;;;;AACO,MAAM,cAAc,CAAC,OAAO,QAAU,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,CAAC,WAAW,IAAI,KAAK,CAAC,GAAG,QAAQ,IAAI,EAAE,KAAK;AAC9I,MAAM,SAAS,CAAC,OAAO,QAAU,QAAQ,YAAY,OAAO,SAAS;AACrE,IAAI,mBAAmB,WAAW,GAAE;IACzC,SAAS,iBAAiB,KAAK;QAC7B,CAAA,GAAA,sKAAA,CAAA,UAAe,AAAD,EAAE,IAAI,EAAE;QACtB,IAAI;QACJ,IAAI,CAAC,OAAO,GAAG;QACf,sCAAsC;QACtC,IAAI,iBAAiB,kBAAkB;YACrC,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS,CAAC,KAAK;YACtC,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACrF,OAAO,IAAI,iBAAiB,KAAK,KAAK;oBACtC,SAAS,KAAK,OAAO;gBACvB,CAAC;YACD,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;YAC5B;QACF;QACA,MAAM,UAAU,MAAM,OAAO,CAAC;QAC9B,IAAI,WAAW,MAAM,MAAM,EAAE;YAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,EACvB,OAAO,CAAC,EACR,OAAO,EACR,GAAK,CAAC;oBACL,OAAO,IAAI,iBAAiB;oBAC5B;gBACF,CAAC;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,mKAAA,CAAA,QAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS;QAC7D,OAAO;YACL,IAAI,CAAC,SAAS,GAAG,IAAI,mKAAA,CAAA,QAAO,CAAC,UAAU,KAAK;QAC9C;QACA,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACrC,IAAI,CAAC,OAAO,GAAG;QACjB;IACF;IACA,OAAO,CAAA,GAAA,mKAAA,CAAA,UAAY,AAAD,EAAE,kBAAkB;QAAC;YACrC,KAAK;YACL,OAAO,SAAS;gBACd,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;YAC7B;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW;YACnC;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,OAAO,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG;YACvD;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW;YACnC;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK;YAC7B;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW;YACnC;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO;YACvC;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,OAAO,IAAI,CAAC,MAAM,IAAI;oBAAC;wBACrB,OAAO,IAAI;wBACX,SAAS;oBACX;iBAAE;YACJ;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS;gBACd,MAAM,EACJ,MAAM,EACP,GAAG,IAAI;gBACR,oBAAoB;gBACpB,IAAI,QAAQ;oBACV,MAAM,YAAY,OAAO,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;oBACjF,OAAO,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;gBAC/C;gBACA,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW;YACnC;QACF;QAAG;YACD,KAAK;YACL,OAAO,SAAS,OAAO,KAAK;gBAC1B,IAAI,CAAC,SAAS,IAAI,CAAC,UAAU,OAAO,MAAM,UAAU,IAAI;oBACtD,OAAO;gBACT;gBACA,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI;oBACtB,OAAO,IAAI,CAAC,WAAW,OAAO,MAAM,WAAW;gBACjD;gBACA,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG;oBACzE,MAAM,SAAS,MAAM,MAAM,CAAC,EAAE;oBAC9B,OAAO,EAAE,OAAO,KAAK,OAAO,OAAO,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,KAAK;gBACpE;YACF;QACF;KAAE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/util.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { Color as RcColor } from '@rc-component/color-picker';\nimport { AggregationColor } from './color';\nexport const generateColor = color => {\n  if (color instanceof AggregationColor) {\n    return color;\n  }\n  return new AggregationColor(color);\n};\nexport const getRoundNumber = value => Math.round(Number(value || 0));\nexport const getColorAlpha = color => getRoundNumber(color.toHsb().a * 100);\n/** Return the color whose `alpha` is 1 */\nexport const genAlphaColor = (color, alpha) => {\n  const rgba = color.toRgb();\n  // Color from hsb input may get `rgb` is (0/0/0) when `hsb.b` is 0\n  // So if rgb is empty, we should get from hsb\n  if (!rgba.r && !rgba.g && !rgba.b) {\n    const hsba = color.toHsb();\n    hsba.a = alpha || 1;\n    return generateColor(hsba);\n  }\n  rgba.a = alpha || 1;\n  return generateColor(rgba);\n};\n/**\n * Get percent position color. e.g. [10%-#fff, 20%-#000], 15% => #888\n */\nexport const getGradientPercentColor = (colors, percent) => {\n  const filledColors = [{\n    percent: 0,\n    color: colors[0].color\n  }].concat(_toConsumableArray(colors), [{\n    percent: 100,\n    color: colors[colors.length - 1].color\n  }]);\n  for (let i = 0; i < filledColors.length - 1; i += 1) {\n    const startPtg = filledColors[i].percent;\n    const endPtg = filledColors[i + 1].percent;\n    const startColor = filledColors[i].color;\n    const endColor = filledColors[i + 1].color;\n    if (startPtg <= percent && percent <= endPtg) {\n      const dist = endPtg - startPtg;\n      if (dist === 0) {\n        return startColor;\n      }\n      const ratio = (percent - startPtg) / dist * 100;\n      const startRcColor = new RcColor(startColor);\n      const endRcColor = new RcColor(endColor);\n      return startRcColor.mix(endRcColor, ratio).toRgbString();\n    }\n  }\n  // This will never reach\n  /* istanbul ignore next */\n  return '';\n};"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;AACA;;;;AACO,MAAM,gBAAgB,CAAA;IAC3B,IAAI,iBAAiB,sJAAA,CAAA,mBAAgB,EAAE;QACrC,OAAO;IACT;IACA,OAAO,IAAI,sJAAA,CAAA,mBAAgB,CAAC;AAC9B;AACO,MAAM,iBAAiB,CAAA,QAAS,KAAK,KAAK,CAAC,OAAO,SAAS;AAC3D,MAAM,gBAAgB,CAAA,QAAS,eAAe,MAAM,KAAK,GAAG,CAAC,GAAG;AAEhE,MAAM,gBAAgB,CAAC,OAAO;IACnC,MAAM,OAAO,MAAM,KAAK;IACxB,kEAAkE;IAClE,6CAA6C;IAC7C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACjC,MAAM,OAAO,MAAM,KAAK;QACxB,KAAK,CAAC,GAAG,SAAS;QAClB,OAAO,cAAc;IACvB;IACA,KAAK,CAAC,GAAG,SAAS;IAClB,OAAO,cAAc;AACvB;AAIO,MAAM,0BAA0B,CAAC,QAAQ;IAC9C,MAAM,eAAe;QAAC;YACpB,SAAS;YACT,OAAO,MAAM,CAAC,EAAE,CAAC,KAAK;QACxB;KAAE,CAAC,MAAM,CAAC,CAAA,GAAA,yKAAA,CAAA,UAAkB,AAAD,EAAE,SAAS;QAAC;YACrC,SAAS;YACT,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,KAAK;QACxC;KAAE;IACF,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,GAAG,GAAG,KAAK,EAAG;QACnD,MAAM,WAAW,YAAY,CAAC,EAAE,CAAC,OAAO;QACxC,MAAM,SAAS,YAAY,CAAC,IAAI,EAAE,CAAC,OAAO;QAC1C,MAAM,aAAa,YAAY,CAAC,EAAE,CAAC,KAAK;QACxC,MAAM,WAAW,YAAY,CAAC,IAAI,EAAE,CAAC,KAAK;QAC1C,IAAI,YAAY,WAAW,WAAW,QAAQ;YAC5C,MAAM,OAAO,SAAS;YACtB,IAAI,SAAS,GAAG;gBACd,OAAO;YACT;YACA,MAAM,QAAQ,CAAC,UAAU,QAAQ,IAAI,OAAO;YAC5C,MAAM,eAAe,IAAI,mKAAA,CAAA,QAAO,CAAC;YACjC,MAAM,aAAa,IAAI,mKAAA,CAAA,QAAO,CAAC;YAC/B,OAAO,aAAa,GAAG,CAAC,YAAY,OAAO,WAAW;QACxD;IACF;IACA,wBAAwB;IACxB,wBAAwB,GACxB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorPresets.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useMemo } from 'react';\nimport { ColorBlock, Color as RcColor } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Collapse from '../../collapse';\nimport { useLocale } from '../../locale';\nimport { useToken } from '../../theme/internal';\nimport { generateColor } from '../util';\nconst genPresetColor = list => list.map(value => {\n  value.colors = value.colors.map(generateColor);\n  return value;\n});\nexport const isBright = (value, bgColorToken) => {\n  const {\n    r,\n    g,\n    b,\n    a\n  } = value.toRgb();\n  const hsv = new RcColor(value.toRgbString()).onBackground(bgColorToken).toHsv();\n  if (a <= 0.5) {\n    // Adapted to dark mode\n    return hsv.v > 0.5;\n  }\n  return r * 0.299 + g * 0.587 + b * 0.114 > 192;\n};\nconst genCollapsePanelKey = (preset, index) => {\n  var _a;\n  const mergedKey = (_a = preset.key) !== null && _a !== void 0 ? _a : index;\n  return `panel-${mergedKey}`;\n};\nconst ColorPresets = ({\n  prefixCls,\n  presets,\n  value: color,\n  onChange\n}) => {\n  const [locale] = useLocale('ColorPicker');\n  const [, token] = useToken();\n  const [presetsValue] = useMergedState(genPresetColor(presets), {\n    value: genPresetColor(presets),\n    postState: genPresetColor\n  });\n  const colorPresetsPrefixCls = `${prefixCls}-presets`;\n  const activeKeys = useMemo(() => presetsValue.reduce((acc, preset, index) => {\n    const {\n      defaultOpen = true\n    } = preset;\n    if (defaultOpen) {\n      acc.push(genCollapsePanelKey(preset, index));\n    }\n    return acc;\n  }, []), [presetsValue]);\n  const handleClick = colorValue => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(colorValue);\n  };\n  const items = presetsValue.map((preset, index) => {\n    var _a;\n    return {\n      key: genCollapsePanelKey(preset, index),\n      label: /*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-label`\n      }, preset === null || preset === void 0 ? void 0 : preset.label),\n      children: (/*#__PURE__*/React.createElement(\"div\", {\n        className: `${colorPresetsPrefixCls}-items`\n      }, Array.isArray(preset === null || preset === void 0 ? void 0 : preset.colors) && ((_a = preset.colors) === null || _a === void 0 ? void 0 : _a.length) > 0 ? preset.colors.map((presetColor, index) => (/*#__PURE__*/React.createElement(ColorBlock\n      // eslint-disable-next-line react/no-array-index-key\n      , {\n        // eslint-disable-next-line react/no-array-index-key\n        key: `preset-${index}-${presetColor.toHexString()}`,\n        color: generateColor(presetColor).toRgbString(),\n        prefixCls: prefixCls,\n        className: classNames(`${colorPresetsPrefixCls}-color`, {\n          [`${colorPresetsPrefixCls}-color-checked`]: presetColor.toHexString() === (color === null || color === void 0 ? void 0 : color.toHexString()),\n          [`${colorPresetsPrefixCls}-color-bright`]: isBright(presetColor, token.colorBgElevated)\n        }),\n        onClick: () => handleClick(presetColor)\n      }))) : (/*#__PURE__*/React.createElement(\"span\", {\n        className: `${colorPresetsPrefixCls}-empty`\n      }, locale.presetEmpty))))\n    };\n  });\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorPresetsPrefixCls\n  }, /*#__PURE__*/React.createElement(Collapse, {\n    defaultActiveKey: activeKeys,\n    ghost: true,\n    items: items\n  }));\n};\nexport default ColorPresets;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAUA,MAAM,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;QACtC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,qJAAA,CAAA,gBAAa;QAC7C,OAAO;IACT;AACO,MAAM,WAAW,CAAC,OAAO;IAC9B,MAAM,EACJ,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACF,GAAG,MAAM,KAAK;IACf,MAAM,MAAM,IAAI,mKAAA,CAAA,QAAO,CAAC,MAAM,WAAW,IAAI,YAAY,CAAC,cAAc,KAAK;IAC7E,IAAI,KAAK,KAAK;QACZ,uBAAuB;QACvB,OAAO,IAAI,CAAC,GAAG;IACjB;IACA,OAAO,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ;AAC7C;AACA,MAAM,sBAAsB,CAAC,QAAQ;IACnC,IAAI;IACJ,MAAM,YAAY,CAAC,KAAK,OAAO,GAAG,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACrE,OAAO,CAAC,MAAM,EAAE,WAAW;AAC7B;AACA,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,OAAO,EACP,OAAO,KAAK,EACZ,QAAQ,EACT;IACC,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;IAC3B,MAAM,GAAG,MAAM,GAAG,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAc,AAAD,EAAE,eAAe,UAAU;QAC7D,OAAO,eAAe;QACtB,WAAW;IACb;IACA,MAAM,wBAAwB,GAAG,UAAU,QAAQ,CAAC;IACpD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,aAAa,MAAM,CAAC,CAAC,KAAK,QAAQ;YACjE,MAAM,EACJ,cAAc,IAAI,EACnB,GAAG;YACJ,IAAI,aAAa;gBACf,IAAI,IAAI,CAAC,oBAAoB,QAAQ;YACvC;YACA,OAAO;QACT,GAAG,EAAE,GAAG;QAAC;KAAa;IACtB,MAAM,cAAc,CAAA;QAClB,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS;IAC/D;IACA,MAAM,QAAQ,aAAa,GAAG,CAAC,CAAC,QAAQ;QACtC,IAAI;QACJ,OAAO;YACL,KAAK,oBAAoB,QAAQ;YACjC,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;gBAC7C,WAAW,GAAG,sBAAsB,MAAM,CAAC;YAC7C,GAAG,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK;YAC/D,UAAW,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;gBACjD,WAAW,GAAG,sBAAsB,MAAM,CAAC;YAC7C,GAAG,MAAM,OAAO,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,MAAM,KAAK,CAAC,CAAC,KAAK,OAAO,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,IAAI,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,aAAa,QAAW,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,+NAAA,CAAA,aAAU,EAEnP;oBACA,oDAAoD;oBACpD,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,YAAY,WAAW,IAAI;oBACnD,OAAO,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,WAAW;oBAC7C,WAAW;oBACX,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,GAAG,sBAAsB,MAAM,CAAC,EAAE;wBACtD,CAAC,GAAG,sBAAsB,cAAc,CAAC,CAAC,EAAE,YAAY,WAAW,OAAO,CAAC,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,WAAW,EAAE;wBAC5I,CAAC,GAAG,sBAAsB,aAAa,CAAC,CAAC,EAAE,SAAS,aAAa,MAAM,eAAe;oBACxF;oBACA,SAAS,IAAM,YAAY;gBAC7B,MAAQ,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;gBAC/C,WAAW,GAAG,sBAAsB,MAAM,CAAC;YAC7C,GAAG,OAAO,WAAW;QACvB;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC7C,WAAW;IACb,GAAG,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,+IAAA,CAAA,UAAQ,EAAE;QAC5C,kBAAkB;QAClB,OAAO;QACP,OAAO;IACT;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/context.js"], "sourcesContent": ["import React from 'react';\nexport const PanelPickerContext = /*#__PURE__*/React.createContext({});\nexport const PanelPresetsContext = /*#__PURE__*/React.createContext({});"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,qBAAqB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,CAAC;AAC7D,MAAM,sBAAsB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorClear.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { generateColor } from '../util';\nconst ColorClear = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const handleClick = () => {\n    if (onChange && value && !value.cleared) {\n      const hsba = value.toHsb();\n      hsba.a = 0;\n      const genColor = generateColor(hsba);\n      genColor.cleared = true;\n      onChange(genColor);\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-clear`,\n    onClick: handleClick\n  });\n};\nexport default ColorClear;"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAIA,MAAM,aAAa,CAAC,EAClB,SAAS,EACT,KAAK,EACL,QAAQ,EACT;IACC,MAAM,cAAc;QAClB,IAAI,YAAY,SAAS,CAAC,MAAM,OAAO,EAAE;YACvC,MAAM,OAAO,MAAM,KAAK;YACxB,KAAK,CAAC,GAAG;YACT,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;YAC/B,SAAS,OAAO,GAAG;YACnB,SAAS;QACX;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC7C,WAAW,GAAG,UAAU,MAAM,CAAC;QAC/B,SAAS;IACX;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/interface.js"], "sourcesContent": ["export const FORMAT_HEX = 'hex';\nexport const FORMAT_RGB = 'rgb';\nexport const FORMAT_HSB = 'hsb';"], "names": [], "mappings": ";;;;;AAAO,MAAM,aAAa;AACnB,MAAM,aAAa;AACnB,MAAM,aAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorSteppers.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport classNames from 'classnames';\nimport InputNumber from '../../input-number';\nconst ColorSteppers = ({\n  prefixCls,\n  min = 0,\n  max = 100,\n  value,\n  onChange,\n  className,\n  formatter\n}) => {\n  const colorSteppersPrefixCls = `${prefixCls}-steppers`;\n  const [internalValue, setInternalValue] = useState(0);\n  const stepValue = !Number.isNaN(value) ? value : internalValue;\n  return /*#__PURE__*/React.createElement(InputNumber, {\n    className: classNames(colorSteppersPrefixCls, className),\n    min: min,\n    max: max,\n    value: stepValue,\n    formatter: formatter,\n    size: \"small\",\n    onChange: step => {\n      setInternalValue(step || 0);\n      onChange === null || onChange === void 0 ? void 0 : onChange(step);\n    }\n  });\n};\nexport default ColorSteppers;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAKA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACT,MAAM,CAAC,EACP,MAAM,GAAG,EACT,KAAK,EACL,QAAQ,EACR,SAAS,EACT,SAAS,EACV;IACC,MAAM,yBAAyB,GAAG,UAAU,SAAS,CAAC;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,YAAY,CAAC,OAAO,KAAK,CAAC,SAAS,QAAQ;IACjD,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,sJAAA,CAAA,UAAW,EAAE;QACnD,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,wBAAwB;QAC9C,KAAK;QACL,KAAK;QACL,OAAO;QACP,WAAW;QACX,MAAM;QACN,UAAU,CAAA;YACR,iBAAiB,QAAQ;YACzB,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS;QAC/D;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorAlphaInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getColorAlpha } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorAlphaInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorAlphaInputPrefixCls = `${prefixCls}-alpha-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const alphaValue = value || internalValue;\n  const handleAlphaChange = step => {\n    const hsba = alphaValue.toHsb();\n    hsba.a = (step || 0) / 100;\n    const genColor = generateColor(hsba);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(ColorSteppers, {\n    value: getColorAlpha(alphaValue),\n    prefixCls: prefixCls,\n    formatter: step => `${step}%`,\n    className: colorAlphaInputPrefixCls,\n    onChange: handleAlphaChange\n  });\n};\nexport default ColorAlphaInput;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAKA,MAAM,kBAAkB,CAAC,EACvB,SAAS,EACT,KAAK,EACL,QAAQ,EACT;IACC,MAAM,2BAA2B,GAAG,UAAU,YAAY,CAAC;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;IAChF,MAAM,aAAa,SAAS;IAC5B,MAAM,oBAAoB,CAAA;QACxB,MAAM,OAAO,WAAW,KAAK;QAC7B,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI;QACvB,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;QAC/B,iBAAiB;QACjB,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS;IAC/D;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE;QACrD,OAAO,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;QACrB,WAAW;QACX,WAAW,CAAA,OAAQ,GAAG,KAAK,CAAC,CAAC;QAC7B,WAAW;QACX,UAAU;IACZ;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorHexInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useState } from 'react';\nimport Input from '../../input/Input';\nimport { toHexFormat } from '../color';\nimport { generateColor } from '../util';\nconst hexReg = /(^#[\\da-f]{6}$)|(^#[\\da-f]{8}$)/i;\nconst isHexString = hex => hexReg.test(`#${hex}`);\nconst ColorHexInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorHexInputPrefixCls = `${prefixCls}-hex-input`;\n  const [hexValue, setHexValue] = useState(() => value ? toHexFormat(value.toHexString()) : undefined);\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setHexValue(toHexFormat(value.toHexString()));\n    }\n  }, [value]);\n  const handleHexChange = e => {\n    const originValue = e.target.value;\n    setHexValue(toHexFormat(originValue));\n    if (isHexString(toHexFormat(originValue, true))) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(generateColor(originValue));\n    }\n  };\n  return /*#__PURE__*/React.createElement(Input, {\n    className: colorHexInputPrefixCls,\n    value: hexValue,\n    prefix: \"#\",\n    onChange: handleHexChange,\n    size: \"small\"\n  });\n};\nexport default ColorHexInput;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;AAMA,MAAM,SAAS;AACf,MAAM,cAAc,CAAA,MAAO,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK;AAChD,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACT,KAAK,EACL,QAAQ,EACT;IACC,MAAM,yBAAyB,GAAG,UAAU,UAAU,CAAC;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,QAAQ,CAAA,GAAA,sJAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW,MAAM;IAC1F,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,YAAY,CAAA,GAAA,sJAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW;QAC3C;IACF,GAAG;QAAC;KAAM;IACV,MAAM,kBAAkB,CAAA;QACtB,MAAM,cAAc,EAAE,MAAM,CAAC,KAAK;QAClC,YAAY,CAAA,GAAA,sJAAA,CAAA,cAAW,AAAD,EAAE;QACxB,IAAI,YAAY,CAAA,GAAA,sJAAA,CAAA,cAAW,AAAD,EAAE,aAAa,QAAQ;YAC/C,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;QAC7E;IACF;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4JAAA,CAAA,UAAK,EAAE;QAC7C,WAAW;QACX,OAAO;QACP,QAAQ;QACR,UAAU;QACV,MAAM;IACR;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorHsbInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor, getRoundNumber } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorHsbInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorHsbInputPrefixCls = `${prefixCls}-hsb-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const hsbValue = value || internalValue;\n  const handleHsbChange = (step, type) => {\n    const hsb = hsbValue.toHsb();\n    hsb[type] = type === 'h' ? step : (step || 0) / 100;\n    const genColor = generateColor(hsb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorHsbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 360,\n    min: 0,\n    value: Number(hsbValue.toHsb().h),\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => getRoundNumber(step || 0).toString(),\n    onChange: step => handleHsbChange(Number(step), 'h')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().s) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 's')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 100,\n    min: 0,\n    value: Number(hsbValue.toHsb().b) * 100,\n    prefixCls: prefixCls,\n    className: colorHsbInputPrefixCls,\n    formatter: step => `${getRoundNumber(step || 0)}%`,\n    onChange: step => handleHsbChange(Number(step), 'b')\n  }));\n};\nexport default ColorHsbInput;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAKA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACT,KAAK,EACL,QAAQ,EACT;IACC,MAAM,yBAAyB,GAAG,UAAU,UAAU,CAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;IAChF,MAAM,WAAW,SAAS;IAC1B,MAAM,kBAAkB,CAAC,MAAM;QAC7B,MAAM,MAAM,SAAS,KAAK;QAC1B,GAAG,CAAC,KAAK,GAAG,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,IAAI;QAChD,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;QAC/B,iBAAiB;QACjB,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS;IAC/D;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC7C,WAAW;IACb,GAAG,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE;QACjD,KAAK;QACL,KAAK;QACL,OAAO,OAAO,SAAS,KAAK,GAAG,CAAC;QAChC,WAAW;QACX,WAAW;QACX,WAAW,CAAA,OAAQ,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,QAAQ;QACrD,UAAU,CAAA,OAAQ,gBAAgB,OAAO,OAAO;IAClD,IAAI,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE;QAClD,KAAK;QACL,KAAK;QACL,OAAO,OAAO,SAAS,KAAK,GAAG,CAAC,IAAI;QACpC,WAAW;QACX,WAAW;QACX,WAAW,CAAA,OAAQ,GAAG,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,CAAC,CAAC;QAClD,UAAU,CAAA,OAAQ,gBAAgB,OAAO,OAAO;IAClD,IAAI,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE;QAClD,KAAK;QACL,KAAK;QACL,OAAO,OAAO,SAAS,KAAK,GAAG,CAAC,IAAI;QACpC,WAAW;QACX,WAAW;QACX,WAAW,CAAA,OAAQ,GAAG,CAAA,GAAA,qJAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,CAAC,CAAC;QAClD,UAAU,CAAA,OAAQ,gBAAgB,OAAO,OAAO;IAClD;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorRgbInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { generateColor } from '../util';\nimport ColorSteppers from './ColorSteppers';\nconst ColorRgbInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorRgbInputPrefixCls = `${prefixCls}-rgb-input`;\n  const [internalValue, setInternalValue] = useState(() => generateColor(value || '#000'));\n  const rgbValue = value || internalValue;\n  const handleRgbChange = (step, type) => {\n    const rgb = rgbValue.toRgb();\n    rgb[type] = step || 0;\n    const genColor = generateColor(rgb);\n    setInternalValue(genColor);\n    onChange === null || onChange === void 0 ? void 0 : onChange(genColor);\n  };\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: colorRgbInputPrefixCls\n  }, /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().r),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'r')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().g),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'g')\n  }), /*#__PURE__*/React.createElement(ColorSteppers, {\n    max: 255,\n    min: 0,\n    value: Number(rgbValue.toRgb().b),\n    prefixCls: prefixCls,\n    className: colorRgbInputPrefixCls,\n    onChange: step => handleRgbChange(Number(step), 'b')\n  }));\n};\nexport default ColorRgbInput;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAKA,MAAM,gBAAgB,CAAC,EACrB,SAAS,EACT,KAAK,EACL,QAAQ,EACT;IACC,MAAM,yBAAyB,GAAG,UAAU,UAAU,CAAC;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;IAChF,MAAM,WAAW,SAAS;IAC1B,MAAM,kBAAkB,CAAC,MAAM;QAC7B,MAAM,MAAM,SAAS,KAAK;QAC1B,GAAG,CAAC,KAAK,GAAG,QAAQ;QACpB,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;QAC/B,iBAAiB;QACjB,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS;IAC/D;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC7C,WAAW;IACb,GAAG,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE;QACjD,KAAK;QACL,KAAK;QACL,OAAO,OAAO,SAAS,KAAK,GAAG,CAAC;QAChC,WAAW;QACX,WAAW;QACX,UAAU,CAAA,OAAQ,gBAAgB,OAAO,OAAO;IAClD,IAAI,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE;QAClD,KAAK;QACL,KAAK;QACL,OAAO,OAAO,SAAS,KAAK,GAAG,CAAC;QAChC,WAAW;QACX,WAAW;QACX,UAAU,CAAA,OAAQ,gBAAgB,OAAO,OAAO;IAClD,IAAI,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE;QAClD,KAAK;QACL,KAAK;QACL,OAAO,OAAO,SAAS,KAAK,GAAG,CAAC;QAChC,WAAW;QACX,WAAW;QACX,UAAU,CAAA,OAAQ,gBAAgB,OAAO,OAAO;IAClD;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useMemo } from 'react';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport Select from '../../select';\nimport { FORMAT_HEX, FORMAT_HSB, FORMAT_RGB } from '../interface';\nimport ColorAlphaInput from './ColorAlphaInput';\nimport ColorHexInput from './ColorHexInput';\nimport ColorHsbInput from './ColorHsbInput';\nimport ColorRgbInput from './ColorRgbInput';\nconst selectOptions = [FORMAT_HEX, FORMAT_HSB, FORMAT_RGB].map(format => ({\n  value: format,\n  label: format.toUpperCase()\n}));\nconst ColorInput = props => {\n  const {\n    prefixCls,\n    format,\n    value,\n    disabledAlpha,\n    onFormatChange,\n    onChange,\n    disabledFormat\n  } = props;\n  const [colorFormat, setColorFormat] = useMergedState(FORMAT_HEX, {\n    value: format,\n    onChange: onFormatChange\n  });\n  const colorInputPrefixCls = `${prefixCls}-input`;\n  const handleFormatChange = newFormat => {\n    setColorFormat(newFormat);\n  };\n  const steppersNode = useMemo(() => {\n    const inputProps = {\n      value,\n      prefixCls,\n      onChange\n    };\n    switch (colorFormat) {\n      case FORMAT_HSB:\n        return /*#__PURE__*/React.createElement(ColorHsbInput, Object.assign({}, inputProps));\n      case FORMAT_RGB:\n        return /*#__PURE__*/React.createElement(ColorRgbInput, Object.assign({}, inputProps));\n      // case FORMAT_HEX:\n      default:\n        return /*#__PURE__*/React.createElement(ColorHexInput, Object.assign({}, inputProps));\n    }\n  }, [colorFormat, prefixCls, value, onChange]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: `${colorInputPrefixCls}-container`\n  }, !disabledFormat && (/*#__PURE__*/React.createElement(Select, {\n    value: colorFormat,\n    variant: \"borderless\",\n    getPopupContainer: current => current,\n    popupMatchSelectWidth: 68,\n    placement: \"bottomRight\",\n    onChange: handleFormatChange,\n    className: `${prefixCls}-format-select`,\n    size: \"small\",\n    options: selectOptions\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: colorInputPrefixCls\n  }, steppersNode), !disabledAlpha && (/*#__PURE__*/React.createElement(ColorAlphaInput, {\n    prefixCls: prefixCls,\n    value: value,\n    onChange: onChange\n  })));\n};\nexport default ColorInput;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAUA,MAAM,gBAAgB;IAAC,0JAAA,CAAA,aAAU;IAAE,0JAAA,CAAA,aAAU;IAAE,0JAAA,CAAA,aAAU;CAAC,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;QACxE,OAAO;QACP,OAAO,OAAO,WAAW;IAC3B,CAAC;AACD,MAAM,aAAa,CAAA;IACjB,MAAM,EACJ,SAAS,EACT,MAAM,EACN,KAAK,EACL,aAAa,EACb,cAAc,EACd,QAAQ,EACR,cAAc,EACf,GAAG;IACJ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAc,AAAD,EAAE,0JAAA,CAAA,aAAU,EAAE;QAC/D,OAAO;QACP,UAAU;IACZ;IACA,MAAM,sBAAsB,GAAG,UAAU,MAAM,CAAC;IAChD,MAAM,qBAAqB,CAAA;QACzB,eAAe;IACjB;IACA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,MAAM,aAAa;YACjB;YACA;YACA;QACF;QACA,OAAQ;YACN,KAAK,0JAAA,CAAA,aAAU;gBACb,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG;YAC3E,KAAK,0JAAA,CAAA,aAAU;gBACb,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG;YAC3E,mBAAmB;YACnB;gBACE,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,4KAAA,CAAA,UAAa,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG;QAC7E;IACF,GAAG;QAAC;QAAa;QAAW;QAAO;KAAS;IAC5C,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC7C,WAAW,GAAG,oBAAoB,UAAU,CAAC;IAC/C,GAAG,CAAC,kBAAmB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6IAAA,CAAA,UAAM,EAAE;QAC9D,OAAO;QACP,SAAS;QACT,mBAAmB,CAAA,UAAW;QAC9B,uBAAuB;QACvB,WAAW;QACX,UAAU;QACV,WAAW,GAAG,UAAU,cAAc,CAAC;QACvC,MAAM;QACN,SAAS;IACX,IAAK,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC3C,WAAW;IACb,GAAG,eAAe,CAAC,iBAAkB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,8KAAA,CAAA,UAAe,EAAE;QACrF,WAAW;QACX,OAAO;QACP,UAAU;IACZ;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorSlider.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { UnstableContext } from 'rc-slider';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport Slider from '../../slider';\nimport SliderInternalContext from '../../slider/Context';\nimport { getGradientPercentColor } from '../util';\nexport const GradientColorSlider = props => {\n  const {\n      prefixCls,\n      colors,\n      type,\n      color,\n      range = false,\n      className,\n      activeIndex,\n      onActive,\n      onDragStart,\n      onDragChange,\n      onKeyDelete\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"colors\", \"type\", \"color\", \"range\", \"className\", \"activeIndex\", \"onActive\", \"onDragStart\", \"onDragChange\", \"onKeyDelete\"]);\n  const sliderProps = Object.assign(Object.assign({}, restProps), {\n    track: false\n  });\n  // ========================== Background ==========================\n  const linearCss = React.useMemo(() => {\n    const colorsStr = colors.map(c => `${c.color} ${c.percent}%`).join(', ');\n    return `linear-gradient(90deg, ${colorsStr})`;\n  }, [colors]);\n  const pointColor = React.useMemo(() => {\n    if (!color || !type) {\n      return null;\n    }\n    if (type === 'alpha') {\n      return color.toRgbString();\n    }\n    return `hsl(${color.toHsb().h}, 100%, 50%)`;\n  }, [color, type]);\n  // ======================= Context: Slider ========================\n  const onInternalDragStart = useEvent(onDragStart);\n  const onInternalDragChange = useEvent(onDragChange);\n  const unstableContext = React.useMemo(() => ({\n    onDragStart: onInternalDragStart,\n    onDragChange: onInternalDragChange\n  }), []);\n  // ======================= Context: Render ========================\n  const handleRender = useEvent((ori, info) => {\n    const {\n      onFocus,\n      style,\n      className: handleCls,\n      onKeyDown\n    } = ori.props;\n    // Point Color\n    const mergedStyle = Object.assign({}, style);\n    if (type === 'gradient') {\n      mergedStyle.background = getGradientPercentColor(colors, info.value);\n    }\n    return /*#__PURE__*/React.cloneElement(ori, {\n      onFocus: e => {\n        onActive === null || onActive === void 0 ? void 0 : onActive(info.index);\n        onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n      },\n      style: mergedStyle,\n      className: classNames(handleCls, {\n        [`${prefixCls}-slider-handle-active`]: activeIndex === info.index\n      }),\n      onKeyDown: e => {\n        if ((e.key === 'Delete' || e.key === 'Backspace') && onKeyDelete) {\n          onKeyDelete(info.index);\n        }\n        onKeyDown === null || onKeyDown === void 0 ? void 0 : onKeyDown(e);\n      }\n    });\n  });\n  const sliderContext = React.useMemo(() => ({\n    direction: 'ltr',\n    handleRender\n  }), []);\n  // ============================ Render ============================\n  return /*#__PURE__*/React.createElement(SliderInternalContext.Provider, {\n    value: sliderContext\n  }, /*#__PURE__*/React.createElement(UnstableContext.Provider, {\n    value: unstableContext\n  }, /*#__PURE__*/React.createElement(Slider, Object.assign({}, sliderProps, {\n    className: classNames(className, `${prefixCls}-slider`),\n    tooltip: {\n      open: false\n    },\n    range: {\n      editable: range,\n      minCount: 2\n    },\n    styles: {\n      rail: {\n        background: linearCss\n      },\n      handle: pointColor ? {\n        background: pointColor\n      } : {}\n    },\n    classNames: {\n      rail: `${prefixCls}-slider-rail`,\n      handle: `${prefixCls}-slider-handle`\n    }\n  }))));\n};\nconst SingleColorSlider = props => {\n  const {\n    value,\n    onChange,\n    onChangeComplete\n  } = props;\n  const singleOnChange = v => onChange(v[0]);\n  const singleOnChangeComplete = v => onChangeComplete(v[0]);\n  return /*#__PURE__*/React.createElement(GradientColorSlider, Object.assign({}, props, {\n    value: [value],\n    onChange: singleOnChange,\n    onChangeComplete: singleOnChangeComplete\n  }));\n};\nexport default SingleColorSlider;"], "names": [], "mappings": ";;;;AAUA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAhBA;AAEA,IAAI,SAAS,4CAAQ,yCAAK,MAAM,IAAI,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAChG,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YAAY,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QAC3I,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnG;IACA,OAAO;AACT;;;;;;;;AAQO,MAAM,sBAAsB,CAAA;IACjC,MAAM,EACF,SAAS,EACT,MAAM,EACN,IAAI,EACJ,KAAK,EACL,QAAQ,KAAK,EACb,SAAS,EACT,WAAW,EACX,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,WAAW,EACZ,GAAG,OACJ,YAAY,OAAO,OAAO;QAAC;QAAa;QAAU;QAAQ;QAAS;QAAS;QAAa;QAAe;QAAY;QAAe;QAAgB;KAAc;IACnK,MAAM,cAAc,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,YAAY;QAC9D,OAAO;IACT;IACA,mEAAmE;IACnE,MAAM,YAAY,qMAAA,CAAA,UAAa,CAAC;QAC9B,MAAM,YAAY,OAAO,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QACnE,OAAO,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;IAC/C,GAAG;QAAC;KAAO;IACX,MAAM,aAAa,qMAAA,CAAA,UAAa,CAAC;QAC/B,IAAI,CAAC,SAAS,CAAC,MAAM;YACnB,OAAO;QACT;QACA,IAAI,SAAS,SAAS;YACpB,OAAO,MAAM,WAAW;QAC1B;QACA,OAAO,CAAC,IAAI,EAAE,MAAM,KAAK,GAAG,CAAC,CAAC,YAAY,CAAC;IAC7C,GAAG;QAAC;QAAO;KAAK;IAChB,mEAAmE;IACnE,MAAM,sBAAsB,CAAA,GAAA,qJAAA,CAAA,UAAQ,AAAD,EAAE;IACrC,MAAM,uBAAuB,CAAA,GAAA,qJAAA,CAAA,UAAQ,AAAD,EAAE;IACtC,MAAM,kBAAkB,qMAAA,CAAA,UAAa,CAAC,IAAM,CAAC;YAC3C,aAAa;YACb,cAAc;QAChB,CAAC,GAAG,EAAE;IACN,mEAAmE;IACnE,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,KAAK;QAClC,MAAM,EACJ,OAAO,EACP,KAAK,EACL,WAAW,SAAS,EACpB,SAAS,EACV,GAAG,IAAI,KAAK;QACb,cAAc;QACd,MAAM,cAAc,OAAO,MAAM,CAAC,CAAC,GAAG;QACtC,IAAI,SAAS,YAAY;YACvB,YAAY,UAAU,GAAG,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,KAAK,KAAK;QACrE;QACA,OAAO,WAAW,GAAE,qMAAA,CAAA,eAAkB,CAAC,KAAK;YAC1C,SAAS,CAAA;gBACP,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK;gBACvE,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ;YAC5D;YACA,OAAO;YACP,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,WAAW;gBAC/B,CAAC,GAAG,UAAU,qBAAqB,CAAC,CAAC,EAAE,gBAAgB,KAAK,KAAK;YACnE;YACA,WAAW,CAAA;gBACT,IAAI,CAAC,EAAE,GAAG,KAAK,YAAY,EAAE,GAAG,KAAK,WAAW,KAAK,aAAa;oBAChE,YAAY,KAAK,KAAK;gBACxB;gBACA,cAAc,QAAQ,cAAc,KAAK,IAAI,KAAK,IAAI,UAAU;YAClE;QACF;IACF;IACA,MAAM,gBAAgB,qMAAA,CAAA,UAAa,CAAC,IAAM,CAAC;YACzC,WAAW;YACX;QACF,CAAC,GAAG,EAAE;IACN,mEAAmE;IACnE,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,+IAAA,CAAA,UAAqB,CAAC,QAAQ,EAAE;QACtE,OAAO;IACT,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,6IAAA,CAAA,kBAAe,CAAC,QAAQ,EAAE;QAC5D,OAAO;IACT,GAAG,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,6IAAA,CAAA,UAAM,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,aAAa;QACzE,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,WAAW,GAAG,UAAU,OAAO,CAAC;QACtD,SAAS;YACP,MAAM;QACR;QACA,OAAO;YACL,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;YACN,MAAM;gBACJ,YAAY;YACd;YACA,QAAQ,aAAa;gBACnB,YAAY;YACd,IAAI,CAAC;QACP;QACA,YAAY;YACV,MAAM,GAAG,UAAU,YAAY,CAAC;YAChC,QAAQ,GAAG,UAAU,cAAc,CAAC;QACtC;IACF;AACF;AACA,MAAM,oBAAoB,CAAA;IACxB,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,gBAAgB,EACjB,GAAG;IACJ,MAAM,iBAAiB,CAAA,IAAK,SAAS,CAAC,CAAC,EAAE;IACzC,MAAM,yBAAyB,CAAA,IAAK,iBAAiB,CAAC,CAAC,EAAE;IACzD,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,qBAAqB,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;QACpF,OAAO;YAAC;SAAM;QACd,UAAU;QACV,kBAAkB;IACpB;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/PanelPicker/GradientColorBar.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport * as React from 'react';\nimport { AggregationColor } from '../../color';\nimport { getGradientPercentColor } from '../../util';\nimport { GradientColorSlider } from '../ColorSlider';\nfunction sortColors(colors) {\n  return _toConsumableArray(colors).sort((a, b) => a.percent - b.percent);\n}\n/**\n * GradientColorBar will auto show when the mode is `gradient`.\n */\nconst GradientColorBar = props => {\n  const {\n    prefixCls,\n    mode,\n    onChange,\n    onChangeComplete,\n    onActive,\n    activeIndex,\n    onGradientDragging,\n    colors\n  } = props;\n  const isGradient = mode === 'gradient';\n  // ============================= Colors =============================\n  const colorList = React.useMemo(() => colors.map(info => ({\n    percent: info.percent,\n    color: info.color.toRgbString()\n  })), [colors]);\n  const values = React.useMemo(() => colorList.map(info => info.percent), [colorList]);\n  // ============================== Drag ==============================\n  const colorsRef = React.useRef(colorList);\n  // Record current colors\n  const onDragStart = ({\n    rawValues,\n    draggingIndex,\n    draggingValue\n  }) => {\n    if (rawValues.length > colorList.length) {\n      // Add new node\n      const newPointColor = getGradientPercentColor(colorList, draggingValue);\n      const nextColors = _toConsumableArray(colorList);\n      nextColors.splice(draggingIndex, 0, {\n        percent: draggingValue,\n        color: newPointColor\n      });\n      colorsRef.current = nextColors;\n    } else {\n      colorsRef.current = colorList;\n    }\n    onGradientDragging(true);\n    onChange(new AggregationColor(sortColors(colorsRef.current)), true);\n  };\n  // Adjust color when dragging\n  const onDragChange = ({\n    deleteIndex,\n    draggingIndex,\n    draggingValue\n  }) => {\n    let nextColors = _toConsumableArray(colorsRef.current);\n    if (deleteIndex !== -1) {\n      nextColors.splice(deleteIndex, 1);\n    } else {\n      nextColors[draggingIndex] = Object.assign(Object.assign({}, nextColors[draggingIndex]), {\n        percent: draggingValue\n      });\n      nextColors = sortColors(nextColors);\n    }\n    onChange(new AggregationColor(nextColors), true);\n  };\n  // ============================== Key ===============================\n  const onKeyDelete = index => {\n    const nextColors = _toConsumableArray(colorList);\n    nextColors.splice(index, 1);\n    const nextColor = new AggregationColor(nextColors);\n    onChange(nextColor);\n    onChangeComplete(nextColor);\n  };\n  // ============================= Change =============================\n  const onInternalChangeComplete = nextValues => {\n    onChangeComplete(new AggregationColor(colorList));\n    // Reset `activeIndex` if out of range\n    if (activeIndex >= nextValues.length) {\n      onActive(nextValues.length - 1);\n    }\n    onGradientDragging(false);\n  };\n  // ============================= Render =============================\n  if (!isGradient) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(GradientColorSlider, {\n    min: 0,\n    max: 100,\n    prefixCls: prefixCls,\n    className: `${prefixCls}-gradient-slider`,\n    colors: colorList,\n    color: null,\n    value: values,\n    range: true,\n    onChangeComplete: onInternalChangeComplete,\n    disabled: false,\n    type: \"gradient\",\n    // Active\n    activeIndex: activeIndex,\n    onActive: onActive,\n    // Drag\n    onDragStart: onDragStart,\n    onDragChange: onDragChange,\n    onKeyDelete: onKeyDelete\n  });\n};\nexport default /*#__PURE__*/React.memo(GradientColorBar);"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;AAOA,SAAS,WAAW,MAAM;IACxB,OAAO,CAAA,GAAA,yKAAA,CAAA,UAAkB,AAAD,EAAE,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO;AACxE;AACA;;CAEC,GACD,MAAM,mBAAmB,CAAA;IACvB,MAAM,EACJ,SAAS,EACT,IAAI,EACJ,QAAQ,EACR,gBAAgB,EAChB,QAAQ,EACR,WAAW,EACX,kBAAkB,EAClB,MAAM,EACP,GAAG;IACJ,MAAM,aAAa,SAAS;IAC5B,qEAAqE;IACrE,MAAM,YAAY,qMAAA,CAAA,UAAa,CAAC,IAAM,OAAO,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxD,SAAS,KAAK,OAAO;gBACrB,OAAO,KAAK,KAAK,CAAC,WAAW;YAC/B,CAAC,IAAI;QAAC;KAAO;IACb,MAAM,SAAS,qMAAA,CAAA,UAAa,CAAC,IAAM,UAAU,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,GAAG;QAAC;KAAU;IACnF,qEAAqE;IACrE,MAAM,YAAY,qMAAA,CAAA,SAAY,CAAC;IAC/B,wBAAwB;IACxB,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,aAAa,EACb,aAAa,EACd;QACC,IAAI,UAAU,MAAM,GAAG,UAAU,MAAM,EAAE;YACvC,eAAe;YACf,MAAM,gBAAgB,CAAA,GAAA,qJAAA,CAAA,0BAAuB,AAAD,EAAE,WAAW;YACzD,MAAM,aAAa,CAAA,GAAA,yKAAA,CAAA,UAAkB,AAAD,EAAE;YACtC,WAAW,MAAM,CAAC,eAAe,GAAG;gBAClC,SAAS;gBACT,OAAO;YACT;YACA,UAAU,OAAO,GAAG;QACtB,OAAO;YACL,UAAU,OAAO,GAAG;QACtB;QACA,mBAAmB;QACnB,SAAS,IAAI,sJAAA,CAAA,mBAAgB,CAAC,WAAW,UAAU,OAAO,IAAI;IAChE;IACA,6BAA6B;IAC7B,MAAM,eAAe,CAAC,EACpB,WAAW,EACX,aAAa,EACb,aAAa,EACd;QACC,IAAI,aAAa,CAAA,GAAA,yKAAA,CAAA,UAAkB,AAAD,EAAE,UAAU,OAAO;QACrD,IAAI,gBAAgB,CAAC,GAAG;YACtB,WAAW,MAAM,CAAC,aAAa;QACjC,OAAO;YACL,UAAU,CAAC,cAAc,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,cAAc,GAAG;gBACtF,SAAS;YACX;YACA,aAAa,WAAW;QAC1B;QACA,SAAS,IAAI,sJAAA,CAAA,mBAAgB,CAAC,aAAa;IAC7C;IACA,qEAAqE;IACrE,MAAM,cAAc,CAAA;QAClB,MAAM,aAAa,CAAA,GAAA,yKAAA,CAAA,UAAkB,AAAD,EAAE;QACtC,WAAW,MAAM,CAAC,OAAO;QACzB,MAAM,YAAY,IAAI,sJAAA,CAAA,mBAAgB,CAAC;QACvC,SAAS;QACT,iBAAiB;IACnB;IACA,qEAAqE;IACrE,MAAM,2BAA2B,CAAA;QAC/B,iBAAiB,IAAI,sJAAA,CAAA,mBAAgB,CAAC;QACtC,sCAAsC;QACtC,IAAI,eAAe,WAAW,MAAM,EAAE;YACpC,SAAS,WAAW,MAAM,GAAG;QAC/B;QACA,mBAAmB;IACrB;IACA,qEAAqE;IACrE,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IACA,OAAO,WAAW,GAAE,qMAAA,CAAA,gBAAmB,CAAC,0KAAA,CAAA,sBAAmB,EAAE;QAC3D,KAAK;QACL,KAAK;QACL,WAAW;QACX,WAAW,GAAG,UAAU,gBAAgB,CAAC;QACzC,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,kBAAkB;QAClB,UAAU;QACV,MAAM;QACN,SAAS;QACT,aAAa;QACb,UAAU;QACV,OAAO;QACP,aAAa;QACb,cAAc;QACd,aAAa;IACf;AACF;uCACe,WAAW,GAAE,qMAAA,CAAA,OAAU,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/PanelPicker/index.js"], "sourcesContent": ["\"use client\";\n\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext } from 'react';\nimport RcColorPicker from '@rc-component/color-picker';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport Segmented from '../../../segmented';\nimport { AggregationColor } from '../../color';\nimport { PanelPickerContext } from '../../context';\nimport { genAlphaColor, generateColor } from '../../util';\nimport ColorClear from '../ColorClear';\nimport ColorInput from '../ColorInput';\nimport ColorSlider from '../ColorSlider';\nimport GradientColorBar from './GradientColorBar';\nconst components = {\n  slider: ColorSlider\n};\nconst PanelPicker = () => {\n  const panelPickerContext = useContext(PanelPickerContext);\n  const {\n      mode,\n      onModeChange,\n      modeOptions,\n      prefixCls,\n      allowClear,\n      value,\n      disabledAlpha,\n      onChange,\n      onClear,\n      onChangeComplete,\n      activeIndex,\n      gradientDragging\n    } = panelPickerContext,\n    injectProps = __rest(panelPickerContext, [\"mode\", \"onModeChange\", \"modeOptions\", \"prefixCls\", \"allowClear\", \"value\", \"disabledAlpha\", \"onChange\", \"onClear\", \"onChangeComplete\", \"activeIndex\", \"gradientDragging\"]);\n  // ============================ Colors ============================\n  const colors = React.useMemo(() => {\n    if (!value.cleared) {\n      return value.getColors();\n    }\n    return [{\n      percent: 0,\n      color: new AggregationColor('')\n    }, {\n      percent: 100,\n      color: new AggregationColor('')\n    }];\n  }, [value]);\n  // ========================= Single Color =========================\n  const isSingle = !value.isGradient();\n  // We cache the point color in case user drag the gradient point across another one\n  const [lockedColor, setLockedColor] = React.useState(value);\n  // Use layout effect here since `useEffect` will cause a blink when mouseDown\n  useLayoutEffect(() => {\n    var _a;\n    if (!isSingle) {\n      setLockedColor((_a = colors[activeIndex]) === null || _a === void 0 ? void 0 : _a.color);\n    }\n  }, [gradientDragging, activeIndex]);\n  const activeColor = React.useMemo(() => {\n    var _a;\n    if (isSingle) {\n      return value;\n    }\n    // Use cache when dragging. User can not operation panel when dragging.\n    if (gradientDragging) {\n      return lockedColor;\n    }\n    return (_a = colors[activeIndex]) === null || _a === void 0 ? void 0 : _a.color;\n  }, [value, activeIndex, isSingle, lockedColor, gradientDragging]);\n  // ========================= Picker Color =========================\n  const [pickerColor, setPickerColor] = React.useState(activeColor);\n  const [forceSync, setForceSync] = React.useState(0);\n  const mergedPickerColor = (pickerColor === null || pickerColor === void 0 ? void 0 : pickerColor.equals(activeColor)) ? activeColor : pickerColor;\n  useLayoutEffect(() => {\n    setPickerColor(activeColor);\n  }, [forceSync, activeColor === null || activeColor === void 0 ? void 0 : activeColor.toHexString()]);\n  // ============================ Change ============================\n  const fillColor = (nextColor, info) => {\n    let submitColor = generateColor(nextColor);\n    // Fill alpha color to 100% if origin is cleared color\n    if (value.cleared) {\n      const rgb = submitColor.toRgb();\n      // Auto fill color if origin is `0/0/0` to enhance user experience\n      if (!rgb.r && !rgb.g && !rgb.b && info) {\n        const {\n          type: infoType,\n          value: infoValue = 0\n        } = info;\n        submitColor = new AggregationColor({\n          h: infoType === 'hue' ? infoValue : 0,\n          s: 1,\n          b: 1,\n          a: infoType === 'alpha' ? infoValue / 100 : 1\n        });\n      } else {\n        submitColor = genAlphaColor(submitColor);\n      }\n    }\n    if (mode === 'single') {\n      return submitColor;\n    }\n    const nextColors = _toConsumableArray(colors);\n    nextColors[activeIndex] = Object.assign(Object.assign({}, nextColors[activeIndex]), {\n      color: submitColor\n    });\n    return new AggregationColor(nextColors);\n  };\n  const onPickerChange = (colorValue, fromPicker, info) => {\n    const nextColor = fillColor(colorValue, info);\n    setPickerColor(nextColor.isGradient() ? nextColor.getColors()[activeIndex].color : nextColor);\n    onChange(nextColor, fromPicker);\n  };\n  const onInternalChangeComplete = (nextColor, info) => {\n    // Trigger complete event\n    onChangeComplete(fillColor(nextColor, info));\n    // Back of origin color in case in controlled\n    // This will set after `onChangeComplete` to avoid `setState` trigger rerender\n    // which will make `fillColor` get wrong `color.cleared` state\n    setForceSync(ori => ori + 1);\n  };\n  const onInputChange = colorValue => {\n    onChange(fillColor(colorValue));\n  };\n  // ============================ Render ============================\n  // Operation bar\n  let operationNode = null;\n  const showMode = modeOptions.length > 1;\n  if (allowClear || showMode) {\n    operationNode = /*#__PURE__*/React.createElement(\"div\", {\n      className: `${prefixCls}-operation`\n    }, showMode && (/*#__PURE__*/React.createElement(Segmented, {\n      size: \"small\",\n      options: modeOptions,\n      value: mode,\n      onChange: onModeChange\n    })), /*#__PURE__*/React.createElement(ColorClear, Object.assign({\n      prefixCls: prefixCls,\n      value: value,\n      onChange: clearColor => {\n        onChange(clearColor);\n        onClear === null || onClear === void 0 ? void 0 : onClear();\n      }\n    }, injectProps)));\n  }\n  // Return\n  return /*#__PURE__*/React.createElement(React.Fragment, null, operationNode, /*#__PURE__*/React.createElement(GradientColorBar, Object.assign({}, panelPickerContext, {\n    colors: colors\n  })), /*#__PURE__*/React.createElement(RcColorPicker, {\n    prefixCls: prefixCls,\n    value: mergedPickerColor === null || mergedPickerColor === void 0 ? void 0 : mergedPickerColor.toHsb(),\n    disabledAlpha: disabledAlpha,\n    onChange: (colorValue, info) => {\n      onPickerChange(colorValue, true, info);\n    },\n    onChangeComplete: (colorValue, info) => {\n      onInternalChangeComplete(colorValue, info);\n    },\n    components: components\n  }), /*#__PURE__*/React.createElement(ColorInput, Object.assign({\n    value: activeColor,\n    onChange: onInputChange,\n    prefixCls: prefixCls,\n    disabledAlpha: disabledAlpha\n  }, injectProps)));\n};\nexport default PanelPicker;"], "names": [], "mappings": ";;;AAEA;AASA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA;;AAGA,IAAI,SAAS,4CAAQ,yCAAK,MAAM,IAAI,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAChG,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YAAY,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QAC3I,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnG;IACA,OAAO;AACT;;;;;;;;;;;;AAYA,MAAM,aAAa;IACjB,QAAQ,0KAAA,CAAA,UAAW;AACrB;AACA,MAAM,cAAc;IAClB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,wJAAA,CAAA,qBAAkB;IACxD,MAAM,EACF,IAAI,EACJ,YAAY,EACZ,WAAW,EACX,SAAS,EACT,UAAU,EACV,KAAK,EACL,aAAa,EACb,QAAQ,EACR,OAAO,EACP,gBAAgB,EAChB,WAAW,EACX,gBAAgB,EACjB,GAAG,oBACJ,cAAc,OAAO,oBAAoB;QAAC;QAAQ;QAAgB;QAAe;QAAa;QAAc;QAAS;QAAiB;QAAY;QAAW;QAAoB;QAAe;KAAmB;IACrN,mEAAmE;IACnE,MAAM,SAAS,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC3B,IAAI,CAAC,MAAM,OAAO,EAAE;YAClB,OAAO,MAAM,SAAS;QACxB;QACA,OAAO;YAAC;gBACN,SAAS;gBACT,OAAO,IAAI,sJAAA,CAAA,mBAAgB,CAAC;YAC9B;YAAG;gBACD,SAAS;gBACT,OAAO,IAAI,sJAAA,CAAA,mBAAgB,CAAC;YAC9B;SAAE;IACJ,GAAG;QAAC;KAAM;IACV,mEAAmE;IACnE,MAAM,WAAW,CAAC,MAAM,UAAU;IAClC,mFAAmF;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,6EAA6E;IAC7E,CAAA,GAAA,4JAAA,CAAA,UAAe,AAAD,EAAE;QACd,IAAI;QACJ,IAAI,CAAC,UAAU;YACb,eAAe,CAAC,KAAK,MAAM,CAAC,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;QACzF;IACF,GAAG;QAAC;QAAkB;KAAY;IAClC,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChC,IAAI;QACJ,IAAI,UAAU;YACZ,OAAO;QACT;QACA,uEAAuE;QACvE,IAAI,kBAAkB;YACpB,OAAO;QACT;QACA,OAAO,CAAC,KAAK,MAAM,CAAC,YAAY,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;IACjF,GAAG;QAAC;QAAO;QAAa;QAAU;QAAa;KAAiB;IAChE,mEAAmE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,oBAAoB,CAAC,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,MAAM,CAAC,YAAY,IAAI,cAAc;IACtI,CAAA,GAAA,4JAAA,CAAA,UAAe,AAAD,EAAE;QACd,eAAe;IACjB,GAAG;QAAC;QAAW,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,WAAW;KAAG;IACnG,mEAAmE;IACnE,MAAM,YAAY,CAAC,WAAW;QAC5B,IAAI,cAAc,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;QAChC,sDAAsD;QACtD,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,MAAM,YAAY,KAAK;YAC7B,kEAAkE;YAClE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM;gBACtC,MAAM,EACJ,MAAM,QAAQ,EACd,OAAO,YAAY,CAAC,EACrB,GAAG;gBACJ,cAAc,IAAI,sJAAA,CAAA,mBAAgB,CAAC;oBACjC,GAAG,aAAa,QAAQ,YAAY;oBACpC,GAAG;oBACH,GAAG;oBACH,GAAG,aAAa,UAAU,YAAY,MAAM;gBAC9C;YACF,OAAO;gBACL,cAAc,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;YAC9B;QACF;QACA,IAAI,SAAS,UAAU;YACrB,OAAO;QACT;QACA,MAAM,aAAa,CAAA,GAAA,yKAAA,CAAA,UAAkB,AAAD,EAAE;QACtC,UAAU,CAAC,YAAY,GAAG,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC,YAAY,GAAG;YAClF,OAAO;QACT;QACA,OAAO,IAAI,sJAAA,CAAA,mBAAgB,CAAC;IAC9B;IACA,MAAM,iBAAiB,CAAC,YAAY,YAAY;QAC9C,MAAM,YAAY,UAAU,YAAY;QACxC,eAAe,UAAU,UAAU,KAAK,UAAU,SAAS,EAAE,CAAC,YAAY,CAAC,KAAK,GAAG;QACnF,SAAS,WAAW;IACtB;IACA,MAAM,2BAA2B,CAAC,WAAW;QAC3C,yBAAyB;QACzB,iBAAiB,UAAU,WAAW;QACtC,6CAA6C;QAC7C,8EAA8E;QAC9E,8DAA8D;QAC9D,aAAa,CAAA,MAAO,MAAM;IAC5B;IACA,MAAM,gBAAgB,CAAA;QACpB,SAAS,UAAU;IACrB;IACA,mEAAmE;IACnE,gBAAgB;IAChB,IAAI,gBAAgB;IACpB,MAAM,WAAW,YAAY,MAAM,GAAG;IACtC,IAAI,cAAc,UAAU;QAC1B,gBAAgB,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;YACtD,WAAW,GAAG,UAAU,UAAU,CAAC;QACrC,GAAG,YAAa,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,gJAAA,CAAA,UAAS,EAAE;YAC1D,MAAM;YACN,SAAS;YACT,OAAO;YACP,UAAU;QACZ,IAAK,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,yKAAA,CAAA,UAAU,EAAE,OAAO,MAAM,CAAC;YAC9D,WAAW;YACX,OAAO;YACP,UAAU,CAAA;gBACR,SAAS;gBACT,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI;YACpD;QACF,GAAG;IACL;IACA,SAAS;IACT,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,eAAe,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,8LAAA,CAAA,UAAgB,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,oBAAoB;QACpK,QAAQ;IACV,KAAK,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,mLAAA,CAAA,UAAa,EAAE;QACnD,WAAW;QACX,OAAO,sBAAsB,QAAQ,sBAAsB,KAAK,IAAI,KAAK,IAAI,kBAAkB,KAAK;QACpG,eAAe;QACf,UAAU,CAAC,YAAY;YACrB,eAAe,YAAY,MAAM;QACnC;QACA,kBAAkB,CAAC,YAAY;YAC7B,yBAAyB,YAAY;QACvC;QACA,YAAY;IACd,IAAI,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,yKAAA,CAAA,UAAU,EAAE,OAAO,MAAM,CAAC;QAC7D,OAAO;QACP,UAAU;QACV,WAAW;QACX,eAAe;IACjB,GAAG;AACL;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/PanelPresets.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useContext } from 'react';\nimport { PanelPresetsContext } from '../context';\nimport ColorPresets from './ColorPresets';\nconst PanelPresets = () => {\n  const {\n    prefixCls,\n    value,\n    presets,\n    onChange\n  } = useContext(PanelPresetsContext);\n  return Array.isArray(presets) ? (/*#__PURE__*/React.createElement(ColorPresets, {\n    value: value,\n    presets: presets,\n    prefixCls: prefixCls,\n    onChange: onChange\n  })) : null;\n};\nexport default PanelPresets;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAKA,MAAM,eAAe;IACnB,MAAM,EACJ,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EACT,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,wJAAA,CAAA,sBAAmB;IAClC,OAAO,MAAM,OAAO,CAAC,WAAY,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,2KAAA,CAAA,UAAY,EAAE;QAC9E,OAAO;QACP,SAAS;QACT,WAAW;QACX,UAAU;IACZ,KAAM;AACR;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/ColorPickerPanel.js"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport Divider from '../divider';\nimport PanelPicker from './components/PanelPicker';\nimport PanelPresets from './components/PanelPresets';\nimport { PanelPickerContext, PanelPresetsContext } from './context';\nconst ColorPickerPanel = props => {\n  const {\n    prefixCls,\n    presets,\n    panelRender,\n    value,\n    onChange,\n    onClear,\n    allowClear,\n    disabledAlpha,\n    mode,\n    onModeChange,\n    modeOptions,\n    onChangeComplete,\n    activeIndex,\n    onActive,\n    format,\n    onFormatChange,\n    gradientDragging,\n    onGradientDragging,\n    disabledFormat\n  } = props;\n  const colorPickerPanelPrefixCls = `${prefixCls}-inner`;\n  // ===================== Context ======================\n  const panelContext = React.useMemo(() => ({\n    prefixCls,\n    value,\n    onChange,\n    onClear,\n    allowClear,\n    disabledAlpha,\n    mode,\n    onModeChange,\n    modeOptions,\n    onChangeComplete,\n    activeIndex,\n    onActive,\n    format,\n    onFormatChange,\n    gradientDragging,\n    onGradientDragging,\n    disabledFormat\n  }), [prefixCls, value, onChange, onClear, allowClear, disabledAlpha, mode, onModeChange, modeOptions, onChangeComplete, activeIndex, onActive, format, onFormatChange, gradientDragging, onGradientDragging, disabledFormat]);\n  const presetContext = React.useMemo(() => ({\n    prefixCls,\n    value,\n    presets,\n    onChange\n  }), [prefixCls, value, presets, onChange]);\n  // ====================== Render ======================\n  const innerPanel = /*#__PURE__*/React.createElement(\"div\", {\n    className: `${colorPickerPanelPrefixCls}-content`\n  }, /*#__PURE__*/React.createElement(PanelPicker, null), Array.isArray(presets) && /*#__PURE__*/React.createElement(Divider, null), /*#__PURE__*/React.createElement(PanelPresets, null));\n  return /*#__PURE__*/React.createElement(PanelPickerContext.Provider, {\n    value: panelContext\n  }, /*#__PURE__*/React.createElement(PanelPresetsContext.Provider, {\n    value: presetContext\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: colorPickerPanelPrefixCls\n  }, typeof panelRender === 'function' ? panelRender(innerPanel, {\n    components: {\n      Picker: PanelPicker,\n      Presets: PanelPresets\n    }\n  }) : innerPanel)));\n};\nif (process.env.NODE_ENV !== 'production') {\n  ColorPickerPanel.displayName = 'ColorPickerPanel';\n}\nexport default ColorPickerPanel;"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;AAOA,MAAM,mBAAmB,CAAA;IACvB,MAAM,EACJ,SAAS,EACT,OAAO,EACP,WAAW,EACX,KAAK,EACL,QAAQ,EACR,OAAO,EACP,UAAU,EACV,aAAa,EACb,IAAI,EACJ,YAAY,EACZ,WAAW,EACX,gBAAgB,EAChB,WAAW,EACX,QAAQ,EACR,MAAM,EACN,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,cAAc,EACf,GAAG;IACJ,MAAM,4BAA4B,GAAG,UAAU,MAAM,CAAC;IACtD,uDAAuD;IACvD,MAAM,eAAe,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAM,CAAC;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAW;QAAO;QAAU;QAAS;QAAY;QAAe;QAAM;QAAc;QAAa;QAAkB;QAAa;QAAU;QAAQ;QAAgB;QAAkB;QAAoB;KAAe;IAC5N,MAAM,gBAAgB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAM,CAAC;YACzC;YACA;YACA;YACA;QACF,CAAC,GAAG;QAAC;QAAW;QAAO;QAAS;KAAS;IACzC,uDAAuD;IACvD,MAAM,aAAa,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACzD,WAAW,GAAG,0BAA0B,QAAQ,CAAC;IACnD,GAAG,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,mLAAA,CAAA,UAAW,EAAE,OAAO,MAAM,OAAO,CAAC,YAAY,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,8IAAA,CAAA,UAAO,EAAE,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,2KAAA,CAAA,UAAY,EAAE;IAClL,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,wJAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE;QACnE,OAAO;IACT,GAAG,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,wJAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE;QAChE,OAAO;IACT,GAAG,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACzC,WAAW;IACb,GAAG,OAAO,gBAAgB,aAAa,YAAY,YAAY;QAC7D,YAAY;YACV,QAAQ,mLAAA,CAAA,UAAW;YACnB,SAAS,2KAAA,CAAA,UAAY;QACvB;IACF,KAAK;AACP;AACA,wCAA2C;IACzC,iBAAiB,WAAW,GAAG;AACjC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/components/ColorTrigger.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { forwardRef, useMemo } from 'react';\nimport { ColorBlock } from '@rc-component/color-picker';\nimport classNames from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useLocale } from '../../locale';\nimport { getColorAlpha } from '../util';\nimport ColorClear from './ColorClear';\nconst ColorTrigger = /*#__PURE__*/forwardRef((props, ref) => {\n  const {\n      color,\n      prefixCls,\n      open,\n      disabled,\n      format,\n      className,\n      showText,\n      activeIndex\n    } = props,\n    rest = __rest(props, [\"color\", \"prefixCls\", \"open\", \"disabled\", \"format\", \"className\", \"showText\", \"activeIndex\"]);\n  const colorTriggerPrefixCls = `${prefixCls}-trigger`;\n  const colorTextPrefixCls = `${colorTriggerPrefixCls}-text`;\n  const colorTextCellPrefixCls = `${colorTextPrefixCls}-cell`;\n  const [locale] = useLocale('ColorPicker');\n  // ============================== Text ==============================\n  const desc = React.useMemo(() => {\n    if (!showText) {\n      return '';\n    }\n    if (typeof showText === 'function') {\n      return showText(color);\n    }\n    if (color.cleared) {\n      return locale.transparent;\n    }\n    if (color.isGradient()) {\n      return color.getColors().map((c, index) => {\n        const inactive = activeIndex !== -1 && activeIndex !== index;\n        return /*#__PURE__*/React.createElement(\"span\", {\n          key: index,\n          className: classNames(colorTextCellPrefixCls, inactive && `${colorTextCellPrefixCls}-inactive`)\n        }, c.color.toRgbString(), \" \", c.percent, \"%\");\n      });\n    }\n    const hexString = color.toHexString().toUpperCase();\n    const alpha = getColorAlpha(color);\n    switch (format) {\n      case 'rgb':\n        return color.toRgbString();\n      case 'hsb':\n        return color.toHsbString();\n      // case 'hex':\n      default:\n        return alpha < 100 ? `${hexString.slice(0, 7)},${alpha}%` : hexString;\n    }\n  }, [color, format, showText, activeIndex]);\n  // ============================= Render =============================\n  const containerNode = useMemo(() => color.cleared ? (/*#__PURE__*/React.createElement(ColorClear, {\n    prefixCls: prefixCls\n  })) : (/*#__PURE__*/React.createElement(ColorBlock, {\n    prefixCls: prefixCls,\n    color: color.toCssString()\n  })), [color, prefixCls]);\n  return /*#__PURE__*/React.createElement(\"div\", Object.assign({\n    ref: ref,\n    className: classNames(colorTriggerPrefixCls, className, {\n      [`${colorTriggerPrefixCls}-active`]: open,\n      [`${colorTriggerPrefixCls}-disabled`]: disabled\n    })\n  }, pickAttrs(rest)), containerNode, showText && /*#__PURE__*/React.createElement(\"div\", {\n    className: colorTextPrefixCls\n  }, desc));\n});\nexport default ColorTrigger;"], "names": [], "mappings": ";;;AAUA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAhBA;AAEA,IAAI,SAAS,4CAAQ,yCAAK,MAAM,IAAI,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAChG,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YAAY,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QAC3I,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnG;IACA,OAAO;AACT;;;;;;;;AAQA,MAAM,eAAe,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACnD,MAAM,EACF,KAAK,EACL,SAAS,EACT,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,SAAS,EACT,QAAQ,EACR,WAAW,EACZ,GAAG,OACJ,OAAO,OAAO,OAAO;QAAC;QAAS;QAAa;QAAQ;QAAY;QAAU;QAAa;QAAY;KAAc;IACnH,MAAM,wBAAwB,GAAG,UAAU,QAAQ,CAAC;IACpD,MAAM,qBAAqB,GAAG,sBAAsB,KAAK,CAAC;IAC1D,MAAM,yBAAyB,GAAG,mBAAmB,KAAK,CAAC;IAC3D,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;IAC3B,qEAAqE;IACrE,MAAM,OAAO,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACzB,IAAI,CAAC,UAAU;YACb,OAAO;QACT;QACA,IAAI,OAAO,aAAa,YAAY;YAClC,OAAO,SAAS;QAClB;QACA,IAAI,MAAM,OAAO,EAAE;YACjB,OAAO,OAAO,WAAW;QAC3B;QACA,IAAI,MAAM,UAAU,IAAI;YACtB,OAAO,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,GAAG;gBAC/B,MAAM,WAAW,gBAAgB,CAAC,KAAK,gBAAgB;gBACvD,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;oBAC9C,KAAK;oBACL,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,wBAAwB,YAAY,GAAG,uBAAuB,SAAS,CAAC;gBAChG,GAAG,EAAE,KAAK,CAAC,WAAW,IAAI,KAAK,EAAE,OAAO,EAAE;YAC5C;QACF;QACA,MAAM,YAAY,MAAM,WAAW,GAAG,WAAW;QACjD,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO,MAAM,WAAW;YAC1B,KAAK;gBACH,OAAO,MAAM,WAAW;YAC1B,cAAc;YACd;gBACE,OAAO,QAAQ,MAAM,GAAG,UAAU,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG;QAChE;IACF,GAAG;QAAC;QAAO;QAAQ;QAAU;KAAY;IACzC,qEAAqE;IACrE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,MAAM,OAAO,GAAI,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,yKAAA,CAAA,UAAU,EAAE;YAChG,WAAW;QACb,KAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,+NAAA,CAAA,aAAU,EAAE;YAClD,WAAW;YACX,OAAO,MAAM,WAAW;QAC1B,IAAK;QAAC;QAAO;KAAU;IACvB,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,KAAK;QACL,WAAW,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,uBAAuB,WAAW;YACtD,CAAC,GAAG,sBAAsB,OAAO,CAAC,CAAC,EAAE;YACrC,CAAC,GAAG,sBAAsB,SAAS,CAAC,CAAC,EAAE;QACzC;IACF,GAAG,CAAA,GAAA,6IAAA,CAAA,UAAS,AAAD,EAAE,QAAQ,eAAe,YAAY,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACtF,WAAW;IACb,GAAG;AACL;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1336, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/hooks/useModeColor.js"], "sourcesContent": ["import * as React from 'react';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport { useLocale } from '../../locale';\nimport { generateColor } from '../util';\n/**\n * Combine the `color` and `mode` to make sure sync of state.\n */\nexport default function useModeColor(defaultValue, value, mode) {\n  const [locale] = useLocale('ColorPicker');\n  // ======================== Base ========================\n  // Color\n  const [mergedColor, setMergedColor] = useMergedState(defaultValue, {\n    value\n  });\n  // Mode\n  const [modeState, setModeState] = React.useState('single');\n  const [modeOptionList, modeSet] = React.useMemo(() => {\n    const list = (Array.isArray(mode) ? mode : [mode]).filter(m => m);\n    if (!list.length) {\n      list.push('single');\n    }\n    const modes = new Set(list);\n    const optionList = [];\n    const pushOption = (modeType, localeTxt) => {\n      if (modes.has(modeType)) {\n        optionList.push({\n          label: localeTxt,\n          value: modeType\n        });\n      }\n    };\n    pushOption('single', locale.singleColor);\n    pushOption('gradient', locale.gradientColor);\n    return [optionList, modes];\n  }, [mode]);\n  // ======================== Post ========================\n  // We need align `mode` with `color` state\n  // >>>>> Color\n  const [cacheColor, setCacheColor] = React.useState(null);\n  const setColor = useEvent(nextColor => {\n    setCacheColor(nextColor);\n    setMergedColor(nextColor);\n  });\n  const postColor = React.useMemo(() => {\n    const colorObj = generateColor(mergedColor || '');\n    // Use `cacheColor` in case the color is `cleared`\n    return colorObj.equals(cacheColor) ? cacheColor : colorObj;\n  }, [mergedColor, cacheColor]);\n  // >>>>> Mode\n  const postMode = React.useMemo(() => {\n    var _a;\n    if (modeSet.has(modeState)) {\n      return modeState;\n    }\n    return (_a = modeOptionList[0]) === null || _a === void 0 ? void 0 : _a.value;\n  }, [modeSet, modeState, modeOptionList]);\n  // ======================= Effect =======================\n  // Dynamic update mode when color change\n  React.useEffect(() => {\n    setModeState(postColor.isGradient() ? 'gradient' : 'single');\n  }, [postColor]);\n  // ======================= Return =======================\n  return [postColor, setColor, postMode, setModeState, modeOptionList];\n}"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAIe,SAAS,aAAa,YAAY,EAAE,KAAK,EAAE,IAAI;IAC5D,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,yLAAA,CAAA,YAAS,AAAD,EAAE;IAC3B,yDAAyD;IACzD,QAAQ;IACR,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAc,AAAD,EAAE,cAAc;QACjE;IACF;IACA,OAAO;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,qMAAA,CAAA,WAAc,CAAC;IACjD,MAAM,CAAC,gBAAgB,QAAQ,GAAG,qMAAA,CAAA,UAAa,CAAC;QAC9C,MAAM,OAAO,CAAC,MAAM,OAAO,CAAC,QAAQ,OAAO;YAAC;SAAK,EAAE,MAAM,CAAC,CAAA,IAAK;QAC/D,IAAI,CAAC,KAAK,MAAM,EAAE;YAChB,KAAK,IAAI,CAAC;QACZ;QACA,MAAM,QAAQ,IAAI,IAAI;QACtB,MAAM,aAAa,EAAE;QACrB,MAAM,aAAa,CAAC,UAAU;YAC5B,IAAI,MAAM,GAAG,CAAC,WAAW;gBACvB,WAAW,IAAI,CAAC;oBACd,OAAO;oBACP,OAAO;gBACT;YACF;QACF;QACA,WAAW,UAAU,OAAO,WAAW;QACvC,WAAW,YAAY,OAAO,aAAa;QAC3C,OAAO;YAAC;YAAY;SAAM;IAC5B,GAAG;QAAC;KAAK;IACT,yDAAyD;IACzD,0CAA0C;IAC1C,cAAc;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,WAAc,CAAC;IACnD,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAQ,AAAD,EAAE,CAAA;QACxB,cAAc;QACd,eAAe;IACjB;IACA,MAAM,YAAY,qMAAA,CAAA,UAAa,CAAC;QAC9B,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;QAC9C,kDAAkD;QAClD,OAAO,SAAS,MAAM,CAAC,cAAc,aAAa;IACpD,GAAG;QAAC;QAAa;KAAW;IAC5B,aAAa;IACb,MAAM,WAAW,qMAAA,CAAA,UAAa,CAAC;QAC7B,IAAI;QACJ,IAAI,QAAQ,GAAG,CAAC,YAAY;YAC1B,OAAO;QACT;QACA,OAAO,CAAC,KAAK,cAAc,CAAC,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK;IAC/E,GAAG;QAAC;QAAS;QAAW;KAAe;IACvC,yDAAyD;IACzD,wCAAwC;IACxC,qMAAA,CAAA,YAAe,CAAC;QACd,aAAa,UAAU,UAAU,KAAK,aAAa;IACrD,GAAG;QAAC;KAAU;IACd,yDAAyD;IACzD,OAAO;QAAC;QAAW;QAAU;QAAU;QAAc;KAAe;AACtE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/style/color-block.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\n/**\n * @private Internal usage only\n * see: https://developer.mozilla.org/en-US/docs/Web/CSS/gradient/conic-gradient#checkerboard\n */\nexport const getTransBg = (size, colorFill) => ({\n  backgroundImage: `conic-gradient(${colorFill} 25%, transparent 25% 50%, ${colorFill} 50% 75%, transparent 75% 100%)`,\n  backgroundSize: `${size} ${size}`\n});\nconst genColorBlockStyle = (token, size) => {\n  const {\n    componentCls,\n    borderRadiusSM,\n    colorPickerInsetShadow,\n    lineWidth,\n    colorFillSecondary\n  } = token;\n  return {\n    [`${componentCls}-color-block`]: Object.assign(Object.assign({\n      position: 'relative',\n      borderRadius: borderRadiusSM,\n      width: size,\n      height: size,\n      boxShadow: colorPickerInsetShadow,\n      flex: 'none'\n    }, getTransBg('50%', token.colorFillSecondary)), {\n      [`${componentCls}-color-block-inner`]: {\n        width: '100%',\n        height: '100%',\n        boxShadow: `inset 0 0 0 ${unit(lineWidth)} ${colorFillSecondary}`,\n        borderRadius: 'inherit'\n      }\n    })\n  };\n};\nexport default genColorBlockStyle;"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAKO,MAAM,aAAa,CAAC,MAAM,YAAc,CAAC;QAC9C,iBAAiB,CAAC,eAAe,EAAE,UAAU,2BAA2B,EAAE,UAAU,+BAA+B,CAAC;QACpH,gBAAgB,GAAG,KAAK,CAAC,EAAE,MAAM;IACnC,CAAC;AACD,MAAM,qBAAqB,CAAC,OAAO;IACjC,MAAM,EACJ,YAAY,EACZ,cAAc,EACd,sBAAsB,EACtB,SAAS,EACT,kBAAkB,EACnB,GAAG;IACJ,OAAO;QACL,CAAC,GAAG,aAAa,YAAY,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC;YAC3D,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,WAAW;YACX,MAAM;QACR,GAAG,WAAW,OAAO,MAAM,kBAAkB,IAAI;YAC/C,CAAC,GAAG,aAAa,kBAAkB,CAAC,CAAC,EAAE;gBACrC,OAAO;gBACP,QAAQ;gBACR,WAAW,CAAC,YAAY,EAAE,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,CAAC,EAAE,oBAAoB;gBACjE,cAAc;YAChB;QACF;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/style/input.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genInputStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    fontSizeSM,\n    lineHeightSM,\n    colorPickerAlphaInputWidth,\n    marginXXS,\n    paddingXXS,\n    controlHeightSM,\n    marginXS,\n    fontSizeIcon,\n    paddingXS,\n    colorTextPlaceholder,\n    colorPickerInputNumberHandleWidth,\n    lineWidth\n  } = token;\n  return {\n    [`${componentCls}-input-container`]: {\n      display: 'flex',\n      [`${componentCls}-steppers${antCls}-input-number`]: {\n        fontSize: fontSizeSM,\n        lineHeight: lineHeightSM,\n        [`${antCls}-input-number-input`]: {\n          paddingInlineStart: paddingXXS,\n          paddingInlineEnd: 0\n        },\n        [`${antCls}-input-number-handler-wrap`]: {\n          width: colorPickerInputNumberHandleWidth\n        }\n      },\n      [`${componentCls}-steppers${componentCls}-alpha-input`]: {\n        flex: `0 0 ${unit(colorPickerAlphaInputWidth)}`,\n        marginInlineStart: marginXXS\n      },\n      [`${componentCls}-format-select${antCls}-select`]: {\n        marginInlineEnd: marginXS,\n        width: 'auto',\n        '&-single': {\n          [`${antCls}-select-selector`]: {\n            padding: 0,\n            border: 0\n          },\n          [`${antCls}-select-arrow`]: {\n            insetInlineEnd: 0\n          },\n          [`${antCls}-select-selection-item`]: {\n            paddingInlineEnd: token.calc(fontSizeIcon).add(marginXXS).equal(),\n            fontSize: fontSizeSM,\n            lineHeight: unit(controlHeightSM)\n          },\n          [`${antCls}-select-item-option-content`]: {\n            fontSize: fontSizeSM,\n            lineHeight: lineHeightSM\n          },\n          [`${antCls}-select-dropdown`]: {\n            [`${antCls}-select-item`]: {\n              minHeight: 'auto'\n            }\n          }\n        }\n      },\n      [`${componentCls}-input`]: {\n        gap: marginXXS,\n        alignItems: 'center',\n        flex: 1,\n        width: 0,\n        [`${componentCls}-hsb-input,${componentCls}-rgb-input`]: {\n          display: 'flex',\n          gap: marginXXS,\n          alignItems: 'center'\n        },\n        [`${componentCls}-steppers`]: {\n          flex: 1\n        },\n        [`${componentCls}-hex-input${antCls}-input-affix-wrapper`]: {\n          flex: 1,\n          padding: `0 ${unit(paddingXS)}`,\n          [`${antCls}-input`]: {\n            fontSize: fontSizeSM,\n            textTransform: 'uppercase',\n            lineHeight: unit(token.calc(controlHeightSM).sub(token.calc(lineWidth).mul(2)).equal())\n          },\n          [`${antCls}-input-prefix`]: {\n            color: colorTextPlaceholder\n          }\n        }\n      }\n    }\n  };\n};\nexport default genInputStyle;"], "names": [], "mappings": ";;;AAAA;AAAA;;AACA,MAAM,gBAAgB,CAAA;IACpB,MAAM,EACJ,YAAY,EACZ,MAAM,EACN,UAAU,EACV,YAAY,EACZ,0BAA0B,EAC1B,SAAS,EACT,UAAU,EACV,eAAe,EACf,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,oBAAoB,EACpB,iCAAiC,EACjC,SAAS,EACV,GAAG;IACJ,OAAO;QACL,CAAC,GAAG,aAAa,gBAAgB,CAAC,CAAC,EAAE;YACnC,SAAS;YACT,CAAC,GAAG,aAAa,SAAS,EAAE,OAAO,aAAa,CAAC,CAAC,EAAE;gBAClD,UAAU;gBACV,YAAY;gBACZ,CAAC,GAAG,OAAO,mBAAmB,CAAC,CAAC,EAAE;oBAChC,oBAAoB;oBACpB,kBAAkB;gBACpB;gBACA,CAAC,GAAG,OAAO,0BAA0B,CAAC,CAAC,EAAE;oBACvC,OAAO;gBACT;YACF;YACA,CAAC,GAAG,aAAa,SAAS,EAAE,aAAa,YAAY,CAAC,CAAC,EAAE;gBACvD,MAAM,CAAC,IAAI,EAAE,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,6BAA6B;gBAC/C,mBAAmB;YACrB;YACA,CAAC,GAAG,aAAa,cAAc,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE;gBACjD,iBAAiB;gBACjB,OAAO;gBACP,YAAY;oBACV,CAAC,GAAG,OAAO,gBAAgB,CAAC,CAAC,EAAE;wBAC7B,SAAS;wBACT,QAAQ;oBACV;oBACA,CAAC,GAAG,OAAO,aAAa,CAAC,CAAC,EAAE;wBAC1B,gBAAgB;oBAClB;oBACA,CAAC,GAAG,OAAO,sBAAsB,CAAC,CAAC,EAAE;wBACnC,kBAAkB,MAAM,IAAI,CAAC,cAAc,GAAG,CAAC,WAAW,KAAK;wBAC/D,UAAU;wBACV,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;oBACnB;oBACA,CAAC,GAAG,OAAO,2BAA2B,CAAC,CAAC,EAAE;wBACxC,UAAU;wBACV,YAAY;oBACd;oBACA,CAAC,GAAG,OAAO,gBAAgB,CAAC,CAAC,EAAE;wBAC7B,CAAC,GAAG,OAAO,YAAY,CAAC,CAAC,EAAE;4BACzB,WAAW;wBACb;oBACF;gBACF;YACF;YACA,CAAC,GAAG,aAAa,MAAM,CAAC,CAAC,EAAE;gBACzB,KAAK;gBACL,YAAY;gBACZ,MAAM;gBACN,OAAO;gBACP,CAAC,GAAG,aAAa,WAAW,EAAE,aAAa,UAAU,CAAC,CAAC,EAAE;oBACvD,SAAS;oBACT,KAAK;oBACL,YAAY;gBACd;gBACA,CAAC,GAAG,aAAa,SAAS,CAAC,CAAC,EAAE;oBAC5B,MAAM;gBACR;gBACA,CAAC,GAAG,aAAa,UAAU,EAAE,OAAO,oBAAoB,CAAC,CAAC,EAAE;oBAC1D,MAAM;oBACN,SAAS,CAAC,EAAE,EAAE,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY;oBAC/B,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,EAAE;wBACnB,UAAU;wBACV,eAAe;wBACf,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,IAAI,CAAC,iBAAiB,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,KAAK;oBACtF;oBACA,CAAC,GAAG,OAAO,aAAa,CAAC,CAAC,EAAE;wBAC1B,OAAO;oBACT;gBACF;YACF;QACF;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/style/picker.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genPickerStyle = token => {\n  const {\n    componentCls,\n    controlHeightLG,\n    borderRadiusSM,\n    colorPickerInsetShadow,\n    marginSM,\n    colorBgElevated,\n    colorFillSecondary,\n    lineWidthBold,\n    colorPickerHandlerSize\n  } = token;\n  return {\n    userSelect: 'none',\n    [`${componentCls}-select`]: {\n      [`${componentCls}-palette`]: {\n        minHeight: token.calc(controlHeightLG).mul(4).equal(),\n        overflow: 'hidden',\n        borderRadius: borderRadiusSM\n      },\n      [`${componentCls}-saturation`]: {\n        position: 'absolute',\n        borderRadius: 'inherit',\n        boxShadow: colorPickerInsetShadow,\n        inset: 0\n      },\n      marginBottom: marginSM\n    },\n    // ======================== Panel =========================\n    [`${componentCls}-handler`]: {\n      width: colorPickerHandlerSize,\n      height: colorPickerHandlerSize,\n      border: `${unit(lineWidthBold)} solid ${colorBgElevated}`,\n      position: 'relative',\n      borderRadius: '50%',\n      cursor: 'pointer',\n      boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${colorFillSecondary}`\n    }\n  };\n};\nexport default genPickerStyle;"], "names": [], "mappings": ";;;AAAA;AAAA;;AACA,MAAM,iBAAiB,CAAA;IACrB,MAAM,EACJ,YAAY,EACZ,eAAe,EACf,cAAc,EACd,sBAAsB,EACtB,QAAQ,EACR,eAAe,EACf,kBAAkB,EAClB,aAAa,EACb,sBAAsB,EACvB,GAAG;IACJ,OAAO;QACL,YAAY;QACZ,CAAC,GAAG,aAAa,OAAO,CAAC,CAAC,EAAE;YAC1B,CAAC,GAAG,aAAa,QAAQ,CAAC,CAAC,EAAE;gBAC3B,WAAW,MAAM,IAAI,CAAC,iBAAiB,GAAG,CAAC,GAAG,KAAK;gBACnD,UAAU;gBACV,cAAc;YAChB;YACA,CAAC,GAAG,aAAa,WAAW,CAAC,CAAC,EAAE;gBAC9B,UAAU;gBACV,cAAc;gBACd,WAAW;gBACX,OAAO;YACT;YACA,cAAc;QAChB;QACA,2DAA2D;QAC3D,CAAC,GAAG,aAAa,QAAQ,CAAC,CAAC,EAAE;YAC3B,OAAO;YACP,QAAQ;YACR,QAAQ,GAAG,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,EAAE,iBAAiB;YACzD,UAAU;YACV,cAAc;YACd,QAAQ;YACR,WAAW,GAAG,uBAAuB,YAAY,EAAE,oBAAoB;QACzE;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1596, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/style/presets.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genPresetsStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    colorTextQuaternary,\n    paddingXXS,\n    colorPickerPresetColorSize,\n    fontSizeSM,\n    colorText,\n    lineHeightSM,\n    lineWidth,\n    borderRadius,\n    colorFill,\n    colorWhite,\n    marginXXS,\n    paddingXS,\n    fontHeightSM\n  } = token;\n  return {\n    [`${componentCls}-presets`]: {\n      [`${antCls}-collapse-item > ${antCls}-collapse-header`]: {\n        padding: 0,\n        [`${antCls}-collapse-expand-icon`]: {\n          height: fontHeightSM,\n          color: colorTextQuaternary,\n          paddingInlineEnd: paddingXXS\n        }\n      },\n      [`${antCls}-collapse`]: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: marginXXS\n      },\n      [`${antCls}-collapse-item > ${antCls}-collapse-content > ${antCls}-collapse-content-box`]: {\n        padding: `${unit(paddingXS)} 0`\n      },\n      '&-label': {\n        fontSize: fontSizeSM,\n        color: colorText,\n        lineHeight: lineHeightSM\n      },\n      '&-items': {\n        display: 'flex',\n        flexWrap: 'wrap',\n        gap: token.calc(marginXXS).mul(1.5).equal(),\n        [`${componentCls}-presets-color`]: {\n          position: 'relative',\n          cursor: 'pointer',\n          width: colorPickerPresetColorSize,\n          height: colorPickerPresetColorSize,\n          '&::before': {\n            content: '\"\"',\n            pointerEvents: 'none',\n            width: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),\n            height: token.calc(colorPickerPresetColorSize).add(token.calc(lineWidth).mul(4)).equal(),\n            position: 'absolute',\n            top: token.calc(lineWidth).mul(-2).equal(),\n            insetInlineStart: token.calc(lineWidth).mul(-2).equal(),\n            borderRadius,\n            border: `${unit(lineWidth)} solid transparent`,\n            transition: `border-color ${token.motionDurationMid} ${token.motionEaseInBack}`\n          },\n          '&:hover::before': {\n            borderColor: colorFill\n          },\n          '&::after': {\n            boxSizing: 'border-box',\n            position: 'absolute',\n            top: '50%',\n            insetInlineStart: '21.5%',\n            display: 'table',\n            width: token.calc(colorPickerPresetColorSize).div(13).mul(5).equal(),\n            height: token.calc(colorPickerPresetColorSize).div(13).mul(8).equal(),\n            border: `${unit(token.lineWidthBold)} solid ${token.colorWhite}`,\n            borderTop: 0,\n            borderInlineStart: 0,\n            transform: 'rotate(45deg) scale(0) translate(-50%,-50%)',\n            opacity: 0,\n            content: '\"\"',\n            transition: `all ${token.motionDurationFast} ${token.motionEaseInBack}, opacity ${token.motionDurationFast}`\n          },\n          [`&${componentCls}-presets-color-checked`]: {\n            '&::after': {\n              opacity: 1,\n              borderColor: colorWhite,\n              transform: 'rotate(45deg) scale(1) translate(-50%,-50%)',\n              transition: `transform ${token.motionDurationMid} ${token.motionEaseOutBack} ${token.motionDurationFast}`\n            },\n            [`&${componentCls}-presets-color-bright`]: {\n              '&::after': {\n                borderColor: 'rgba(0, 0, 0, 0.45)'\n              }\n            }\n          }\n        }\n      },\n      '&-empty': {\n        fontSize: fontSizeSM,\n        color: colorTextQuaternary\n      }\n    }\n  };\n};\nexport default genPresetsStyle;"], "names": [], "mappings": ";;;AAAA;AAAA;;AACA,MAAM,kBAAkB,CAAA;IACtB,MAAM,EACJ,YAAY,EACZ,MAAM,EACN,mBAAmB,EACnB,UAAU,EACV,0BAA0B,EAC1B,UAAU,EACV,SAAS,EACT,YAAY,EACZ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,UAAU,EACV,SAAS,EACT,SAAS,EACT,YAAY,EACb,GAAG;IACJ,OAAO;QACL,CAAC,GAAG,aAAa,QAAQ,CAAC,CAAC,EAAE;YAC3B,CAAC,GAAG,OAAO,iBAAiB,EAAE,OAAO,gBAAgB,CAAC,CAAC,EAAE;gBACvD,SAAS;gBACT,CAAC,GAAG,OAAO,qBAAqB,CAAC,CAAC,EAAE;oBAClC,QAAQ;oBACR,OAAO;oBACP,kBAAkB;gBACpB;YACF;YACA,CAAC,GAAG,OAAO,SAAS,CAAC,CAAC,EAAE;gBACtB,SAAS;gBACT,eAAe;gBACf,KAAK;YACP;YACA,CAAC,GAAG,OAAO,iBAAiB,EAAE,OAAO,oBAAoB,EAAE,OAAO,qBAAqB,CAAC,CAAC,EAAE;gBACzF,SAAS,GAAG,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,EAAE,CAAC;YACjC;YACA,WAAW;gBACT,UAAU;gBACV,OAAO;gBACP,YAAY;YACd;YACA,WAAW;gBACT,SAAS;gBACT,UAAU;gBACV,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,CAAC,KAAK,KAAK;gBACzC,CAAC,GAAG,aAAa,cAAc,CAAC,CAAC,EAAE;oBACjC,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,QAAQ;oBACR,aAAa;wBACX,SAAS;wBACT,eAAe;wBACf,OAAO,MAAM,IAAI,CAAC,4BAA4B,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,KAAK;wBACrF,QAAQ,MAAM,IAAI,CAAC,4BAA4B,GAAG,CAAC,MAAM,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,KAAK;wBACtF,UAAU;wBACV,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK;wBACxC,kBAAkB,MAAM,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK;wBACrD;wBACA,QAAQ,GAAG,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,kBAAkB,CAAC;wBAC9C,YAAY,CAAC,aAAa,EAAE,MAAM,iBAAiB,CAAC,CAAC,EAAE,MAAM,gBAAgB,EAAE;oBACjF;oBACA,mBAAmB;wBACjB,aAAa;oBACf;oBACA,YAAY;wBACV,WAAW;wBACX,UAAU;wBACV,KAAK;wBACL,kBAAkB;wBAClB,SAAS;wBACT,OAAO,MAAM,IAAI,CAAC,4BAA4B,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK;wBAClE,QAAQ,MAAM,IAAI,CAAC,4BAA4B,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,KAAK;wBACnE,QAAQ,GAAG,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,aAAa,EAAE,OAAO,EAAE,MAAM,UAAU,EAAE;wBAChE,WAAW;wBACX,mBAAmB;wBACnB,WAAW;wBACX,SAAS;wBACT,SAAS;wBACT,YAAY,CAAC,IAAI,EAAE,MAAM,kBAAkB,CAAC,CAAC,EAAE,MAAM,gBAAgB,CAAC,UAAU,EAAE,MAAM,kBAAkB,EAAE;oBAC9G;oBACA,CAAC,CAAC,CAAC,EAAE,aAAa,sBAAsB,CAAC,CAAC,EAAE;wBAC1C,YAAY;4BACV,SAAS;4BACT,aAAa;4BACb,WAAW;4BACX,YAAY,CAAC,UAAU,EAAE,MAAM,iBAAiB,CAAC,CAAC,EAAE,MAAM,iBAAiB,CAAC,CAAC,EAAE,MAAM,kBAAkB,EAAE;wBAC3G;wBACA,CAAC,CAAC,CAAC,EAAE,aAAa,qBAAqB,CAAC,CAAC,EAAE;4BACzC,YAAY;gCACV,aAAa;4BACf;wBACF;oBACF;gBACF;YACF;YACA,WAAW;gBACT,UAAU;gBACV,OAAO;YACT;QACF;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1694, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/style/slider.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { getTransBg } from './color-block';\nconst genSliderStyle = token => {\n  const {\n    componentCls,\n    colorPickerInsetShadow,\n    colorBgElevated,\n    colorFillSecondary,\n    lineWidthBold,\n    colorPickerHandlerSizeSM,\n    colorPickerSliderHeight,\n    marginSM,\n    marginXS\n  } = token;\n  const handleInnerSize = token.calc(colorPickerHandlerSizeSM).sub(token.calc(lineWidthBold).mul(2).equal()).equal();\n  const handleHoverSize = token.calc(colorPickerHandlerSizeSM).add(token.calc(lineWidthBold).mul(2).equal()).equal();\n  const activeHandleStyle = {\n    '&:after': {\n      transform: 'scale(1)',\n      boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${token.colorPrimaryActive}`\n    }\n  };\n  return {\n    // ======================== Slider ========================\n    [`${componentCls}-slider`]: [getTransBg(unit(colorPickerSliderHeight), token.colorFillSecondary), {\n      margin: 0,\n      padding: 0,\n      height: colorPickerSliderHeight,\n      borderRadius: token.calc(colorPickerSliderHeight).div(2).equal(),\n      '&-rail': {\n        height: colorPickerSliderHeight,\n        borderRadius: token.calc(colorPickerSliderHeight).div(2).equal(),\n        boxShadow: colorPickerInsetShadow\n      },\n      [`& ${componentCls}-slider-handle`]: {\n        width: handleInnerSize,\n        height: handleInnerSize,\n        top: 0,\n        borderRadius: '100%',\n        '&:before': {\n          display: 'block',\n          position: 'absolute',\n          background: 'transparent',\n          left: {\n            _skip_check_: true,\n            value: '50%'\n          },\n          top: '50%',\n          transform: 'translate(-50%, -50%)',\n          width: handleHoverSize,\n          height: handleHoverSize,\n          borderRadius: '100%'\n        },\n        '&:after': {\n          width: colorPickerHandlerSizeSM,\n          height: colorPickerHandlerSizeSM,\n          border: `${unit(lineWidthBold)} solid ${colorBgElevated}`,\n          boxShadow: `${colorPickerInsetShadow}, 0 0 0 1px ${colorFillSecondary}`,\n          outline: 'none',\n          insetInlineStart: token.calc(lineWidthBold).mul(-1).equal(),\n          top: token.calc(lineWidthBold).mul(-1).equal(),\n          background: 'transparent',\n          transition: 'none'\n        },\n        '&:focus': activeHandleStyle\n      }\n    }],\n    // ======================== Layout ========================\n    [`${componentCls}-slider-container`]: {\n      display: 'flex',\n      gap: marginSM,\n      marginBottom: marginSM,\n      // Group\n      [`${componentCls}-slider-group`]: {\n        flex: 1,\n        flexDirection: 'column',\n        justifyContent: 'space-between',\n        display: 'flex',\n        '&-disabled-alpha': {\n          justifyContent: 'center'\n        }\n      }\n    },\n    [`${componentCls}-gradient-slider`]: {\n      marginBottom: marginXS,\n      [`& ${componentCls}-slider-handle`]: {\n        '&:after': {\n          transform: 'scale(0.8)'\n        },\n        '&-active, &:focus': activeHandleStyle\n      }\n    }\n  };\n};\nexport default genSliderStyle;"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AACA,MAAM,iBAAiB,CAAA;IACrB,MAAM,EACJ,YAAY,EACZ,sBAAsB,EACtB,eAAe,EACf,kBAAkB,EAClB,aAAa,EACb,wBAAwB,EACxB,uBAAuB,EACvB,QAAQ,EACR,QAAQ,EACT,GAAG;IACJ,MAAM,kBAAkB,MAAM,IAAI,CAAC,0BAA0B,GAAG,CAAC,MAAM,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK;IAChH,MAAM,kBAAkB,MAAM,IAAI,CAAC,0BAA0B,GAAG,CAAC,MAAM,IAAI,CAAC,eAAe,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK;IAChH,MAAM,oBAAoB;QACxB,WAAW;YACT,WAAW;YACX,WAAW,GAAG,uBAAuB,YAAY,EAAE,MAAM,kBAAkB,EAAE;QAC/E;IACF;IACA,OAAO;QACL,2DAA2D;QAC3D,CAAC,GAAG,aAAa,OAAO,CAAC,CAAC,EAAE;YAAC,CAAA,GAAA,wKAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,0BAA0B,MAAM,kBAAkB;YAAG;gBAChG,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,cAAc,MAAM,IAAI,CAAC,yBAAyB,GAAG,CAAC,GAAG,KAAK;gBAC9D,UAAU;oBACR,QAAQ;oBACR,cAAc,MAAM,IAAI,CAAC,yBAAyB,GAAG,CAAC,GAAG,KAAK;oBAC9D,WAAW;gBACb;gBACA,CAAC,CAAC,EAAE,EAAE,aAAa,cAAc,CAAC,CAAC,EAAE;oBACnC,OAAO;oBACP,QAAQ;oBACR,KAAK;oBACL,cAAc;oBACd,YAAY;wBACV,SAAS;wBACT,UAAU;wBACV,YAAY;wBACZ,MAAM;4BACJ,cAAc;4BACd,OAAO;wBACT;wBACA,KAAK;wBACL,WAAW;wBACX,OAAO;wBACP,QAAQ;wBACR,cAAc;oBAChB;oBACA,WAAW;wBACT,OAAO;wBACP,QAAQ;wBACR,QAAQ,GAAG,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,EAAE,iBAAiB;wBACzD,WAAW,GAAG,uBAAuB,YAAY,EAAE,oBAAoB;wBACvE,SAAS;wBACT,kBAAkB,MAAM,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,KAAK;wBACzD,KAAK,MAAM,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,KAAK;wBAC5C,YAAY;wBACZ,YAAY;oBACd;oBACA,WAAW;gBACb;YACF;SAAE;QACF,2DAA2D;QAC3D,CAAC,GAAG,aAAa,iBAAiB,CAAC,CAAC,EAAE;YACpC,SAAS;YACT,KAAK;YACL,cAAc;YACd,QAAQ;YACR,CAAC,GAAG,aAAa,aAAa,CAAC,CAAC,EAAE;gBAChC,MAAM;gBACN,eAAe;gBACf,gBAAgB;gBAChB,SAAS;gBACT,oBAAoB;oBAClB,gBAAgB;gBAClB;YACF;QACF;QACA,CAAC,GAAG,aAAa,gBAAgB,CAAC,CAAC,EAAE;YACnC,cAAc;YACd,CAAC,CAAC,EAAE,EAAE,aAAa,cAAc,CAAC,CAAC,EAAE;gBACnC,WAAW;oBACT,WAAW;gBACb;gBACA,qBAAqB;YACvB;QACF;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1792, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/style/index.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nimport { genCompactItemStyle } from '../../style/compact-item';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nimport genColorBlockStyle from './color-block';\nimport genInputStyle from './input';\nimport genPickerStyle from './picker';\nimport genPresetsStyle from './presets';\nimport genSliderStyle from './slider';\nexport const genActiveStyle = (token, borderColor, outlineColor) => ({\n  borderInlineEndWidth: token.lineWidth,\n  borderColor,\n  boxShadow: `0 0 0 ${unit(token.controlOutlineWidth)} ${outlineColor}`,\n  outline: 0\n});\nconst genRtlStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    '&-rtl': {\n      [`${componentCls}-presets-color`]: {\n        '&::after': {\n          direction: 'ltr'\n        }\n      },\n      [`${componentCls}-clear`]: {\n        '&::after': {\n          direction: 'ltr'\n        }\n      }\n    }\n  };\n};\nconst genClearStyle = (token, size, extraStyle) => {\n  const {\n    componentCls,\n    borderRadiusSM,\n    lineWidth,\n    colorSplit,\n    colorBorder,\n    red6\n  } = token;\n  return {\n    [`${componentCls}-clear`]: Object.assign(Object.assign({\n      width: size,\n      height: size,\n      borderRadius: borderRadiusSM,\n      border: `${unit(lineWidth)} solid ${colorSplit}`,\n      position: 'relative',\n      overflow: 'hidden',\n      cursor: 'inherit',\n      transition: `all ${token.motionDurationFast}`\n    }, extraStyle), {\n      '&::after': {\n        content: '\"\"',\n        position: 'absolute',\n        insetInlineEnd: token.calc(lineWidth).mul(-1).equal(),\n        top: token.calc(lineWidth).mul(-1).equal(),\n        display: 'block',\n        width: 40,\n        // maximum\n        height: 2,\n        // fixed\n        transformOrigin: `calc(100% - 1px) 1px`,\n        transform: 'rotate(-45deg)',\n        backgroundColor: red6\n      },\n      '&:hover': {\n        borderColor: colorBorder\n      }\n    })\n  };\n};\nconst genStatusStyle = token => {\n  const {\n    componentCls,\n    colorError,\n    colorWarning,\n    colorErrorHover,\n    colorWarningHover,\n    colorErrorOutline,\n    colorWarningOutline\n  } = token;\n  return {\n    [`&${componentCls}-status-error`]: {\n      borderColor: colorError,\n      '&:hover': {\n        borderColor: colorErrorHover\n      },\n      [`&${componentCls}-trigger-active`]: Object.assign({}, genActiveStyle(token, colorError, colorErrorOutline))\n    },\n    [`&${componentCls}-status-warning`]: {\n      borderColor: colorWarning,\n      '&:hover': {\n        borderColor: colorWarningHover\n      },\n      [`&${componentCls}-trigger-active`]: Object.assign({}, genActiveStyle(token, colorWarning, colorWarningOutline))\n    }\n  };\n};\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    controlHeightLG,\n    controlHeightSM,\n    controlHeight,\n    controlHeightXS,\n    borderRadius,\n    borderRadiusSM,\n    borderRadiusXS,\n    borderRadiusLG,\n    fontSizeLG\n  } = token;\n  return {\n    [`&${componentCls}-lg`]: {\n      minWidth: controlHeightLG,\n      minHeight: controlHeightLG,\n      borderRadius: borderRadiusLG,\n      [`${componentCls}-color-block, ${componentCls}-clear`]: {\n        width: controlHeight,\n        height: controlHeight,\n        borderRadius\n      },\n      [`${componentCls}-trigger-text`]: {\n        fontSize: fontSizeLG\n      }\n    },\n    [`&${componentCls}-sm`]: {\n      minWidth: controlHeightSM,\n      minHeight: controlHeightSM,\n      borderRadius: borderRadiusSM,\n      [`${componentCls}-color-block, ${componentCls}-clear`]: {\n        width: controlHeightXS,\n        height: controlHeightXS,\n        borderRadius: borderRadiusXS\n      },\n      [`${componentCls}-trigger-text`]: {\n        lineHeight: unit(controlHeightXS)\n      }\n    }\n  };\n};\nconst genColorPickerStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    colorPickerWidth,\n    colorPrimary,\n    motionDurationMid,\n    colorBgElevated,\n    colorTextDisabled,\n    colorText,\n    colorBgContainerDisabled,\n    borderRadius,\n    marginXS,\n    marginSM,\n    controlHeight,\n    controlHeightSM,\n    colorBgTextActive,\n    colorPickerPresetColorSize,\n    colorPickerPreviewSize,\n    lineWidth,\n    colorBorder,\n    paddingXXS,\n    fontSize,\n    colorPrimaryHover,\n    controlOutline\n  } = token;\n  return [{\n    [componentCls]: Object.assign({\n      [`${componentCls}-inner`]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({\n        '&-content': {\n          display: 'flex',\n          flexDirection: 'column',\n          width: colorPickerWidth,\n          [`& > ${antCls}-divider`]: {\n            margin: `${unit(marginSM)} 0 ${unit(marginXS)}`\n          }\n        },\n        [`${componentCls}-panel`]: Object.assign({}, genPickerStyle(token))\n      }, genSliderStyle(token)), genColorBlockStyle(token, colorPickerPreviewSize)), genInputStyle(token)), genPresetsStyle(token)), genClearStyle(token, colorPickerPresetColorSize, {\n        marginInlineStart: 'auto'\n      })), {\n        // Operation bar\n        [`${componentCls}-operation`]: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          marginBottom: marginXS\n        }\n      }),\n      '&-trigger': Object.assign(Object.assign(Object.assign(Object.assign({\n        minWidth: controlHeight,\n        minHeight: controlHeight,\n        borderRadius,\n        border: `${unit(lineWidth)} solid ${colorBorder}`,\n        cursor: 'pointer',\n        display: 'inline-flex',\n        alignItems: 'flex-start',\n        justifyContent: 'center',\n        transition: `all ${motionDurationMid}`,\n        background: colorBgElevated,\n        padding: token.calc(paddingXXS).sub(lineWidth).equal(),\n        [`${componentCls}-trigger-text`]: {\n          marginInlineStart: marginXS,\n          marginInlineEnd: token.calc(marginXS).sub(token.calc(paddingXXS).sub(lineWidth)).equal(),\n          fontSize,\n          color: colorText,\n          alignSelf: 'center',\n          '&-cell': {\n            '&:not(:last-child):after': {\n              content: '\", \"'\n            },\n            '&-inactive': {\n              color: colorTextDisabled\n            }\n          }\n        },\n        '&:hover': {\n          borderColor: colorPrimaryHover\n        },\n        [`&${componentCls}-trigger-active`]: Object.assign({}, genActiveStyle(token, colorPrimary, controlOutline)),\n        '&-disabled': {\n          color: colorTextDisabled,\n          background: colorBgContainerDisabled,\n          cursor: 'not-allowed',\n          '&:hover': {\n            borderColor: colorBgTextActive\n          },\n          [`${componentCls}-trigger-text`]: {\n            color: colorTextDisabled\n          }\n        }\n      }, genClearStyle(token, controlHeightSM)), genColorBlockStyle(token, controlHeightSM)), genStatusStyle(token)), genSizeStyle(token))\n    }, genRtlStyle(token))\n  }, genCompactItemStyle(token, {\n    focusElCls: `${componentCls}-trigger-active`\n  })];\n};\nexport default genStyleHooks('ColorPicker', token => {\n  const {\n    colorTextQuaternary,\n    marginSM\n  } = token;\n  const colorPickerSliderHeight = 8;\n  const colorPickerToken = mergeToken(token, {\n    colorPickerWidth: 234,\n    colorPickerHandlerSize: 16,\n    colorPickerHandlerSizeSM: 12,\n    colorPickerAlphaInputWidth: 44,\n    colorPickerInputNumberHandleWidth: 16,\n    colorPickerPresetColorSize: 24,\n    colorPickerInsetShadow: `inset 0 0 1px 0 ${colorTextQuaternary}`,\n    colorPickerSliderHeight,\n    colorPickerPreviewSize: token.calc(colorPickerSliderHeight).mul(2).add(marginSM).equal()\n  });\n  return [genColorPickerStyle(colorPickerToken)];\n});"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,MAAM,iBAAiB,CAAC,OAAO,aAAa,eAAiB,CAAC;QACnE,sBAAsB,MAAM,SAAS;QACrC;QACA,WAAW,CAAC,MAAM,EAAE,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,mBAAmB,EAAE,CAAC,EAAE,cAAc;QACrE,SAAS;IACX,CAAC;AACD,MAAM,cAAc,CAAA;IAClB,MAAM,EACJ,YAAY,EACb,GAAG;IACJ,OAAO;QACL,SAAS;YACP,CAAC,GAAG,aAAa,cAAc,CAAC,CAAC,EAAE;gBACjC,YAAY;oBACV,WAAW;gBACb;YACF;YACA,CAAC,GAAG,aAAa,MAAM,CAAC,CAAC,EAAE;gBACzB,YAAY;oBACV,WAAW;gBACb;YACF;QACF;IACF;AACF;AACA,MAAM,gBAAgB,CAAC,OAAO,MAAM;IAClC,MAAM,EACJ,YAAY,EACZ,cAAc,EACd,SAAS,EACT,UAAU,EACV,WAAW,EACX,IAAI,EACL,GAAG;IACJ,OAAO;QACL,CAAC,GAAG,aAAa,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC;YACrD,OAAO;YACP,QAAQ;YACR,cAAc;YACd,QAAQ,GAAG,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,EAAE,YAAY;YAChD,UAAU;YACV,UAAU;YACV,QAAQ;YACR,YAAY,CAAC,IAAI,EAAE,MAAM,kBAAkB,EAAE;QAC/C,GAAG,aAAa;YACd,YAAY;gBACV,SAAS;gBACT,UAAU;gBACV,gBAAgB,MAAM,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK;gBACnD,KAAK,MAAM,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,KAAK;gBACxC,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,iBAAiB,CAAC,oBAAoB,CAAC;gBACvC,WAAW;gBACX,iBAAiB;YACnB;YACA,WAAW;gBACT,aAAa;YACf;QACF;IACF;AACF;AACA,MAAM,iBAAiB,CAAA;IACrB,MAAM,EACJ,YAAY,EACZ,UAAU,EACV,YAAY,EACZ,eAAe,EACf,iBAAiB,EACjB,iBAAiB,EACjB,mBAAmB,EACpB,GAAG;IACJ,OAAO;QACL,CAAC,CAAC,CAAC,EAAE,aAAa,aAAa,CAAC,CAAC,EAAE;YACjC,aAAa;YACb,WAAW;gBACT,aAAa;YACf;YACA,CAAC,CAAC,CAAC,EAAE,aAAa,eAAe,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,eAAe,OAAO,YAAY;QAC3F;QACA,CAAC,CAAC,CAAC,EAAE,aAAa,eAAe,CAAC,CAAC,EAAE;YACnC,aAAa;YACb,WAAW;gBACT,aAAa;YACf;YACA,CAAC,CAAC,CAAC,EAAE,aAAa,eAAe,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,eAAe,OAAO,cAAc;QAC7F;IACF;AACF;AACA,MAAM,eAAe,CAAA;IACnB,MAAM,EACJ,YAAY,EACZ,eAAe,EACf,eAAe,EACf,aAAa,EACb,eAAe,EACf,YAAY,EACZ,cAAc,EACd,cAAc,EACd,cAAc,EACd,UAAU,EACX,GAAG;IACJ,OAAO;QACL,CAAC,CAAC,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC,EAAE;YACvB,UAAU;YACV,WAAW;YACX,cAAc;YACd,CAAC,GAAG,aAAa,cAAc,EAAE,aAAa,MAAM,CAAC,CAAC,EAAE;gBACtD,OAAO;gBACP,QAAQ;gBACR;YACF;YACA,CAAC,GAAG,aAAa,aAAa,CAAC,CAAC,EAAE;gBAChC,UAAU;YACZ;QACF;QACA,CAAC,CAAC,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC,EAAE;YACvB,UAAU;YACV,WAAW;YACX,cAAc;YACd,CAAC,GAAG,aAAa,cAAc,EAAE,aAAa,MAAM,CAAC,CAAC,EAAE;gBACtD,OAAO;gBACP,QAAQ;gBACR,cAAc;YAChB;YACA,CAAC,GAAG,aAAa,aAAa,CAAC,CAAC,EAAE;gBAChC,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;YACnB;QACF;IACF;AACF;AACA,MAAM,sBAAsB,CAAA;IAC1B,MAAM,EACJ,MAAM,EACN,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,eAAe,EACf,iBAAiB,EACjB,SAAS,EACT,wBAAwB,EACxB,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,eAAe,EACf,iBAAiB,EACjB,0BAA0B,EAC1B,sBAAsB,EACtB,SAAS,EACT,WAAW,EACX,UAAU,EACV,QAAQ,EACR,iBAAiB,EACjB,cAAc,EACf,GAAG;IACJ,OAAO;QAAC;YACN,CAAC,aAAa,EAAE,OAAO,MAAM,CAAC;gBAC5B,CAAC,GAAG,aAAa,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC;oBAC7G,aAAa;wBACX,SAAS;wBACT,eAAe;wBACf,OAAO;wBACP,CAAC,CAAC,IAAI,EAAE,OAAO,QAAQ,CAAC,CAAC,EAAE;4BACzB,QAAQ,GAAG,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,GAAG,EAAE,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW;wBACjD;oBACF;oBACA,CAAC,GAAG,aAAa,MAAM,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,CAAA,GAAA,gKAAA,CAAA,UAAc,AAAD,EAAE;gBAC9D,GAAG,CAAA,GAAA,gKAAA,CAAA,UAAc,AAAD,EAAE,SAAS,CAAA,GAAA,wKAAA,CAAA,UAAkB,AAAD,EAAE,OAAO,0BAA0B,CAAA,GAAA,+JAAA,CAAA,UAAa,AAAD,EAAE,SAAS,CAAA,GAAA,iKAAA,CAAA,UAAe,AAAD,EAAE,SAAS,cAAc,OAAO,4BAA4B;oBAC9K,mBAAmB;gBACrB,KAAK;oBACH,gBAAgB;oBAChB,CAAC,GAAG,aAAa,UAAU,CAAC,CAAC,EAAE;wBAC7B,SAAS;wBACT,gBAAgB;wBAChB,cAAc;oBAChB;gBACF;gBACA,aAAa,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC;oBACnE,UAAU;oBACV,WAAW;oBACX;oBACA,QAAQ,GAAG,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,EAAE,aAAa;oBACjD,QAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,YAAY,CAAC,IAAI,EAAE,mBAAmB;oBACtC,YAAY;oBACZ,SAAS,MAAM,IAAI,CAAC,YAAY,GAAG,CAAC,WAAW,KAAK;oBACpD,CAAC,GAAG,aAAa,aAAa,CAAC,CAAC,EAAE;wBAChC,mBAAmB;wBACnB,iBAAiB,MAAM,IAAI,CAAC,UAAU,GAAG,CAAC,MAAM,IAAI,CAAC,YAAY,GAAG,CAAC,YAAY,KAAK;wBACtF;wBACA,OAAO;wBACP,WAAW;wBACX,UAAU;4BACR,4BAA4B;gCAC1B,SAAS;4BACX;4BACA,cAAc;gCACZ,OAAO;4BACT;wBACF;oBACF;oBACA,WAAW;wBACT,aAAa;oBACf;oBACA,CAAC,CAAC,CAAC,EAAE,aAAa,eAAe,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,GAAG,eAAe,OAAO,cAAc;oBAC3F,cAAc;wBACZ,OAAO;wBACP,YAAY;wBACZ,QAAQ;wBACR,WAAW;4BACT,aAAa;wBACf;wBACA,CAAC,GAAG,aAAa,aAAa,CAAC,CAAC,EAAE;4BAChC,OAAO;wBACT;oBACF;gBACF,GAAG,cAAc,OAAO,mBAAmB,CAAA,GAAA,wKAAA,CAAA,UAAkB,AAAD,EAAE,OAAO,mBAAmB,eAAe,SAAS,aAAa;YAC/H,GAAG,YAAY;QACjB;QAAG,CAAA,GAAA,sJAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;YAC5B,YAAY,GAAG,aAAa,eAAe,CAAC;QAC9C;KAAG;AACL;uCACe,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,CAAA;IAC1C,MAAM,EACJ,mBAAmB,EACnB,QAAQ,EACT,GAAG;IACJ,MAAM,0BAA0B;IAChC,MAAM,mBAAmB,CAAA,GAAA,qNAAA,CAAA,aAAU,AAAD,EAAE,OAAO;QACzC,kBAAkB;QAClB,wBAAwB;QACxB,0BAA0B;QAC1B,4BAA4B;QAC5B,mCAAmC;QACnC,4BAA4B;QAC5B,wBAAwB,CAAC,gBAAgB,EAAE,qBAAqB;QAChE;QACA,wBAAwB,MAAM,IAAI,CAAC,yBAAyB,GAAG,CAAC,GAAG,GAAG,CAAC,UAAU,KAAK;IACxF;IACA,OAAO;QAAC,oBAAoB;KAAkB;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2017, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/ColorPicker.js"], "sourcesContent": ["\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React, { useContext, useMemo } from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport ContextIsolator from '../_util/ContextIsolator';\nimport genPurePanel from '../_util/PurePanel';\nimport { getStatusClassNames } from '../_util/statusUtils';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider/context';\nimport DisabledContext from '../config-provider/DisabledContext';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport { FormItemInputContext } from '../form/context';\nimport Popover from '../popover';\nimport { useCompactItemContext } from '../space/Compact';\nimport { AggregationColor } from './color';\nimport ColorPickerPanel from './ColorPickerPanel';\nimport ColorTrigger from './components/ColorTrigger';\nimport useModeColor from './hooks/useModeColor';\nimport useStyle from './style';\nimport { genAlphaColor, generateColor, getColorAlpha } from './util';\nconst ColorPicker = props => {\n  const {\n      mode,\n      value,\n      defaultValue,\n      format,\n      defaultFormat,\n      allowClear = false,\n      presets,\n      children,\n      trigger = 'click',\n      open,\n      disabled,\n      placement = 'bottomLeft',\n      arrow = true,\n      panelRender,\n      showText,\n      style,\n      className,\n      size: customizeSize,\n      rootClassName,\n      prefixCls: customizePrefixCls,\n      styles,\n      disabledAlpha = false,\n      onFormatChange,\n      onChange,\n      onClear,\n      onOpenChange,\n      onChangeComplete,\n      getPopupContainer,\n      autoAdjustOverflow = true,\n      destroyTooltipOnHide,\n      destroyOnHidden,\n      disabledFormat\n    } = props,\n    rest = __rest(props, [\"mode\", \"value\", \"defaultValue\", \"format\", \"defaultFormat\", \"allowClear\", \"presets\", \"children\", \"trigger\", \"open\", \"disabled\", \"placement\", \"arrow\", \"panelRender\", \"showText\", \"style\", \"className\", \"size\", \"rootClassName\", \"prefixCls\", \"styles\", \"disabledAlpha\", \"onFormatChange\", \"onChange\", \"onClear\", \"onOpenChange\", \"onChangeComplete\", \"getPopupContainer\", \"autoAdjustOverflow\", \"destroyTooltipOnHide\", \"destroyOnHidden\", \"disabledFormat\"]);\n  const {\n    getPrefixCls,\n    direction,\n    colorPicker\n  } = useContext(ConfigContext);\n  const contextDisabled = useContext(DisabledContext);\n  const mergedDisabled = disabled !== null && disabled !== void 0 ? disabled : contextDisabled;\n  const [popupOpen, setPopupOpen] = useMergedState(false, {\n    value: open,\n    postState: openData => !mergedDisabled && openData,\n    onChange: onOpenChange\n  });\n  const [formatValue, setFormatValue] = useMergedState(format, {\n    value: format,\n    defaultValue: defaultFormat,\n    onChange: onFormatChange\n  });\n  const prefixCls = getPrefixCls('color-picker', customizePrefixCls);\n  // ================== Value & Mode =================\n  const [mergedColor, setColor, modeState, setModeState, modeOptions] = useModeColor(defaultValue, value, mode);\n  const isAlphaColor = useMemo(() => getColorAlpha(mergedColor) < 100, [mergedColor]);\n  // ==================== Change =====================\n  // To enhance user experience, we cache the gradient color when switch from gradient to single\n  // If user not modify single color, we will use the cached gradient color.\n  const [cachedGradientColor, setCachedGradientColor] = React.useState(null);\n  const onInternalChangeComplete = color => {\n    if (onChangeComplete) {\n      let changeColor = generateColor(color);\n      // ignore alpha color\n      if (disabledAlpha && isAlphaColor) {\n        changeColor = genAlphaColor(color);\n      }\n      onChangeComplete(changeColor);\n    }\n  };\n  const onInternalChange = (data, changeFromPickerDrag) => {\n    let color = generateColor(data);\n    // ignore alpha color\n    if (disabledAlpha && isAlphaColor) {\n      color = genAlphaColor(color);\n    }\n    setColor(color);\n    setCachedGradientColor(null);\n    // Trigger change event\n    if (onChange) {\n      onChange(color, color.toCssString());\n    }\n    // Only for drag-and-drop color picking\n    if (!changeFromPickerDrag) {\n      onInternalChangeComplete(color);\n    }\n  };\n  // =================== Gradient ====================\n  const [activeIndex, setActiveIndex] = React.useState(0);\n  const [gradientDragging, setGradientDragging] = React.useState(false);\n  // Mode change should also trigger color change\n  const onInternalModeChange = newMode => {\n    setModeState(newMode);\n    if (newMode === 'single' && mergedColor.isGradient()) {\n      setActiveIndex(0);\n      onInternalChange(new AggregationColor(mergedColor.getColors()[0].color));\n      // Should after `onInternalChange` since it will clear the cached color\n      setCachedGradientColor(mergedColor);\n    } else if (newMode === 'gradient' && !mergedColor.isGradient()) {\n      const baseColor = isAlphaColor ? genAlphaColor(mergedColor) : mergedColor;\n      onInternalChange(new AggregationColor(cachedGradientColor || [{\n        percent: 0,\n        color: baseColor\n      }, {\n        percent: 100,\n        color: baseColor\n      }]));\n    }\n  };\n  // ================== Form Status ==================\n  const {\n    status: contextStatus\n  } = React.useContext(FormItemInputContext);\n  // ==================== Compact ====================\n  const {\n    compactSize,\n    compactItemClassnames\n  } = useCompactItemContext(prefixCls, direction);\n  // ===================== Style =====================\n  const mergedSize = useSize(ctx => {\n    var _a;\n    return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : compactSize) !== null && _a !== void 0 ? _a : ctx;\n  });\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const rtlCls = {\n    [`${prefixCls}-rtl`]: direction\n  };\n  const mergedRootCls = classNames(rootClassName, cssVarCls, rootCls, rtlCls);\n  const mergedCls = classNames(getStatusClassNames(prefixCls, contextStatus), {\n    [`${prefixCls}-sm`]: mergedSize === 'small',\n    [`${prefixCls}-lg`]: mergedSize === 'large'\n  }, compactItemClassnames, colorPicker === null || colorPicker === void 0 ? void 0 : colorPicker.className, mergedRootCls, className, hashId);\n  const mergedPopupCls = classNames(prefixCls, mergedRootCls);\n  // ===================== Warning ======================\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('ColorPicker');\n    process.env.NODE_ENV !== \"production\" ? warning(!(disabledAlpha && isAlphaColor), 'usage', '`disabledAlpha` will make the alpha to be 100% when use alpha color.') : void 0;\n  }\n  const popoverProps = {\n    open: popupOpen,\n    trigger,\n    placement,\n    arrow,\n    rootClassName,\n    getPopupContainer,\n    autoAdjustOverflow,\n    destroyOnHidden: destroyOnHidden !== null && destroyOnHidden !== void 0 ? destroyOnHidden : !!destroyTooltipOnHide\n  };\n  const mergedStyle = Object.assign(Object.assign({}, colorPicker === null || colorPicker === void 0 ? void 0 : colorPicker.style), style);\n  // ============================ zIndex ============================\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Popover, Object.assign({\n    style: styles === null || styles === void 0 ? void 0 : styles.popup,\n    styles: {\n      body: styles === null || styles === void 0 ? void 0 : styles.popupOverlayInner\n    },\n    onOpenChange: visible => {\n      if (!visible || !mergedDisabled) {\n        setPopupOpen(visible);\n      }\n    },\n    content: /*#__PURE__*/React.createElement(ContextIsolator, {\n      form: true\n    }, /*#__PURE__*/React.createElement(ColorPickerPanel, {\n      mode: modeState,\n      onModeChange: onInternalModeChange,\n      modeOptions: modeOptions,\n      prefixCls: prefixCls,\n      value: mergedColor,\n      allowClear: allowClear,\n      disabled: mergedDisabled,\n      disabledAlpha: disabledAlpha,\n      presets: presets,\n      panelRender: panelRender,\n      format: formatValue,\n      onFormatChange: setFormatValue,\n      onChange: onInternalChange,\n      onChangeComplete: onInternalChangeComplete,\n      onClear: onClear,\n      activeIndex: activeIndex,\n      onActive: setActiveIndex,\n      gradientDragging: gradientDragging,\n      onGradientDragging: setGradientDragging,\n      disabledFormat: disabledFormat\n    })),\n    classNames: {\n      root: mergedPopupCls\n    }\n  }, popoverProps), children || (/*#__PURE__*/React.createElement(ColorTrigger, Object.assign({\n    activeIndex: popupOpen ? activeIndex : -1,\n    open: popupOpen,\n    className: mergedCls,\n    style: mergedStyle,\n    prefixCls: prefixCls,\n    disabled: mergedDisabled,\n    showText: showText,\n    format: formatValue\n  }, rest, {\n    color: mergedColor\n  })))));\n};\nif (process.env.NODE_ENV !== 'production') {\n  ColorPicker.displayName = 'ColorPicker';\n}\nconst PurePanel = genPurePanel(ColorPicker, undefined, props => Object.assign(Object.assign({}, props), {\n  placement: 'bottom',\n  autoAdjustOverflow: false\n}), 'color-picker', /* istanbul ignore next */\nprefixCls => prefixCls);\nColorPicker._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nexport default ColorPicker;"], "names": [], "mappings": ";;;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA7BA;AAEA,IAAI,SAAS,4CAAQ,yCAAK,MAAM,IAAI,SAAU,CAAC,EAAE,CAAC;IAChD,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAChG,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YAAY,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QAC3I,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnG;IACA,OAAO;AACT;;;;;;;;;;;;;;;;;;;;;AAqBA,MAAM,cAAc,CAAA;IAClB,MAAM,EACF,IAAI,EACJ,KAAK,EACL,YAAY,EACZ,MAAM,EACN,aAAa,EACb,aAAa,KAAK,EAClB,OAAO,EACP,QAAQ,EACR,UAAU,OAAO,EACjB,IAAI,EACJ,QAAQ,EACR,YAAY,YAAY,EACxB,QAAQ,IAAI,EACZ,WAAW,EACX,QAAQ,EACR,KAAK,EACL,SAAS,EACT,MAAM,aAAa,EACnB,aAAa,EACb,WAAW,kBAAkB,EAC7B,MAAM,EACN,gBAAgB,KAAK,EACrB,cAAc,EACd,QAAQ,EACR,OAAO,EACP,YAAY,EACZ,gBAAgB,EAChB,iBAAiB,EACjB,qBAAqB,IAAI,EACzB,oBAAoB,EACpB,eAAe,EACf,cAAc,EACf,GAAG,OACJ,OAAO,OAAO,OAAO;QAAC;QAAQ;QAAS;QAAgB;QAAU;QAAiB;QAAc;QAAW;QAAY;QAAW;QAAQ;QAAY;QAAa;QAAS;QAAe;QAAY;QAAS;QAAa;QAAQ;QAAiB;QAAa;QAAU;QAAiB;QAAkB;QAAY;QAAW;QAAgB;QAAoB;QAAqB;QAAsB;QAAwB;QAAmB;KAAiB;IACpd,MAAM,EACJ,YAAY,EACZ,SAAS,EACT,WAAW,EACZ,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,2JAAA,CAAA,gBAAa;IAC5B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,mKAAA,CAAA,UAAe;IAClD,MAAM,iBAAiB,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAc,AAAD,EAAE,OAAO;QACtD,OAAO;QACP,WAAW,CAAA,WAAY,CAAC,kBAAkB;QAC1C,UAAU;IACZ;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAc,AAAD,EAAE,QAAQ;QAC3D,OAAO;QACP,cAAc;QACd,UAAU;IACZ;IACA,MAAM,YAAY,aAAa,gBAAgB;IAC/C,oDAAoD;IACpD,MAAM,CAAC,aAAa,UAAU,WAAW,cAAc,YAAY,GAAG,CAAA,GAAA,sKAAA,CAAA,UAAY,AAAD,EAAE,cAAc,OAAO;IACxG,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,KAAK;QAAC;KAAY;IAClF,oDAAoD;IACpD,8FAA8F;IAC9F,0EAA0E;IAC1E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrE,MAAM,2BAA2B,CAAA;QAC/B,IAAI,kBAAkB;YACpB,IAAI,cAAc,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;YAChC,qBAAqB;YACrB,IAAI,iBAAiB,cAAc;gBACjC,cAAc,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;YAC9B;YACA,iBAAiB;QACnB;IACF;IACA,MAAM,mBAAmB,CAAC,MAAM;QAC9B,IAAI,QAAQ,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,qBAAqB;QACrB,IAAI,iBAAiB,cAAc;YACjC,QAAQ,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE;QACxB;QACA,SAAS;QACT,uBAAuB;QACvB,uBAAuB;QACvB,IAAI,UAAU;YACZ,SAAS,OAAO,MAAM,WAAW;QACnC;QACA,uCAAuC;QACvC,IAAI,CAAC,sBAAsB;YACzB,yBAAyB;QAC3B;IACF;IACA,oDAAoD;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/D,+CAA+C;IAC/C,MAAM,uBAAuB,CAAA;QAC3B,aAAa;QACb,IAAI,YAAY,YAAY,YAAY,UAAU,IAAI;YACpD,eAAe;YACf,iBAAiB,IAAI,sJAAA,CAAA,mBAAgB,CAAC,YAAY,SAAS,EAAE,CAAC,EAAE,CAAC,KAAK;YACtE,uEAAuE;YACvE,uBAAuB;QACzB,OAAO,IAAI,YAAY,cAAc,CAAC,YAAY,UAAU,IAAI;YAC9D,MAAM,YAAY,eAAe,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;YAC9D,iBAAiB,IAAI,sJAAA,CAAA,mBAAgB,CAAC,uBAAuB;gBAAC;oBAC5D,SAAS;oBACT,OAAO;gBACT;gBAAG;oBACD,SAAS;oBACT,OAAO;gBACT;aAAE;QACJ;IACF;IACA,oDAAoD;IACpD,MAAM,EACJ,QAAQ,aAAa,EACtB,GAAG,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC,6IAAA,CAAA,uBAAoB;IACzC,oDAAoD;IACpD,MAAM,EACJ,WAAW,EACX,qBAAqB,EACtB,GAAG,CAAA,GAAA,8IAAA,CAAA,wBAAqB,AAAD,EAAE,WAAW;IACrC,oDAAoD;IACpD,MAAM,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,CAAA;QACzB,IAAI;QACJ,OAAO,CAAC,KAAK,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,gBAAgB,WAAW,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAClI;IACA,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,UAAY,AAAD,EAAE;IAC7B,MAAM,CAAC,YAAY,QAAQ,UAAU,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,WAAW;IAC5D,MAAM,SAAS;QACb,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC,EAAE;IACxB;IACA,MAAM,gBAAgB,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,eAAe,WAAW,SAAS;IACpE,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,gBAAgB;QAC1E,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,EAAE,eAAe;QACpC,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,EAAE,eAAe;IACtC,GAAG,uBAAuB,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,SAAS,EAAE,eAAe,WAAW;IACrI,MAAM,iBAAiB,CAAA,GAAA,mIAAA,CAAA,UAAU,AAAD,EAAE,WAAW;IAC7C,uDAAuD;IACvD,wCAA2C;QACzC,MAAM,UAAU,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE;QAC9B,uCAAwC,QAAQ,CAAC,CAAC,iBAAiB,YAAY,GAAG,SAAS,0EAA0E;IACvK;IACA,MAAM,eAAe;QACnB,MAAM;QACN;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,kBAAkB,CAAC,CAAC;IAChG;IACA,MAAM,cAAc,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,gBAAgB,QAAQ,gBAAgB,KAAK,IAAI,KAAK,IAAI,YAAY,KAAK,GAAG;IAClI,mEAAmE;IACnE,OAAO,WAAW,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,8IAAA,CAAA,UAAO,EAAE,OAAO,MAAM,CAAC;QACxE,OAAO,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,KAAK;QACnE,QAAQ;YACN,MAAM,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB;QAChF;QACA,cAAc,CAAA;YACZ,IAAI,CAAC,WAAW,CAAC,gBAAgB;gBAC/B,aAAa;YACf;QACF;QACA,SAAS,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,sJAAA,CAAA,UAAe,EAAE;YACzD,MAAM;QACR,GAAG,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iKAAA,CAAA,UAAgB,EAAE;YACpD,MAAM;YACN,cAAc;YACd,aAAa;YACb,WAAW;YACX,OAAO;YACP,YAAY;YACZ,UAAU;YACV,eAAe;YACf,SAAS;YACT,aAAa;YACb,QAAQ;YACR,gBAAgB;YAChB,UAAU;YACV,kBAAkB;YAClB,SAAS;YACT,aAAa;YACb,UAAU;YACV,kBAAkB;YAClB,oBAAoB;YACpB,gBAAgB;QAClB;QACA,YAAY;YACV,MAAM;QACR;IACF,GAAG,eAAe,YAAa,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,2KAAA,CAAA,UAAY,EAAE,OAAO,MAAM,CAAC;QAC1F,aAAa,YAAY,cAAc,CAAC;QACxC,MAAM;QACN,WAAW;QACX,OAAO;QACP,WAAW;QACX,UAAU;QACV,UAAU;QACV,QAAQ;IACV,GAAG,MAAM;QACP,OAAO;IACT;AACF;AACA,wCAA2C;IACzC,YAAY,WAAW,GAAG;AAC5B;AACA,MAAM,YAAY,CAAA,GAAA,gJAAA,CAAA,UAAY,AAAD,EAAE,aAAa,WAAW,CAAA,QAAS,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,QAAQ;QACtG,WAAW;QACX,oBAAoB;IACtB,IAAI,gBAAgB,wBAAwB,GAC5C,CAAA,YAAa;AACb,YAAY,sCAAsC,GAAG;uCACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2279, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/node_modules/antd/es/color-picker/index.js"], "sourcesContent": ["\"use client\";\n\nimport ColorPicker from './ColorPicker';\nexport default ColorPicker;"], "names": [], "mappings": ";;;AAEA;AAFA;;uCAGe,4JAAA,CAAA,UAAW", "ignoreList": [0], "debugId": null}}]}