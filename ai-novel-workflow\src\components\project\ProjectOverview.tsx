'use client';

import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  Modal, 
  Form, 
  Input, 
  Select, 
  InputNumber,
  message,
  Empty,
  Tag,
  Popconfirm
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  PlayCircleOutlined,
  FileTextOutlined,
  CalendarOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/store';
import type { Project } from '@/types';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const ProjectOverview: React.FC = () => {
  const { 
    projects, 
    currentProject,
    createProject, 
    updateProject, 
    deleteProject, 
    setCurrentProject,
    setActiveTab
  } = useAppStore();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [form] = Form.useForm();

  const handleCreateProject = () => {
    setEditingProject(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditProject = (project: Project) => {
    setEditingProject(project);
    form.setFieldsValue({
      name: project.name,
      description: project.description,
      genre: project.settings.genre,
      style: project.settings.style,
      targetWordCount: project.settings.targetWordCount,
      chapterCount: project.settings.chapterCount,
      language: project.settings.language,
    });
    setIsModalVisible(true);
  };

  const handleDeleteProject = (projectId: string) => {
    deleteProject(projectId);
    message.success('项目已删除');
  };

  const handleSelectProject = (project: Project) => {
    setCurrentProject(project.id);
    message.success(`已选择项目: ${project.name}`);
  };

  const handleStartWorkflow = (project: Project) => {
    setCurrentProject(project.id);
    setActiveTab('workflow');
    message.info(`正在打开项目 ${project.name} 的工作流编辑器`);
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingProject) {
        // 更新项目
        updateProject(editingProject.id, {
          name: values.name,
          description: values.description,
          settings: {
            genre: values.genre,
            style: values.style,
            targetWordCount: values.targetWordCount,
            chapterCount: values.chapterCount,
            language: values.language,
          }
        });
        message.success('项目已更新');
      } else {
        // 创建新项目
        createProject({
          name: values.name,
          description: values.description,
          status: 'draft',
          settings: {
            genre: values.genre,
            style: values.style,
            targetWordCount: values.targetWordCount,
            chapterCount: values.chapterCount,
            language: values.language,
          }
        });
        message.success('项目已创建');
      }
      
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
    setEditingProject(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'default';
      case 'in-progress': return 'processing';
      case 'completed': return 'success';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'draft': return '草稿';
      case 'in-progress': return '进行中';
      case 'completed': return '已完成';
      default: return '未知';
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Title level={2}>项目总览</Title>
          <Text type="secondary">管理您的AI小说创作项目</Text>
        </div>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={handleCreateProject}
        >
          创建新项目
        </Button>
      </div>

      {projects.length === 0 ? (
        <Card className="text-center py-12">
          <Empty
            description={
              <div>
                <Text type="secondary">还没有任何项目</Text>
                <br />
                <Text type="secondary">点击上方按钮创建您的第一个AI小说项目</Text>
              </div>
            }
          />
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => (
            <Card
              key={project.id}
              className={`hover:shadow-lg transition-shadow ${
                currentProject?.id === project.id ? 'ring-2 ring-blue-500' : ''
              }`}
              actions={[
                <Button 
                  key="select"
                  type="text" 
                  icon={<SettingOutlined />}
                  onClick={() => handleSelectProject(project)}
                >
                  选择
                </Button>,
                <Button 
                  key="workflow"
                  type="text" 
                  icon={<PlayCircleOutlined />}
                  onClick={() => handleStartWorkflow(project)}
                >
                  工作流
                </Button>,
                <Button 
                  key="edit"
                  type="text" 
                  icon={<EditOutlined />}
                  onClick={() => handleEditProject(project)}
                >
                  编辑
                </Button>,
                <Popconfirm
                  key="delete"
                  title="确定要删除这个项目吗？"
                  description="删除后将无法恢复，请谨慎操作。"
                  onConfirm={() => handleDeleteProject(project.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button 
                    type="text" 
                    danger
                    icon={<DeleteOutlined />}
                  >
                    删除
                  </Button>
                </Popconfirm>
              ]}
            >
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <Title level={4} className="mb-0">{project.name}</Title>
                  <Tag color={getStatusColor(project.status)}>
                    {getStatusText(project.status)}
                  </Tag>
                </div>
                
                <Paragraph 
                  type="secondary" 
                  ellipsis={{ rows: 2 }}
                  className="mb-3"
                >
                  {project.description}
                </Paragraph>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <Text type="secondary">类型:</Text>
                    <Text>{project.settings.genre}</Text>
                  </div>
                  <div className="flex items-center justify-between">
                    <Text type="secondary">风格:</Text>
                    <Text>{project.settings.style}</Text>
                  </div>
                  <div className="flex items-center justify-between">
                    <Text type="secondary">目标字数:</Text>
                    <Text>{project.settings.targetWordCount.toLocaleString()}</Text>
                  </div>
                  <div className="flex items-center justify-between">
                    <Text type="secondary">章节数:</Text>
                    <Text>{project.settings.chapterCount}</Text>
                  </div>
                </div>

                <div className="mt-4 pt-3 border-t border-gray-100">
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className="flex items-center">
                      <CalendarOutlined className="mr-1" />
                      创建: {new Date(project.createdAt).toLocaleDateString()}
                    </span>
                    <span className="flex items-center">
                      <FileTextOutlined className="mr-1" />
                      更新: {new Date(project.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* 创建/编辑项目模态框 */}
      <Modal
        title={editingProject ? '编辑项目' : '创建新项目'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        width={600}
        okText={editingProject ? '更新' : '创建'}
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            genre: '现代都市',
            style: '轻松幽默',
            targetWordCount: 100000,
            chapterCount: 50,
            language: 'zh-CN',
          }}
        >
          <Form.Item
            name="name"
            label="项目名称"
            rules={[{ required: true, message: '请输入项目名称' }]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="项目描述"
            rules={[{ required: true, message: '请输入项目描述' }]}
          >
            <Input.TextArea 
              rows={3} 
              placeholder="请简要描述您的小说项目..." 
            />
          </Form.Item>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="genre"
              label="小说类型"
              rules={[{ required: true, message: '请选择小说类型' }]}
            >
              <Select placeholder="请选择类型">
                <Option value="现代都市">现代都市</Option>
                <Option value="古代言情">古代言情</Option>
                <Option value="玄幻修仙">玄幻修仙</Option>
                <Option value="科幻未来">科幻未来</Option>
                <Option value="悬疑推理">悬疑推理</Option>
                <Option value="历史军事">历史军事</Option>
                <Option value="青春校园">青春校园</Option>
                <Option value="商战职场">商战职场</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="style"
              label="写作风格"
              rules={[{ required: true, message: '请选择写作风格' }]}
            >
              <Select placeholder="请选择风格">
                <Option value="轻松幽默">轻松幽默</Option>
                <Option value="深沉严肃">深沉严肃</Option>
                <Option value="浪漫温馨">浪漫温馨</Option>
                <Option value="紧张刺激">紧张刺激</Option>
                <Option value="文艺清新">文艺清新</Option>
                <Option value="热血激昂">热血激昂</Option>
              </Select>
            </Form.Item>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="targetWordCount"
              label="目标字数"
              rules={[{ required: true, message: '请输入目标字数' }]}
            >
              <InputNumber
                min={10000}
                max={1000000}
                step={10000}
                placeholder="100000"
                className="w-full"
                formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
              />
            </Form.Item>

            <Form.Item
              name="chapterCount"
              label="预计章节数"
              rules={[{ required: true, message: '请输入章节数' }]}
            >
              <InputNumber
                min={1}
                max={500}
                placeholder="50"
                className="w-full"
              />
            </Form.Item>
          </div>

          <Form.Item
            name="language"
            label="语言"
            rules={[{ required: true, message: '请选择语言' }]}
          >
            <Select placeholder="请选择语言">
              <Option value="zh-CN">简体中文</Option>
              <Option value="en-US">English</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProjectOverview;
