import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import ProductFilledSvg from "@ant-design/icons-svg/es/asn/ProductFilled";
import AntdIcon from "../components/AntdIcon";
var ProductFilled = function ProductFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: ProductFilledSvg
  }));
};

/**![product](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTYwIDE0NGgzMDRhMTYgMTYgMCAwMTE2IDE2djMwNGExNiAxNiAwIDAxLTE2IDE2SDE2MGExNiAxNiAwIDAxLTE2LTE2VjE2MGExNiAxNiAwIDAxMTYtMTZtNTY0LjMxLTI1LjMzbDE4MS4wMiAxODEuMDJhMTYgMTYgMCAwMTAgMjIuNjJMNzI0LjMxIDUwMy4zM2ExNiAxNiAwIDAxLTIyLjYyIDBMNTIwLjY3IDMyMi4zMWExNiAxNiAwIDAxMC0yMi42MmwxODEuMDItMTgxLjAyYTE2IDE2IDAgMDEyMi42MiAwTTE2MCA1NDRoMzA0YTE2IDE2IDAgMDExNiAxNnYzMDRhMTYgMTYgMCAwMS0xNiAxNkgxNjBhMTYgMTYgMCAwMS0xNi0xNlY1NjBhMTYgMTYgMCAwMTE2LTE2bTQwMCAwaDMwNGExNiAxNiAwIDAxMTYgMTZ2MzA0YTE2IDE2IDAgMDEtMTYgMTZINTYwYTE2IDE2IDAgMDEtMTYtMTZWNTYwYTE2IDE2IDAgMDExNi0xNiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(ProductFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'ProductFilled';
}
export default RefIcon;