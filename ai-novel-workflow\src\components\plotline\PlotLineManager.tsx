'use client';

import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tabs,
  List,
  Tag,
  Popconfirm,
  message,
  Row,
  Col,
  Timeline,
  Tooltip,
  Badge,
  Divider,
  Slider
} from 'antd';
import {
  BranchesOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  FireOutlined,
  HeartOutlined,
  ThunderboltOutlined,
  TeamOutlined,
  BookOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { useAppStore } from '@/store';
import type { PlotLine, PlotEvent, Conflict } from '@/types';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const PlotLineManager: React.FC = () => {
  const {
    currentProject,
    plotLines,
    addPlotLine,
    updatePlotLine,
    deletePlotLine,
    getPlotLines
  } = useAppStore();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isEventModalVisible, setIsEventModalVisible] = useState(false);
  const [isConflictModalVisible, setIsConflictModalVisible] = useState(false);
  const [editingPlotLine, setEditingPlotLine] = useState<PlotLine | null>(null);
  const [editingEvent, setEditingEvent] = useState<PlotEvent | null>(null);
  const [editingConflict, setEditingConflict] = useState<Conflict | null>(null);
  const [selectedPlotLine, setSelectedPlotLine] = useState<PlotLine | null>(null);
  const [activeTab, setActiveTab] = useState('plotlines');
  const [timelineFilter, setTimelineFilter] = useState<'all' | 'main' | 'subplot'>('all');
  const [form] = Form.useForm();
  const [eventForm] = Form.useForm();
  const [conflictForm] = Form.useForm();

  const projectPlotLines = currentProject ? getPlotLines(currentProject.id) : [];
  const filteredPlotLines = timelineFilter === 'all'
    ? projectPlotLines
    : projectPlotLines.filter(line => line.type === timelineFilter);

  // 获取所有事件并按时间排序
  const allEvents = projectPlotLines.flatMap(line =>
    line.timeline.map(event => ({ ...event, plotLineId: line.id, plotLineName: line.name }))
  ).sort((a, b) => a.timestamp - b.timestamp);

  const handleCreatePlotLine = () => {
    setEditingPlotLine(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditPlotLine = (plotLine: PlotLine) => {
    setEditingPlotLine(plotLine);
    form.setFieldsValue({
      name: plotLine.name,
      type: plotLine.type,
      description: plotLine.description,
      characters: plotLine.characters,
      resolution: plotLine.resolution,
    });
    setIsModalVisible(true);
  };

  const handleDeletePlotLine = (plotLineId: string) => {
    if (currentProject) {
      deletePlotLine(currentProject.id, plotLineId);
      message.success('故事线已删除');
      if (selectedPlotLine?.id === plotLineId) {
        setSelectedPlotLine(null);
      }
    }
  };

  const handleCreateEvent = (plotLine?: PlotLine) => {
    setEditingEvent(null);
    eventForm.resetFields();
    if (plotLine) {
      eventForm.setFieldsValue({ plotLineId: plotLine.id });
    }
    setIsEventModalVisible(true);
  };

  const handleCreateConflict = (plotLine?: PlotLine) => {
    setEditingConflict(null);
    conflictForm.resetFields();
    if (plotLine) {
      conflictForm.setFieldsValue({ plotLineId: plotLine.id });
    }
    setIsConflictModalVisible(true);
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'critical': return 'red';
      case 'major': return 'orange';
      case 'minor': return 'blue';
      default: return 'default';
    }
  };

  const getImportanceIcon = (importance: string) => {
    switch (importance) {
      case 'critical': return <FireOutlined className="text-red-500" />;
      case 'major': return <ExclamationCircleOutlined className="text-orange-500" />;
      case 'minor': return <ClockCircleOutlined className="text-blue-500" />;
      default: return <ClockCircleOutlined className="text-gray-500" />;
    }
  };

  const getConflictTypeIcon = (type: string) => {
    switch (type) {
      case 'internal': return <HeartOutlined className="text-pink-500" />;
      case 'external': return <ThunderboltOutlined className="text-yellow-500" />;
      case 'interpersonal': return <TeamOutlined className="text-blue-500" />;
      default: return <ExclamationCircleOutlined className="text-gray-500" />;
    }
  };

  const getConflictTypeText = (type: string) => {
    switch (type) {
      case 'internal': return '内心冲突';
      case 'external': return '外部冲突';
      case 'interpersonal': return '人际冲突';
      default: return '未知冲突';
    }
  };

  if (!currentProject) {
    return (
      <div className="p-8 text-center">
        <Title level={3}>请先选择或创建一个项目</Title>
        <Text type="secondary">
          您需要先在项目总览中创建或选择一个项目，然后才能管理故事线。
        </Text>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <Title level={2}>主线管理</Title>
          <Text type="secondary">管理故事主线和支线情节 - 项目: {currentProject.name}</Text>
        </div>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreatePlotLine}
          >
            创建故事线
          </Button>
        </Space>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'plotlines',
            label: (
              <span>
                <BranchesOutlined />
                故事线 ({projectPlotLines.length})
              </span>
            ),
            children: (
              <div>
                {projectPlotLines.length === 0 ? (
                  <Card className="text-center py-12">
                    <BranchesOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                    <div className="mt-4">
                      <Text type="secondary">还没有创建任何故事线</Text>
                      <br />
                      <Text type="secondary">点击上方按钮创建您的第一条故事线</Text>
                    </div>
                  </Card>
                ) : (
                  <Row gutter={[16, 16]}>
                    {projectPlotLines.map((plotLine) => (
                      <Col span={12} key={plotLine.id}>
                        <Card
                          className="hover:shadow-lg transition-shadow cursor-pointer"
                          onClick={() => setSelectedPlotLine(plotLine)}
                          actions={[
                            <Tooltip title="添加事件" key="event">
                              <ClockCircleOutlined onClick={(e) => {
                                e.stopPropagation();
                                handleCreateEvent(plotLine);
                              }} />
                            </Tooltip>,
                            <Tooltip title="添加冲突" key="conflict">
                              <ExclamationCircleOutlined onClick={(e) => {
                                e.stopPropagation();
                                handleCreateConflict(plotLine);
                              }} />
                            </Tooltip>,
                            <Tooltip title="编辑" key="edit">
                              <EditOutlined onClick={(e) => {
                                e.stopPropagation();
                                handleEditPlotLine(plotLine);
                              }} />
                            </Tooltip>,
                            <Popconfirm
                              key="delete"
                              title="确定要删除这条故事线吗？"
                              onConfirm={(e) => {
                                e?.stopPropagation();
                                handleDeletePlotLine(plotLine.id);
                              }}
                              okText="确定"
                              cancelText="取消"
                            >
                              <DeleteOutlined onClick={(e) => e.stopPropagation()} />
                            </Popconfirm>
                          ]}
                        >
                          <div className="mb-3">
                            <div className="flex items-center justify-between mb-2">
                              <Title level={5} className="mb-0">{plotLine.name}</Title>
                              <Tag color={plotLine.type === 'main' ? 'red' : 'blue'}>
                                {plotLine.type === 'main' ? '主线' : '支线'}
                              </Tag>
                            </div>

                            <Paragraph
                              ellipsis={{ rows: 2 }}
                              type="secondary"
                              className="mb-3"
                            >
                              {plotLine.description}
                            </Paragraph>

                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span className="flex items-center space-x-1">
                                  <ClockCircleOutlined />
                                  <Text type="secondary">事件</Text>
                                </span>
                                <Badge count={plotLine.timeline.length} showZero />
                              </div>

                              <div className="flex items-center justify-between text-sm">
                                <span className="flex items-center space-x-1">
                                  <ExclamationCircleOutlined />
                                  <Text type="secondary">冲突</Text>
                                </span>
                                <Badge count={plotLine.conflicts.length} showZero />
                              </div>

                              <div className="flex items-center justify-between text-sm">
                                <span className="flex items-center space-x-1">
                                  <TeamOutlined />
                                  <Text type="secondary">角色</Text>
                                </span>
                                <Badge count={plotLine.characters.length} showZero />
                              </div>
                            </div>

                            {plotLine.resolution && (
                              <div className="mt-3 pt-3 border-t border-gray-100">
                                <Text type="secondary" className="text-sm">
                                  <CheckCircleOutlined className="mr-1" />
                                  已设定结局
                                </Text>
                              </div>
                            )}
                          </div>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                )}
              </div>
            ),
          },
          {
            key: 'timeline',
            label: (
              <span>
                <ClockCircleOutlined />
                时间轴
              </span>
            ),
            children: (
              <div>
                <Card
                  title="故事时间轴"
                  extra={
                    <Space>
                      <Select
                        value={timelineFilter}
                        onChange={setTimelineFilter}
                        style={{ width: 120 }}
                      >
                        <Option value="all">全部</Option>
                        <Option value="main">主线</Option>
                        <Option value="subplot">支线</Option>
                      </Select>
                    </Space>
                  }
                >
                  {allEvents.length === 0 ? (
                    <div className="text-center py-8">
                      <ClockCircleOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                      <div className="mt-4">
                        <Text type="secondary">还没有任何事件</Text>
                        <br />
                        <Text type="secondary">在故事线中添加事件来构建时间轴</Text>
                      </div>
                    </div>
                  ) : (
                    <Timeline mode="left">
                      {allEvents.map((event, index) => (
                        <Timeline.Item
                          key={`${event.plotLineId}-${event.id}`}
                          dot={getImportanceIcon(event.importance)}
                          color={getImportanceColor(event.importance)}
                        >
                          <div className="pb-4">
                            <div className="flex items-center justify-between mb-2">
                              <Title level={5} className="mb-0">{event.title}</Title>
                              <Space>
                                <Tag color="blue">{event.plotLineName}</Tag>
                                <Tag color={getImportanceColor(event.importance)}>
                                  {event.importance === 'critical' ? '关键' :
                                   event.importance === 'major' ? '重要' : '次要'}
                                </Tag>
                              </Space>
                            </div>
                            <Paragraph type="secondary" className="mb-2">
                              {event.description}
                            </Paragraph>
                            {event.consequences.length > 0 && (
                              <div>
                                <Text strong className="text-sm">后果影响:</Text>
                                <ul className="mt-1 ml-4">
                                  {event.consequences.map((consequence, idx) => (
                                    <li key={idx} className="text-sm text-gray-600">
                                      {consequence}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </Timeline.Item>
                      ))}
                    </Timeline>
                  )}
                </Card>
              </div>
            ),
          },
          {
            key: 'conflicts',
            label: (
              <span>
                <ExclamationCircleOutlined />
                冲突分析
              </span>
            ),
            children: (
              <div>
                <Card title="冲突总览">
                  {projectPlotLines.length === 0 ? (
                    <div className="text-center py-8">
                      <ExclamationCircleOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
                      <div className="mt-4">
                        <Text type="secondary">还没有任何冲突</Text>
                        <br />
                        <Text type="secondary">在故事线中添加冲突来分析情节张力</Text>
                      </div>
                    </div>
                  ) : (
                    <Row gutter={[16, 16]}>
                      {projectPlotLines.map((plotLine) => (
                        plotLine.conflicts.map((conflict) => (
                          <Col span={8} key={conflict.id}>
                            <Card size="small">
                              <div className="mb-2">
                                <div className="flex items-center justify-between">
                                  <Text strong>{conflict.description}</Text>
                                  <div className="flex items-center space-x-1">
                                    {getConflictTypeIcon(conflict.type)}
                                    <Text type="secondary" className="text-xs">
                                      {getConflictTypeText(conflict.type)}
                                    </Text>
                                  </div>
                                </div>
                              </div>
                              <div className="mb-2">
                                <Text type="secondary" className="text-sm">
                                  故事线: {plotLine.name}
                                </Text>
                              </div>
                              {conflict.participants.length > 0 && (
                                <div className="mb-2">
                                  <Text type="secondary" className="text-xs">参与者:</Text>
                                  <div className="mt-1">
                                    {conflict.participants.map((participant, idx) => (
                                      <Tag key={idx} size="small">{participant}</Tag>
                                    ))}
                                  </div>
                                </div>
                              )}
                              {conflict.resolution && (
                                <div className="mt-2 pt-2 border-t border-gray-100">
                                  <Text type="secondary" className="text-xs">
                                    <CheckCircleOutlined className="mr-1" />
                                    已解决
                                  </Text>
                                </div>
                              )}
                            </Card>
                          </Col>
                        ))
                      ))}
                    </Row>
                  )}
                </Card>
              </div>
            ),
          },
        ]}
      />

      {/* 故事线详情 */}
      {selectedPlotLine && (
        <Card
          className="mt-6"
          title={`故事线详情 - ${selectedPlotLine.name}`}
          extra={
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => handleEditPlotLine(selectedPlotLine)}
            >
              编辑故事线
            </Button>
          }
        >
          <Row gutter={24}>
            <Col span={16}>
              <div className="space-y-4">
                <div>
                  <Text strong>故事线描述:</Text>
                  <Paragraph className="mt-2">{selectedPlotLine.description}</Paragraph>
                </div>

                {selectedPlotLine.resolution && (
                  <div>
                    <Text strong>结局设定:</Text>
                    <Paragraph className="mt-2">{selectedPlotLine.resolution}</Paragraph>
                  </div>
                )}

                {selectedPlotLine.timeline.length > 0 && (
                  <div>
                    <Text strong>关键事件:</Text>
                    <List
                      size="small"
                      className="mt-2"
                      dataSource={selectedPlotLine.timeline}
                      renderItem={(event) => (
                        <List.Item>
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center space-x-2">
                              {getImportanceIcon(event.importance)}
                              <Text>{event.title}</Text>
                            </div>
                            <Tag color={getImportanceColor(event.importance)}>
                              {event.importance === 'critical' ? '关键' :
                               event.importance === 'major' ? '重要' : '次要'}
                            </Tag>
                          </div>
                        </List.Item>
                      )}
                    />
                  </div>
                )}
              </div>
            </Col>
            <Col span={8}>
              <div className="space-y-4">
                <div>
                  <Text strong>故事线类型:</Text>
                  <div className="mt-1">
                    <Tag color={selectedPlotLine.type === 'main' ? 'red' : 'blue'}>
                      {selectedPlotLine.type === 'main' ? '主线' : '支线'}
                    </Tag>
                  </div>
                </div>

                <div>
                  <Text strong>涉及角色:</Text>
                  <div className="mt-2">
                    {selectedPlotLine.characters.map((character, index) => (
                      <Tag key={index}>{character}</Tag>
                    ))}
                  </div>
                </div>

                <div>
                  <Text strong>事件数量:</Text>
                  <div className="mt-1">
                    <Badge count={selectedPlotLine.timeline.length} showZero>
                      <Text>个事件</Text>
                    </Badge>
                  </div>
                </div>

                <div>
                  <Text strong>冲突数量:</Text>
                  <div className="mt-1">
                    <Badge count={selectedPlotLine.conflicts.length} showZero>
                      <Text>个冲突</Text>
                    </Badge>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Card>
      )}

      {/* 创建/编辑故事线模态框 */}
      <Modal
        title={editingPlotLine ? '编辑故事线' : '创建故事线'}
        open={isModalVisible}
        onOk={async () => {
          try {
            const values = await form.validateFields();

            if (editingPlotLine) {
              // 更新故事线
              if (currentProject) {
                updatePlotLine(currentProject.id, editingPlotLine.id, {
                  ...values,
                  timeline: editingPlotLine.timeline || [],
                  conflicts: editingPlotLine.conflicts || [],
                });
                message.success('故事线已更新');
              }
            } else {
              // 创建新故事线
              if (currentProject) {
                addPlotLine(currentProject.id, {
                  ...values,
                  timeline: [],
                  conflicts: [],
                });
                message.success('故事线已创建');
              }
            }

            setIsModalVisible(false);
            form.resetFields();
          } catch (error) {
            console.error('表单验证失败:', error);
          }
        }}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        width={800}
        okText={editingPlotLine ? '更新' : '创建'}
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item
                name="name"
                label="故事线名称"
                rules={[{ required: true, message: '请输入故事线名称' }]}
              >
                <Input placeholder="请输入故事线名称" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="type"
                label="故事线类型"
                rules={[{ required: true, message: '请选择故事线类型' }]}
              >
                <Select placeholder="请选择类型">
                  <Option value="main">主线</Option>
                  <Option value="subplot">支线</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="故事线描述"
            rules={[{ required: true, message: '请输入故事线描述' }]}
          >
            <TextArea
              rows={4}
              placeholder="请描述这条故事线的主要内容和发展..."
            />
          </Form.Item>

          <Form.Item
            name="characters"
            label="涉及角色"
            help="请输入参与这条故事线的角色名称"
          >
            <Select
              mode="tags"
              placeholder="请输入角色名称，按回车添加"
              tokenSeparators={[',']}
            />
          </Form.Item>

          <Form.Item name="resolution" label="结局设定">
            <TextArea
              rows={3}
              placeholder="请描述这条故事线的结局或解决方案..."
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 其他模态框占位符 */}
      <Modal
        title="添加事件"
        open={isEventModalVisible}
        onCancel={() => setIsEventModalVisible(false)}
        footer={null}
      >
        <div className="text-center py-8">
          <ClockCircleOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
          <div className="mt-4">
            <Text type="secondary">事件编辑功能开发中</Text>
          </div>
        </div>
      </Modal>

      <Modal
        title="添加冲突"
        open={isConflictModalVisible}
        onCancel={() => setIsConflictModalVisible(false)}
        footer={null}
      >
        <div className="text-center py-8">
          <ExclamationCircleOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
          <div className="mt-4">
            <Text type="secondary">冲突编辑功能开发中</Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default PlotLineManager;
