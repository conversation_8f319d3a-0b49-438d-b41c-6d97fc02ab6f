# AI小说生成工作流平台

一个基于Next.js + TypeScript的智能小说创作工作流管理平台，支持可视化工作流编辑和多站点内容管理。

## 🚀 项目特性

### 已实现功能

- ✅ **项目基础架构**
  - Next.js 15 + TypeScript + Tailwind CSS
  - Ant Design UI组件库集成
  - Zustand状态管理
  - 响应式布局设计

- ✅ **主布局系统**
  - 侧边栏导航菜单
  - 顶部工具栏
  - 主题切换（亮色/暗色）
  - 通知系统
  - 用户菜单

- ✅ **项目管理**
  - 项目创建、编辑、删除
  - 项目设置（类型、风格、字数、章节数）
  - 项目状态管理
  - 项目选择和切换

- ✅ **工作流编辑器基础**
  - 节点库面板
  - 工作流画布
  - 节点属性面板
  - 执行控制（开始、暂停、停止）
  - 执行状态显示
  - 🤖 **AI工作流助手** - 智能工作流生成和推荐

### 预定义节点类型

- 📝 **输入节点** - 用户参数输入
- 📚 **书名生成** - AI生成书名候选
- 📄 **详情生成** - 生成小说基础信息
- 👥 **角色创建** - AI生成角色设定
- 🌍 **世界观构建** - AI生成世界背景
- 📈 **主线规划** - AI生成主要故事线
- 📋 **大纲规划** - AI生成故事结构
- 🔢 **章节数量** - 用户指定章节总数
- 📝 **细纲生成** - 生成详细情节要点
- 📖 **章节生成** - AI生成具体章节内容
- ✨ **内容润色** - AI优化文本质量
- ✅ **一致性检查** - 检查内容一致性
- 📤 **输出节点** - 最终结果展示

### 管理站点 ✅ (已完成)

- ✅ **大纲管理** - 树形大纲结构、章节管理、进度跟踪
- ✅ **角色管理** - 角色卡片、关系图谱、角色模板
- ✅ **世界观管理** - 世界设定、分类系统、一致性检查
- ✅ **主线管理** - 故事线时间轴、支线管理、冲突分析
- ✅ **书名管理** - AI书名生成器、评分分析、收藏管理
- ✅ **文档管理** - 统一文档结构、版本控制、导入导出
- ✅ **提示词管理** - 提示词模板、性能分析、分类管理

### 🤖 AI工作流助手 ✨ (新功能)

AI工作流助手是一个智能化的工作流生成系统，能够根据用户需求自动创建最优的小说创作工作流。

#### 核心功能

- 🎯 **智能推荐** - 基于创作需求分析推荐最适合的工作流模板
- 🛠️ **自定义生成** - AI根据具体需求动态生成定制化工作流
- ⚡ **一键优化** - 分析现有工作流并提供优化建议
- 📊 **需求分析** - 多维度分析用户创作需求和经验水平

#### 预置模板

1. **快速小说生成** - 适合新手的简单工作流
   - 复杂度：简单 | 节点数：6个 | 预估时间：10-20分钟
   - 适用：短篇小说、新手创作者

2. **专业小说创作** - 完整的专业级工作流
   - 复杂度：复杂 | 节点数：12个 | 预估时间：1-2小时
   - 适用：长篇小说、专业创作者

3. **奇幻小说专用** - 专为奇幻类小说设计
   - 复杂度：中等 | 节点数：8个 | 预估时间：30-60分钟
   - 适用：奇幻、魔法、冒险题材

4. **言情小说工作流** - 专注情感描写的创作流程
   - 复杂度：中等 | 节点数：8个 | 预估时间：30-60分钟
   - 适用：言情、情感、关系题材

#### 使用方式

1. **进入工作流编辑器** - 点击左侧菜单"工作流编辑器"
2. **打开AI助手** - 点击"AI工作流助手"按钮
3. **选择模式** - 智能推荐/自定义生成/优化现有
4. **填写需求** - 输入小说类型、风格、长度等信息
5. **获取推荐** - AI分析需求并推荐最适合的工作流
6. **预览应用** - 查看工作流结构并一键应用

## 🛠️ 技术栈

- **前端框架**: Next.js 15 (App Router)
- **开发语言**: TypeScript
- **样式方案**: Tailwind CSS
- **UI组件库**: Ant Design 5.x
- **状态管理**: Zustand
- **工作流可视化**: @xyflow/react (计划集成)
- **图标库**: Ant Design Icons + Lucide React

## 📦 安装和运行

### 环境要求

- Node.js 18.0+
- npm 或 yarn

### 安装依赖

```bash
cd ai-novel-workflow
npm install
```

### 启动开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
npm start
```

## 🎯 使用指南

### 1. 创建项目

1. 点击右上角"创建新项目"按钮
2. 填写项目基本信息：
   - 项目名称
   - 项目描述
   - 小说类型（现代都市、古代言情、玄幻修仙等）
   - 写作风格（轻松幽默、深沉严肃等）
   - 目标字数
   - 预计章节数
3. 点击"创建"完成项目创建

### 2. 工作流编辑

1. 选择或创建项目后，进入"工作流编辑器"
2. 从左侧节点库中选择需要的节点类型
3. 点击节点添加到画布
4. 配置节点属性和连接关系
5. 点击"开始执行"运行工作流

### 3. 内容管理

- **大纲管理**: 管理故事结构和章节安排
- **角色管理**: 创建和管理角色设定
- **世界观管理**: 构建故事世界背景
- **主线管理**: 规划主要故事线和支线

## 📁 项目结构

```
ai-novel-workflow/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── layout.tsx       # 根布局
│   │   └── page.tsx         # 主页面
│   ├── components/          # React组件
│   │   ├── layout/          # 布局组件
│   │   ├── workflow/        # 工作流相关
│   │   ├── project/         # 项目管理
│   │   ├── character/       # 角色管理
│   │   ├── worldbuilding/   # 世界观管理
│   │   ├── outline/         # 大纲管理
│   │   ├── plotline/        # 主线管理
│   │   ├── title/           # 书名管理
│   │   ├── document/        # 文档管理
│   │   └── prompt/          # 提示词管理
│   ├── store/               # Zustand状态管理
│   │   └── index.ts         # 主store
│   └── types/               # TypeScript类型定义
│       └── index.ts         # 核心类型
├── public/                  # 静态资源
└── package.json            # 项目配置
```

## 🔄 开发计划

### 第一阶段 ✅ (已完成)
- [x] 项目初始化和基础架构
- [x] 核心状态管理和路由系统
- [x] UI组件库集成和主题系统
- [x] 基础布局和导航

### 第二阶段 ✅ (已完成)
- [x] 工作流编辑器基础框架
- [x] React Flow集成和节点系统
- [x] 拖拽编辑功能
- [x] 节点配置面板

### 第三阶段 ✅ (已完成)
- [x] 角色管理站点完整实现
- [x] 世界观管理站点
- [x] 大纲管理站点
- [x] 主线管理站点
- [x] 书名管理站点

### 第四阶段 ✅ (已完成)
- [x] 提示词管理系统
- [x] 文档管理系统
- [x] 基础功能完整实现
- [x] 测试和文档完善

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React框架
- [Ant Design](https://ant.design/) - UI组件库
- [Zustand](https://github.com/pmndrs/zustand) - 状态管理
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架
- [@xyflow/react](https://reactflow.dev/) - 工作流可视化

---

**注意**: 这是一个正在开发中的项目，部分功能仍在实现中。如有问题或建议，欢迎提交Issue。
