{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Layout, Menu, Button, Dropdown, Avatar, Badge, Space, Typography } from 'antd';\nimport { \n  MenuFoldOutlined, \n  MenuUnfoldOutlined,\n  BellOutlined,\n  SettingOutlined,\n  UserOutlined,\n  ProjectOutlined,\n  FileTextOutlined,\n  TeamOutlined,\n  GlobalOutlined,\n  BookOutlined,\n  BranchesOutlined,\n  ThunderboltOutlined,\n  FolderOutlined,\n  SunOutlined,\n  MoonOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { MenuProps } from 'antd';\n\nconst { Header, Sider, Content } = Layout;\nconst { Text } = Typography;\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout: React.FC<MainLayoutProps> = ({ children }) => {\n  const {\n    ui,\n    toggleSidebar,\n    setTheme,\n    setActiveTab,\n    currentProject\n  } = useAppStore();\n\n  const [collapsed, setCollapsed] = useState(ui.sidebarCollapsed);\n\n  const handleToggleSidebar = () => {\n    setCollapsed(!collapsed);\n    toggleSidebar();\n  };\n\n  const handleThemeToggle = () => {\n    setTheme(ui.theme === 'light' ? 'dark' : 'light');\n  };\n\n  // 侧边栏菜单项\n  const menuItems: MenuProps['items'] = [\n    {\n      key: 'workflow',\n      icon: <ThunderboltOutlined />,\n      label: '工作流编辑器',\n    },\n    {\n      key: 'projects',\n      icon: <ProjectOutlined />,\n      label: '项目总览',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'content-management',\n      icon: <FileTextOutlined />,\n      label: '内容管理',\n      children: [\n        {\n          key: 'outlines',\n          icon: <FileTextOutlined />,\n          label: '大纲管理',\n        },\n        {\n          key: 'characters',\n          icon: <TeamOutlined />,\n          label: '角色管理',\n        },\n        {\n          key: 'worldbuilding',\n          icon: <GlobalOutlined />,\n          label: '世界观管理',\n        },\n        {\n          key: 'plotlines',\n          icon: <BranchesOutlined />,\n          label: '主线管理',\n        },\n        {\n          key: 'titles',\n          icon: <BookOutlined />,\n          label: '书名管理',\n        },\n      ],\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'documents',\n      icon: <FolderOutlined />,\n      label: '文档管理',\n    },\n    {\n      key: 'prompts',\n      icon: <FileTextOutlined />,\n      label: '提示词管理',\n    },\n  ];\n\n  // 用户菜单\n  const userMenuItems: MenuProps['items'] = [\n    {\n      key: 'profile',\n      icon: <UserOutlined />,\n      label: '个人资料',\n    },\n    {\n      key: 'settings',\n      icon: <SettingOutlined />,\n      label: '设置',\n    },\n    {\n      type: 'divider',\n    },\n    {\n      key: 'logout',\n      label: '退出登录',\n    },\n  ];\n\n  // 通知菜单\n  const notifications = ui?.notifications || [];\n  const notificationMenuItems: MenuProps['items'] = notifications.slice(0, 5).map(notification => ({\n    key: notification.id,\n    label: (\n      <div className=\"max-w-xs\">\n        <div className=\"font-medium text-sm\">{notification.title}</div>\n        <div className=\"text-xs text-gray-500 mt-1\">{notification.message}</div>\n        <div className=\"text-xs text-gray-400 mt-1\">\n          {notification.timestamp.toLocaleTimeString()}\n        </div>\n      </div>\n    ),\n  }));\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    setActiveTab(key);\n  };\n\n  return (\n    <Layout className=\"min-h-screen\">\n      {/* 侧边栏 */}\n      <Sider \n        trigger={null} \n        collapsible \n        collapsed={collapsed}\n        width={240}\n        className=\"bg-white border-r border-gray-200\"\n        theme=\"light\"\n      >\n        {/* Logo区域 */}\n        <div className=\"h-16 flex items-center justify-center border-b border-gray-200\">\n          {collapsed ? (\n            <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n              <ThunderboltOutlined className=\"text-white text-lg\" />\n            </div>\n          ) : (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n                <ThunderboltOutlined className=\"text-white text-lg\" />\n              </div>\n              <Text strong className=\"text-lg\">AI小说工作流</Text>\n            </div>\n          )}\n        </div>\n\n        {/* 当前项目信息 */}\n        {!collapsed && currentProject && (\n          <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n            <Text type=\"secondary\" className=\"text-xs\">当前项目</Text>\n            <div className=\"mt-1\">\n              <Text strong className=\"text-sm\">{currentProject.name}</Text>\n            </div>\n            <div className=\"mt-1\">\n              <Text type=\"secondary\" className=\"text-xs\">\n                {currentProject.status === 'draft' && '草稿'}\n                {currentProject.status === 'in-progress' && '进行中'}\n                {currentProject.status === 'completed' && '已完成'}\n              </Text>\n            </div>\n          </div>\n        )}\n\n        {/* 菜单 */}\n        <Menu\n          mode=\"inline\"\n          selectedKeys={[ui.activeTab]}\n          items={menuItems}\n          onClick={handleMenuClick}\n          className=\"border-none\"\n        />\n      </Sider>\n\n      <Layout>\n        {/* 顶部导航栏 */}\n        <Header className=\"bg-white border-b border-gray-200 px-4 flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={handleToggleSidebar}\n              className=\"text-lg\"\n            />\n            \n            {/* 面包屑导航 */}\n            <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\n              <span>\n                {ui.activeTab === 'workflow' && '工作流编辑器'}\n                {ui.activeTab === 'projects' && '项目总览'}\n                {ui.activeTab === 'outlines' && '大纲管理'}\n                {ui.activeTab === 'characters' && '角色管理'}\n                {ui.activeTab === 'worldbuilding' && '世界观管理'}\n                {ui.activeTab === 'plotlines' && '主线管理'}\n                {ui.activeTab === 'titles' && '书名管理'}\n                {ui.activeTab === 'documents' && '文档管理'}\n                {ui.activeTab === 'prompts' && '提示词管理'}\n              </span>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {/* 主题切换 */}\n            <Button\n              type=\"text\"\n              icon={ui.theme === 'light' ? <MoonOutlined /> : <SunOutlined />}\n              onClick={handleThemeToggle}\n              className=\"text-lg\"\n            />\n\n            {/* 通知 */}\n            <Dropdown\n              menu={{ items: notificationMenuItems }}\n              trigger={['click']}\n              placement=\"bottomRight\"\n            >\n              <Button type=\"text\" className=\"text-lg\">\n                <Badge count={notifications.filter(n => !n.read).length} size=\"small\">\n                  <BellOutlined />\n                </Badge>\n              </Button>\n            </Dropdown>\n\n            {/* 用户菜单 */}\n            <Dropdown\n              menu={{ items: userMenuItems }}\n              trigger={['click']}\n              placement=\"bottomRight\"\n            >\n              <Space className=\"cursor-pointer\">\n                <Avatar icon={<UserOutlined />} />\n                <Text>用户</Text>\n              </Space>\n            </Dropdown>\n          </div>\n        </Header>\n\n        {/* 主内容区域 */}\n        <Content className=\"bg-gray-50 overflow-auto\">\n          {children}\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;;;AArBA;;;;;AAwBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzC,MAAM,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAM3B,MAAM,aAAwC;QAAC,EAAE,QAAQ,EAAE;;IACzD,MAAM,EACJ,EAAE,EACF,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,cAAc,EACf,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,gBAAgB;IAE9D,MAAM,sBAAsB;QAC1B,aAAa,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,SAAS,GAAG,KAAK,KAAK,UAAU,SAAS;IAC3C;IAEA,SAAS;IACT,MAAM,YAAgC;QACpC;YACE,KAAK;YACL,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;QACT;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;YACP,UAAU;gBACR;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;oBACvB,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;oBACnB,OAAO;gBACT;aACD;QACH;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;YACvB,OAAO;QACT;KACD;IAED,OAAO;IACP,MAAM,gBAAoC;QACxC;YACE,KAAK;YACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;QACT;QACA;YACE,KAAK;YACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;QACT;QACA;YACE,MAAM;QACR;QACA;YACE,KAAK;YACL,OAAO;QACT;KACD;IAED,OAAO;IACP,MAAM,gBAAgB,CAAA,eAAA,yBAAA,GAAI,aAAa,KAAI,EAAE;IAC7C,MAAM,wBAA4C,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,eAAgB,CAAC;YAC/F,KAAK,aAAa,EAAE;YACpB,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAuB,aAAa,KAAK;;;;;;kCACxD,6LAAC;wBAAI,WAAU;kCAA8B,aAAa,OAAO;;;;;;kCACjE,6LAAC;wBAAI,WAAU;kCACZ,aAAa,SAAS,CAAC,kBAAkB;;;;;;;;;;;;QAIlD,CAAC;IAED,MAAM,kBAAkB;YAAC,EAAE,GAAG,EAAmB;QAC/C,aAAa;IACf;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,WAAU;;0BAEhB,6LAAC;gBACC,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,WAAU;gBACV,OAAM;;kCAGN,6LAAC;wBAAI,WAAU;kCACZ,0BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;;;qFAGjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAEjC,6LAAC;oCAAK,MAAM;oCAAC,WAAU;8CAAU;;;;;;;;;;;;;;;;;oBAMtC,CAAC,aAAa,gCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,MAAK;gCAAY,WAAU;0CAAU;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,MAAM;oCAAC,WAAU;8CAAW,eAAe,IAAI;;;;;;;;;;;0CAEvD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,MAAK;oCAAY,WAAU;;wCAC9B,eAAe,MAAM,KAAK,WAAW;wCACrC,eAAe,MAAM,KAAK,iBAAiB;wCAC3C,eAAe,MAAM,KAAK,eAAe;;;;;;;;;;;;;;;;;;kCAOlD,6LAAC,iLAAA,CAAA,OAAI;wBACH,MAAK;wBACL,cAAc;4BAAC,GAAG,SAAS;yBAAC;wBAC5B,OAAO;wBACP,SAAS;wBACT,WAAU;;;;;;;;;;;;0BAId,6LAAC,qLAAA,CAAA,SAAM;;kCAEL,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,0BAAY,6LAAC,iOAAA,CAAA,qBAAkB;;;;mEAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAIZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;gDACE,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,cAAc;gDAC/B,GAAG,SAAS,KAAK,gBAAgB;gDACjC,GAAG,SAAS,KAAK,mBAAmB;gDACpC,GAAG,SAAS,KAAK,eAAe;gDAChC,GAAG,SAAS,KAAK,YAAY;gDAC7B,GAAG,SAAS,KAAK,eAAe;gDAChC,GAAG,SAAS,KAAK,aAAa;;;;;;;;;;;;;;;;;;0CAKrC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,qMAAA,CAAA,SAAM;wCACL,MAAK;wCACL,MAAM,GAAG,KAAK,KAAK,wBAAU,6LAAC,qNAAA,CAAA,eAAY;;;;mEAAM,6LAAC,mNAAA,CAAA,cAAW;;;;;wCAC5D,SAAS;wCACT,WAAU;;;;;;kDAIZ,6LAAC,yLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAsB;wCACrC,SAAS;4CAAC;yCAAQ;wCAClB,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,WAAU;sDAC5B,cAAA,6LAAC,mLAAA,CAAA,QAAK;gDAAC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;gDAAE,MAAK;0DAC5D,cAAA,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kDAMnB,6LAAC,yLAAA,CAAA,WAAQ;wCACP,MAAM;4CAAE,OAAO;wCAAc;wCAC7B,SAAS;4CAAC;yCAAQ;wCAClB,WAAU;kDAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,qLAAA,CAAA,SAAM;oDAAC,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;8DAC3B,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,6LAAC;wBAAQ,WAAU;kCAChB;;;;;;;;;;;;;;;;;;AAKX;GAtPM;;QAOA,wHAAA,CAAA,cAAW;;;KAPX;uCAwPS", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/project/ProjectOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Card, \n  Button, \n  Space, \n  Typography, \n  Modal, \n  Form, \n  Input, \n  Select, \n  InputNumber,\n  message,\n  Empty,\n  Tag,\n  Popconfirm\n} from 'antd';\nimport { \n  PlusOutlined, \n  EditOutlined, \n  DeleteOutlined,\n  PlayCircleOutlined,\n  FileTextOutlined,\n  CalendarOutlined,\n  SettingOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { Project } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\n\nconst ProjectOverview: React.FC = () => {\n  const { \n    projects, \n    currentProject,\n    createProject, \n    updateProject, \n    deleteProject, \n    setCurrentProject,\n    setActiveTab\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState<Project | null>(null);\n  const [form] = Form.useForm();\n\n  const handleCreateProject = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEditProject = (project: Project) => {\n    setEditingProject(project);\n    form.setFieldsValue({\n      name: project.name,\n      description: project.description,\n      genre: project.settings.genre,\n      style: project.settings.style,\n      targetWordCount: project.settings.targetWordCount,\n      chapterCount: project.settings.chapterCount,\n      language: project.settings.language,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteProject = (projectId: string) => {\n    deleteProject(projectId);\n    message.success('项目已删除');\n  };\n\n  const handleSelectProject = (project: Project) => {\n    setCurrentProject(project.id);\n    message.success(`已选择项目: ${project.name}`);\n  };\n\n  const handleStartWorkflow = (project: Project) => {\n    setCurrentProject(project.id);\n    setActiveTab('workflow');\n    message.info(`正在打开项目 ${project.name} 的工作流编辑器`);\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (editingProject) {\n        // 更新项目\n        updateProject(editingProject.id, {\n          name: values.name,\n          description: values.description,\n          settings: {\n            genre: values.genre,\n            style: values.style,\n            targetWordCount: values.targetWordCount,\n            chapterCount: values.chapterCount,\n            language: values.language,\n          }\n        });\n        message.success('项目已更新');\n      } else {\n        // 创建新项目\n        createProject({\n          name: values.name,\n          description: values.description,\n          status: 'draft',\n          settings: {\n            genre: values.genre,\n            style: values.style,\n            targetWordCount: values.targetWordCount,\n            chapterCount: values.chapterCount,\n            language: values.language,\n          }\n        });\n        message.success('项目已创建');\n      }\n      \n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n    setEditingProject(null);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'draft': return 'default';\n      case 'in-progress': return 'processing';\n      case 'completed': return 'success';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'draft': return '草稿';\n      case 'in-progress': return '进行中';\n      case 'completed': return '已完成';\n      default: return '未知';\n    }\n  };\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>项目总览</Title>\n          <Text type=\"secondary\">管理您的AI小说创作项目</Text>\n        </div>\n        <Button \n          type=\"primary\" \n          icon={<PlusOutlined />}\n          onClick={handleCreateProject}\n        >\n          创建新项目\n        </Button>\n      </div>\n\n      {projects.length === 0 ? (\n        <Card className=\"text-center py-12\">\n          <Empty\n            description={\n              <div>\n                <Text type=\"secondary\">还没有任何项目</Text>\n                <br />\n                <Text type=\"secondary\">点击上方按钮创建您的第一个AI小说项目</Text>\n              </div>\n            }\n          />\n        </Card>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {projects.map((project) => (\n            <Card\n              key={project.id}\n              className={`hover:shadow-lg transition-shadow ${\n                currentProject?.id === project.id ? 'ring-2 ring-blue-500' : ''\n              }`}\n              actions={[\n                <Button \n                  key=\"select\"\n                  type=\"text\" \n                  icon={<SettingOutlined />}\n                  onClick={() => handleSelectProject(project)}\n                >\n                  选择\n                </Button>,\n                <Button \n                  key=\"workflow\"\n                  type=\"text\" \n                  icon={<PlayCircleOutlined />}\n                  onClick={() => handleStartWorkflow(project)}\n                >\n                  工作流\n                </Button>,\n                <Button \n                  key=\"edit\"\n                  type=\"text\" \n                  icon={<EditOutlined />}\n                  onClick={() => handleEditProject(project)}\n                >\n                  编辑\n                </Button>,\n                <Popconfirm\n                  key=\"delete\"\n                  title=\"确定要删除这个项目吗？\"\n                  description=\"删除后将无法恢复，请谨慎操作。\"\n                  onConfirm={() => handleDeleteProject(project.id)}\n                  okText=\"确定\"\n                  cancelText=\"取消\"\n                >\n                  <Button \n                    type=\"text\" \n                    danger\n                    icon={<DeleteOutlined />}\n                  >\n                    删除\n                  </Button>\n                </Popconfirm>\n              ]}\n            >\n              <div className=\"mb-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <Title level={4} className=\"mb-0\">{project.name}</Title>\n                  <Tag color={getStatusColor(project.status)}>\n                    {getStatusText(project.status)}\n                  </Tag>\n                </div>\n                \n                <Paragraph \n                  type=\"secondary\" \n                  ellipsis={{ rows: 2 }}\n                  className=\"mb-3\"\n                >\n                  {project.description}\n                </Paragraph>\n\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">类型:</Text>\n                    <Text>{project.settings.genre}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">风格:</Text>\n                    <Text>{project.settings.style}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">目标字数:</Text>\n                    <Text>{project.settings.targetWordCount.toLocaleString()}</Text>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <Text type=\"secondary\">章节数:</Text>\n                    <Text>{project.settings.chapterCount}</Text>\n                  </div>\n                </div>\n\n                <div className=\"mt-4 pt-3 border-t border-gray-100\">\n                  <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                    <span className=\"flex items-center\">\n                      <CalendarOutlined className=\"mr-1\" />\n                      创建: {new Date(project.createdAt).toLocaleDateString()}\n                    </span>\n                    <span className=\"flex items-center\">\n                      <FileTextOutlined className=\"mr-1\" />\n                      更新: {new Date(project.updatedAt).toLocaleDateString()}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </Card>\n          ))}\n        </div>\n      )}\n\n      {/* 创建/编辑项目模态框 */}\n      <Modal\n        title={editingProject ? '编辑项目' : '创建新项目'}\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={600}\n        okText={editingProject ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            genre: '现代都市',\n            style: '轻松幽默',\n            targetWordCount: 100000,\n            chapterCount: 50,\n            language: 'zh-CN',\n          }}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"项目名称\"\n            rules={[{ required: true, message: '请输入项目名称' }]}\n          >\n            <Input placeholder=\"请输入项目名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"项目描述\"\n            rules={[{ required: true, message: '请输入项目描述' }]}\n          >\n            <Input.TextArea \n              rows={3} \n              placeholder=\"请简要描述您的小说项目...\" \n            />\n          </Form.Item>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"genre\"\n              label=\"小说类型\"\n              rules={[{ required: true, message: '请选择小说类型' }]}\n            >\n              <Select placeholder=\"请选择类型\">\n                <Option value=\"现代都市\">现代都市</Option>\n                <Option value=\"古代言情\">古代言情</Option>\n                <Option value=\"玄幻修仙\">玄幻修仙</Option>\n                <Option value=\"科幻未来\">科幻未来</Option>\n                <Option value=\"悬疑推理\">悬疑推理</Option>\n                <Option value=\"历史军事\">历史军事</Option>\n                <Option value=\"青春校园\">青春校园</Option>\n                <Option value=\"商战职场\">商战职场</Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item\n              name=\"style\"\n              label=\"写作风格\"\n              rules={[{ required: true, message: '请选择写作风格' }]}\n            >\n              <Select placeholder=\"请选择风格\">\n                <Option value=\"轻松幽默\">轻松幽默</Option>\n                <Option value=\"深沉严肃\">深沉严肃</Option>\n                <Option value=\"浪漫温馨\">浪漫温馨</Option>\n                <Option value=\"紧张刺激\">紧张刺激</Option>\n                <Option value=\"文艺清新\">文艺清新</Option>\n                <Option value=\"热血激昂\">热血激昂</Option>\n              </Select>\n            </Form.Item>\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-4\">\n            <Form.Item\n              name=\"targetWordCount\"\n              label=\"目标字数\"\n              rules={[{ required: true, message: '请输入目标字数' }]}\n            >\n              <InputNumber\n                min={10000}\n                max={1000000}\n                step={10000}\n                placeholder=\"100000\"\n                className=\"w-full\"\n                formatter={(value) => `${value}`.replace(/\\B(?=(\\d{3})+(?!\\d))/g, ',')}\n                parser={(value) => value!.replace(/\\$\\s?|(,*)/g, '')}\n              />\n            </Form.Item>\n\n            <Form.Item\n              name=\"chapterCount\"\n              label=\"预计章节数\"\n              rules={[{ required: true, message: '请输入章节数' }]}\n            >\n              <InputNumber\n                min={1}\n                max={500}\n                placeholder=\"50\"\n                className=\"w-full\"\n              />\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            name=\"language\"\n            label=\"语言\"\n            rules={[{ required: true, message: '请选择语言' }]}\n          >\n            <Select placeholder=\"请选择语言\">\n              <Option value=\"zh-CN\">简体中文</Option>\n              <Option value=\"en-US\">English</Option>\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectOverview;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;AA3BA;;;;;AA8BA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AAEzB,MAAM,kBAA4B;;IAChC,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,aAAa,EACb,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,YAAY,EACb,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,sBAAsB;QAC1B,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,KAAK,cAAc,CAAC;YAClB,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,OAAO,QAAQ,QAAQ,CAAC,KAAK;YAC7B,OAAO,QAAQ,QAAQ,CAAC,KAAK;YAC7B,iBAAiB,QAAQ,QAAQ,CAAC,eAAe;YACjD,cAAc,QAAQ,QAAQ,CAAC,YAAY;YAC3C,UAAU,QAAQ,QAAQ,CAAC,QAAQ;QACrC;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,QAAQ,EAAE;QAC5B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,AAAC,UAAsB,OAAb,QAAQ,IAAI;IACxC;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB,QAAQ,EAAE;QAC5B,aAAa;QACb,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,AAAC,UAAsB,OAAb,QAAQ,IAAI,EAAC;IACtC;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,gBAAgB;gBAClB,OAAO;gBACP,cAAc,eAAe,EAAE,EAAE;oBAC/B,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,UAAU;wBACR,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,iBAAiB,OAAO,eAAe;wBACvC,cAAc,OAAO,YAAY;wBACjC,UAAU,OAAO,QAAQ;oBAC3B;gBACF;gBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,QAAQ;gBACR,cAAc;oBACZ,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,QAAQ;oBACR,UAAU;wBACR,OAAO,OAAO,KAAK;wBACnB,OAAO,OAAO,KAAK;wBACnB,iBAAiB,OAAO,eAAe;wBACvC,cAAc,OAAO,YAAY;wBACjC,UAAU,OAAO,QAAQ;oBAC3B;gBACF;gBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,kBAAkB;YAClB,KAAK,WAAW;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;kCAEzB,6LAAC,qMAAA,CAAA,SAAM;wBACL,MAAK;wBACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBACnB,SAAS;kCACV;;;;;;;;;;;;YAKF,SAAS,MAAM,KAAK,kBACnB,6LAAC,iLAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mLAAA,CAAA,QAAK;oBACJ,2BACE,6LAAC;;0CACC,6LAAC;gCAAK,MAAK;0CAAY;;;;;;0CACvB,6LAAC;;;;;0CACD,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;;;;;;;;;;yEAM/B,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,iLAAA,CAAA,OAAI;wBAEH,WAAW,AAAC,qCAEX,OADC,CAAA,2BAAA,qCAAA,eAAgB,EAAE,MAAK,QAAQ,EAAE,GAAG,yBAAyB;wBAE/D,SAAS;0CACP,6LAAC,qMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;gCACtB,SAAS,IAAM,oBAAoB;0CACpC;+BAJK;;;;;0CAON,6LAAC,qMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,6LAAC,iOAAA,CAAA,qBAAkB;;;;;gCACzB,SAAS,IAAM,oBAAoB;0CACpC;+BAJK;;;;;0CAON,6LAAC,qMAAA,CAAA,SAAM;gCAEL,MAAK;gCACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gCACnB,SAAS,IAAM,kBAAkB;0CAClC;+BAJK;;;;;0CAON,6LAAC,6LAAA,CAAA,aAAU;gCAET,OAAM;gCACN,aAAY;gCACZ,WAAW,IAAM,oBAAoB,QAAQ,EAAE;gCAC/C,QAAO;gCACP,YAAW;0CAEX,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,MAAM;oCACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;8CACtB;;;;;;+BAXG;;;;;yBAeP;kCAED,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,OAAO;4CAAG,WAAU;sDAAQ,QAAQ,IAAI;;;;;;sDAC/C,6LAAC,+KAAA,CAAA,MAAG;4CAAC,OAAO,eAAe,QAAQ,MAAM;sDACtC,cAAc,QAAQ,MAAM;;;;;;;;;;;;8CAIjC,6LAAC;oCACC,MAAK;oCACL,UAAU;wCAAE,MAAM;oCAAE;oCACpB,WAAU;8CAET,QAAQ,WAAW;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;8DAAM,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sDAE/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;8DAAM,QAAQ,QAAQ,CAAC,KAAK;;;;;;;;;;;;sDAE/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;8DAAM,QAAQ,QAAQ,CAAC,eAAe,CAAC,cAAc;;;;;;;;;;;;sDAExD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DAAY;;;;;;8DACvB,6LAAC;8DAAM,QAAQ,QAAQ,CAAC,YAAY;;;;;;;;;;;;;;;;;;8CAIxC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,6NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAAS;oDAChC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;0DAErD,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,6NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAAS;oDAChC,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;uBA1FtD,QAAQ,EAAE;;;;;;;;;;0BAqGvB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,iBAAiB,SAAS;gBACjC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ,iBAAiB,OAAO;gBAChC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;oBACP,eAAe;wBACb,OAAO;wBACP,OAAO;wBACP,iBAAiB;wBACjB,cAAc;wBACd,UAAU;oBACZ;;sCAEA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;gCACb,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;8CAIzB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,6LAAC,qLAAA,CAAA,SAAM;wCAAC,aAAY;;0DAClB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,6LAAC;gDAAO,OAAM;0DAAO;;;;;;;;;;;;;;;;;;;;;;;sCAK3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAU;qCAAE;8CAE/C,cAAA,6LAAC,mMAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,MAAM;wCACN,aAAY;wCACZ,WAAU;wCACV,WAAW,CAAC,QAAU,AAAC,GAAQ,OAAN,OAAQ,OAAO,CAAC,yBAAyB;wCAClE,QAAQ,CAAC,QAAU,MAAO,OAAO,CAAC,eAAe;;;;;;;;;;;8CAIrD,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;oCACR,MAAK;oCACL,OAAM;oCACN,OAAO;wCAAC;4CAAE,UAAU;4CAAM,SAAS;wCAAS;qCAAE;8CAE9C,cAAA,6LAAC,mMAAA,CAAA,cAAW;wCACV,KAAK;wCACL,KAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAKhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCAAC,aAAY;;kDAClB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,6LAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GAhXM;;QASA,wHAAA,CAAA,cAAW;QAIA,iLAAA,CAAA,OAAI,CAAC;;;KAbhB;uCAkXS", "debugId": null}}, {"offset": {"line": 1448, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/worldbuilding/WorldBuildingManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Tabs,\n  List,\n  Tag,\n  Popconfirm,\n  message,\n  Row,\n  Col,\n  Divider,\n  Tree,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  GlobalOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EnvironmentOutlined,\n  BookOutlined,\n  SettingOutlined,\n  ThunderboltOutlined,\n  CrownOutlined,\n  HomeOutlined,\n  TeamOutlined,\n  DollarOutlined,\n  HistoryOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { WorldBuilding, WorldCategory } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst WorldBuildingManager: React.FC = () => {\n  const {\n    currentProject,\n    worldBuilding,\n    addWorldElement,\n    updateWorldElement,\n    deleteWorldElement,\n    getWorldElements\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingElement, setEditingElement] = useState<WorldBuilding | null>(null);\n  const [selectedCategory, setSelectedCategory] = useState<WorldCategory | 'all'>('all');\n  const [activeTab, setActiveTab] = useState('elements');\n  const [form] = Form.useForm();\n\n  const projectWorldElements = currentProject ? getWorldElements(currentProject.id) : [];\n\n  // 世界观分类配置\n  const worldCategories = [\n    {\n      key: 'setting' as WorldCategory,\n      label: '环境设定',\n      icon: <EnvironmentOutlined />,\n      color: 'green',\n      description: '地理环境、气候、地形等自然环境设定'\n    },\n    {\n      key: 'magic-system' as WorldCategory,\n      label: '魔法体系',\n      icon: <ThunderboltOutlined />,\n      color: 'purple',\n      description: '魔法规则、能力体系、超自然力量设定'\n    },\n    {\n      key: 'technology' as WorldCategory,\n      label: '科技水平',\n      icon: <SettingOutlined />,\n      color: 'blue',\n      description: '科技发展程度、工具、武器、交通等'\n    },\n    {\n      key: 'culture' as WorldCategory,\n      label: '文化背景',\n      icon: <BookOutlined />,\n      color: 'orange',\n      description: '宗教信仰、传统习俗、艺术文化等'\n    },\n    {\n      key: 'geography' as WorldCategory,\n      label: '地理结构',\n      icon: <GlobalOutlined />,\n      color: 'cyan',\n      description: '大陆分布、城市布局、重要地标等'\n    },\n    {\n      key: 'history' as WorldCategory,\n      label: '历史背景',\n      icon: <HistoryOutlined />,\n      color: 'gold',\n      description: '重大历史事件、时代变迁、传说故事等'\n    },\n    {\n      key: 'society' as WorldCategory,\n      label: '社会制度',\n      icon: <CrownOutlined />,\n      color: 'red',\n      description: '政治体制、社会阶层、法律制度等'\n    },\n    {\n      key: 'economy' as WorldCategory,\n      label: '经济体系',\n      icon: <DollarOutlined />,\n      color: 'lime',\n      description: '货币制度、贸易体系、经济结构等'\n    }\n  ];\n\n  const filteredElements = selectedCategory === 'all'\n    ? projectWorldElements\n    : projectWorldElements.filter(element => element.category === selectedCategory);\n\n  const handleCreateElement = (category?: WorldCategory) => {\n    setEditingElement(null);\n    if (category) {\n      form.setFieldsValue({ category });\n    } else {\n      form.resetFields();\n    }\n    setIsModalVisible(true);\n  };\n\n  const handleEditElement = (element: WorldBuilding) => {\n    setEditingElement(element);\n    form.setFieldsValue({\n      name: element.name,\n      category: element.category,\n      description: element.description,\n      details: element.details,\n    });\n    setIsModalVisible(true);\n  };\n\n  const handleDeleteElement = (elementId: string) => {\n    if (currentProject) {\n      deleteWorldElement(currentProject.id, elementId);\n      message.success('世界观元素已删除');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n\n      if (editingElement) {\n        // 更新元素\n        if (currentProject) {\n          updateWorldElement(currentProject.id, editingElement.id, {\n            ...values,\n            relationships: editingElement.relationships || [],\n            consistency: editingElement.consistency || [],\n          });\n          message.success('世界观元素已更新');\n        }\n      } else {\n        // 创建新元素\n        if (currentProject) {\n          addWorldElement(currentProject.id, {\n            ...values,\n            relationships: [],\n            consistency: [],\n          });\n          message.success('世界观元素已创建');\n        }\n      }\n\n      setIsModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    }\n  };\n\n  const handleModalCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n    setEditingElement(null);\n  };\n\n  const getCategoryConfig = (category: WorldCategory) => {\n    return worldCategories.find(cat => cat.key === category) || worldCategories[0];\n  };\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理世界观。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>世界观管理</Title>\n          <Text type=\"secondary\">管理故事世界观设定和背景 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => handleCreateElement()}\n          >\n            添加设定\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'elements',\n            label: (\n              <span>\n                <GlobalOutlined />\n                世界观元素 ({projectWorldElements.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {/* 分类过滤器 */}\n                <Card className=\"mb-6\" title=\"分类浏览\">\n                  <Row gutter={[16, 16]}>\n                    <Col span={3}>\n                      <Card\n                        size=\"small\"\n                        className={`cursor-pointer transition-all ${\n                          selectedCategory === 'all' ? 'ring-2 ring-blue-500' : ''\n                        }`}\n                        onClick={() => setSelectedCategory('all')}\n                      >\n                        <div className=\"text-center\">\n                          <GlobalOutlined className=\"text-2xl text-gray-500\" />\n                          <div className=\"mt-2\">\n                            <Text strong>全部</Text>\n                            <div className=\"text-xs text-gray-500\">\n                              {projectWorldElements.length} 个元素\n                            </div>\n                          </div>\n                        </div>\n                      </Card>\n                    </Col>\n                    {worldCategories.map((category) => {\n                      const count = projectWorldElements.filter(e => e.category === category.key).length;\n                      return (\n                        <Col span={3} key={category.key}>\n                          <Card\n                            size=\"small\"\n                            className={`cursor-pointer transition-all ${\n                              selectedCategory === category.key ? 'ring-2 ring-blue-500' : ''\n                            }`}\n                            onClick={() => setSelectedCategory(category.key)}\n                          >\n                            <div className=\"text-center\">\n                              <div className={`text-2xl text-${category.color}-500`}>\n                                {category.icon}\n                              </div>\n                              <div className=\"mt-2\">\n                                <Text strong>{category.label}</Text>\n                                <div className=\"text-xs text-gray-500\">\n                                  {count} 个元素\n                                </div>\n                              </div>\n                            </div>\n                          </Card>\n                        </Col>\n                      );\n                    })}\n                  </Row>\n                </Card>\n\n                {/* 快速创建模板 */}\n                <Card className=\"mb-6\" title=\"快速创建\">\n                  <Row gutter={16}>\n                    {worldCategories.slice(0, 4).map((category) => (\n                      <Col span={6} key={category.key}>\n                        <Card\n                          size=\"small\"\n                          className=\"cursor-pointer hover:shadow-md transition-shadow\"\n                          onClick={() => handleCreateElement(category.key)}\n                        >\n                          <div className=\"text-center\">\n                            <div className={`text-xl text-${category.color}-500`}>\n                              {category.icon}\n                            </div>\n                            <div className=\"mt-2\">\n                              <Text strong>{category.label}</Text>\n                              <div className=\"text-xs text-gray-500 mt-1\">\n                                {category.description}\n                              </div>\n                            </div>\n                          </div>\n                        </Card>\n                      </Col>\n                    ))}\n                  </Row>\n                </Card>\n\n                {/* 世界观元素列表 */}\n                {filteredElements.length === 0 ? (\n                  <Card className=\"text-center py-12\">\n                    <GlobalOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                    <div className=\"mt-4\">\n                      <Text type=\"secondary\">\n                        {selectedCategory === 'all' ? '还没有创建任何世界观元素' : `还没有创建${getCategoryConfig(selectedCategory as WorldCategory).label}相关的元素`}\n                      </Text>\n                      <br />\n                      <Text type=\"secondary\">点击上方按钮或使用模板快速创建</Text>\n                    </div>\n                  </Card>\n                ) : (\n                  <List\n                    grid={{ gutter: 16, column: 2 }}\n                    dataSource={filteredElements}\n                    renderItem={(element) => {\n                      const categoryConfig = getCategoryConfig(element.category);\n                      return (\n                        <List.Item>\n                          <Card\n                            className=\"hover:shadow-lg transition-shadow\"\n                            actions={[\n                              <Tooltip title=\"编辑\" key=\"edit\">\n                                <EditOutlined onClick={() => handleEditElement(element)} />\n                              </Tooltip>,\n                              <Popconfirm\n                                key=\"delete\"\n                                title=\"确定要删除这个世界观元素吗？\"\n                                onConfirm={() => handleDeleteElement(element.id)}\n                                okText=\"确定\"\n                                cancelText=\"取消\"\n                              >\n                                <DeleteOutlined />\n                              </Popconfirm>\n                            ]}\n                          >\n                            <div className=\"mb-3\">\n                              <div className=\"flex items-center justify-between mb-2\">\n                                <Title level={5} className=\"mb-0\">{element.name}</Title>\n                                <Tag\n                                  color={categoryConfig.color}\n                                  icon={categoryConfig.icon}\n                                >\n                                  {categoryConfig.label}\n                                </Tag>\n                              </div>\n                              <Paragraph\n                                ellipsis={{ rows: 3 }}\n                                type=\"secondary\"\n                              >\n                                {element.description}\n                              </Paragraph>\n                            </div>\n\n                            {element.details && Object.keys(element.details).length > 0 && (\n                              <div className=\"mt-3 pt-3 border-t border-gray-100\">\n                                <Text type=\"secondary\" className=\"text-sm\">\n                                  包含 {Object.keys(element.details).length} 个详细设定\n                                </Text>\n                              </div>\n                            )}\n\n                            {element.relationships.length > 0 && (\n                              <div className=\"mt-2\">\n                                <Badge count={element.relationships.length} showZero={false}>\n                                  <Text type=\"secondary\" className=\"text-sm\">关联元素</Text>\n                                </Badge>\n                              </div>\n                            )}\n                          </Card>\n                        </List.Item>\n                      );\n                    }}\n                  />\n                )}\n              </div>\n            ),\n          },\n          {\n            key: 'consistency',\n            label: (\n              <span>\n                <CheckCircleOutlined />\n                一致性检查\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <CheckCircleOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">一致性检查功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持世界观元素间的逻辑一致性验证</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n\n      {/* 创建/编辑世界观元素模态框 */}\n      <Modal\n        title={editingElement ? '编辑世界观元素' : '创建世界观元素'}\n        open={isModalVisible}\n        onOk={handleModalOk}\n        onCancel={handleModalCancel}\n        width={800}\n        okText={editingElement ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"name\"\n                label=\"元素名称\"\n                rules={[{ required: true, message: '请输入元素名称' }]}\n              >\n                <Input placeholder=\"请输入世界观元素名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"category\"\n                label=\"分类\"\n                rules={[{ required: true, message: '请选择分类' }]}\n              >\n                <Select placeholder=\"请选择分类\">\n                  {worldCategories.map((category) => (\n                    <Option key={category.key} value={category.key}>\n                      <Space>\n                        {category.icon}\n                        {category.label}\n                      </Space>\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"基本描述\"\n            rules={[{ required: true, message: '请输入基本描述' }]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请描述这个世界观元素的基本信息...\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"details\" label=\"详细设定\">\n            <TextArea\n              rows={6}\n              placeholder=\"请输入更详细的设定信息，如规则、特点、影响等...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default WorldBuildingManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;;;AAzCA;;;;;AA4CA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAE1B,MAAM,uBAAiC;;IACrC,MAAM,EACJ,cAAc,EACd,aAAa,EACb,eAAe,EACf,kBAAkB,EAClB,kBAAkB,EAClB,gBAAgB,EACjB,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC3E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,MAAM,uBAAuB,iBAAiB,iBAAiB,eAAe,EAAE,IAAI,EAAE;IAEtF,UAAU;IACV,MAAM,kBAAkB;QACtB;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,mOAAA,CAAA,sBAAmB;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;YACnB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;YACtB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;YACpB,OAAO;YACP,aAAa;QACf;QACA;YACE,KAAK;YACL,OAAO;YACP,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,mBAAmB,qBAAqB,QAC1C,uBACA,qBAAqB,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEhE,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;QAClB,IAAI,UAAU;YACZ,KAAK,cAAc,CAAC;gBAAE;YAAS;QACjC,OAAO;YACL,KAAK,WAAW;QAClB;QACA,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,KAAK,cAAc,CAAC;YAClB,MAAM,QAAQ,IAAI;YAClB,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,SAAS,QAAQ,OAAO;QAC1B;QACA,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,gBAAgB;YAClB,mBAAmB,eAAe,EAAE,EAAE;YACtC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,gBAAgB;gBAClB,OAAO;gBACP,IAAI,gBAAgB;oBAClB,mBAAmB,eAAe,EAAE,EAAE,eAAe,EAAE,EAAE;wBACvD,GAAG,MAAM;wBACT,eAAe,eAAe,aAAa,IAAI,EAAE;wBACjD,aAAa,eAAe,WAAW,IAAI,EAAE;oBAC/C;oBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF,OAAO;gBACL,QAAQ;gBACR,IAAI,gBAAgB;oBAClB,gBAAgB,eAAe,EAAE,EAAE;wBACjC,GAAG,MAAM;wBACT,eAAe,EAAE;wBACjB,aAAa,EAAE;oBACjB;oBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;gBAClB;YACF;YAEA,kBAAkB;YAClB,KAAK,WAAW;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,aAAa,eAAe,CAAC,EAAE;IAChF;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;;oCAAY;oCAAoB,eAAe,IAAI;;;;;;;;;;;;;kCAEhE,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM;sCAChB;;;;;;;;;;;;;;;;;0BAML,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCAAG;gCACV,qBAAqB,MAAM;gCAAC;;;;;;;wBAGxC,wBACE,6LAAC;;8CAEC,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,OAAM;8CAC3B,cAAA,6LAAC,+KAAA,CAAA,MAAG;wCAAC,QAAQ;4CAAC;4CAAI;yCAAG;;0DACnB,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAW,AAAC,iCAEX,OADC,qBAAqB,QAAQ,yBAAyB;oDAExD,SAAS,IAAM,oBAAoB;8DAEnC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,yNAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;0EAC1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAM;kFAAC;;;;;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,qBAAqB,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAMtC,gBAAgB,GAAG,CAAC,CAAC;gDACpB,MAAM,QAAQ,qBAAqB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,GAAG,EAAE,MAAM;gDAClF,qBACE,6LAAC,+KAAA,CAAA,MAAG;oDAAC,MAAM;8DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAW,AAAC,iCAEX,OADC,qBAAqB,SAAS,GAAG,GAAG,yBAAyB;wDAE/D,SAAS,IAAM,oBAAoB,SAAS,GAAG;kEAE/C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,AAAC,iBAA+B,OAAf,SAAS,KAAK,EAAC;8EAC7C,SAAS,IAAI;;;;;;8EAEhB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,MAAM;sFAAE,SAAS,KAAK;;;;;;sFAC5B,6LAAC;4EAAI,WAAU;;gFACZ;gFAAM;;;;;;;;;;;;;;;;;;;;;;;;mDAfE,SAAS,GAAG;;;;;4CAsBnC;;;;;;;;;;;;8CAKJ,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;oCAAO,OAAM;8CAC3B,cAAA,6LAAC,+KAAA,CAAA,MAAG;wCAAC,QAAQ;kDACV,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAChC,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,oBAAoB,SAAS,GAAG;8DAE/C,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,AAAC,gBAA8B,OAAf,SAAS,KAAK,EAAC;0EAC5C,SAAS,IAAI;;;;;;0EAEhB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAM;kFAAE,SAAS,KAAK;;;;;;kFAC5B,6LAAC;wEAAI,WAAU;kFACZ,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;+CAbZ,SAAS,GAAG;;;;;;;;;;;;;;;gCAwBpC,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,OAAO;gDAAE,UAAU;gDAAI,OAAO;4CAAU;;;;;;sDACxD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,MAAK;8DACR,qBAAqB,QAAQ,iBAAiB,AAAC,QAAkE,OAA3D,kBAAkB,kBAAmC,KAAK,EAAC;;;;;;8DAEpH,6LAAC;;;;;8DACD,6LAAC;oDAAK,MAAK;8DAAY;;;;;;;;;;;;;;;;;2DAI3B,6LAAC,iLAAA,CAAA,OAAI;oCACH,MAAM;wCAAE,QAAQ;wCAAI,QAAQ;oCAAE;oCAC9B,YAAY;oCACZ,YAAY,CAAC;wCACX,MAAM,iBAAiB,kBAAkB,QAAQ,QAAQ;wCACzD,qBACE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;sDACR,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDACH,WAAU;gDACV,SAAS;kEACP,6LAAC,uLAAA,CAAA,UAAO;wDAAC,OAAM;kEACb,cAAA,6LAAC,qNAAA,CAAA,eAAY;4DAAC,SAAS,IAAM,kBAAkB;;;;;;uDADzB;;;;;kEAGxB,6LAAC,6LAAA,CAAA,aAAU;wDAET,OAAM;wDACN,WAAW,IAAM,oBAAoB,QAAQ,EAAE;wDAC/C,QAAO;wDACP,YAAW;kEAEX,cAAA,6LAAC,yNAAA,CAAA,iBAAc;;;;;uDANX;;;;;iDAQP;;kEAED,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAM,OAAO;wEAAG,WAAU;kFAAQ,QAAQ,IAAI;;;;;;kFAC/C,6LAAC,+KAAA,CAAA,MAAG;wEACF,OAAO,eAAe,KAAK;wEAC3B,MAAM,eAAe,IAAI;kFAExB,eAAe,KAAK;;;;;;;;;;;;0EAGzB,6LAAC;gEACC,UAAU;oEAAE,MAAM;gEAAE;gEACpB,MAAK;0EAEJ,QAAQ,WAAW;;;;;;;;;;;;oDAIvB,QAAQ,OAAO,IAAI,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE,MAAM,GAAG,mBACxD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,MAAK;4DAAY,WAAU;;gEAAU;gEACrC,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE,MAAM;gEAAC;;;;;;;;;;;;oDAK7C,QAAQ,aAAa,CAAC,MAAM,GAAG,mBAC9B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;4DAAC,OAAO,QAAQ,aAAa,CAAC,MAAM;4DAAE,UAAU;sEACpD,cAAA,6LAAC;gEAAK,MAAK;gEAAY,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAOzD;;;;;;;;;;;;oBAKV;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,mOAAA,CAAA,sBAAmB;;;;;gCAAG;;;;;;;wBAI3B,wBACE,6LAAC,iLAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mOAAA,CAAA,sBAAmB;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CAC7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;0BAIH,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,iBAAiB,YAAY;gBACpC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,QAAQ,iBAAiB,OAAO;gBAChC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;yCAAE;kDAE7C,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;sDACjB,gBAAgB,GAAG,CAAC,CAAC,yBACpB,6LAAC;oDAA0B,OAAO,SAAS,GAAG;8DAC5C,cAAA,6LAAC,mMAAA,CAAA,QAAK;;4DACH,SAAS,IAAI;4DACb,SAAS,KAAK;;;;;;;mDAHN,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYnC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAIhB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BAAC,MAAK;4BAAU,OAAM;sCAC9B,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GArbM;;QAQA,wHAAA,CAAA,cAAW;QAMA,iLAAA,CAAA,OAAI,CAAC;;;KAdhB;uCAubS", "debugId": null}}, {"offset": {"line": 2433, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/document/DocumentManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Tree,\n  Tabs,\n  List,\n  Tag,\n  message,\n  Row,\n  Col,\n  Progress,\n  Tooltip,\n  Badge\n} from 'antd';\nimport {\n  FolderOutlined,\n  FileOutlined,\n  DownloadOutlined,\n  UploadOutlined,\n  SearchOutlined,\n  HistoryOutlined,\n  FileTextOutlined,\n  FilePdfOutlined,\n  FileWordOutlined,\n  FileExcelOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\n\nconst { Title, Text } = Typography;\n\nconst DocumentManager: React.FC = () => {\n  const { currentProject } = useAppStore();\n  const [activeTab, setActiveTab] = useState('structure');\n\n  // 模拟文档结构\n  const documentStructure = [\n    {\n      title: 'workflow-configs',\n      key: 'workflow-configs',\n      icon: <FolderOutlined />,\n      children: [\n        { title: 'main-workflow.json', key: 'main-workflow', icon: <FileOutlined /> },\n        { title: 'backup-workflow.json', key: 'backup-workflow', icon: <FileOutlined /> },\n      ]\n    },\n    {\n      title: 'prompts',\n      key: 'prompts',\n      icon: <FolderOutlined />,\n      children: [\n        { title: 'character-prompts.md', key: 'character-prompts', icon: <FileTextOutlined /> },\n        { title: 'plot-prompts.md', key: 'plot-prompts', icon: <FileTextOutlined /> },\n      ]\n    },\n    {\n      title: 'generated-content',\n      key: 'generated-content',\n      icon: <FolderOutlined />,\n      children: [\n        {\n          title: 'outlines',\n          key: 'outlines',\n          icon: <FolderOutlined />,\n          children: [\n            { title: 'main-outline.md', key: 'main-outline', icon: <FileTextOutlined /> },\n            { title: 'detailed-outline.md', key: 'detailed-outline', icon: <FileTextOutlined /> },\n          ]\n        },\n        {\n          title: 'characters',\n          key: 'characters',\n          icon: <FolderOutlined />,\n          children: [\n            { title: 'protagonist.json', key: 'protagonist', icon: <FileOutlined /> },\n            { title: 'supporting-characters.json', key: 'supporting', icon: <FileOutlined /> },\n          ]\n        },\n        {\n          title: 'chapters',\n          key: 'chapters',\n          icon: <FolderOutlined />,\n          children: [\n            { title: 'chapter-01.md', key: 'chapter-01', icon: <FileTextOutlined /> },\n            { title: 'chapter-02.md', key: 'chapter-02', icon: <FileTextOutlined /> },\n            { title: 'chapter-03.md', key: 'chapter-03', icon: <FileTextOutlined /> },\n          ]\n        }\n      ]\n    },\n    {\n      title: 'exports',\n      key: 'exports',\n      icon: <FolderOutlined />,\n      children: [\n        { title: 'novel-draft.docx', key: 'novel-docx', icon: <FileWordOutlined /> },\n        { title: 'novel-draft.pdf', key: 'novel-pdf', icon: <FilePdfOutlined /> },\n      ]\n    }\n  ];\n\n  if (!currentProject) {\n    return (\n      <div className=\"p-8 text-center\">\n        <Title level={3}>请先选择或创建一个项目</Title>\n        <Text type=\"secondary\">\n          您需要先在项目总览中创建或选择一个项目，然后才能管理文档。\n        </Text>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>文档管理</Title>\n          <Text type=\"secondary\">管理项目文档和文件 - 项目: {currentProject.name}</Text>\n        </div>\n        <Space>\n          <Button icon={<UploadOutlined />}>\n            导入文档\n          </Button>\n          <Button type=\"primary\" icon={<DownloadOutlined />}>\n            导出项目\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'structure',\n            label: (\n              <span>\n                <FolderOutlined />\n                文档结构\n              </span>\n            ),\n            children: (\n              <Row gutter={16}>\n                <Col span={8}>\n                  <Card title=\"项目文档树\">\n                    <Tree\n                      showIcon\n                      defaultExpandAll\n                      treeData={documentStructure}\n                      onSelect={(keys, info) => {\n                        if (keys.length > 0) {\n                          message.info(`选中文件: ${info.node.title}`);\n                        }\n                      }}\n                    />\n                  </Card>\n                </Col>\n                <Col span={16}>\n                  <Card title=\"文档预览\">\n                    <div className=\"text-center py-12\">\n                      <FileTextOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                      <div className=\"mt-4\">\n                        <Text type=\"secondary\">选择左侧文档查看内容</Text>\n                        <br />\n                        <Text type=\"secondary\">支持Markdown、JSON、文本文件预览</Text>\n                      </div>\n                    </div>\n                  </Card>\n                </Col>\n              </Row>\n            ),\n          },\n          {\n            key: 'versions',\n            label: (\n              <span>\n                <HistoryOutlined />\n                版本历史\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <HistoryOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">版本控制功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持文档版本管理和历史对比</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n    </div>\n  );\n};\n\nexport default DocumentManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AA/BA;;;;;AAiCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,6LAAA,CAAA,aAAU;AAElC,MAAM,kBAA4B;;IAChC,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,SAAS;IACT,MAAM,oBAAoB;QACxB;YACE,OAAO;YACP,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAsB,KAAK;oBAAiB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBAAI;gBAC5E;oBAAE,OAAO;oBAAwB,KAAK;oBAAmB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;gBAAI;aACjF;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAwB,KAAK;oBAAqB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBAAI;gBACtF;oBAAE,OAAO;oBAAmB,KAAK;oBAAgB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBAAI;aAC7E;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBACE,OAAO;oBACP,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,UAAU;wBACR;4BAAE,OAAO;4BAAmB,KAAK;4BAAgB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;wBAC5E;4BAAE,OAAO;4BAAuB,KAAK;4BAAoB,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;qBACrF;gBACH;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,UAAU;wBACR;4BAAE,OAAO;4BAAoB,KAAK;4BAAe,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBAAI;wBACxE;4BAAE,OAAO;4BAA8B,KAAK;4BAAc,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBAAI;qBAClF;gBACH;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBACrB,UAAU;wBACR;4BAAE,OAAO;4BAAiB,KAAK;4BAAc,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;wBACxE;4BAAE,OAAO;4BAAiB,KAAK;4BAAc,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;wBACxE;4BAAE,OAAO;4BAAiB,KAAK;4BAAc,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;wBAAI;qBACzE;gBACH;aACD;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,UAAU;gBACR;oBAAE,OAAO;oBAAoB,KAAK;oBAAc,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gBAAI;gBAC3E;oBAAE,OAAO;oBAAmB,KAAK;oBAAa,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;gBAAI;aACzE;QACH;KACD;IAED,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAM,OAAO;8BAAG;;;;;;8BACjB,6LAAC;oBAAK,MAAK;8BAAY;;;;;;;;;;;;IAK7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;;oCAAY;oCAAiB,eAAe,IAAI;;;;;;;;;;;;;kCAE7D,6LAAC,mMAAA,CAAA,QAAK;;0CACJ,6LAAC,qMAAA,CAAA,SAAM;gCAAC,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;0CAAK;;;;;;0CAGlC,6LAAC,qMAAA,CAAA,SAAM;gCAAC,MAAK;gCAAU,oBAAM,6LAAC,6NAAA,CAAA,mBAAgB;;;;;0CAAK;;;;;;;;;;;;;;;;;;0BAMvD,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,yNAAA,CAAA,iBAAc;;;;;gCAAG;;;;;;;wBAItB,wBACE,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;wCAAC,OAAM;kDACV,cAAA,6LAAC,iLAAA,CAAA,OAAI;4CACH,QAAQ;4CACR,gBAAgB;4CAChB,UAAU;4CACV,UAAU,CAAC,MAAM;gDACf,IAAI,KAAK,MAAM,GAAG,GAAG;oDACnB,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,AAAC,SAAwB,OAAhB,KAAK,IAAI,CAAC,KAAK;gDACvC;4CACF;;;;;;;;;;;;;;;;8CAIN,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;wCAAC,OAAM;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6NAAA,CAAA,mBAAgB;oDAAC,OAAO;wDAAE,UAAU;wDAAI,OAAO;oDAAU;;;;;;8DAC1D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,MAAK;sEAAY;;;;;;sEACvB,6LAAC;;;;;sEACD,6LAAC;4DAAK,MAAK;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOrC;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,2NAAA,CAAA,kBAAe;;;;;gCAAG;;;;;;;wBAIvB,wBACE,6LAAC,iLAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,2NAAA,CAAA,kBAAe;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CACzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;;;;;;;AAIT;GApKM;;QACuB,wHAAA,CAAA,cAAW;;;KADlC;uCAsKS", "debugId": null}}, {"offset": {"line": 2982, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/aa1/ai-novel-workflow/src/components/prompt/PromptManager.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Typography,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  Tabs,\n  List,\n  Tag,\n  Rate,\n  message,\n  Row,\n  Col,\n  Divider,\n  Badge,\n  Progress\n} from 'antd';\nimport {\n  FileTextOutlined,\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  CopyOutlined,\n  StarOutlined,\n  ExperimentOutlined,\n  Bar<PERSON>hartOutlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport { useAppStore } from '@/store';\nimport type { PromptTemplate } from '@/types';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst PromptManager: React.FC = () => {\n  const {\n    promptTemplates,\n    addPromptTemplate,\n    updatePromptTemplate,\n    deletePromptTemplate,\n    getPromptTemplatesByCategory\n  } = useAppStore();\n\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);\n  const [activeTab, setActiveTab] = useState('templates');\n  const [selectedCategory, setSelectedCategory] = useState<string>('all');\n  const [form] = Form.useForm();\n\n  // 提示词分类\n  const categories = [\n    { key: 'character', label: '角色生成', color: 'blue' },\n    { key: 'plot', label: '情节规划', color: 'green' },\n    { key: 'dialogue', label: '对话生成', color: 'orange' },\n    { key: 'description', label: '场景描述', color: 'purple' },\n    { key: 'title', label: '书名生成', color: 'red' },\n    { key: 'outline', label: '大纲规划', color: 'cyan' },\n    { key: 'worldbuilding', label: '世界观构建', color: 'gold' },\n    { key: 'polish', label: '内容润色', color: 'lime' },\n  ];\n\n  // 模拟提示词模板数据\n  const sampleTemplates: PromptTemplate[] = [\n    {\n      id: '1',\n      name: '主角角色生成',\n      category: 'character',\n      template: '请为一部{genre}小说创建一个{age}岁的{gender}主角。角色应该具有{personality}的性格特征，背景设定为{background}。请详细描述角色的外貌、性格、能力和成长经历。',\n      variables: [\n        { name: 'genre', type: 'string', description: '小说类型', defaultValue: '现代都市', required: true },\n        { name: 'age', type: 'number', description: '角色年龄', defaultValue: 25, required: true },\n        { name: 'gender', type: 'string', description: '角色性别', defaultValue: '男', required: true },\n        { name: 'personality', type: 'string', description: '性格特征', defaultValue: '勇敢、善良', required: true },\n        { name: 'background', type: 'string', description: '背景设定', defaultValue: '普通家庭', required: false },\n      ],\n      version: 1,\n      isActive: true,\n      performance: {\n        usage: 156,\n        rating: 4.5,\n        feedback: ['生成的角色很有特色', '背景设定合理', '可以增加更多细节'],\n      }\n    },\n    {\n      id: '2',\n      name: '情节冲突生成',\n      category: 'plot',\n      template: '为{genre}类型的小说设计一个{conflict_type}冲突。冲突应该涉及{characters}，发生在{setting}环境中。请描述冲突的起因、发展过程和可能的解决方案。',\n      variables: [\n        { name: 'genre', type: 'string', description: '小说类型', required: true },\n        { name: 'conflict_type', type: 'string', description: '冲突类型', required: true },\n        { name: 'characters', type: 'array', description: '涉及角色', required: true },\n        { name: 'setting', type: 'string', description: '场景设定', required: true },\n      ],\n      version: 2,\n      isActive: true,\n      performance: {\n        usage: 89,\n        rating: 4.2,\n        feedback: ['冲突设计有趣', '逻辑性强'],\n      }\n    }\n  ];\n\n  const filteredTemplates = selectedCategory === 'all'\n    ? sampleTemplates\n    : sampleTemplates.filter(template => template.category === selectedCategory);\n\n  const handleCreateTemplate = () => {\n    setEditingTemplate(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  const handleEditTemplate = (template: PromptTemplate) => {\n    setEditingTemplate(template);\n    form.setFieldsValue({\n      name: template.name,\n      category: template.category,\n      template: template.template,\n    });\n    setIsModalVisible(true);\n  };\n\n  const getCategoryConfig = (category: string) => {\n    return categories.find(cat => cat.key === category) || categories[0];\n  };\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <Title level={2}>提示词管理</Title>\n          <Text type=\"secondary\">管理AI提示词模板和配置</Text>\n        </div>\n        <Space>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={handleCreateTemplate}\n          >\n            创建模板\n          </Button>\n        </Space>\n      </div>\n\n      <Tabs\n        activeKey={activeTab}\n        onChange={setActiveTab}\n        items={[\n          {\n            key: 'templates',\n            label: (\n              <span>\n                <FileTextOutlined />\n                提示词模板 ({filteredTemplates.length})\n              </span>\n            ),\n            children: (\n              <div>\n                {/* 分类过滤器 */}\n                <Card className=\"mb-6\">\n                  <Row gutter={16}>\n                    <Col span={3}>\n                      <Card\n                        size=\"small\"\n                        className={`cursor-pointer transition-all ${\n                          selectedCategory === 'all' ? 'ring-2 ring-blue-500' : ''\n                        }`}\n                        onClick={() => setSelectedCategory('all')}\n                      >\n                        <div className=\"text-center\">\n                          <FileTextOutlined className=\"text-2xl text-gray-500\" />\n                          <div className=\"mt-2\">\n                            <Text strong>全部</Text>\n                            <div className=\"text-xs text-gray-500\">\n                              {sampleTemplates.length} 个模板\n                            </div>\n                          </div>\n                        </div>\n                      </Card>\n                    </Col>\n                    {categories.map((category) => {\n                      const count = sampleTemplates.filter(t => t.category === category.key).length;\n                      return (\n                        <Col span={3} key={category.key}>\n                          <Card\n                            size=\"small\"\n                            className={`cursor-pointer transition-all ${\n                              selectedCategory === category.key ? 'ring-2 ring-blue-500' : ''\n                            }`}\n                            onClick={() => setSelectedCategory(category.key)}\n                          >\n                            <div className=\"text-center\">\n                              <FileTextOutlined className={`text-2xl text-${category.color}-500`} />\n                              <div className=\"mt-2\">\n                                <Text strong>{category.label}</Text>\n                                <div className=\"text-xs text-gray-500\">\n                                  {count} 个模板\n                                </div>\n                              </div>\n                            </div>\n                          </Card>\n                        </Col>\n                      );\n                    })}\n                  </Row>\n                </Card>\n\n                {/* 模板列表 */}\n                <List\n                  grid={{ gutter: 16, column: 1 }}\n                  dataSource={filteredTemplates}\n                  renderItem={(template) => {\n                    const categoryConfig = getCategoryConfig(template.category);\n                    return (\n                      <List.Item>\n                        <Card\n                          className=\"hover:shadow-lg transition-shadow\"\n                          actions={[\n                            <Button\n                              key=\"copy\"\n                              type=\"text\"\n                              icon={<CopyOutlined />}\n                              onClick={() => {\n                                navigator.clipboard.writeText(template.template);\n                                message.success('模板已复制到剪贴板');\n                              }}\n                            >\n                              复制\n                            </Button>,\n                            <Button\n                              key=\"edit\"\n                              type=\"text\"\n                              icon={<EditOutlined />}\n                              onClick={() => handleEditTemplate(template)}\n                            >\n                              编辑\n                            </Button>,\n                            <Button\n                              key=\"delete\"\n                              type=\"text\"\n                              danger\n                              icon={<DeleteOutlined />}\n                              onClick={() => {\n                                message.info('删除功能开发中');\n                              }}\n                            >\n                              删除\n                            </Button>\n                          ]}\n                        >\n                          <Row gutter={24}>\n                            <Col span={16}>\n                              <div className=\"mb-3\">\n                                <div className=\"flex items-center justify-between mb-2\">\n                                  <Title level={5} className=\"mb-0\">{template.name}</Title>\n                                  <Space>\n                                    <Tag color={categoryConfig.color}>\n                                      {categoryConfig.label}\n                                    </Tag>\n                                    <Tag color={template.isActive ? 'green' : 'default'}>\n                                      {template.isActive ? '启用' : '禁用'}\n                                    </Tag>\n                                    <Text type=\"secondary\">v{template.version}</Text>\n                                  </Space>\n                                </div>\n\n                                <Paragraph\n                                  ellipsis={{ rows: 2 }}\n                                  type=\"secondary\"\n                                  className=\"mb-3\"\n                                >\n                                  {template.template}\n                                </Paragraph>\n\n                                <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                                  <span>变量: {template.variables.length} 个</span>\n                                  <span>使用: {template.performance.usage} 次</span>\n                                  <div className=\"flex items-center space-x-1\">\n                                    <Rate\n                                      disabled\n                                      value={template.performance.rating}\n                                      allowHalf\n                                      style={{ fontSize: 12 }}\n                                    />\n                                    <Text type=\"secondary\">({template.performance.rating})</Text>\n                                  </div>\n                                </div>\n                              </div>\n                            </Col>\n                            <Col span={8}>\n                              <div className=\"text-center\">\n                                <div className=\"mb-2\">\n                                  <Text strong className=\"text-lg\">{template.performance.rating}</Text>\n                                  <Text type=\"secondary\" className=\"ml-1\">/5.0</Text>\n                                </div>\n                                <Progress\n                                  percent={template.performance.rating * 20}\n                                  size=\"small\"\n                                  showInfo={false}\n                                />\n                                <div className=\"mt-2\">\n                                  <Badge count={template.performance.usage} showZero>\n                                    <Text type=\"secondary\" className=\"text-sm\">使用次数</Text>\n                                  </Badge>\n                                </div>\n                              </div>\n                            </Col>\n                          </Row>\n                        </Card>\n                      </List.Item>\n                    );\n                  }}\n                />\n              </div>\n            ),\n          },\n          {\n            key: 'analytics',\n            label: (\n              <span>\n                <BarChartOutlined />\n                性能分析\n              </span>\n            ),\n            children: (\n              <Card className=\"text-center py-12\">\n                <BarChartOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />\n                <div className=\"mt-4\">\n                  <Text type=\"secondary\">性能分析功能开发中</Text>\n                  <br />\n                  <Text type=\"secondary\">即将支持模板使用统计和效果分析</Text>\n                </div>\n              </Card>\n            ),\n          },\n        ]}\n      />\n\n      {/* 创建/编辑模板模态框 */}\n      <Modal\n        title={editingTemplate ? '编辑提示词模板' : '创建提示词模板'}\n        open={isModalVisible}\n        onOk={async () => {\n          try {\n            const values = await form.validateFields();\n            message.success(editingTemplate ? '模板已更新' : '模板已创建');\n            setIsModalVisible(false);\n            form.resetFields();\n          } catch (error) {\n            console.error('表单验证失败:', error);\n          }\n        }}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n        }}\n        width={800}\n        okText={editingTemplate ? '更新' : '创建'}\n        cancelText=\"取消\"\n      >\n        <Form form={form} layout=\"vertical\">\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"name\"\n                label=\"模板名称\"\n                rules={[{ required: true, message: '请输入模板名称' }]}\n              >\n                <Input placeholder=\"请输入模板名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"category\"\n                label=\"分类\"\n                rules={[{ required: true, message: '请选择分类' }]}\n              >\n                <Select placeholder=\"请选择分类\">\n                  {categories.map((category) => (\n                    <Option key={category.key} value={category.key}>\n                      {category.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"template\"\n            label=\"提示词模板\"\n            rules={[{ required: true, message: '请输入提示词模板' }]}\n            help=\"使用 {变量名} 的格式定义变量，如 {genre}、{character_name} 等\"\n          >\n            <TextArea\n              rows={8}\n              placeholder=\"请输入提示词模板，使用 {变量名} 定义可替换的变量...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default PromptManager;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAlCA;;;;;AAqCA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,6LAAA,CAAA,aAAU;AAC7C,MAAM,EAAE,MAAM,EAAE,GAAG,qLAAA,CAAA,SAAM;AACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,mLAAA,CAAA,QAAK;AAE1B,MAAM,gBAA0B;;IAC9B,MAAM,EACJ,eAAe,EACf,iBAAiB,EACjB,oBAAoB,EACpB,oBAAoB,EACpB,4BAA4B,EAC7B,GAAG,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,QAAQ;IACR,MAAM,aAAa;QACjB;YAAE,KAAK;YAAa,OAAO;YAAQ,OAAO;QAAO;QACjD;YAAE,KAAK;YAAQ,OAAO;YAAQ,OAAO;QAAQ;QAC7C;YAAE,KAAK;YAAY,OAAO;YAAQ,OAAO;QAAS;QAClD;YAAE,KAAK;YAAe,OAAO;YAAQ,OAAO;QAAS;QACrD;YAAE,KAAK;YAAS,OAAO;YAAQ,OAAO;QAAM;QAC5C;YAAE,KAAK;YAAW,OAAO;YAAQ,OAAO;QAAO;QAC/C;YAAE,KAAK;YAAiB,OAAO;YAAS,OAAO;QAAO;QACtD;YAAE,KAAK;YAAU,OAAO;YAAQ,OAAO;QAAO;KAC/C;IAED,YAAY;IACZ,MAAM,kBAAoC;QACxC;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;gBACT;oBAAE,MAAM;oBAAS,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAQ,UAAU;gBAAK;gBAC3F;oBAAE,MAAM;oBAAO,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAI,UAAU;gBAAK;gBACrF;oBAAE,MAAM;oBAAU,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAK,UAAU;gBAAK;gBACzF;oBAAE,MAAM;oBAAe,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAS,UAAU;gBAAK;gBAClG;oBAAE,MAAM;oBAAc,MAAM;oBAAU,aAAa;oBAAQ,cAAc;oBAAQ,UAAU;gBAAM;aAClG;YACD,SAAS;YACT,UAAU;YACV,aAAa;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;oBAAC;oBAAa;oBAAU;iBAAW;YAC/C;QACF;QACA;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;gBACT;oBAAE,MAAM;oBAAS,MAAM;oBAAU,aAAa;oBAAQ,UAAU;gBAAK;gBACrE;oBAAE,MAAM;oBAAiB,MAAM;oBAAU,aAAa;oBAAQ,UAAU;gBAAK;gBAC7E;oBAAE,MAAM;oBAAc,MAAM;oBAAS,aAAa;oBAAQ,UAAU;gBAAK;gBACzE;oBAAE,MAAM;oBAAW,MAAM;oBAAU,aAAa;oBAAQ,UAAU;gBAAK;aACxE;YACD,SAAS;YACT,UAAU;YACV,aAAa;gBACX,OAAO;gBACP,QAAQ;gBACR,UAAU;oBAAC;oBAAU;iBAAO;YAC9B;QACF;KACD;IAED,MAAM,oBAAoB,qBAAqB,QAC3C,kBACA,gBAAgB,MAAM,CAAC,CAAA,WAAY,SAAS,QAAQ,KAAK;IAE7D,MAAM,uBAAuB;QAC3B,mBAAmB;QACnB,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,KAAK,cAAc,CAAC;YAClB,MAAM,SAAS,IAAI;YACnB,UAAU,SAAS,QAAQ;YAC3B,UAAU,SAAS,QAAQ;QAC7B;QACA,kBAAkB;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,aAAa,UAAU,CAAC,EAAE;IACtE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,OAAO;0CAAG;;;;;;0CACjB,6LAAC;gCAAK,MAAK;0CAAY;;;;;;;;;;;;kCAEzB,6LAAC,mMAAA,CAAA,QAAK;kCACJ,cAAA,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS;sCACV;;;;;;;;;;;;;;;;;0BAML,6LAAC,iLAAA,CAAA,OAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;oBACL;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gCAAG;gCACZ,kBAAkB,MAAM;gCAAC;;;;;;;wBAGrC,wBACE,6LAAC;;8CAEC,6LAAC,iLAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,+KAAA,CAAA,MAAG;wCAAC,QAAQ;;0DACX,6LAAC,+KAAA,CAAA,MAAG;gDAAC,MAAM;0DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;oDACH,MAAK;oDACL,WAAW,AAAC,iCAEX,OADC,qBAAqB,QAAQ,yBAAyB;oDAExD,SAAS,IAAM,oBAAoB;8DAEnC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6NAAA,CAAA,mBAAgB;gEAAC,WAAU;;;;;;0EAC5B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,MAAM;kFAAC;;;;;;kFACb,6LAAC;wEAAI,WAAU;;4EACZ,gBAAgB,MAAM;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAMjC,WAAW,GAAG,CAAC,CAAC;gDACf,MAAM,QAAQ,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,SAAS,GAAG,EAAE,MAAM;gDAC7E,qBACE,6LAAC,+KAAA,CAAA,MAAG;oDAAC,MAAM;8DACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;wDACH,MAAK;wDACL,WAAW,AAAC,iCAEX,OADC,qBAAqB,SAAS,GAAG,GAAG,yBAAyB;wDAE/D,SAAS,IAAM,oBAAoB,SAAS,GAAG;kEAE/C,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6NAAA,CAAA,mBAAgB;oEAAC,WAAW,AAAC,iBAA+B,OAAf,SAAS,KAAK,EAAC;;;;;;8EAC7D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,MAAM;sFAAE,SAAS,KAAK;;;;;;sFAC5B,6LAAC;4EAAI,WAAU;;gFACZ;gFAAM;;;;;;;;;;;;;;;;;;;;;;;;mDAbE,SAAS,GAAG;;;;;4CAoBnC;;;;;;;;;;;;8CAKJ,6LAAC,iLAAA,CAAA,OAAI;oCACH,MAAM;wCAAE,QAAQ;wCAAI,QAAQ;oCAAE;oCAC9B,YAAY;oCACZ,YAAY,CAAC;wCACX,MAAM,iBAAiB,kBAAkB,SAAS,QAAQ;wCAC1D,qBACE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;sDACR,cAAA,6LAAC,iLAAA,CAAA,OAAI;gDACH,WAAU;gDACV,SAAS;kEACP,6LAAC,qMAAA,CAAA,SAAM;wDAEL,MAAK;wDACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wDACnB,SAAS;4DACP,UAAU,SAAS,CAAC,SAAS,CAAC,SAAS,QAAQ;4DAC/C,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;wDAClB;kEACD;uDAPK;;;;;kEAUN,6LAAC,qMAAA,CAAA,SAAM;wDAEL,MAAK;wDACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wDACnB,SAAS,IAAM,mBAAmB;kEACnC;uDAJK;;;;;kEAON,6LAAC,qMAAA,CAAA,SAAM;wDAEL,MAAK;wDACL,MAAM;wDACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;wDACrB,SAAS;4DACP,uLAAA,CAAA,UAAO,CAAC,IAAI,CAAC;wDACf;kEACD;uDAPK;;;;;iDAUP;0DAED,cAAA,6LAAC,+KAAA,CAAA,MAAG;oDAAC,QAAQ;;sEACX,6LAAC,+KAAA,CAAA,MAAG;4DAAC,MAAM;sEACT,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAM,OAAO;gFAAG,WAAU;0FAAQ,SAAS,IAAI;;;;;;0FAChD,6LAAC,mMAAA,CAAA,QAAK;;kGACJ,6LAAC,+KAAA,CAAA,MAAG;wFAAC,OAAO,eAAe,KAAK;kGAC7B,eAAe,KAAK;;;;;;kGAEvB,6LAAC,+KAAA,CAAA,MAAG;wFAAC,OAAO,SAAS,QAAQ,GAAG,UAAU;kGACvC,SAAS,QAAQ,GAAG,OAAO;;;;;;kGAE9B,6LAAC;wFAAK,MAAK;;4FAAY;4FAAE,SAAS,OAAO;;;;;;;;;;;;;;;;;;;kFAI7C,6LAAC;wEACC,UAAU;4EAAE,MAAM;wEAAE;wEACpB,MAAK;wEACL,WAAU;kFAET,SAAS,QAAQ;;;;;;kFAGpB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;;oFAAK;oFAAK,SAAS,SAAS,CAAC,MAAM;oFAAC;;;;;;;0FACrC,6LAAC;;oFAAK;oFAAK,SAAS,WAAW,CAAC,KAAK;oFAAC;;;;;;;0FACtC,6LAAC;gFAAI,WAAU;;kGACb,6LAAC,iLAAA,CAAA,OAAI;wFACH,QAAQ;wFACR,OAAO,SAAS,WAAW,CAAC,MAAM;wFAClC,SAAS;wFACT,OAAO;4FAAE,UAAU;wFAAG;;;;;;kGAExB,6LAAC;wFAAK,MAAK;;4FAAY;4FAAE,SAAS,WAAW,CAAC,MAAM;4FAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAK7D,6LAAC,+KAAA,CAAA,MAAG;4DAAC,MAAM;sEACT,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,MAAM;gFAAC,WAAU;0FAAW,SAAS,WAAW,CAAC,MAAM;;;;;;0FAC7D,6LAAC;gFAAK,MAAK;gFAAY,WAAU;0FAAO;;;;;;;;;;;;kFAE1C,6LAAC,yLAAA,CAAA,WAAQ;wEACP,SAAS,SAAS,WAAW,CAAC,MAAM,GAAG;wEACvC,MAAK;wEACL,UAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,mLAAA,CAAA,QAAK;4EAAC,OAAO,SAAS,WAAW,CAAC,KAAK;4EAAE,QAAQ;sFAChD,cAAA,6LAAC;gFAAK,MAAK;gFAAY,WAAU;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAS7D;;;;;;;;;;;;oBAIR;oBACA;wBACE,KAAK;wBACL,qBACE,6LAAC;;8CACC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;gCAAG;;;;;;;wBAIxB,wBACE,6LAAC,iLAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,6NAAA,CAAA,mBAAgB;oCAAC,OAAO;wCAAE,UAAU;wCAAI,OAAO;oCAAU;;;;;;8CAC1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,MAAK;sDAAY;;;;;;sDACvB,6LAAC;;;;;sDACD,6LAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;oBAI/B;iBACD;;;;;;0BAIH,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,kBAAkB,YAAY;gBACrC,MAAM;gBACN,MAAM;oBACJ,IAAI;wBACF,MAAM,SAAS,MAAM,KAAK,cAAc;wBACxC,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,kBAAkB,UAAU;wBAC5C,kBAAkB;wBAClB,KAAK,WAAW;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B;gBACF;gBACA,UAAU;oBACR,kBAAkB;oBAClB,KAAK,WAAW;gBAClB;gBACA,OAAO;gBACP,QAAQ,kBAAkB,OAAO;gBACjC,YAAW;0BAEX,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,MAAM;oBAAM,QAAO;;sCACvB,6LAAC,+KAAA,CAAA,MAAG;4BAAC,QAAQ;;8CACX,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAU;yCAAE;kDAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;4CAAC,aAAY;;;;;;;;;;;;;;;;8CAGvB,6LAAC,+KAAA,CAAA,MAAG;oCAAC,MAAM;8CACT,cAAA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;wCACR,MAAK;wCACL,OAAM;wCACN,OAAO;4CAAC;gDAAE,UAAU;gDAAM,SAAS;4CAAQ;yCAAE;kDAE7C,cAAA,6LAAC,qLAAA,CAAA,SAAM;4CAAC,aAAY;sDACjB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oDAA0B,OAAO,SAAS,GAAG;8DAC3C,SAAS,KAAK;mDADJ,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;sCASnC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAW;6BAAE;4BAChD,MAAK;sCAEL,cAAA,6LAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GAnXM;;QAOA,wHAAA,CAAA,cAAW;QAMA,iLAAA,CAAA,OAAI,CAAC;;;KAbhB;uCAqXS", "debugId": null}}]}