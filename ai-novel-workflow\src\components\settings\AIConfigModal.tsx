'use client';

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Card,
  Space,
  Typography,
  Alert,
  Divider,
  InputNumber,
  Tabs,
  message
} from 'antd';
import {
  SettingOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons';
import { aiService } from '@/utils/aiService';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface AIConfigModalProps {
  visible: boolean;
  onClose: () => void;
  onConfigured?: () => void;
}

const AIConfigModal: React.FC<AIConfigModalProps> = ({
  visible,
  onClose,
  onConfigured
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [provider, setProvider] = useState<string>('openai');

  useEffect(() => {
    if (visible) {
      const config = aiService.getConfig();
      if (config) {
        form.setFieldsValue(config);
        setProvider(config.provider);
      }
    }
  }, [visible, form]);

  const handleProviderChange = (value: string) => {
    setProvider(value);
    setTestResult(null);
  };

  const handleTest = async () => {
    try {
      setTesting(true);
      const values = await form.validateFields();
      
      // 临时设置配置进行测试
      aiService.setConfig(values);
      
      // 发送测试请求
      const response = await aiService.analyzeWorkflowRequirements({
        genre: '现代都市',
        style: '轻松幽默',
        length: '短篇',
        experience: '新手',
        features: []
      });

      if (response.success) {
        setTestResult({ success: true, message: 'API连接成功！' });
      } else {
        setTestResult({ success: false, message: response.error || '连接失败' });
      }
    } catch (error: any) {
      setTestResult({ success: false, message: error.message || '测试失败' });
    } finally {
      setTesting(false);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      aiService.setConfig(values);
      message.success('AI配置已保存');
      onConfigured?.();
      onClose();
    } catch (error) {
      console.error('保存配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getProviderInfo = (provider: string) => {
    const info = {
      openai: {
        name: 'OpenAI',
        description: '支持GPT-3.5、GPT-4等模型',
        defaultModel: 'gpt-3.5-turbo',
        defaultUrl: 'https://api.openai.com/v1/chat/completions',
        keyPlaceholder: 'sk-...',
        models: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview']
      },
      claude: {
        name: 'Anthropic Claude',
        description: '支持Claude-3系列模型',
        defaultModel: 'claude-3-sonnet-20240229',
        defaultUrl: 'https://api.anthropic.com/v1/messages',
        keyPlaceholder: 'sk-ant-...',
        models: ['claude-3-haiku-20240307', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229']
      },
      gemini: {
        name: 'Google Gemini',
        description: '支持Gemini Pro模型',
        defaultModel: 'gemini-pro',
        defaultUrl: 'https://generativelanguage.googleapis.com/v1beta/models',
        keyPlaceholder: 'AI...',
        models: ['gemini-pro', 'gemini-pro-vision']
      },
      custom: {
        name: '自定义API',
        description: '支持兼容OpenAI格式的自定义API',
        defaultModel: 'custom-model',
        defaultUrl: 'https://your-api.com/v1/chat/completions',
        keyPlaceholder: '您的API密钥',
        models: []
      }
    };
    return info[provider as keyof typeof info];
  };

  const providerInfo = getProviderInfo(provider);

  return (
    <Modal
      title={
        <div className="flex items-center space-x-2">
          <ApiOutlined className="text-blue-500" />
          <span>AI API 配置</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={700}
      footer={[
        <Button key="test" onClick={handleTest} loading={testing}>
          测试连接
        </Button>,
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave} loading={loading}>
          保存配置
        </Button>
      ]}
    >
      <div className="space-y-6">
        <Alert
          message="配置AI API以启用智能功能"
          description="配置后可使用AI工作流助手、智能书名生成、角色创建等功能"
          type="info"
          showIcon
        />

        <Form form={form} layout="vertical">
          <Form.Item
            name="provider"
            label="AI服务提供商"
            rules={[{ required: true, message: '请选择AI服务提供商' }]}
          >
            <Select onChange={handleProviderChange} placeholder="请选择提供商">
              <Option value="openai">
                <div className="flex items-center justify-between">
                  <span>OpenAI</span>
                  <Text type="secondary">GPT-3.5/4</Text>
                </div>
              </Option>
              <Option value="claude">
                <div className="flex items-center justify-between">
                  <span>Anthropic Claude</span>
                  <Text type="secondary">Claude-3</Text>
                </div>
              </Option>
              <Option value="gemini">
                <div className="flex items-center justify-between">
                  <span>Google Gemini</span>
                  <Text type="secondary">Gemini Pro</Text>
                </div>
              </Option>
              <Option value="custom">
                <div className="flex items-center justify-between">
                  <span>自定义API</span>
                  <Text type="secondary">兼容格式</Text>
                </div>
              </Option>
            </Select>
          </Form.Item>

          {providerInfo && (
            <Card size="small" className="bg-gray-50">
              <div className="space-y-2">
                <Title level={5} className="mb-1">{providerInfo.name}</Title>
                <Text type="secondary">{providerInfo.description}</Text>
              </div>
            </Card>
          )}

          <Form.Item
            name="apiKey"
            label="API密钥"
            rules={[{ required: true, message: '请输入API密钥' }]}
          >
            <Input.Password
              placeholder={providerInfo?.keyPlaceholder}
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Form.Item
            name="baseUrl"
            label="API地址"
            help="留空使用默认地址，或输入自定义API地址"
          >
            <Input placeholder={providerInfo?.defaultUrl} />
          </Form.Item>

          <Form.Item
            name="model"
            label="模型名称"
            rules={[{ required: true, message: '请选择或输入模型名称' }]}
          >
            {providerInfo?.models.length ? (
              <Select placeholder="请选择模型">
                {providerInfo.models.map(model => (
                  <Option key={model} value={model}>{model}</Option>
                ))}
              </Select>
            ) : (
              <Input placeholder={providerInfo?.defaultModel} />
            )}
          </Form.Item>

          <Divider>高级设置</Divider>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="temperature"
              label="创造性 (Temperature)"
              help="0-1之间，越高越有创造性"
            >
              <InputNumber
                min={0}
                max={1}
                step={0.1}
                placeholder="0.7"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="maxTokens"
              label="最大输出长度"
              help="生成内容的最大token数"
            >
              <InputNumber
                min={100}
                max={4000}
                step={100}
                placeholder="2000"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </div>

          {testResult && (
            <Alert
              message={testResult.success ? "连接成功" : "连接失败"}
              description={testResult.message}
              type={testResult.success ? "success" : "error"}
              showIcon
              icon={testResult.success ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
            />
          )}
        </Form>

        <Card size="small" title="获取API密钥">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <Text>OpenAI:</Text>
              <Button 
                type="link" 
                size="small"
                onClick={() => window.open('https://platform.openai.com/api-keys', '_blank')}
              >
                获取API Key
              </Button>
            </div>
            <div className="flex justify-between items-center">
              <Text>Anthropic:</Text>
              <Button 
                type="link" 
                size="small"
                onClick={() => window.open('https://console.anthropic.com/', '_blank')}
              >
                获取API Key
              </Button>
            </div>
            <div className="flex justify-between items-center">
              <Text>Google AI:</Text>
              <Button 
                type="link" 
                size="small"
                onClick={() => window.open('https://makersuite.google.com/app/apikey', '_blank')}
              >
                获取API Key
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </Modal>
  );
};

export default AIConfigModal;
